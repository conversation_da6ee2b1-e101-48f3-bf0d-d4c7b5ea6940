-- 创建财务报表相关表
-- 执行日期: 2025-07-14

-- 营收报表日报表
CREATE TABLE IF NOT EXISTS `erp_finance_income_report_daily` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `report_date` date NOT NULL COMMENT '报表日期',
    `class_id` int(11) NOT NULL COMMENT '班级ID',
    `class_name` varchar(255) DEFAULT NULL COMMENT '班级名称',
    `class_type` varchar(50) DEFAULT NULL COMMENT '班型(长期/短期)',
    `period` varchar(100) DEFAULT NULL COMMENT '期段',
    `start_date` date DEFAULT NULL COMMENT '开班时间',
    `class_status` int(11) DEFAULT NULL COMMENT '班级状态',
    `course_type` varchar(100) DEFAULT NULL COMMENT '课程类型',
    `course_name` varchar(255) DEFAULT NULL COMMENT '课程名称',
    `teacher_id` int(11) DEFAULT NULL COMMENT '授课教师ID',
    `teacher_name` varchar(100) DEFAULT NULL COMMENT '授课教师名称',
    `student_count` int(11) DEFAULT 0 COMMENT '学生数量',
    `total_class_times` int(11) DEFAULT 0 COMMENT '总课次',
    `completed_class_times` int(11) DEFAULT 0 COMMENT '已完成课次',
    `course_income_receivable` decimal(15,2) DEFAULT 0.00 COMMENT '课程收入应收',
    `discount_amount` decimal(15,2) DEFAULT 0.00 COMMENT '优惠金额',
    `course_refund` decimal(15,2) DEFAULT 0.00 COMMENT '课程退款',
    `course_income_actual` decimal(15,2) DEFAULT 0.00 COMMENT '课程收入实收',
    `lecture_fee_income` decimal(15,2) DEFAULT 0.00 COMMENT '讲义费收入',
    `lecture_fee_refund` decimal(15,2) DEFAULT 0.00 COMMENT '讲义费退费',
    `unconsumed_amount` decimal(15,2) DEFAULT 0.00 COMMENT '未课消金额',
    `consumed_amount` decimal(15,2) DEFAULT 0.00 COMMENT '已课消金额',
    `course_advisor_commission` decimal(15,2) DEFAULT 0.00 COMMENT '课程顾问提成',
    `actual_teacher_class_fee` decimal(15,2) DEFAULT 0.00 COMMENT '实际教师课时费',
    `current_gross_profit` decimal(15,2) DEFAULT 0.00 COMMENT '当前毛利',
    `current_gross_profit_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '当前毛利率',
    `other_expenses` decimal(15,2) DEFAULT 0.00 COMMENT '其他支出',
    `current_actual_profit` decimal(15,2) DEFAULT 0.00 COMMENT '当前实际利润',
    `current_actual_profit_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '当前实际利润率',
    `average_profit_per_class` decimal(15,2) DEFAULT 0.00 COMMENT '平均利润(每节)',
    `average_profit_per_student` decimal(15,2) DEFAULT 0.00 COMMENT '平均利润(每人次)',
    `expected_course_total_income` decimal(15,2) DEFAULT 0.00 COMMENT '预计课程总收入',
    `expected_teacher_class_fee` decimal(15,2) DEFAULT 0.00 COMMENT '预计教师课时费',
    `expected_profit` decimal(15,2) DEFAULT 0.00 COMMENT '预期利润',
    `expected_profit_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '预期利润率',
    `expected_average_profit_per_class` decimal(15,2) DEFAULT 0.00 COMMENT '预期平均利润(每节)',
    `expected_average_profit_per_student` decimal(15,2) DEFAULT 0.00 COMMENT '预期平均利润(每人次)',
    `disable` int(11) DEFAULT 0 COMMENT '是否禁用 0-否 1-是',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` int(11) DEFAULT NULL COMMENT '创建人ID',
    `update_by` int(11) DEFAULT NULL COMMENT '更新人ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_report_date_class` (`report_date`, `class_id`),
    KEY `idx_report_date` (`report_date`),
    KEY `idx_class_id` (`class_id`),
    KEY `idx_teacher_id` (`teacher_id`),
    KEY `idx_disable` (`disable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='营收报表日报表';

-- 简易报表日报表
CREATE TABLE IF NOT EXISTS `erp_finance_simple_report_daily` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `report_date` date NOT NULL COMMENT '报表日期',
    `cost_type_id` int(11) NOT NULL COMMENT '费用类型ID',
    `cost_type_name` varchar(255) DEFAULT NULL COMMENT '费用类型名称',
    `parent_id` int(11) DEFAULT NULL COMMENT '父级费用类型ID',
    `is_parent` tinyint(1) DEFAULT 0 COMMENT '是否为父级分类',
    `label_category` varchar(50) DEFAULT NULL COMMENT '标签大类(营业毛利/总成本/其他收入/其他支出)',
    `january` decimal(15,2) DEFAULT 0.00 COMMENT '一月金额',
    `february` decimal(15,2) DEFAULT 0.00 COMMENT '二月金额',
    `march` decimal(15,2) DEFAULT 0.00 COMMENT '三月金额',
    `april` decimal(15,2) DEFAULT 0.00 COMMENT '四月金额',
    `may` decimal(15,2) DEFAULT 0.00 COMMENT '五月金额',
    `june` decimal(15,2) DEFAULT 0.00 COMMENT '六月金额',
    `july` decimal(15,2) DEFAULT 0.00 COMMENT '七月金额',
    `august` decimal(15,2) DEFAULT 0.00 COMMENT '八月金额',
    `september` decimal(15,2) DEFAULT 0.00 COMMENT '九月金额',
    `october` decimal(15,2) DEFAULT 0.00 COMMENT '十月金额',
    `november` decimal(15,2) DEFAULT 0.00 COMMENT '十一月金额',
    `december` decimal(15,2) DEFAULT 0.00 COMMENT '十二月金额',
    `quarter1` decimal(15,2) DEFAULT 0.00 COMMENT '第一季度金额',
    `quarter2` decimal(15,2) DEFAULT 0.00 COMMENT '第二季度金额',
    `quarter3` decimal(15,2) DEFAULT 0.00 COMMENT '第三季度金额',
    `quarter4` decimal(15,2) DEFAULT 0.00 COMMENT '第四季度金额',
    `half_year1` decimal(15,2) DEFAULT 0.00 COMMENT '上半年金额',
    `half_year2` decimal(15,2) DEFAULT 0.00 COMMENT '下半年金额',
    `annual` decimal(15,2) DEFAULT 0.00 COMMENT '全年金额',
    `total` decimal(15,2) DEFAULT 0.00 COMMENT '费用类型合计金额',
    `summary` text COMMENT '费用类型摘要信息',
    `disable` int(11) DEFAULT 0 COMMENT '是否禁用 0-否 1-是',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` int(11) DEFAULT NULL COMMENT '创建人ID',
    `update_by` int(11) DEFAULT NULL COMMENT '更新人ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_report_date_cost_type` (`report_date`, `cost_type_id`),
    KEY `idx_report_date` (`report_date`),
    KEY `idx_cost_type_id` (`cost_type_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_label_category` (`label_category`),
    KEY `idx_disable` (`disable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简易报表日报表';

-- 报表生成日志
CREATE TABLE IF NOT EXISTS `erp_finance_report_generation_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `report_type` varchar(50) NOT NULL COMMENT '报表类型(income_report/simple_report)',
    `report_date` date NOT NULL COMMENT '报表日期',
    `generation_start_time` datetime DEFAULT NULL COMMENT '生成开始时间',
    `generation_end_time` datetime DEFAULT NULL COMMENT '生成结束时间',
    `generation_duration` int(11) DEFAULT NULL COMMENT '生成耗时(秒)',
    `status` varchar(20) DEFAULT 'running' COMMENT '生成状态(running/success/failed)',
    `records_processed` int(11) DEFAULT 0 COMMENT '处理记录数',
    `error_message` text COMMENT '错误信息',
    `disable` int(11) DEFAULT 0 COMMENT '是否禁用 0-否 1-是',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` int(11) DEFAULT NULL COMMENT '创建人ID',
    `update_by` int(11) DEFAULT NULL COMMENT '更新人ID',
    PRIMARY KEY (`id`),
    KEY `idx_report_type_date` (`report_type`, `report_date`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_disable` (`disable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报表生成日志';