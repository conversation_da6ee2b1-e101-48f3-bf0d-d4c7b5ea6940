-- 课消统计相关数据库索引优化SQL
-- 创建时间: 2024-12-20
-- 用途: 优化课消统计查询性能

-- ==============================
-- 订单学生表相关索引
-- ==============================

-- 订单学生表 - 班级和禁用状态索引
CREATE INDEX IF NOT EXISTS idx_erp_order_student_class_disable 
ON erp_order_student (class_id, disable);

-- 订单学生表 - 学生和禁用状态索引
CREATE INDEX IF NOT EXISTS idx_erp_order_student_stu_disable 
ON erp_order_student (stu_id, disable);

-- 订单学生表 - 学生状态和禁用状态索引
CREATE INDEX IF NOT EXISTS idx_erp_order_student_state_disable 
ON erp_order_student (student_state, disable);

-- ==============================
-- 订单表相关索引
-- ==============================

-- 订单表 - 订单学生ID、类型和禁用状态索引
CREATE INDEX IF NOT EXISTS idx_erp_order_order_student_type_disable 
ON erp_order (order_student_id, order_class_type, disable);

-- 订单表 - 创建时间和禁用状态索引
CREATE INDEX IF NOT EXISTS idx_erp_order_create_time_disable 
ON erp_order (create_time, disable);

-- 订单表 - 类型、禁用状态、创建时间复合索引
CREATE INDEX IF NOT EXISTS idx_erp_order_type_disable_create_time 
ON erp_order (order_class_type, disable, create_time);

-- ==============================
-- 签到表相关索引
-- ==============================

-- 签到表 - 订单学生ID和禁用状态索引
CREATE INDEX IF NOT EXISTS idx_erp_class_checking_order_student_disable 
ON erp_class_checking (order_student_id, disable);

-- 签到表 - 签到状态和禁用状态索引
CREATE INDEX IF NOT EXISTS idx_erp_class_checking_status_disable 
ON erp_class_checking (check_status, disable);

-- 签到表 - 创建时间和禁用状态索引
CREATE INDEX IF NOT EXISTS idx_erp_class_checking_create_time_disable 
ON erp_class_checking (create_time, disable);

-- 签到表 - 订单学生ID、签到状态、禁用状态复合索引（最重要的索引）
CREATE INDEX IF NOT EXISTS idx_erp_class_checking_order_status_disable 
ON erp_class_checking (order_student_id, check_status, disable);

-- ==============================
-- 退款详情表相关索引
-- ==============================

-- 退款详情表 - 订单学生ID、退款状态、禁用状态索引
CREATE INDEX IF NOT EXISTS idx_erp_order_refund_detail_order_student_state 
ON erp_order_refund_detail (order_student_id, refund_state, disable);

-- ==============================
-- 关联表索引（用于JOIN优化）
-- ==============================

-- 班级表 - 教室ID和禁用状态索引
CREATE INDEX IF NOT EXISTS idx_erp_class_classroom_disable 
ON erp_class (classroom_id, disable);

-- 班级表 - 课程ID和禁用状态索引
CREATE INDEX IF NOT EXISTS idx_erp_class_course_disable 
ON erp_class (course_id, disable);

-- 学生表 - 学生姓名和禁用状态索引（支持模糊查询）
CREATE INDEX IF NOT EXISTS idx_erp_student_name_disable 
ON erp_student (stu_name, disable);

-- 课程表 - 课程名称和禁用状态索引（支持模糊查询）
CREATE INDEX IF NOT EXISTS idx_erp_course_name_disable 
ON erp_course (course_name, disable);

-- 班级表 - 班级名称和禁用状态索引（支持模糊查询）
CREATE INDEX IF NOT EXISTS idx_erp_class_name_disable 
ON erp_class (class_name, disable);

-- 教室表 - 教学中心和禁用状态索引
CREATE INDEX IF NOT EXISTS idx_erp_office_classroom_center_disable 
ON erp_office_classroom (center_id, disable);

-- ==============================
-- 索引创建完成提示
-- ==============================

-- 查看创建的索引
SELECT 
    table_name,
    index_name,
    column_name,
    seq_in_index
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name IN (
    'erp_order_student', 
    'erp_order', 
    'erp_class_checking', 
    'erp_order_refund_detail',
    'erp_class',
    'erp_student',
    'erp_course',
    'erp_office_classroom'
)
AND index_name LIKE 'idx_%'
ORDER BY table_name, index_name, seq_in_index; 