import os

from pythonnet import load
load("coreclr")

import clr

DLL_PATH = os.path.join(os.getcwd(), 'modules', 'cmb', 'c_merchant_bank', 'dll', 'payLibrary.dll')
# print(f"Depend on third filepath：{DLL_PATH}")
# 加载dll文件,导入c#命名空间
clr.AddReference(DLL_PATH)

from payLibrary import GmUtil as CGmUtil, SignUtil as CSignUtil

PK = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEI2JuBTsKz9bAJudAxaGEAshoYFTOAGs2XQzGg93NlW67em9KSacSd0z0KVHsV87JtO38ZrEExHw7+qSCB3Qmsw=='
SK = '3D9E6435699C50197A58BE1FA0BFA7F92ABCAFC6D69217DF68C05ED8319253AB'


class SignUtil:
    """
    招行 签名工具
    """

    @staticmethod
    def get_sign_content(sorted_params):
        """
        参数排序
        """
        content = []
        sorted_dict = sorted(sorted_params.items())
        for key, value in sorted_dict:
            if key is not None and value is not None:
                content.append(f"{key}={value}")
        result = "&".join(content)
        return result

    @staticmethod
    def generate_sm_sign(data, merchant_config=None):
        """
        加签
        """
        sorted_data = SignUtil.get_sign_content(data)
        return CGmUtil.generateSmSign(sorted_data, merchant_config.Sk)

    @staticmethod
    def verify_sm_sign(result, merchant_config=None):
        """
        验签
        """
        sign = result['sign']
        del result['sign']
        sign_content = SignUtil.get_sign_content(result)
        flag = CGmUtil.verifySmSign(sign_content, sign, merchant_config.Pk)
        return flag
