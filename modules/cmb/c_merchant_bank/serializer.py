from fastapi import FastAPI
from pydantic import BaseModel, Field
from typing import Optional


class MiniProgramOrderRequest(BaseModel):
    """
    小程序下单参数
    """
    version: str = Field(..., max_length=5)
    encoding: str = Field(..., max_length=20)
    sign: str = Field(..., max_length=1024)
    signMethod: str = Field(..., max_length=2)
    merId: str = Field(..., max_length=32)
    deviceInfo: Optional[str] = Field(None, max_length=32)
    orderId: str = Field(..., max_length=32)
    tradeType: str = Field(..., max_length=16)
    userId: str = Field(..., max_length=32)
    body: str = Field(..., max_length=128)
    goodsDetail: Optional[str] = Field(None, max_length=6000)
    goodsTag: Optional[str] = Field(None, max_length=32)
    attach: Optional[str] = Field(None, max_length=127)
    mchReserved: Optional[str] = Field(None, max_length=128)
    payValidTime: Optional[str] = Field(None, max_length=10)
    notifyUrl: str = Field(..., max_length=256)
    txnAmt: str = Field(..., max_length=13)
    orderOrigAmt: Optional[str] = Field(None, max_length=13)
    orderCouponAmt: Optional[str] = Field(None, max_length=13)
    currencyCode: Optional[str] = Field(None, max_length=3)
    spbillCreateIp: str = Field(..., max_length=64)
    limitPay: Optional[str] = Field(None, max_length=32)
    identity: Optional[str] = Field(None, max_length=256)
    policyNo: Optional[str] = Field(None, max_length=100)
    region: Optional[str] = Field(None, max_length=64)
    terminalInfo: Optional[str] = Field(None, max_length=6000)
    limitPayer: Optional[str] = Field(None, max_length=1000)
    tradeAddressInfo: Optional[str] = Field(None, max_length=1024)


class MiniProgramBankResponse(BaseModel):
    """小程序下单银行响应"""
    version: str = Field(..., max_length=5)
    encoding: str = Field(..., max_length=20)
    sign: str = Field(..., max_length=1024)
    signMethod: str = Field(..., max_length=2)
    returnCode: str = Field(..., max_length=7)
    merId: Optional[str] = Field(None, max_length=32)
    orderId: Optional[str] = Field(None, max_length=32)
    tradeType: Optional[str] = Field(None, max_length=16)
    respCode: Optional[str] = Field(None, max_length=10)
    errDescription: Optional[str] = Field(None, max_length=256)
    cmbOrderId: Optional[str] = Field(None, max_length=32)
    txnTime: Optional[str] = Field(None, max_length=14)
    encryptedCmbOrderId: Optional[str] = Field(None, max_length=32)
    encryptedTradeInfo: Optional[str] = Field(None, max_length=8000)
    cmbMiniAppId: Optional[str] = Field(None, max_length=32)
    errCode: Optional[str] = Field(None, max_length=32)
    respMsg: Optional[str] = Field(None, max_length=256)
