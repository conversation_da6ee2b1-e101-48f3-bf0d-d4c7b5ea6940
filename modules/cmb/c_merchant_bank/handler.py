
import copy
import json
from collections import OrderedDict
from settings import logger
import aiohttp
import hashlib
import time
from modules.cmb.c_merchant_bank.assets.sign_util import SignUtil

PUB_PARAMS = {
    "version": "0.0.1",
    "encoding": "UTF-8",
    "signMethod": "02",
}


class MerchantBank:

    def __init__(self):
        self.merchant_config = None
        self.main_url = None

    @classmethod
    async def create(cls, mc=None):
        """异步工厂方法，用于创建类的实例并加载配置。"""
        instance = cls()
        # if not refund_order_no:
        #     mc = await order_module.get_merchant_config(db, merchant_type)
        #     # logger.info(f'获取默认商户配置：{mc}, {mc.MerchantName}', )
        # else:
        #     if str(refund_order_no).startswith('R'):
        #         mc = await order_crud.get_merchant_config_by_order(db, refund_order_no)
        #     else:
        #         mc = await order_crud.get_merchant_config_by_orderno(db, refund_order_no)
        #     logger.info(f'获取原商户配置：{mc.MerchantName}, 订单号：{refund_order_no}', )
        if not mc:
            return None
        instance.merchant_config = mc
        instance.main_url = mc.CmbBaseUrl
        instance._initialize_urls()
        return instance

    def _initialize_urls(self):
        """初始化所有的URL。"""
        self.wechat_xcx_url = f'{self.main_url}/polypay/v1.0/mchorders/MiniAppOrderApply'  # 微信小程序
        self.wechat_union_url = f'{self.main_url}/polypay/v1.0/mchorders/onlinepay'  # 微信统一下单
        self.alipay_url = f'{self.main_url}/polypay/v1.0/mchorders/servpay'  # 支付宝服务窗
        self.ysf_url = f'{self.main_url}/polypay/v1.0/mchorders/cloudpay'  # 云闪付
        self.query_result_url = f'{self.main_url}/polypay/v1.0/mchorders/orderquery'
        self.refund_url = f'{self.main_url}/polypay/v1.0/mchorders/refund'
        self.refund_query_url = f'{self.main_url}/polypay/v1.0/mchorders/refundquery'
        self.close_order_url = f'{self.main_url}/polypay/v1.0/mchorders/close'
        self.bill_download_url = f'{self.main_url}/polypay/v1.0/mchorders/statementurl'

        self.test_url = f'{self.main_url}/polypay/v1.0/mchorders/onlinepay'
        # biz_content模版
        self.biz_content_template = OrderedDict([
            ("merId", self.merchant_config.CmbMerId),
            ("userId", self.merchant_config.CmbUserId),
        ])

    async def request(self, url, data):
        async with aiohttp.ClientSession() as session:
            async with session.post(url, data=json.dumps(data), headers=self.get_header(data)) as response:
                resp = await response.json()
                return resp

    def get_header(self, data):
        timestamp = int(time.time())
        api_sign = {
            "appid": self.merchant_config.CmdAppId,
            "secret": self.merchant_config.CmdAppSecret,
            "sign": data['sign'],
            "timestamp": str(timestamp),
        }
        # MD5
        md5_content = SignUtil.get_sign_content(api_sign)
        md5 = hashlib.md5()
        md5.update(md5_content.encode('utf-8'))
        api_sign_string = md5.hexdigest()

        api_header = {
            "appid": self.merchant_config.CmdAppId,
            "timestamp": str(timestamp),
            "apisign": api_sign_string,
        }
        return api_header

    async def request_with_sign(self, url, data):
        # 加签
        sign = SignUtil.generate_sm_sign(data, self.merchant_config)
        data['sign'] = sign
        # 请求
        resp = await self.request(url, data=data)
        # logger.info(f"【响应】：{resp}")
        # 验签
        flag = SignUtil.verify_sm_sign(resp, self.merchant_config)
        if not flag:
            logger.warning('验签失败')
            return False
        return resp

    async def cmb_union_order(self, order_data):
        """
        微信统一下单接口
            拿到响应报文中的paydata参数，再用paydata去调用微信原生的jsapi支付接口就可以拉起支付
        1、商户系统生成订单并调用统一下单接口；
        2、商户收到聚合收单平台同步应答的支付数据，商户自行调用微信SDK并将支付数据填充到指定字段拉起微信支付；
        3、用户输入密码或指纹完成支付；
        """
        data = {}
        data.update(PUB_PARAMS)  # 加入公共参数
        order_data.update(self.biz_content_template)  # 加入模版参数
        biz_content = order_data
        sorted_biz_content = OrderedDict(sorted(biz_content.items(), key=lambda t: t[0]))  # 排序
        data["biz_content"] = json.dumps(sorted_biz_content)  # 转json
        logger.info(f'【微信下单参数】：{data}')
        resp = await self.request_with_sign(self.wechat_union_url, data)
        logger.info(f"【微信下单响应】：{resp}")
        return resp

    async def cmb_alipay_order(self, order_data):
        """
        支付宝服务窗支付
            拿到响应报文中的paydata参数，再用paydata去调用微信原生的jsapi支付接口就可以拉起支付
        1、商户系统生成订单并调用统一下单接口；
        2、商户收到聚合收单平台同步应答的支付数据，商户自行调用微信SDK并将支付数据填充到指定字段拉起微信支付；
        3、用户输入密码或指纹完成支付；
        """
        data = {}
        data.update(PUB_PARAMS)  # 加入公共参数
        order_data.update(self.biz_content_template)  # 加入模版参数
        biz_content = order_data
        sorted_biz_content = OrderedDict(sorted(biz_content.items(), key=lambda t: t[0]))  # 排序
        data["biz_content"] = json.dumps(sorted_biz_content)  # 转json
        logger.info(f'【支付宝下单参数】：{data}')
        resp = await self.request_with_sign(self.alipay_url, data)
        logger.info(f"【支付宝下单响应】：{resp}")
        return resp

    async def cmb_query_order(self, order_no):
        """
        支付结果查询
        1、商户系统生成订单并调用统一下单接口；
        2、商户收到聚合收单平台同步应答的支付数据，商户自行调用微信SDK并将支付数据填充到指定字段拉起微信支付；
        3、用户输入密码或指纹界面退出支付；
        4、商户在拉起支付后一定时间内未收到异步回调通知，应由定时作业主动发起查询；
        4、一定时间后（尽量超过5分钟）且在订单有效时间内用户再次发起支付并输入密码或指纹完成支付；
        5、用户支付完成后商户收到聚合收单平台异步回调通知，更新交易状态并停止查询；
        （建议商户在拉起支付5秒钟之后第一次发起查询，后续每5-10秒查询一次，轮询10次。)
        """

        data = {}
        order_data = {
            "orderId": order_no,
        }
        data.update(PUB_PARAMS)  # 加入公共参数
        order_data.update(self.biz_content_template)  # 加入模版参数
        biz_content = order_data
        sorted_biz_content = OrderedDict(sorted(biz_content.items(), key=lambda t: t[0]))  # 排序
        data["biz_content"] = json.dumps(sorted_biz_content)  # 转json
        return await self.request_with_sign(self.query_result_url, data)

    async def cmb_refund_apply(self, order_no,refund_order_no, txn_amt, refund_amt):
        """
        退款申请
        1、商户端对使用微信绑定的借记卡支付成功的订单发起退款申请；
        2、用户手机APP客户端收到微信的退款发起通知，商户后台收到聚合收单平台同步应答的退款受理成功（P状态）应答，应由定时作业主动发起查询；
        3、一定时间（正常情况下一两分钟）内查回退款成功状态，不给用户发货；

        """
        # if DEBUG:
        #     return False
        data = {}
        order_data = {
            "origOrderId": order_no,  # 原订单号
            "mchReserved": "Refund",
            "orderId": refund_order_no,  # 退款单号
            "txnAmt": f"{int(float(txn_amt) * 100)}",
            "refundAmt": f"{int(float(refund_amt) * 100)}",
        }
        data.update(PUB_PARAMS)  # 加入公共参数
        order_data.update(self.biz_content_template)  # 加入模版参数
        # logger.info(f'【退款发起】:{order_data}')
        biz_content = order_data
        sorted_biz_content = OrderedDict(sorted(biz_content.items(), key=lambda t: t[0]))  # 排序
        data["biz_content"] = json.dumps(sorted_biz_content)  # 转json
        logger.info(f'【退款参数】：{data}')
        resp = await self.request_with_sign(self.refund_url, data)
        logger.info(f"【退款响应】：{resp}")
        return resp

    async def cmb_refund_query(self, order_no):
        """
        退款结果查询
        1、商户端对使用微信绑定的信用卡支付成功的订单发起退款申请；
        2、用户手机APP客户端收到微信的退款发起通知，商户后台收到聚合收单平台同步应答的退款受理成功（P状态）应答，应由定时作业主动发起查询；
        3、建议查询机制为30秒查一次，一定时间（如十分钟）内未查回退款成功状态，则停止查询并以T+1日对账单状态为准；
        """
        data = {}
        order_data = {
            "orderId": order_no,
        }
        data.update(PUB_PARAMS)  # 加入公共参数
        order_data.update(self.biz_content_template)  # 加入模版参数
        biz_content = order_data
        sorted_biz_content = OrderedDict(sorted(biz_content.items(), key=lambda t: t[0]))  # 排序
        data["biz_content"] = json.dumps(sorted_biz_content)  # 转json
        return await self.request_with_sign(self.refund_query_url, data)

    async def cmb_close_order(self, order_no):
        """
        关单
        1、商户调用付款码收款关单接口对案例CMB_test003的订单发起关单；
        2、商户收到聚合收单平台同步应答的关单成功应答；
        """
        data = {}
        order_data = {
            "origOrderId": order_no,
        }
        data.update(PUB_PARAMS)  # 加入公共参数
        order_data.update(self.biz_content_template)  # 加入模版参数
        biz_content = order_data
        sorted_biz_content = OrderedDict(sorted(biz_content.items(), key=lambda t: t[0]))  # 排序
        data["biz_content"] = json.dumps(sorted_biz_content)  # 转json
        return await self.request_with_sign(self.close_order_url, data)

    async def cmb_bill_download(self, date_str):
        """
        对账单下载
        1、商户根据商户号，账单日期，获取对账单下载链接，通过链接下载对账单文件；（注：所查询日期有成功订单，
            并且支付类型建议包含支付宝、微信和银联，兼有支付和退款。原则上T日的账单只能等到T+1日下午16:30后下载；
        2、商户通过对账单下载链接获取账单详情，核查对账单数据明细与商户侧订单信息；
        """
        data = {}
        order_data = {
            "billDate": "20230927",
        }
        data.update(PUB_PARAMS)  # 加入公共参数
        new_biz_content = copy.deepcopy(self.biz_content_template)
        del new_biz_content['userId']
        order_data.update(new_biz_content)  # 加入模版参数
        biz_content = order_data
        sorted_biz_content = OrderedDict(sorted(biz_content.items(), key=lambda t: t[0]))  # 排序
        data["biz_content"] = json.dumps(sorted_biz_content)  # 转json
        return await self.request_with_sign(self.bill_download_url, data)
