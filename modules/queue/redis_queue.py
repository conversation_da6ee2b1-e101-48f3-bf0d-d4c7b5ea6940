import json

import aioredis
import asyncio
from typing import AsyncGenerator, Optional
from settings import logger, redis_url as ru
from tenacity import retry, wait_fixed, stop_after_attempt


class RedisQueue:
    def __init__(self, redis_url: str = ru):
        self.redis_url = redis_url
        self.connection_timeout = 3.0  # 连接超时时间
        self.operation_timeout = 5.0   # 操作超时时间

    async def get_redis(self) -> AsyncGenerator[aioredis.Redis, None]:
        retries = 3  # 减少重试次数
        for attempt in range(retries):
            try:
                # 添加连接超时
                redis = await asyncio.wait_for(
                    aioredis.from_url(self.redis_url), 
                    timeout=self.connection_timeout
                )
                try:
                    yield redis
                finally:
                    await redis.close()
                break
            except asyncio.TimeoutError:
                if attempt < retries - 1:
                    logger.warning(f"Redis连接超时，第{attempt + 1}次重试...")
                    await asyncio.sleep(1)
                else:
                    logger.error(f"Redis连接超时，已重试{retries}次")
                    raise aioredis.ConnectionError("Redis连接超时")
            except (aioredis.exceptions.ConnectionError, aioredis.ConnectionError) as e:
                if attempt < retries - 1:
                    logger.warning(f"Redis connection failed, retrying ({attempt + 1}/{retries})...")
                    await asyncio.sleep(1)  # 等待一段时间再重试
                else:
                    logger.error("Redis connection failed after multiple attempts")
                    raise e

    @retry(wait=wait_fixed(2), stop=stop_after_attempt(3))
    async def produce_message(self, queue_name: str, message: dict):
        try:
            async for redis in self.get_redis():
                await asyncio.wait_for(
                    redis.rpush(queue_name, json.dumps(message)),
                    timeout=self.operation_timeout
                )
        except asyncio.TimeoutError:
            logger.error(f"Redis队列消息生产超时: {queue_name}")
            raise
        except Exception as e:
            logger.error(f"Redis队列消息生产失败: {str(e)}")
            raise

    async def consume_message(self, queue_name: str) -> Optional[dict]:
        """
        从队列中消费一条消息
        
        Returns:
            dict: 消息数据，如果队列为空则返回None
        """
        max_retries = 3  # 减少重试次数
        for attempt in range(max_retries):
            try:
                async for redis in self.get_redis():
                    # 添加操作超时
                    message = await asyncio.wait_for(
                        redis.lpop(queue_name),
                        timeout=self.operation_timeout
                    )
                    if message:
                        return json.loads(message)
                    return None  # 队列为空，直接返回None
            except asyncio.TimeoutError:
                if attempt < max_retries - 1:
                    logger.warning(f"Redis消费操作超时，第{attempt + 1}次重试...")
                    await asyncio.sleep(1)
                else:
                    logger.error(f"Redis消费操作超时，已重试{max_retries}次")
                    return None
            except (aioredis.exceptions.ConnectionError, aioredis.ConnectionError) as e:
                if attempt < max_retries - 1:
                    logger.warning(f"Redis consume failed, retrying ({attempt + 1}/{max_retries})...")
                    await asyncio.sleep(2)
                else:
                    logger.error(f"Redis consume failed after {max_retries} attempts: {e}")
                    return None
            except json.JSONDecodeError as e:
                logger.error(f"Failed to decode message from queue {queue_name}: {e}")
                return None
            except Exception as e:
                logger.error(f"Unexpected error in consume_message: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                else:
                    return None
        
        return None
