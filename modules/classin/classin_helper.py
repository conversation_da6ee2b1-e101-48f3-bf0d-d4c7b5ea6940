import asyncio
import hashlib
import json
from datetime import datetime, timezone
from typing import Dict, Any
import aiohttp

import settings

headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded",
    "Host": "dynamic.eeo.cn",
    "Origin": "https://console.eeo.cn",
    "Referer": "https://console.eeo.cn/",
    "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    "Sec-Ch-Ua-Mobile": "?0",
    "Sec-Ch-Ua-Platform": '"Windows"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                  "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
}


async def fetch_data(cookies, url, data):
    async with aiohttp.ClientSession(cookies=cookies, headers=headers) as session:
        async with session.post(url, data=data) as response:
            response_data = await response.text()
            status = response.status
            return status, response_data


class ClassInHelper:

    @staticmethod
    def get_safe_key(secretkey: str) -> (str, str):
        timestamp = ClassInHelper.get_time_stamp()
        return hashlib.md5((secretkey + timestamp).encode()).hexdigest(), timestamp

    @staticmethod
    def get_time_stamp() -> str:
        dt_start = datetime(1970, 1, 1, tzinfo=timezone.utc)
        dt_now = datetime.now(timezone.utc)
        to_now = (dt_now - dt_start).total_seconds()
        return str(int(to_now))

    @staticmethod
    def get_time_stamp_from_date(dt_now: datetime) -> str:
        dt_start = datetime.utcfromtimestamp(0)
        to_now = (dt_now - dt_start).total_seconds()
        return str(int(to_now))

    @staticmethod
    def get_json_to_convert(json_obj: Dict[str, Any]) -> str:
        return '&'.join([f'{k}={v}' for k, v in json_obj.items()])

    @staticmethod
    async def request_classin(url, params):
        """
        请求 class in
        """
        # 获取安全码
        safe_key, timestamp = ClassInHelper.get_safe_key(settings.CLASSIN_CONFIG['SECRET'])
        params.update({
            'SID': settings.CLASSIN_CONFIG['SID'],
            'safeKey': safe_key,
            'timeStamp': timestamp,
        })
        # 格式化数据
        data = ClassInHelper.get_json_to_convert(params)
        # 请求
        async with aiohttp.ClientSession() as session:
            async with session.post(url,
                                    data=data.encode('utf-8'),
                                    headers={
                                        'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'
                                    }) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {'data': 0, 'error_info': {'errno': -1, 'error': "数据解析失败"}}

    @staticmethod
    async def register_user(telephone: str, nickname: str, password: str, addToSchoolMember: int = None):
        """
        注册用户
        """
        url = "https://api.eeo.cn/partner/api/course.api.php?action=register"
        params = {
            'telephone': telephone,
            'nickname': nickname,
            'password': password or "jjsw@2024...",
            'addToSchoolMember': addToSchoolMember or 1
        }
        return await ClassInHelper.request_classin(url, params)

    @staticmethod
    async def add_school_student(user_model: Dict[str, Any]) -> Dict[str, Any]:
        """
        增加注册学生
        """
        url = "https://api.eeo.cn/partner/api/course.api.php?action=addSchoolStudent"
        return await ClassInHelper.request_classin(url, {
            'studentAccount': user_model['studentAccount'],
            'studentName': user_model['studentName']
        })

    @staticmethod
    async def add_course_student(user_model: Dict[str, Any]) -> Dict[str, Any]:
        """
        增加课程下学生
        """
        url = "https://api.eeo.cn/partner/api/course.api.php?action=addCourseStudent"
        return await ClassInHelper.request_classin(url, {
            "courseId": user_model['courseId'],
            "identity": user_model['identity'],
            "studentUid": user_model['studentUid'],
            "studentName": user_model['studentName'],
        })

    @staticmethod
    async def remove_course_student(user_model: Dict[str, Any]) -> Dict[str, Any]:
        """
        删除课程下学生
        """
        url = "https://api.eeo.cn/partner/api/course.api.php?action=delCourseStudent"
        return await ClassInHelper.request_classin(url, {
            "courseId": user_model['courseId'],
            "identity": user_model['identity'],
            "studentUid": user_model['studentUid'],
        })

    @staticmethod
    async def add_course_insert_student(user_model: Dict[str, Any]) -> Dict[str, Any]:
        """
        增加插班生
        """
        url = "https://api.eeo.cn/partner/api/course.api.php?action=addClassStudentMultiple"
        return await ClassInHelper.request_classin(url, {
            'courseId': user_model['courseId'],
            'classId': user_model['classId'],
            'identity': user_model['identity'],
            'studentJson': json.dumps(user_model['studentJson']),
        })

    @staticmethod
    async def remove_course_insert_student(user_model: Dict[str, Any]) -> Dict[str, Any]:
        """
        移除插班生
        """
        url = "https://api.eeo.cn/partner/api/course.api.php?action=delClassStudentMultiple"
        return await ClassInHelper.request_classin(url, {
            'courseId': user_model['courseId'],
            'classId': user_model['classId'],
            'identity': user_model['identity'],
            'studentUidJson': json.dumps(user_model['studentJson']),
        })

    @staticmethod
    async def ModifyGroupMemberNickname(user_model: Dict[str, Any]) -> Dict[str, Any]:
        """
        学生添加完毕之后 更新群班级昵称
        """
        url = "https://api.eeo.cn/partner/api/course.api.php?action=modifyGroupMemberNickname"
        return await ClassInHelper.request_classin(url, {
            "courseId": user_model['courseId'],
        })
