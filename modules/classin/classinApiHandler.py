import asyncio
import hashlib
import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Union
import aiohttp
import time

import settings

logger = logging.getLogger(__name__)

class ClassInApiBase:
    """ClassIn API 基础类，提供签名和请求方法"""

    def __init__(self, sid: str = None, secret: str = None, base_url: str = None):
        """
        初始化 ClassIn API 基础类
        
        Args:
            sid: ClassIn SID，默认从settings获取
            secret: ClassIn Secret，默认从settings获取
            base_url: API 基础URL，默认为官方API地址
        """
        self.sid = sid or settings.CLASSIN_CONFIG.get('SID')
        self.secret = secret or settings.CLASSIN_CONFIG.get('SECRET')
        self.base_url = base_url or "https://api.eeo.cn/partner/api/course.api.php"
        self.base_url_v2 = "https://api.eeo.cn"
        
        if not self.sid or not self.secret:
            raise ValueError("ClassIn SID和SECRET必须提供，请检查配置文件")
            
        self.headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'
        }
        
        self.headers_v2 = {
            'Content-Type': 'application/json'
        }

    @staticmethod
    def get_time_stamp() -> str:
        """获取当前时间戳（秒）"""
        dt_start = datetime(1970, 1, 1, tzinfo=timezone.utc)
        dt_now = datetime.now(timezone.utc)
        to_now = (dt_now - dt_start).total_seconds()
        return str(int(to_now))

    @staticmethod
    def get_time_stamp_from_date(dt_now: datetime) -> str:
        """从日期对象获取时间戳（秒）"""
        # 检查是否带有时区信息
        if dt_now.tzinfo is None:
            # 如果没有时区信息，假定为本地时间，转换为UTC时间
            # 获取本地时区的UTC偏移量（秒）
            utc_offset = -time.timezone
            if time.localtime().tm_isdst:
                utc_offset = -time.altzone
            # 创建时区对象
            local_tz = timezone(timedelta(seconds=utc_offset))
            # 添加本地时区信息
            dt_now = dt_now.replace(tzinfo=local_tz)
            # 转换到UTC时间
            dt_now = dt_now.astimezone(timezone.utc)
        elif dt_now.tzinfo != timezone.utc:
            # 如果有时区信息但不是UTC，转换为UTC
            dt_now = dt_now.astimezone(timezone.utc)
            
        # 从UTC时间计算时间戳
        dt_start = datetime(1970, 1, 1, tzinfo=timezone.utc)
        to_now = (dt_now - dt_start).total_seconds()
        return str(int(to_now))

    def get_safe_key(self) -> (str, str):
        """
        生成安全密钥和时间戳
        
        Returns:
            tuple: (安全密钥, 时间戳)
        """
        timestamp = self.get_time_stamp()
        return hashlib.md5((self.secret + timestamp).encode()).hexdigest(), timestamp
    
    def get_sign_v2(self, params: Dict[str, Any], timestamp: str) -> str:
        """
        生成V2版本API的签名
        
        Args:
            params: 请求参数字典
            timestamp: 时间戳
            
        Returns:
            签名字符串
        """
        # 过滤掉不参与签名的参数（数组、对象和长度超过1024的字符串）
        sign_params = {}
        for k, v in params.items():
            if not isinstance(v, (list, dict)) and (isinstance(v, (int, float, bool)) or len(str(v)) <= 1024):
                sign_params[k] = v
        
        # 添加sid和timeStamp参数
        sign_params['sid'] = self.sid
        sign_params['timeStamp'] = timestamp
        
        # 按参数名ASCII码从小到大排序
        sorted_params = sorted(sign_params.items(), key=lambda x: x[0])
        
        # 构建待签名字符串
        str_to_sign = '&'.join([f'{k}={v}' for k, v in sorted_params])
        str_to_sign += f'&key={self.secret}'
        
        # 计算MD5签名
        sign = hashlib.md5(str_to_sign.encode()).hexdigest()
        return sign
    
    @staticmethod
    def format_params(params: Dict[str, Any]) -> str:
        """
        格式化请求参数为URL编码格式
        
        Args:
            params: 请求参数字典
            
        Returns:
            格式化后的参数字符串
        """
        return '&'.join([f'{k}={v}' for k, v in params.items()])

    async def request(self, action: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送请求到ClassIn API (V1版本)
        
        Args:
            action: API操作名称或API路径
                - 如果是操作名称(无'/')，则使用旧版api格式: ?action={action}
                - 如果是API路径(包含'/')，则使用新版api格式: 直接请求该路径
            params: 请求参数
            
        Returns:
            API返回的JSON响应
        """
        # 生成安全密钥和时间戳
        safe_key, timestamp = self.get_safe_key()
        
        # 添加公共参数
        params.update({
            'SID': self.sid,
            'safeKey': safe_key,
            'timeStamp': timestamp,
        })
        
        # 格式化请求数据
        data = self.format_params(params)
        
        # 确定URL格式 - 检查action是否包含'/'来区分旧格式和新格式
        if '/' in action:
            # 新版API路径格式
            url = f"{self.base_url.split('partner/api/course.api.php')[0]}/{action}"
        else:
            # 旧版操作名称格式
            url = f"{self.base_url}?action={action}"
        
        try:
            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    data=data.encode('utf-8'),
                    headers=self.headers
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.debug(f"ClassIn API 请求成功: {action}, 参数: {params}, 结果: {result}")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"ClassIn API 请求失败: {action}, 状态码: {response.status}, 错误: {error_text}")
                        return {'data': 0, 'error_info': {'errno': response.status, 'error': error_text}}
        except Exception as e:
            logger.exception(f"ClassIn API 请求异常: {action}, 参数: {params}, 异常: {str(e)}")
            return {'data': 0, 'error_info': {'errno': -1, 'error': str(e)}}

    async def request_v2(self, path: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送请求到ClassIn API V2版本
        
        Args:
            path: API路径，例如 'lms/unit/create'
            params: 请求参数
            
        Returns:
            API返回的JSON响应
        """
        # 获取时间戳
        timestamp = self.get_time_stamp()
        
        # 生成签名
        sign = self.get_sign_v2(params, timestamp)
        
        # 设置请求头
        headers = self.headers_v2.copy()
        headers.update({
            'X-EEO-SIGN': sign,
            'X-EEO-UID': self.sid,
            'X-EEO-TS': timestamp
        })
        
        # 构建完整URL
        url = f"{self.base_url_v2}/{path}"
        
        try:
            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    json=params,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.debug(f"ClassIn API V2 请求成功: {path}, 参数: {params}, 结果: {result}")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"ClassIn API V2 请求失败: {path}, 状态码: {response.status}, 错误: {error_text}")
                        return {'code': response.status, 'message': error_text}
        except Exception as e:
            logger.exception(f"ClassIn API V2 请求异常: {path}, 参数: {params}, 异常: {str(e)}")
            return {'code': -1, 'message': str(e)}


class UserApi(ClassInApiBase):
    """用户相关API接口"""
    
    async def register_user(self, telephone: str, nickname: str, password: str = None, 
                           add_to_school_member: int = 1) -> Dict[str, Any]:
        """
        注册用户
        
        Args:
            telephone: 用户手机号
            nickname: 用户昵称
            password: 用户密码，默认为"jjsw@2024..."
            add_to_school_member: 是否添加为机构成员，0 不加为机构成员；1 加为机构学生；2 加为机构老师；其他值不加为机构成员。不填默认为0。
            
        Returns:
            API响应结果
        """
        params = {
            'telephone': telephone,
            'nickname': nickname,
            'password': password or "jjsw@2024...",
            'addToSchoolMember': add_to_school_member
        }
        return await self.request('register', params)
    
    async def register_multiple_users(self, users: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量注册用户
        
        Args:
            users: 用户信息列表，每个用户包含telephone, nickname, password字段
            
        Returns:
            API响应结果
        """
        params = {
            'userJson': json.dumps(users)
        }
        return await self.request('registerMultiple', params)
    
    async def modify_password(self, telephone: str, password: str) -> Dict[str, Any]:
        """
        修改用户密码
        
        Args:
            telephone: 用户手机号
            password: 新密码
            
        Returns:
            API响应结果
        """
        params = {
            'telephone': telephone,
            'password': password
        }
        return await self.request('modifyPassword', params)
    
    async def add_school_student(self, student_account: str, student_name: str) -> Dict[str, Any]:
        """
        添加学生
        
        Args:
            student_account: 学生账号（手机号）
            student_name: 学生姓名
            
        Returns:
            API响应结果
        """
        params = {
            'studentAccount': student_account,
            'studentName': student_name
        }
        return await self.request('addSchoolStudent', params)
    
    async def edit_school_student(self, student_uid: str, student_name: str) -> Dict[str, Any]:
        """
        编辑学生信息
        
        Args:
            student_uid: 学生UID
            student_name: 学生姓名
            
        Returns:
            API响应结果
        """
        params = {
            'studentUid': student_uid,
            'studentName': student_name
        }
        return await self.request('editSchoolStudent', params)
    
    async def sync_student_nickname(self, course_id: str) -> Dict[str, Any]:
        """
        同步学生班级昵称
        
        Args:
            course_id: 课程ID
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id
        }
        return await self.request('modifyGroupMemberNickname', params)
    
    async def add_teacher(self, teacher_account: str, teacher_name: str) -> Dict[str, Any]:
        """
        添加老师
        
        Args:
            teacher_account: 老师账号（手机号）
            teacher_name: 老师姓名
            
        Returns:
            API响应结果
        """
        params = {
            'teacherAccount': teacher_account,
            'teacherName': teacher_name
        }
        return await self.request('addTeacher', params)
    
    async def edit_teacher(self, teacher_uid: str, teacher_name: str) -> Dict[str, Any]:
        """
        编辑老师信息
        
        Args:
            teacher_uid: 老师UID
            teacher_name: 老师姓名
            
        Returns:
            API响应结果
        """
        params = {
            'teacherUid': teacher_uid,
            'teacherName': teacher_name
        }
        return await self.request('editSchoolTeacher', params)
    
    async def disable_teacher(self, teacher_uid: str) -> Dict[str, Any]:
        """
        停用老师
        
        Args:
            teacher_uid: 老师UID
            
        Returns:
            API响应结果
        """
        params = {
            'teacherUid': teacher_uid
        }
        return await self.request('disableSchoolTeacher', params)
    
    async def enable_teacher(self, teacher_uid: str) -> Dict[str, Any]:
        """
        启用老师
        
        Args:
            teacher_uid: 老师UID
            
        Returns:
            API响应结果
        """
        params = {
            'teacherUid': teacher_uid
        }
        return await self.request('enableSchoolTeacher', params)
    
    async def update_student_evaluation(self, class_id: str, student_uid: str, score: int, 
                                      evaluation: str) -> Dict[str, Any]:
        """
        更新课节教师对学生评价
        
        Args:
            class_id: 课节ID
            student_uid: 学生UID
            score: 分数
            evaluation: 评价内容
            
        Returns:
            API响应结果
        """
        params = {
            'classId': class_id,
            'studentUid': student_uid,
            'score': score,
            'evaluation': evaluation
        }
        return await self.request('setEvaluationToStudent', params)


class ClassroomApi(ClassInApiBase):
    """教室相关API接口"""
    
    async def addCourse(self, course_name: str, 
                        mainTeacherUid: str, 
                        start_time: str, 
                        end_time: str, 
                           course_unique_identity: str = None) -> Dict[str, Any]:
        """
        创建课程
        
        Args:
            course_name: 课程名称
            mainTeacherUid: 教师ID
            start_time: 课程开始时间戳（秒）
            end_time: 课程结束时间戳（秒）
            student_num: 学生人数上限，默认50
            course_unique_identity: 课程唯一标识
        Returns:
            API响应结果
        """
        params = {
            'courseName': course_name,
            'mainTeacherUid': mainTeacherUid,
        }
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
        if course_unique_identity:
            params['courseUniqueIdentity'] = course_unique_identity
        print(f"创建课程参数: {params}")
        return await self.request('addCourse', params)
    
    async def edit_course(self, course_id: str, course_name: str = None, student_num: int = None,
                         start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """
        编辑课程
        
        Args:
            course_id: 课程ID
            course_name: 课程名称（可选）
            student_num: 学生人数上限（可选）
            start_time: 课程开始时间戳（秒）（可选）
            end_time: 课程结束时间戳（秒）（可选）
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id
        }
        
        if course_name:
            params['courseName'] = course_name
        if student_num:
            params['studentNum'] = student_num
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
            
        return await self.request('editCourse', params)
    
    async def end_course(self, course_id: str) -> Dict[str, Any]:
        """
        结束课程
        
        Args:
            course_id: 课程ID
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id
        }
        return await self.request('endCourse', params)
    
    async def create_class(self, course_id: str, class_name: str, start_time: str, end_time: str,
                          is_live_class: int = 0, student_num_limit: int = 0) -> Dict[str, Any]:
        """
        创建课节(单个)
        
        Args:
            course_id: 课程ID
            class_name: 课节名称
            start_time: 课节开始时间戳（秒）
            end_time: 课节结束时间戳（秒）
            is_live_class: 是否为直播课，0-否，1-是
            student_num_limit: 上台学生数量限制，0表示不限制
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'className': class_name,
            'startTime': start_time,
            'endTime': end_time,
            'isLiveClass': is_live_class,
            'studentNumLimit': student_num_limit
        }
        return await self.request('createClass', params)
    
    async def add_multiple_classes_multiple(self, course_id: str, class_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        创建课节(多个)
        
        Args:
            course_id: 课程ID
            class_list: 课节列表，每个课节包含className, startTime, endTime, isLiveClass, studentNumLimit字段
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classJson': json.dumps(class_list),
        }
        return await self.request('addCourseClassMultiple', params)
    
    async def edit_class(self, course_id: str, class_id: str, class_name: str = None, 
                        start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """
        修改课节信息
        
        Args:
            course_id: 课程ID
            class_id: 课节ID
            class_name: 课节名称（可选）
            start_time: 课节开始时间戳（秒）（可选）
            end_time: 课节结束时间戳（秒）（可选）
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classId': class_id
        }
        
        if class_name:
            params['className'] = class_name
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
            
        return await self.request('editClass', params)
    
    async def edit_class_student_limit(self, course_id: str, class_id: str, 
                                     student_num_limit: int) -> Dict[str, Any]:
        """
        修改课节上台学生数
        
        Args:
            course_id: 课程ID
            class_id: 课节ID
            student_num_limit: 上台学生数量限制
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classId': class_id,
            'studentNumLimit': student_num_limit
        }
        return await self.request('editClassStudentNumLimit', params)
    
    async def delete_class(self, course_id: str, class_id: str) -> Dict[str, Any]:
        """
        删除课节
        
        Args:
            course_id: 课程ID
            class_id: 课节ID
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classId': class_id
        }
        return await self.request('deleteClass', params)
    
    async def add_course_student(self, course_id: str, student_uid: str, student_name: str, 
                               identity: int = 1) -> Dict[str, Any]:
        """
        课程下添加学生/旁听（单个）
        
        Args:
            course_id: 课程ID
            student_uid: 学生UID
            student_name: 学生姓名
            identity: 身份，1-学生，2-旁听
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'studentUid': student_uid,
            'studentName': student_name,
            'identity': identity
        }
        print(f"添加学生到课程参数: {params}")
        return await self.request('addCourseStudent', params)
    
    async def delete_course_student(self, course_id: str, student_uid: str, 
                                  identity: int = 1) -> Dict[str, Any]:
        """
        课程下删除学生/旁听（单个）
        
        Args:
            course_id: 课程ID
            student_uid: 学生UID
            identity: 身份，1-学生，2-旁听
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'studentUid': student_uid,
            'identity': identity
        }
        return await self.request('delCourseStudent', params)
    
    async def add_course_students(self, course_id: str, students: List[Dict[str, Any]], 
                                identity: int = 1) -> Dict[str, Any]:
        """
        课程下添加学生/旁听（多个）
        
        Args:
            course_id: 课程ID
            students: 学生列表，每个学生包含studentUid, studentName字段
            identity: 身份，1-学生，2-旁听
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'studentJson': json.dumps(students),
            'identity': identity
        }
        return await self.request('addCourseStudentMultiple', params)
    
    async def delete_course_students(self, course_id: str, student_uids: List[str], 
                                   identity: int = 1) -> Dict[str, Any]:
        """
        课程下删除学生/旁听（多个）
        
        Args:
            course_id: 课程ID
            student_uids: 学生UID列表
            identity: 身份，1-学生，2-旁听
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'studentUidJson': json.dumps(student_uids),
            'identity': identity
        }
        return await self.request('delCourseStudentMultiple', params)
    
    async def add_class_students(self, course_id: str, class_id: str, students: List[Dict[str, Any]], 
                               identity: int = 1) -> Dict[str, Any]:
        """
        课节下添加学生（多个）
        
        Args:
            course_id: 课程ID
            class_id: 课节ID
            students: 学生列表，每个学生包含studentUid, studentName字段
            identity: 身份，1-学生，2-旁听
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classId': class_id,
            'identity': identity,
            'studentJson': json.dumps(students)
        }
        return await self.request('addClassStudentMultiple', params)
    
    async def delete_class_students(self, course_id: str, class_id: str, student_uids: List[str], 
                                  identity: int = 1) -> Dict[str, Any]:
        """
        课节下删除学生（多个）
        
        Args:
            course_id: 课程ID
            class_id: 课节ID
            student_uids: 学生UID列表
            identity: 身份，1-学生，2-旁听
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classId': class_id,
            'identity': identity,
            'studentUidJson': json.dumps(student_uids)
        }
        return await self.request('delClassStudentMultiple', params)
    
    async def change_course_teacher(self, course_id: str, teacher_id: str, teacher_name: str) -> Dict[str, Any]:
        """
        更换课程老师
        
        Args:
            course_id: 课程ID
            teacher_id: 新教师ID
            teacher_name: 新教师姓名
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'teacherId': teacher_id,
            'teacherName': teacher_name
        }
        return await self.request('changeCourseTeacher', params)
    
    async def remove_course_teacher(self, course_id: str, teacher_id: str) -> Dict[str, Any]:
        """
        移除课程老师
        
        Args:
            course_id: 课程ID
            teacher_id: 教师ID
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'teacherId': teacher_id
        }
        return await self.request('delCourseTeacher', params)
    
    async def add_course_teacher(self, course_id: str, teacher_id: str, teacher_name: str) -> Dict[str, Any]:
        """
        添加课程老师
        
        Args:
            course_id: 课程ID
            teacher_id: 教师ID
            teacher_name: 教师姓名
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'teacherId': teacher_id,
            'teacherName': teacher_name
        }
        return await self.request('addCourseTeacher', params)
    
    async def add_course_class_student(self, course_id: str, class_ids: List[str], student_uid: str) -> Dict[str, Any]:
        """
        课程下多个课节添加学生
        
        Args:
            course_id: 课程ID
            class_ids: 课节ID列表
            students: 学生列表，每个学生包含studentUid, studentName字段
            identity: 身份，1-学生，2-旁听
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classJson': json.dumps(class_ids),
            'studentUid': student_uid,
        }
        return await self.request('addCourseClassStudent', params)
    
    async def operate_course_tag(self, course_id: str, tag: str, operation: str = 'add') -> Dict[str, Any]:
        """
        添加/修改/删除课程标签
        
        Args:
            course_id: 课程ID
            tag: 标签内容
            operation: 操作类型，add-添加，edit-修改，delete-删除
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'tag': tag,
            'operation': operation
        }
        return await self.request('courseTagOperation', params)
    
    async def operate_class_tag(self, course_id: str, class_id: str, tag: str, 
                              operation: str = 'add') -> Dict[str, Any]:
        """
        添加/修改/删除课节标签
        
        Args:
            course_id: 课程ID
            class_id: 课节ID
            tag: 标签内容
            operation: 操作类型，add-添加，edit-修改，delete-删除
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classId': class_id,
            'tag': tag,
            'operation': operation
        }
        return await self.request('classTagOperation', params)
    
    async def create_course_group(self, group_name: str) -> Dict[str, Any]:
        """
        创建课程分组
        
        Args:
            group_name: 分组名称
            
        Returns:
            API响应结果
        """
        params = {
            'groupName': group_name
        }
        return await self.request('createCourseGroup', params)
    
    async def edit_course_group(self, group_id: str, group_name: str) -> Dict[str, Any]:
        """
        编辑课程分组
        
        Args:
            group_id: 分组ID
            group_name: 分组名称
            
        Returns:
            API响应结果
        """
        params = {
            'groupId': group_id,
            'groupName': group_name
        }
        return await self.request('editCourseGroup', params)
    
    async def delete_course_group(self, group_id: str) -> Dict[str, Any]:
        """
        删除课程分组
        
        Args:
            group_id: 分组ID
            
        Returns:
            API响应结果
        """
        params = {
            'groupId': group_id
        }
        return await self.request('deleteCourseGroup', params)


class LmsApi(ClassInApiBase):
    """LMS相关API接口"""
    
    async def create_unit(self, course_id: int, name: str, publish_flag: int = 2, content: str = None) -> Dict[str, Any]:
        """
        创建单元
        
        Args:
            course_id: 班级（课程）ID
            name: 单元名称，长度不超过50字。注：课程下不支持创建同名单元
            publish_flag: 是否发布，0=草稿，2=已发布（显示），默认已发布
            content: 单元介绍，不传默认为空
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'name': name,
            'publishFlag': publish_flag
        }
        
        if content:
            params['content'] = content
            
        return await self.request_v2('lms/unit/create', params)
    
    async def edit_unit(self, course_id: int, unit_id: int, name: str = None, 
                        publish_flag: int = None, content: str = None) -> Dict[str, Any]:
        """
        编辑单元
        
        Args:
            course_id: 班级（课程）ID
            unit_id: 单元ID
            name: 单元名称，长度不超过50字（可选）
            publish_flag: 是否发布，0=草稿，2=已发布（显示）（可选）
            content: 单元介绍（可选）
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'unitId': unit_id
        }
        
        if name:
            params['name'] = name
        if publish_flag is not None:
            params['publishFlag'] = publish_flag
        if content is not None:
            params['content'] = content
            
        return await self.request_v2('lms/unit/update', params)
    
    async def delete_unit(self, course_id: int, unit_id: int) -> Dict[str, Any]:
        """
        删除单元
        
        Args:
            course_id: 班级（课程）ID
            unit_id: 单元ID
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'unitId': unit_id
        }
        return await self.request_v2('lms/unit/delete', params)
    
    async def create_non_class_activity(self, course_id: int, unit_id: int, name: str, 
                                      activity_type: int, start_time: str, end_time: str,
                                      content: str = None, publish_flag: int = 0) -> Dict[str, Any]:
        """
        创建非课堂活动
        
        Args:
            course_id: 班级（课程）ID
            unit_id: 单元ID
            name: 活动名称
            activity_type: 活动类型，1=作业，2=测验，3=讨论
            start_time: 开始时间戳（秒）
            end_time: 结束时间戳（秒）
            content: 活动内容（可选）
            publish_flag: 是否发布，0=草稿，2=已发布（显示），默认为草稿
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'unitId': unit_id,
            'name': name,
            'activityType': activity_type,
            'startTime': start_time,
            'endTime': end_time,
            'publishFlag': publish_flag
        }
        
        if content:
            params['content'] = content

        return await self.request_v2('lms/activity/createActivityNoClass', params)
    
    async def create_class_activity(self, 
                                    course_id: int, 
                                    unit_id: int, 
                                    name: str, 
                                    start_time: str, 
                                    end_time: str, 
                                    teacher_uid: str, 
                                    seat_num: int = 1, 
                                    is_hd: int = 0, 
                                    is_auto_onstage: int = 0, 
                                    live_state: int = 1, 
                                    record_type: int = 0, 
                                    record_state: int = 1, 
                                    open_state: int = 1
                                    ) -> Dict[str, Any]:
        """
        创建课堂活动
        
        Args:
            'course_id': class_obj.classin_id,
            'unit_id': unit_id,
            'name': lesson_name,    # 1-50个字，不区分中英文，超过50字会自动截取为50字
            'start_time': plan_start_timestamp,     # 开课时间须在3年以内
            'end_time': plan_end_timestamp,
            'teacher_uid': teacher_uid,
            'seat_num': seat_num,   # 传1时，表示1v0，台上只显示老师头像。默认为7（1v6）| 小学1v1-2, 其他1v0-1
            'is_hd': 0,   #  0= 非高清，1 = 高清，2 = 全高清
            'is_auto_onstage': 0,   # 0 = 不自动，1 = 自动。默认为1
            'live_state': 1,  # 0 = 不直播（关闭），1 = 直播（开启）
            'record_type': 0,   # 0 = 录制教室，1 = 录制现场
            'record_state':1,   # 0 = 不录课（关闭），1 = 录课（开启）
            'open_state': 1   # 0 = 不公开（关闭），1 = 公开（开启）
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': int(course_id),
            'unitId': int(unit_id),
            'name': name,
            'startTime': int(start_time),
            'endTime': int(end_time),
            'teacherUid': int(teacher_uid),
            'seatNum': int(seat_num),
            'isHd': is_hd,
            'isAutoOnstage': is_auto_onstage,
            'liveState': live_state,
            'recordType': record_type,
            'recordState': record_state,
            'openState': open_state
        }
        # print(f"创建课堂活动参数: {params}")
        
        return await self.request_v2('lms/activity/createClass', params)
    
    async def edit_class_activity(self, course_id: int, activity_id: int,
                              teacher_uid: int = None, 
                             start_time: int = None, end_time: int = None,
                             ) -> Dict[str, Any]:
        """
        编辑课堂活动
        
        Args:
            course_id: 班级（课程）ID
            activity_id: 课堂活动ID
            teacher_uid: 主讲教师UID（可选）
            start_time: 活动开始时间戳（可选）
            end_time: 活动结束时间戳（可选）
            
        Returns:
            API响应结果
        """
        params = {
            "courseId": int(course_id),
            "activityId": int(activity_id),
        }
        
        # 添加可选参数
        if teacher_uid is not None:
            params["teacherUid"] = int(teacher_uid)
        if start_time is not None:
            params["startTime"] = int(start_time)
        if end_time is not None:
            params["endTime"] = int(end_time)
        
        # 设置发布状态为已发布(2)
        params["publishFlag"] = 2

        return await self.request_v2('lms/activity/updateClass', params)
    
    async def publish_activity(self, course_id: int, activity_id: int, publish_flag: int = 2) -> Dict[str, Any]:
        """
        发布活动
        
        Args:
            course_id: 班级（课程）ID
            activity_id: 活动ID
            publish_flag: 是否发布，0=草稿，2=已发布（显示），默认发布
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'activityId': activity_id,
            'publishFlag': publish_flag
        }
        return await self.request_v2('lms/activity/release', params)
    
    async def delete_activity(self, course_id: int, activity_id: int) -> Dict[str, Any]:
        """
        删除活动
        
        Args:
            course_id: 班级（课程）ID
            activity_id: 活动ID
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': int(course_id),
            'activityId': int(activity_id)
        }
        return await self.request_v2('lms/activity/delete', params)
    
    async def move_activity(self, course_id: int, activity_id: int, target_id: int, 
                          move_type: int) -> Dict[str, Any]:
        """
        移动活动
        
        Args:
            course_id: 班级（课程）ID
            activity_id: 活动ID
            target_id: 目标ID
            move_type: 移动类型，1=移动到单元，2=移动到课节
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'activityId': activity_id,
            'targetId': target_id,
            'moveType': move_type
        }
        return await self.request_v2('lms/activity/move', params)
    
    async def add_activity_members(self, course_id: int, activity_id: int, 
                                 student_uids: List[str]) -> Dict[str, Any]:
        """
        添加活动成员
        
        Args:
            course_id: 班级（课程）ID
            activity_id: 活动ID
            student_uids: 学生UID列表
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'activityId': activity_id,
            'studentUidJson': student_uids
        }
        return await self.request_v2('lms/activity/member/addStudent', params)
    
    async def delete_activity_members(self, course_id: int, activity_id: int, 
                                    student_uids: List[str]) -> Dict[str, Any]:
        """
        删除活动成员
        
        Args:
            course_id: 班级（课程）ID
            activity_id: 活动ID
            student_uids: 学生UID列表
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'activityId': activity_id,
            'studentUidJson': student_uids
        }
        return await self.request_v2('lms/activity/member/deleteStudent', params)


class DualTeacherApi(ClassInApiBase):
    """双师课相关API接口"""
    
    async def create_dual_teacher_class(self, course_id: str, class_name: str, 
                                      start_time: str, end_time: str,
                                      remote_teacher_id: str, remote_teacher_name: str,
                                      local_teacher_id: str = None, local_teacher_name: str = None,
                                      student_num_limit: int = 0) -> Dict[str, Any]:
        """
        创建在线双师课节
        
        Args:
            course_id: 课程ID
            class_name: 课节名称
            start_time: 开始时间戳（秒）
            end_time: 结束时间戳（秒）
            remote_teacher_id: 远程教师ID
            remote_teacher_name: 远程教师姓名
            local_teacher_id: 本地教师ID（可选）
            local_teacher_name: 本地教师姓名（可选）
            student_num_limit: 上台学生数量限制，0表示不限制
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'className': class_name,
            'startTime': start_time,
            'endTime': end_time,
            'remoteTeacherId': remote_teacher_id,
            'remoteTeacherName': remote_teacher_name,
            'studentNumLimit': student_num_limit
        }
        
        if local_teacher_id and local_teacher_name:
            params['localTeacherId'] = local_teacher_id
            params['localTeacherName'] = local_teacher_name
            
        return await self.request('createDualTeacherClass', params)
    
    async def edit_dual_teacher_class(self, course_id: str, class_id: str, class_name: str = None,
                                    start_time: str = None, end_time: str = None,
                                    remote_teacher_id: str = None, remote_teacher_name: str = None,
                                    local_teacher_id: str = None, local_teacher_name: str = None,
                                    student_num_limit: int = None) -> Dict[str, Any]:
        """
        编辑在线双师课节
        
        Args:
            course_id: 课程ID
            class_id: 课节ID
            class_name: 课节名称（可选）
            start_time: 开始时间戳（秒）（可选）
            end_time: 结束时间戳（秒）（可选）
            remote_teacher_id: 远程教师ID（可选）
            remote_teacher_name: 远程教师姓名（可选）
            local_teacher_id: 本地教师ID（可选）
            local_teacher_name: 本地教师姓名（可选）
            student_num_limit: 上台学生数量限制（可选）
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classId': class_id
        }
        
        if class_name:
            params['className'] = class_name
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
        if remote_teacher_id:
            params['remoteTeacherId'] = remote_teacher_id
        if remote_teacher_name:
            params['remoteTeacherName'] = remote_teacher_name
        if local_teacher_id:
            params['localTeacherId'] = local_teacher_id
        if local_teacher_name:
            params['localTeacherName'] = local_teacher_name
        if student_num_limit is not None:
            params['studentNumLimit'] = student_num_limit
            
        return await self.request('editDualTeacherClass', params)
    
    async def delete_dual_teacher_class(self, course_id: str, class_id: str) -> Dict[str, Any]:
        """
        删除在线双师课节
        
        Args:
            course_id: 课程ID
            class_id: 课节ID
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classId': class_id
        }
        return await self.request('deleteDualTeacherClass', params)


class OrganizationApi(ClassInApiBase):
    """机构相关API接口"""
    
    async def create_school_tag(self, tag_name: str) -> Dict[str, Any]:
        """
        创建机构标签
        
        Args:
            tag_name: 标签名称
            
        Returns:
            API响应结果
        """
        params = {
            'tagName': tag_name
        }
        return await self.request('createSchoolTag', params)
    
    async def edit_school_tag(self, tag_id: str, tag_name: str) -> Dict[str, Any]:
        """
        修改机构标签
        
        Args:
            tag_id: 标签ID
            tag_name: 标签名称
            
        Returns:
            API响应结果
        """
        params = {
            'tagId': tag_id,
            'tagName': tag_name
        }
        return await self.request('editSchoolTag', params)
    
    async def delete_school_tag(self, tag_id: str) -> Dict[str, Any]:
        """
        删除机构标签
        
        Args:
            tag_id: 标签ID
            
        Returns:
            API响应结果
        """
        params = {
            'tagId': tag_id
        }
        return await self.request('deleteSchoolTag', params)
    
    async def edit_school_settings(self, settings_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        修改学校设置
        
        Args:
            settings_dict: 设置字典，包含各种设置参数
            
        Returns:
            API响应结果
        """
        return await self.request('editSchoolSettings', settings_dict)


class ClassGroupApi(ClassInApiBase):
    """班级群相关API接口"""
    
    async def modify_group_member_nickname(self, course_id: str) -> Dict[str, Any]:
        """
        修改群成员的班级昵称
        
        Args:
            course_id: 课程ID
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id
        }
        return await self.request('modifyGroupMemberNickname', params)


class CloudDiskApi(ClassInApiBase):
    """云盘相关API接口"""
    
    async def get_folder_list(self, folder_id: str = None) -> Dict[str, Any]:
        """
        获取云盘文件夹列表
        
        Args:
            folder_id: 文件夹ID，不传则获取根目录列表
            
        Returns:
            API响应结果
        """
        params = {}
        if folder_id:
            params['folderId'] = folder_id
            
        return await self.request('getFolderList', params)
    
    async def get_file_list(self, folder_id: str) -> Dict[str, Any]:
        """
        获取指定文件夹下的文件及文件夹列表
        
        Args:
            folder_id: 文件夹ID
            
        Returns:
            API响应结果
        """
        params = {
            'folderId': folder_id
        }
        return await self.request('getFileList', params)
    
    async def get_root_folder_id(self) -> Dict[str, Any]:
        """
        获取顶级文件夹ID
        
        Returns:
            API响应结果
        """
        return await self.request('getRootFolderId', {})
    
    async def upload_file(self, folder_id: str, file_name: str, file_data: bytes) -> Dict[str, Any]:
        """
        上传文件
        
        Args:
            folder_id: 文件夹ID
            file_name: 文件名
            file_data: 文件数据
            
        Returns:
            API响应结果
        """
        params = {
            'folderId': folder_id,
            'fileName': file_name,
            'fileData': file_data.decode('utf-8') if isinstance(file_data, bytes) else file_data
        }
        return await self.request('uploadFile', params)
    
    async def rename_file(self, file_id: str, new_name: str) -> Dict[str, Any]:
        """
        重命名文件
        
        Args:
            file_id: 文件ID
            new_name: 新文件名
            
        Returns:
            API响应结果
        """
        params = {
            'fileId': file_id,
            'newName': new_name
        }
        return await self.request('renameFile', params)
    
    async def delete_file(self, file_id: str) -> Dict[str, Any]:
        """
        删除文件
        
        Args:
            file_id: 文件ID
            
        Returns:
            API响应结果
        """
        params = {
            'fileId': file_id
        }
        return await self.request('deleteFile', params)
    
    async def create_folder(self, parent_id: str, folder_name: str) -> Dict[str, Any]:
        """
        创建文件夹
        
        Args:
            parent_id: 父文件夹ID
            folder_name: 文件夹名称
            
        Returns:
            API响应结果
        """
        params = {
            'parentId': parent_id,
            'folderName': folder_name
        }
        return await self.request('createFolder', params)
    
    async def rename_folder(self, folder_id: str, new_name: str) -> Dict[str, Any]:
        """
        重命名文件夹
        
        Args:
            folder_id: 文件夹ID
            new_name: 新文件夹名
            
        Returns:
            API响应结果
        """
        params = {
            'folderId': folder_id,
            'newName': new_name
        }
        return await self.request('renameFolder', params)
    
    async def delete_folder(self, folder_id: str) -> Dict[str, Any]:
        """
        删除文件夹
        
        Args:
            folder_id: 文件夹ID
            
        Returns:
            API响应结果
        """
        params = {
            'folderId': folder_id
        }
        return await self.request('deleteFolder', params)
    
    async def modify_courseware_auth(self, teacher_id: str, file_id: str, auth: int) -> Dict[str, Any]:
        """
        修改教师授权云盘的课件资源
        
        Args:
            teacher_id: 教师ID
            file_id: 文件ID
            auth: 授权，1-授权，0-取消授权
            
        Returns:
            API响应结果
        """
        params = {
            'teacherId': teacher_id,
            'fileId': file_id,
            'auth': auth
        }
        return await self.request('modifyCoursewareAuth', params)


class LiveApi(ClassInApiBase):
    """直播相关API接口"""
    
    async def set_classes_video_config(self, course_id: str, class_ids: List[str], 
                                     record: int = None, live: int = None, 
                                     playback: int = None) -> Dict[str, Any]:
        """
        课节设置录课、直播、回放（多个）
        
        Args:
            course_id: 课程ID
            class_ids: 课节ID列表
            record: 是否录制，1-开启，0-关闭
            live: 是否直播，1-开启，0-关闭
            playback: 是否回放，1-开启，0-关闭
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classIdJson': json.dumps(class_ids)
        }
        
        if record is not None:
            params['record'] = record
        if live is not None:
            params['live'] = live
        if playback is not None:
            params['playback'] = playback
            
        return await self.request('setClassesVideoConfig', params)
    
    async def delete_class_video(self, course_id: str, class_id: str) -> Dict[str, Any]:
        """
        删除单个课节视频
        
        Args:
            course_id: 课程ID
            class_id: 课节ID
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classId': class_id
        }
        return await self.request('deleteClassVideo', params)
    
    async def modify_class_video_lock_status(self, course_id: str, class_id: str, 
                                           is_lock: int) -> Dict[str, Any]:
        """
        修改课节视频锁定状态
        
        Args:
            course_id: 课程ID
            class_id: 课节ID
            is_lock: 是否锁定，1-锁定，0-解锁
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'classId': class_id,
            'isLock': is_lock
        }
        return await self.request('modifyClassVideoLockStatus', params)
    
    async def get_course_video_url(self, course_id: str, user_id: str, 
                                 user_name: str, video_type: int) -> Dict[str, Any]:
        """
        获取课程直播/回放播放器地址
        
        Args:
            course_id: 课程ID
            user_id: 用户ID
            user_name: 用户姓名
            video_type: 视频类型，1-直播，2-回放
            
        Returns:
            API响应结果
        """
        params = {
            'courseId': course_id,
            'userId': user_id,
            'userName': user_name,
            'videoType': video_type
        }
        return await self.request('getCourseVideoUrl', params)


class ClientApi(ClassInApiBase):
    """客户端唤醒相关接口"""
    
    async def get_client_launch_url(self, user_id: str, user_name: str, course_id: str = None, 
                                  class_id: str = None, identity: int = None,
                                  return_url: str = None) -> str:
        """
        获取唤醒客户端并进入教室链接
        
        Args:
            user_id: 用户ID
            user_name: 用户姓名
            course_id: 课程ID（可选）
            class_id: 课节ID（可选）
            identity: 身份，1-学生，2-旁听，3-老师
            return_url: 返回URL（可选）
            
        Returns:
            唤醒客户端的URL
        """
        # 生成安全密钥和时间戳
        safe_key, timestamp = self.get_safe_key()
        
        # 构建参数
        params = {
            'SID': self.sid,
            'safeKey': safe_key,
            'timeStamp': timestamp,
            'userId': user_id,
            'userName': user_name
        }
        
        if course_id:
            params['courseId'] = course_id
        if class_id:
            params['classId'] = class_id
        if identity:
            params['identity'] = identity
        if return_url:
            params['returnUrl'] = return_url
            
        # 构建URL
        query_string = '&'.join([f'{k}={v}' for k, v in params.items()])
        return f"https://classroom.eeo.cn/#{query_string}"


class ClassInSDK:
    """ClassIn SDK 主类，整合所有API接口"""
    
    def __init__(self, sid: str = None, secret: str = None, base_url: str = None):
        """
        初始化 ClassIn SDK
        
        Args:
            sid: ClassIn SID，默认从settings获取
            secret: ClassIn Secret，默认从settings获取
            base_url: API 基础URL，默认为官方API地址
        """
        self.sid = sid or settings.CLASSIN_CONFIG.get('SID')
        self.secret = secret or settings.CLASSIN_CONFIG.get('SECRET')
        self.base_url = base_url or "https://api.eeo.cn/partner/api/course.api.php"
        
        # 初始化各个API模块
        self.user = UserApi(sid, secret, base_url)
        self.classroom = ClassroomApi(sid, secret, base_url)
        self.lms = LmsApi(sid, secret, base_url)
        self.dual_teacher = DualTeacherApi(sid, secret, base_url)
        self.organization = OrganizationApi(sid, secret, base_url)
        self.class_group = ClassGroupApi(sid, secret, base_url)
        self.cloud_disk = CloudDiskApi(sid, secret, base_url)
        self.live = LiveApi(sid, secret, base_url)
        self.client = ClientApi(sid, secret, base_url)
    
    @staticmethod
    def get_time_stamp() -> str:
        """获取当前时间戳（秒）"""
        return ClassInApiBase.get_time_stamp()
    
    @staticmethod
    def get_time_stamp_from_date(dt_now: datetime) -> str:
        """从日期对象获取时间戳（秒）"""
        return ClassInApiBase.get_time_stamp_from_date(dt_now)
