"""
企业微信欢迎语相关辅助工具类
"""

import asyncio
from typing import Dict, Any, List, Optional
from modules.qy_wechat.base import QWechatBase
from utils.net.http_handler import HttpHandler
from settings import logger


class QyWechatWelcomeHelper:
    """
    企业微信欢迎语辅助类
    """
    
    def __init__(self):
        self.base_url = 'https://qyapi.weixin.qq.com/cgi-bin'
        self.qw_base = QWechatBase()
    
    async def get_access_token(self):
        """获取access_token"""
        return await self.qw_base.get_access_token()
    
    async def send_welcome_msg(self, welcome_code: str, text: str = None, attachments: List[Dict] = None, max_retries: int = 1) -> Dict[str, Any]:
        """
        发送新客户欢迎语
        
        Args:
            welcome_code: 通过添加外部联系人事件推送给企业的welcome_code
            text: 消息文本内容，最长为4000字节
            attachments: 附件，最多可添加9个附件
            max_retries: 最大重试次数，默认1次
            
        Returns:
            Dict: 发送结果
        """
        url = "/externalcontact/send_welcome_msg"
        data = {
            "welcome_code": welcome_code
        }
        
        # 构建消息内容
        if text:
            data["text"] = {"content": text}
        
        if attachments:
            data["attachments"] = attachments
        
        # 重试机制
        for attempt in range(max_retries):
            try:
                resp = await HttpHandler().post(f"{self.base_url}{url}",
                                              params={"access_token": await self.get_access_token()},
                                              data=data)
                
                if resp.get('errcode') == 0:
                    logger.info("发送欢迎语成功")
                    return resp
                elif resp.get('errcode') == 41096:
                    # 欢迎语正在分发中，需要等待后重试
                    if attempt < max_retries - 1:
                        wait_time = 3  # 等待3秒后重试
                        logger.warning(f"欢迎语正在分发中(错误码: 41096)，第{attempt + 1}次重试，等待{wait_time}秒...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        logger.error(f"发送欢迎语失败，已重试{max_retries}次: {resp.get('errmsg')}")
                        return resp
                elif resp.get('errcode') == 40001:
                    # access_token无效，刷新token后重试
                    if attempt < max_retries - 1:
                        logger.warning(f"access_token无效，第{attempt + 1}次重试...")
                        # 等待一段时间后重试，让缓存过期
                        await asyncio.sleep(1)
                        continue
                    else:
                        logger.error(f"发送欢迎语失败，access_token无效: {resp.get('errmsg')}")
                        return resp
                else:
                    # 其他错误，不重试
                    logger.error(f"发送欢迎语失败: {resp.get('errmsg')}")
                    return resp
                    
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"发送欢迎语异常，第{attempt + 1}次重试: {str(e)}")
                    await asyncio.sleep(1)
                    continue
                else:
                    logger.error(f"发送欢迎语异常: {str(e)}")
                    return {"errcode": -1, "errmsg": str(e)}
        
        # 如果所有重试都失败了
        return {"errcode": -1, "errmsg": "发送欢迎语失败，已达到最大重试次数"}
    
    async def get_external_contact(self, external_userid: str) -> Dict[str, Any]:
        """
        获取外部联系人详情
        
        Args:
            external_userid: 外部联系人的userid
            
        Returns:
            Dict: 外部联系人详情
        """
        url = "/externalcontact/get"
        
        try:
            resp = await HttpHandler().get(f"{self.base_url}{url}",
                                         params={
                                             "access_token": await self.get_access_token(),
                                             "external_userid": external_userid
                                         })
            if resp.get('errcode') != 0:
                logger.error(f"获取外部联系人详情失败: {resp.get('errmsg')}")
                return resp
            
            return resp
        except Exception as e:
            logger.error(f"获取外部联系人详情异常: {str(e)}")
            return {"errcode": -1, "errmsg": str(e)}
    
    async def get_user_info(self, userid: str) -> Dict[str, Any]:
        """
        获取成员详情
        
        Args:
            userid: 成员UserID
            
        Returns:
            Dict: 成员详情
        """
        url = "/user/get"
        
        try:
            resp = await HttpHandler().get(f"{self.base_url}{url}",
                                         params={
                                             "access_token": await self.get_access_token(),
                                             "userid": userid
                                         })
            if resp.get('errcode') != 0:
                logger.error(f"获取成员详情失败: {resp.get('errmsg')}")
                return resp
            
            return resp
        except Exception as e:
            logger.error(f"获取成员详情异常: {str(e)}")
            return {"errcode": -1, "errmsg": str(e)}
    
    async def add_external_contact_tag(self, userid: str, external_userid: str, tag_list: List[str]) -> Dict[str, Any]:
        """
        编辑客户企业标签
        
        Args:
            userid: 添加外部联系人的userid
            external_userid: 外部联系人userid
            tag_list: 要标记的标签列表
            
        Returns:
            Dict: 操作结果
        """
        url = "/externalcontact/mark_tag"
        data = {
            "userid": userid,
            "external_userid": external_userid,
            "add_tag": tag_list
        }
        
        try:
            resp = await HttpHandler().post(f"{self.base_url}{url}",
                                          params={"access_token": await self.get_access_token()},
                                          data=data)
            if resp.get('errcode') != 0:
                logger.error(f"编辑客户企业标签失败: {resp.get('errmsg')}")
                return resp
            
            logger.info("编辑客户企业标签成功")
            return resp
        except Exception as e:
            logger.error(f"编辑客户企业标签异常: {str(e)}")
            return {"errcode": -1, "errmsg": str(e)}
    
    async def update_external_contact_remark(self, userid: str, external_userid: str, remark: str) -> Dict[str, Any]:
        """
        修改客户备注信息
        
        Args:
            userid: 企业成员的userid
            external_userid: 外部联系人userid
            remark: 备注信息
            
        Returns:
            Dict: 操作结果
        """
        url = "/externalcontact/remark"
        data = {
            "userid": userid,
            "external_userid": external_userid,
            "remark": remark
        }
        
        try:
            resp = await HttpHandler().post(f"{self.base_url}{url}",
                                          params={"access_token": await self.get_access_token()},
                                          data=data)
            if resp.get('errcode') != 0:
                logger.error(f"修改客户备注信息失败: {resp.get('errmsg')}")
                return resp
            
            logger.info("修改客户备注信息成功")
            return resp
        except Exception as e:
            logger.error(f"修改客户备注信息异常: {str(e)}")
            return {"errcode": -1, "errmsg": str(e)}
    
    async def get_groupchat_detail(self, chat_id: str) -> Dict[str, Any]:
        """
        获取客户群详情
        
        Args:
            chat_id: 客户群ID
            
        Returns:
            Dict: 客户群详情
        """
        url = "/externalcontact/groupchat/get"
        data = {
            "chat_id": chat_id,
            "need_name": 1
        }
        
        try:
            resp = await HttpHandler().post(f"{self.base_url}{url}",
                                          params={"access_token": await self.get_access_token()},
                                          data=data)
            if resp.get('errcode') != 0:
                logger.error(f"获取客户群详情失败: {resp.get('errmsg')}")
                return resp
            
            return resp
        except Exception as e:
            logger.error(f"获取客户群详情异常: {str(e)}")
            return {"errcode": -1, "errmsg": str(e)} 