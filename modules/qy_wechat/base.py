from typing import List
from settings import logger, QY_WECHAT_CONFIG, REDIS_CONFIG, redis_url
from aiocache import cached, Cache
from aiocache.serializers import JsonSerializer
from utils.net.http_handler import HttpHandler
import asyncio
import time


class QWechatBase:
    def __init__(self):
        self.base_url = 'https://qyapi.weixin.qq.com/cgi-bin'
        self.headers = {
        }
        # 内存缓存作为Redis的备选方案
        self._memory_cache = {}
        self._cache_expire_time = {}

    async def get_access_token(self):
        """获取access_token，带Redis缓存和内存缓存降级"""
        cache_key = "access_erp_token"
        
        # 首先尝试从Redis缓存获取
        try:
            cached_token = await self._get_from_redis_cache(cache_key)
            if cached_token:
                return cached_token
        except Exception as e:
            logger.warning(f"Redis缓存获取失败，尝试内存缓存: {str(e)}")
        
        # Redis失败，尝试内存缓存
        if cache_key in self._memory_cache:
            expire_time = self._cache_expire_time.get(cache_key, 0)
            if time.time() < expire_time:
                logger.info("使用内存缓存的access_token")
                return self._memory_cache[cache_key]
        
        # 缓存都失败，重新获取token
        logger.info('重新获取access_token')
        url = "/gettoken"
        data = await HttpHandler().get(f"{self.base_url}{url}", params={
            "corpid": QY_WECHAT_CONFIG['CORPID'],
            "corpsecret": QY_WECHAT_CONFIG['ERP_CORPSECRET'],
        })
        
        access_token = data["access_token"]
        
        # 同时保存到Redis和内存缓存
        await self._save_to_cache(cache_key, access_token)
        
        return access_token

    async def get_address_access_token(self):
        """获取通讯录access_token，带Redis缓存和内存缓存降级"""
        cache_key = "access_address_token"
        
        # 首先尝试从Redis缓存获取
        try:
            cached_token = await self._get_from_redis_cache(cache_key)
            if cached_token:
                return cached_token
        except Exception as e:
            logger.warning(f"Redis缓存获取失败，尝试内存缓存: {str(e)}")
        
        # Redis失败，尝试内存缓存
        if cache_key in self._memory_cache:
            expire_time = self._cache_expire_time.get(cache_key, 0)
            if time.time() < expire_time:
                logger.info("使用内存缓存的address_access_token")
                return self._memory_cache[cache_key]
        
        # 缓存都失败，重新获取token
        logger.info('重新获取address_access_token')
        url = "/gettoken"
        data = await HttpHandler().get(f"{self.base_url}{url}", params={
            "corpid": QY_WECHAT_CONFIG['CORPID'],
            "corpsecret": QY_WECHAT_CONFIG['ADDRESS_CORPSECRET'],
        })
        
        access_token = data["access_token"]
        
        # 同时保存到Redis和内存缓存
        await self._save_to_cache(cache_key, access_token)
        
        return access_token

    async def _get_from_redis_cache(self, key: str):
        """从Redis缓存获取数据，带超时控制"""
        try:
            # 使用aiocache，但添加超时控制
            cache = Cache(Cache.REDIS, 
                         endpoint=REDIS_CONFIG["host"], 
                         port=REDIS_CONFIG["port"], 
                         db=REDIS_CONFIG["db"], 
                         password=REDIS_CONFIG["password"],
                         serializer=JsonSerializer(),
                         namespace="qywechat")
            
            # 设置3秒超时
            return await asyncio.wait_for(cache.get(key), timeout=3.0)
        except asyncio.TimeoutError:
            logger.warning(f"Redis缓存获取超时: {key}")
            return None
        except Exception as e:
            logger.warning(f"Redis缓存获取异常: {str(e)}")
            return None

    async def _save_to_cache(self, key: str, value: str):
        """保存到Redis和内存缓存"""
        # 保存到内存缓存
        self._memory_cache[key] = value
        self._cache_expire_time[key] = time.time() + 6000  # 100分钟过期
        
        # 尝试保存到Redis缓存
        try:
            cache = Cache(Cache.REDIS, 
                         endpoint=REDIS_CONFIG["host"], 
                         port=REDIS_CONFIG["port"], 
                         db=REDIS_CONFIG["db"], 
                         password=REDIS_CONFIG["password"],
                         serializer=JsonSerializer(),
                         namespace="qywechat")
            
            # 设置3秒超时
            await asyncio.wait_for(cache.set(key, value, ttl=6000), timeout=3.0)
            logger.info(f"Redis缓存保存成功: {key}")
        except asyncio.TimeoutError:
            logger.warning(f"Redis缓存保存超时: {key}")
        except Exception as e:
            logger.warning(f"Redis缓存保存异常: {str(e)}")

    async def get_user_info(self, code, access_token):
        url = "/auth/getuserinfo"
        resp = await HttpHandler().get(f"{self.base_url}{url}", params={
            "access_token": access_token,
            "code": code,
        })
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'])
            return False
        return resp

    async def get_user_detail_info(self, access_token, userid):
        """获取用户详细信息"""
        url = "/user/get"
        resp = await HttpHandler().get(f"{self.base_url}{url}", params={
            "access_token": access_token,
            "userid": userid,
            "debug": 1,
        }, headers=self.headers)
        if resp['errcode'] != 0:
            # logger.info(resp['errmsg'])
            return False
        return resp

    async def get_userid_by_phone(self, access_token, phone):
        url = "/user/getuserid"
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                        params={"access_token": access_token},
                                        data={"mobile": phone}
                                        )
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'], access_token)
            return False
        return resp

    async def get_openid_by_userid(self, access_token, userid):
        url = "/user/convert_to_openid"
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                        params={"access_token": access_token},
                                        data={"userid": userid}
                                        )
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'])
            return False
        return resp

    async def create_user(self, access_token, params):
        url = "/user/create"
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                        params={"access_token": access_token},
                                        data=params
                                        )
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'])
            return False
        return resp

    async def delete_user(self, access_token, userid):
        url = "/user/delete"
        resp = await HttpHandler().get(f"{self.base_url}{url}", params={
            "access_token": access_token,
            "userid": userid,
        })
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'])
            return False
        return resp

    async def update_user(self, access_token, params):
        url = "/user/update"
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                        params={"access_token": access_token},
                                        data=params
                                        )
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'])
            return False
        return resp

    async def get_user_department_all(self, address_access_token):
        url = "/user/list_id"
        resp = await HttpHandler().get(f"{self.base_url}{url}", params={
            "access_token": address_access_token,
        })
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'])
            return False
        return resp

    async def get_department_all(self, address_access_token):
        url = "/department/simplelist"
        resp = await HttpHandler().get(f"{self.base_url}{url}", params={
            "access_token": address_access_token,
        })
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'])
            return False
        return resp

    async def send_email(self, access_token,
                         userids: List[str],
                         subject: str,
                         content: str,
                         attachment_list: List = None):
        """
        :param attachment_list:
        :param access_token: <erp access_token>
        :param userids: 邮件通知人userid列表
        :param subject: 邮件主题
        :param content: 邮件内容
        :return:
        """
        url = "/exmail/app/compose_send"
        data = {
            "to": {"userids": userids},
            "subject": subject,
            "content": content,
            "enable_id_trans": 1,
            "attachment_list": attachment_list,
        }
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                        params={"access_token": access_token},
                                        data=data
                                        )
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'])
            return False, resp['errmsg']
        return True, resp

    async def create_department(self, access_token, params):
        url = "/department/create"
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                        params={"access_token": access_token},
                                        data=params
                                        )
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'])
            return False
        return resp

    async def update_department(self, access_token, params):
        url = "/department/update"
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                        params={"access_token": access_token},
                                        data=params
                                        )
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'])
            return False
        return resp

    async def delete_department(self, access_token, dept_id):
        url = "/department/delete"
        resp = await HttpHandler().get(f"{self.base_url}{url}", params={
            "access_token": access_token,
            "id": dept_id
        })
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'])
            return False
        return resp

    

    