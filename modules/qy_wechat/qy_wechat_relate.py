"""
author: Lyq
date: 2025-05-23
description: 企微相关接口
"""


from modules.qy_wechat.base import QWechatBase
from utils.net.http_handler import HttpHandler
from settings import logger


qw_base = QWechatBase()

class QyWechatRelate:
    def __init__(self):
        self.base_url = 'https://qyapi.weixin.qq.com/cgi-bin'
        self.headers = {}

    
    def get_access_token(self):
        return qw_base.get_access_token()
        
    async def batch_get_external_contact(self, userid_list, cursor="", limit=100):
        """批量获取客户详情
        
        Args:
            access_token: 调用接口凭证
            userid_list: 企业成员的userid列表，字符串类型，最多支持100个
            cursor: 用于分页查询的游标，字符串类型，由上一次调用返回，首次调用可不填
            limit: 返回的最大记录数，整型，最大值100，默认值50，超过最大值时取最大值
            
        Returns:
            客户详情列表或False（如果发生错误）
        """
        url = "/externalcontact/batch/get_by_user"
        data = {
            "userid_list": userid_list,
            "cursor": cursor,
            "limit": limit
        }
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                        params={"access_token": await self.get_access_token()},
                                        data=data
                                        )
        if resp['errcode'] != 0:
            logger.info(resp['errmsg'])
            return False
        return resp
        
    async def get_groupchat_list(self, status_filter=None, owner_filter=None, cursor=None, limit=1000):
        """获取客户群列表
        
        Args:
            status_filter: 客户群跟进状态过滤。0 - 所有列表(即不过滤)，1 - 离职待继承，2 - 离职继承中，3 - 离职继承完成
            owner_filter: 群主过滤。如果不填，表示获取应用可见范围内全部群主的数据
                userid_list: 用户ID列表。最多100个
                partyid_list: 部门ID列表。最多100个
            cursor: 用于分页查询的游标，字符串类型，由上一次调用返回，首次调用不填
            limit: 分页，预期请求的数据量，取值范围 1 ~ 1000
            
        Returns:
            客户群列表或False（如果发生错误）
        """
        url = "/externalcontact/groupchat/list"
        data = {}
        
        # 添加可选参数
        if status_filter is not None:
            data["status_filter"] = status_filter
        if owner_filter is not None:
            data["owner_filter"] = owner_filter
        if cursor:
            data["cursor"] = cursor
        if limit:
            data["limit"] = limit
            
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"获取客户群列表失败: {resp['errmsg']}")
            return False
        return resp
    
    async def get_groupchat_detail(self, chat_id, need_name=1):
        """获取客户群详情
        
        Args:
            chat_id: 客户群ID
            need_name: 是否需要返回群成员的名字，0-不返回，1-返回。默认为1
            
        Returns:
            客户群详情或False（如果发生错误）
        """
        url = "/externalcontact/groupchat/get"
        data = {
            "chat_id": chat_id,
            "need_name": need_name
        }
            
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"获取客户群详情失败: {resp['errmsg']}")
            return False
        return resp

    ######################## 联系我管理 ########################
    
    async def add_contact_way(self, type, scene, user=None, party=None, style=None, remark=None, skip_verify=True, 
                             state=None, is_temp=False, expires_in=None, chat_expires_in=None, 
                             unionid=None, is_exclusive=False, conclusions=None):
        """配置客户联系「联系我」方式
        
        Args:
            type: 联系方式类型，1-单人，2-多人
            scene: 场景，1-在小程序中联系，2-通过二维码联系
            user: 使用该联系方式的用户userID列表，在type为1时为必填，且只能有一个
            party: 使用该联系方式的部门id列表，只在type为2时有效
            style: 在小程序中联系时使用的控件样式，详见文档附表
            remark: 联系方式的备注信息，用于助记，不超过30个字符
            skip_verify: 外部客户添加时是否无需验证，默认为True
            state: 企业自定义的state参数，用于区分不同的添加渠道，不超过30个字符
            is_temp: 是否临时会话模式，默认为False
            expires_in: 临时会话二维码有效期，以秒为单位，默认7天
            chat_expires_in: 临时会话有效期，以秒为单位，默认添加好友后24小时
            unionid: 可进行临时会话的客户unionid
            is_exclusive: 是否开启同一外部企业客户只能添加同一个员工
            conclusions: 结束语，会话结束时自动发送给客户
            
        Returns:
            配置ID和二维码URL或False（如果发生错误）
        """
        url = "/externalcontact/add_contact_way"
        data = {
            "type": type,
            "scene": scene,
            "skip_verify": skip_verify
        }
        
        # 根据类型添加用户或部门
        if type == 1:
            if not user or len(user) != 1:
                logger.error("联系方式类型为单人时，user必填且只能有一个")
                return False
            data["user"] = user
        else:
            if party:
                data["party"] = party
            if user:
                data["user"] = user
        
        # 添加可选参数
        if style:
            data["style"] = style
        if remark:
            data["remark"] = remark
        if state:
            data["state"] = state
        if is_temp:
            data["is_temp"] = is_temp
        if expires_in:
            data["expires_in"] = expires_in
        if chat_expires_in:
            data["chat_expires_in"] = chat_expires_in
        if unionid:
            data["unionid"] = unionid
        if is_exclusive:
            data["is_exclusive"] = is_exclusive
        if conclusions:
            data["conclusions"] = conclusions
        
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"配置联系我方式失败: {resp['errmsg']}")
            return False
        return resp
    
    async def get_contact_way(self, config_id):
        """获取企业已配置的「联系我」方式
        
        Args:
            config_id: 联系方式的配置id
            
        Returns:
            联系方式详情或False（如果发生错误）
        """
        url = "/externalcontact/get_contact_way"
        data = {
            "config_id": config_id
        }
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"获取联系我方式失败: {resp['errmsg']}")
            return False
        return resp
    
    async def list_contact_way(self, start_time=None, end_time=None, cursor=None, limit=1000):
        """获取企业已配置的「联系我」列表
        
        Args:
            start_time: 「联系我」创建起始时间戳
            end_time: 「联系我」创建结束时间戳
            cursor: 分页查询使用的游标
            limit: 每次查询的分页大小，默认为1000条
            
        Returns:
            联系方式列表或False（如果发生错误）
        """
        url = "/externalcontact/list_contact_way"
        data = {}
        if start_time:
            data["start_time"] = start_time
        if end_time:
            data["end_time"] = end_time
        if cursor:
            data["cursor"] = cursor
        if limit:
            data["limit"] = limit
            
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"获取联系我列表失败: {resp['errmsg']}")
            return False
        return resp
    
    async def update_contact_way(self, config_id, remark=None, skip_verify=None, style=None, 
                                state=None, user=None, party=None, expires_in=None, 
                                chat_expires_in=None, unionid=None, conclusions=None):
        """更新企业已配置的「联系我」方式
        
        Args:
            config_id: 企业联系方式的配置id
            remark: 联系方式的备注信息，不超过30个字符
            skip_verify: 外部客户添加时是否无需验证
            style: 样式，只针对"在小程序中联系"的配置生效
            state: 企业自定义的state参数，用于区分不同的添加渠道
            user: 使用该联系方式的用户列表，将覆盖原有用户列表
            party: 使用该联系方式的部门列表，将覆盖原有部门列表
            expires_in: 临时会话二维码有效期，以秒为单位
            chat_expires_in: 临时会话有效期，以秒为单位
            unionid: 可进行临时会话的客户unionid
            conclusions: 结束语，会话结束时自动发送给客户
            
        Returns:
            成功返回True，失败返回False
        """
        url = "/externalcontact/update_contact_way"
        data = {
            "config_id": config_id
        }
        
        # 添加可选参数
        if remark is not None:
            data["remark"] = remark
        if skip_verify is not None:
            data["skip_verify"] = skip_verify
        if style is not None:
            data["style"] = style
        if state is not None:
            data["state"] = state
        if user is not None:
            data["user"] = user
        if party is not None:
            data["party"] = party
        if expires_in is not None:
            data["expires_in"] = expires_in
        if chat_expires_in is not None:
            data["chat_expires_in"] = chat_expires_in
        if unionid is not None:
            data["unionid"] = unionid
        if conclusions is not None:
            data["conclusions"] = conclusions
            
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"更新联系我方式失败: {resp['errmsg']}")
            return False
        return True
    
    async def del_contact_way(self, config_id):
        """删除企业已配置的「联系我」方式
        
        Args:
            config_id: 企业联系方式的配置id
            
        Returns:
            成功返回True，失败返回False
        """
        url = "/externalcontact/del_contact_way"
        data = {
            "config_id": config_id
        }
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"删除联系我方式失败: {resp['errmsg']}")
            return False
        return True
    
    async def close_temp_chat(self, userid, external_userid):
        """结束临时会话
        
        Args:
            userid: 企业成员的userid
            external_userid: 客户的外部联系人userid
            
        Returns:
            成功返回True，失败返回False
        """
        url = "/externalcontact/close_temp_chat"
        data = {
            "userid": userid,
            "external_userid": external_userid
        }
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"结束临时会话失败: {resp['errmsg']}")
            return False
        return True

    ######################## 加入群聊管理 #######################
    
    async def add_join_way(self, scene, chat_id_list, remark=None, auto_create_room=1, 
                           room_base_name=None, room_base_id=None, state=None):
        """配置客户群进群方式
        
        Args:
            scene: 场景，1-群的小程序插件，2-群的二维码插件
            chat_id_list: 使用该配置的客户群ID列表，最多支持5个
            remark: 联系方式的备注信息，用于助记，超过30个字符将被截断
            auto_create_room: 当群满了后，是否自动新建群。0-否；1-是。默认为1
            room_base_name: 自动建群的群名前缀，当auto_create_room为1时有效
            room_base_id: 自动建群的群起始序号，当auto_create_room为1时有效
            state: 企业自定义的state参数，用于区分不同的入群渠道
            
        Returns:
            配置ID或False（如果发生错误）
        """
        url = "/externalcontact/groupchat/add_join_way"
        data = {
            "scene": scene,
            "chat_id_list": chat_id_list,
            "auto_create_room": auto_create_room
        }
        
        # 添加可选参数
        if remark:
            data["remark"] = remark
        if room_base_name:
            data["room_base_name"] = room_base_name
        if room_base_id:
            data["room_base_id"] = room_base_id
        if state:
            data["state"] = state
            
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"配置客户群进群方式失败: {resp['errmsg']}")
            return False
        return resp
    
    async def get_join_way(self, config_id):
        """获取企业已配置的群二维码或小程序按钮
        
        Args:
            config_id: 联系方式的配置id
            
        Returns:
            群进群方式配置详情或False（如果发生错误）
        """
        url = "/externalcontact/groupchat/get_join_way"
        data = {
            "config_id": config_id
        }
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"获取客户群进群方式失败: {resp['errmsg']}")
            return False
        return resp
    
    async def update_join_way(self, config_id, scene, chat_id_list, remark=None, 
                             auto_create_room=None, room_base_name=None, 
                             room_base_id=None, state=None):
        """更新进群方式配置信息
        
        Args:
            config_id: 企业联系方式的配置id
            scene: 场景，1-群的小程序插件，2-群的二维码插件
            chat_id_list: 使用该配置的客户群ID列表，最多支持5个
            remark: 联系方式的备注信息，超过30个字符将被截断
            auto_create_room: 当群满了后，是否自动新建群。0-否；1-是。
            room_base_name: 自动建群的群名前缀，当auto_create_room为1时有效
            room_base_id: 自动建群的群起始序号，当auto_create_room为1时有效
            state: 企业自定义的state参数，用于区分不同的入群渠道
            
        Returns:
            成功返回True，失败返回False
        """
        url = "/externalcontact/groupchat/update_join_way"
        data = {
            "config_id": config_id,
            "scene": scene,
            "chat_id_list": chat_id_list
        }
        
        # 添加可选参数
        if remark is not None:
            data["remark"] = remark
        if auto_create_room is not None:
            data["auto_create_room"] = auto_create_room
        if room_base_name is not None:
            data["room_base_name"] = room_base_name
        if room_base_id is not None:
            data["room_base_id"] = room_base_id
        if state is not None:
            data["state"] = state
            
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"更新客户群进群方式失败: {resp['errmsg']}")
            return False
        return True
    
    async def del_join_way(self, config_id):
        """删除一个进群方式配置
        
        Args:
            config_id: 企业联系方式的配置id
            
        Returns:
            成功返回True，失败返回False
        """
        url = "/externalcontact/groupchat/del_join_way"
        data = {
            "config_id": config_id
        }
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"删除客户群进群方式失败: {resp['errmsg']}")
            return False
        return True
    
    ######################## 群聊管理 ########################
    
    async def create_groupchat(self, name, owner, userlist, chatid="", agentid=0):
        """创建群聊
        
        Args:
            name: 群聊名，最多50个utf8字符，超过将截断
            owner: 群主的userid
            userlist: 群成员列表，成员用userid标识。群主必须在成员列表中
            chatid: 群聊id，不能与已有的群聊id重复；字符串类型，最长32个字符，只允许字符0-9及字母a-zA-Z
            agentid: 授权方安装的应用agentid。仅旧的第三方多应用套件需要填此参数
            
        Returns:
            创建结果，包含群聊ID或错误信息
        """
        url = "/appchat/create"
        data = {
            "name": name,
            "owner": owner,
            "userlist": userlist
        }
        
        if chatid:
            data["chatid"] = chatid
        if agentid:
            data["agentid"] = agentid
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"创建群聊失败: {resp['errmsg']}")
            return resp
        return resp
    
    async def update_groupchat(self, chatid, name=None, owner=None, add_user_list=None, del_user_list=None):
        """修改群聊信息
        
        Args:
            chatid: 群聊id
            name: 新的群聊名，最多50个utf8字符，超过将截断
            owner: 新群主的userid
            add_user_list: 添加成员的userid列表
            del_user_list: 踢出成员的userid列表
            
        Returns:
            修改结果
        """
        url = "/appchat/update"
        data = {
            "chatid": chatid
        }
        
        if name is not None:
            data["name"] = name
        if owner is not None:
            data["owner"] = owner
        if add_user_list is not None:
            data["add_user_list"] = add_user_list
        if del_user_list is not None:
            data["del_user_list"] = del_user_list
            
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"修改群聊信息失败: {resp['errmsg']}")
            return False
        return True
    
    async def get_groupchat_info(self, chatid):
        """获取群聊信息
        
        Args:
            chatid: 群聊id
            
        Returns:
            群聊信息或False（如果发生错误）
        """
        url = "/appchat/get"
        
        resp = await HttpHandler().get(f"{self.base_url}{url}",
                                      params={
                                          "access_token": await self.get_access_token(),
                                          "chatid": chatid
                                      })
        if resp['errcode'] != 0:
            logger.error(f"获取群聊信息失败: {resp['errmsg']}")
            return False
        return resp
    
    async def send_groupchat_message(self, chatid, msgtype, content):
        """发送群聊消息
        
        Args:
            chatid: 群聊id
            msgtype: 消息类型，text/image/voice/video/file/textcard/news/mpnews/markdown
            content: 消息内容
            
        Returns:
            发送结果
        """
        url = "/appchat/send"
        data = {
            "chatid": chatid,
            "msgtype": msgtype
        }
        
        # 根据消息类型设置内容
        data[msgtype] = content
            
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"发送群聊消息失败: {resp['errmsg']}")
            return False
        return True
    
    async def send_external_contact_message(self, userid, external_userid, msgtype, content):
        """发送消息给外部联系人
        
        Args:
            userid: 企业成员的userid
            external_userid: 外部联系人的userid
            msgtype: 消息类型，text/image/voice/video/file等
            content: 消息内容
            
        Returns:
            发送结果
        """
        # 注意：企业微信API不支持直接发送消息给外部联系人
        # 这里使用群发消息功能代替
        url = "/externalcontact/add_msg_template"
        data = {
            "chat_type": "single",
            "external_userid": [external_userid],
            "sender": userid,
            msgtype: content
        }
            
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"发送外部联系人消息失败: {resp['errmsg']}")
            return False
        
        # 获取群发任务ID并发送
        msg_id = resp.get('msgid')
        if msg_id:
            send_result = await self.send_group_msg(msg_id)
            return send_result
        return True
    
    async def send_group_msg(self, msgid):
        """发送群发消息
        
        Args:
            msgid: 群发消息的id
            
        Returns:
            发送结果
        """
        url = "/externalcontact/send_msg"
        data = {
            "msgid": msgid
        }
            
        resp = await HttpHandler().post(f"{self.base_url}{url}",
                                       params={"access_token": await self.get_access_token()},
                                       data=data
                                       )
        if resp['errcode'] != 0:
            logger.error(f"发送群发消息失败: {resp['errmsg']}")
            return False
        return True
    