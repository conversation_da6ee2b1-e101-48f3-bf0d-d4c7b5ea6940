def print_jjsw_logo():
    logo = r"""
    
    ██╗    ██╗███████╗██╗    ██╗    ██████╗ 
    ██║    ██║██╔════╝██║    ██║    ╚════██╗
    ██║    ██║███████╗██║ █╗ ██║     █████╔╝
██  ██║██  ██║╚════██║██║███╗██║    ██╔═══╝ 
╚████╔╝╚████╔╝███████║╚███╔███╔╝    ███████╗   
 ╚═══╝  ╚═══╝ ╚══════╝ ╚══╝╚══╝     ╚══════╝   
    ════════════════════════════════════
           ══════════════════════
"""

    color = "\033[38;2;245;87;108m"  # ANSI escape sequence for RGB color #f5576c
    reset = "\033[0m"  # ANSI escape sequence to reset color
    colored_logo = f"{color}{logo}{reset}"
    print(colored_logo)
