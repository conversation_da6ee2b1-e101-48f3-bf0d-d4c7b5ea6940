"""
独立的后台服务进程
用于运行所有异步检查任务，避免与主应用进程冲突
"""
import asyncio
import colorama
import logging
import traceback
from datetime import datetime
from modules.other.logo import print_jjsw_logo
from tasks import auto_renew, finance_task, qwechat2erp, send_salary, backup, sync_class, sync_classin, sync_refund, task_qwechat_relate, close_expired_offers, update_class_plan_finish, class_consumption_sync, finance_report_generation, update_checking_price
from tests import async_old_erp_data
from utils.message.workflow_message_queue import start_cc_message_consumer
from app_teach.modules import start_class_approval_monitor
import settings

# 终端颜色
colorama.init(autoreset=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('log/service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class TaskManager:
    """任务管理器，负责管理和监控后台任务"""
    
    def __init__(self):
        self.tasks = {}
        self.task_configs = [
            # ("地址同步", qwechat2erp.address_sync, (60,)),
            # ("企微离职员工同步", qwechat2erp.account_qw_leave, (0.5,)),
            # ("考勤报告备份", backup.backup_attendance_report, (24 * 60,)),
            # ("工资发放通知", send_salary.service_of_send_salary, ()),
            # ("退费任务检测", sync_refund.task_of_sync_refund, ()),
            # ("财务任务检测", finance_task.run_finance_task, ()),
            # ("过期报价单关闭", close_expired_offers.close_expired_offers_task, ()),
            # ("课节完成状态更新", update_class_plan_finish.update_class_plan_finish_task, ()),
            # ("工作流消息队列", start_cc_message_consumer, ()),
            # ("班级审核监控", start_class_approval_monitor, ()),
            # ("班级数据同步", sync_class.main_of_sync_class, ()),
            # ("classin数据同步", sync_classin.task_of_sync_classin, ()),
            # ("课消统计数据同步", class_consumption_sync.class_consumption_sync_task, ()),  # 已弃用，替换为签到价格更新任务
            ("签到价格更新", update_checking_price.checking_price_update_task, ()),
            ("财务报表日报生成", finance_report_generation.finance_report_generation_task, ()),
            # ("续报规则自动创建", auto_renew.auto_renew, ()),
            # ("企微相关任务", task_qwechat_relate.task_of_qwechat_relate, ()),
        ]
        self.is_running = False
        self.restart_count = {}
    
    async def create_task_with_restart(self, name, func, args):
        """创建带有自动重启功能的任务"""
        while self.is_running:
            try:
                logger.info(f"🚀 启动任务: {name}")
                if args:
                    await func(*args)
                else:
                    await func()
            except asyncio.CancelledError:
                logger.info(f"📋 任务 {name} 被取消")
                break
            except Exception as e:
                self.restart_count[name] = self.restart_count.get(name, 0) + 1
                logger.error(f"❌ 任务 {name} 发生错误 (重启次数: {self.restart_count[name]}): {e}")
                logger.error(f"错误详情: {traceback.format_exc()}")
                
                # 如果某个任务频繁重启，增加等待时间
                wait_time = min(30, self.restart_count[name] * 5)
                logger.info(f"⏳ 任务 {name} 将在 {wait_time} 秒后重启")
                
                try:
                    await asyncio.sleep(wait_time)
                except asyncio.CancelledError:
                    break
    
    async def start_all_tasks(self):
        """启动所有后台任务"""
        self.is_running = True
        logger.info(f"✅ 准备启动 {len(self.task_configs)} 个后台任务")
        
        # 创建所有任务
        for name, func, args in self.task_configs:
            task = asyncio.create_task(
                self.create_task_with_restart(name, func, args),
                name=name
            )
            self.tasks[name] = task
        
        logger.info(f"🎯 所有任务已创建并开始运行")
        
        # 监控任务状态
        await self.monitor_tasks()
    
    async def monitor_tasks(self):
        """监控任务状态"""
        while self.is_running:
            try:
                # 每30秒检查一次任务状态
                await asyncio.sleep(30)
                
                alive_tasks = sum(1 for task in self.tasks.values() if not task.done())
                logger.info(f"📊 任务状态监控: {alive_tasks}/{len(self.tasks)} 个任务正在运行")
                
                # 检查是否有任务意外完成
                for name, task in self.tasks.items():
                    if task.done() and not task.cancelled():
                        logger.warning(f"⚠️  任务 {name} 意外完成，状态: {task}")
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 任务监控发生错误: {e}")
                await asyncio.sleep(5)
    
    async def stop_all_tasks(self):
        """停止所有任务"""
        logger.info("🛑 开始停止所有后台任务...")
        self.is_running = False
        
        # 取消所有任务
        for name, task in self.tasks.items():
            if not task.done():
                logger.info(f"📋 取消任务: {name}")
                task.cancel()
        
        # 等待所有任务完成取消
        if self.tasks:
            await asyncio.gather(*self.tasks.values(), return_exceptions=True)
        
        logger.info("✅ 所有后台任务已安全停止")


async def start_all_background_tasks():
    """
    启动所有后台异步任务
    """
    print_jjsw_logo()
    print("🚀 启动后台服务进程...")
    
    task_manager = TaskManager()
    
    try:
        await task_manager.start_all_tasks()
    except KeyboardInterrupt:
        logger.info("\n🛑 收到中断信号，正在关闭后台服务...")
        await task_manager.stop_all_tasks()
        logger.info("✅ 后台服务已安全关闭")
    except Exception as e:
        logger.error(f"❌ 后台服务发生严重错误: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        await task_manager.stop_all_tasks()
        raise


async def run_service_with_restart():
    """带有自动重启功能的服务运行器"""
    restart_count = 0
    max_restarts = 5  # 最大重启次数
    
    while restart_count < max_restarts:
        try:
            logger.info(f"🔧 启动后台服务 (重启次数: {restart_count})")
            await start_all_background_tasks()
            break  # 正常退出
            
        except KeyboardInterrupt:
            logger.info("👋 收到用户中断信号，退出服务")
            break
            
        except Exception as e:
            restart_count += 1
            logger.error(f"❌ 服务崩溃 (第 {restart_count} 次): {e}")
            
            if restart_count < max_restarts:
                wait_time = min(60, restart_count * 10)
                logger.info(f"⏳ 服务将在 {wait_time} 秒后自动重启...")
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"💥 服务已达到最大重启次数 ({max_restarts})，停止重启")
                break


if __name__ == '__main__':
    print("🔧 启动独立后台服务进程...")
    try:
        asyncio.run(run_service_with_restart())
    except KeyboardInterrupt:
        print("\n👋 后台服务已退出")
    except Exception as e:
        print(f"❌ 启动后台服务失败: {e}")
        exit(1) 