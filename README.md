# 进阶ERP服务 2.0.0

## 项目简介
进阶ERP服务是一个基于FastAPI框架开发的企业资源管理系统，提供了全面的企业管理解决方案。

## 技术栈
- Python 3.10
- FastAPI 0.97.0
- SQLAlchemy 1.4.49
- Redis 5.2.0
- Uvicorn 0.32.0

## 项目结构
```
jjswerp2.0.0-backend/
├── app_desk/           # 前台管理模块
├── app_teach/          # 教学管理模块
├── app_competition/    # 竞赛管理模块
├── app_finance/        # 财务管理模块
├── app_human_resources/# 人力资源管理模块
├── app_office/         # 办公管理模块
├── app_order/         # 订单管理模块
├── app_user/          # 用户管理模块
├── models/            # 数据模型
├── modules/           # 公共模块
├── public_api/        # 公共API接口
├── tasks/             # 异步任务
├── utils/             # 工具类
├── templates/         # 模板文件
├── static/           # 静态文件
├── log/              # 日志文件
├── main.py           # 主程序入口
└── settings.py       # 配置文件
```

## 主要功能模块
1. 前台管理 (app_desk)
   - 课程顾问管理
   - 班级管理
   - 其他前台相关功能

2. 教学管理 (app_teach)
   - 在线考试
   - 课程管理
   - 教学资源管理

3. 人力资源管理 (app_human_resources)
   - 员工管理
   - 考勤管理
   - 薪资管理

4. 财务管理 (app_finance)
   - 收支管理
   - 财务报表
   - 工资发放

5. 办公管理 (app_office)
   - 日常办公
   - 文档管理
   - 会议管理

6. 订单管理 (app_order)
   - 订单处理
   - 订单跟踪
   - 订单统计

## 系统特性
- 基于FastAPI的高性能异步API服务
- 完善的权限管理和认证系统
- 企业微信集成
- 短信服务集成
- 文件服务器集成
- 定时任务支持
- 日志管理系统

## 环境要求
- Python 3.10+
- Redis 5.2.0+
- MySQL 5.7+

## 部署说明
1. 安装依赖
```bash
pip install -r requirements.txt
```

2. 配置环境
- 修改 `settings.py` 中的数据库配置
- 配置Redis连接
- 配置企业微信和短信服务相关参数

3. 启动服务
```bash
uvicorn main:app --host 0.0.0.0 --port 20000
```

## API文档
- Swagger UI: `http://your-domain/e5c54f204f583e52700aba0359701713`

## 定时任务
系统包含以下定时任务：
- 企业微信通讯录同步
- 考勤报告备份
- 工资发放服务
- 旧系统数据同步

## 安全说明
- 使用JWT进行身份认证
- 接口访问权限控制
- 数据加密传输
- 密码加密存储

## 开发团队
- 进阶思维技术团队

## 联系方式
- 网站：http://erp.jjsw.vip

## Redis锁问题修复记录

### 问题描述
在使用 aioredis 2.0.1 版本时，当使用 `with redis_client.lock()` 语法时，会出现 `__enter__` 错误。这是因为 aioredis 2.0.1 版本中的 Redis 锁没有正确实现 `__aenter__` 方法，导致在使用异步上下文管理器时出现错误。

错误日志：
```
2025-03-10 16:44:27.441 | ERROR    | tasks.sync_refund:refund_async_trade:133 - 退款成功，但更新报价单状态失败: __enter__
```

### 解决方案
将 Redis 锁的使用方式从 `with` 语句改为显式获取和释放锁：

```python
# 修改前
with redis_client.lock(lock_key, timeout=5, blocking_timeout=5):
    # 锁内的操作
    pass

# 修改后
lock = redis_client.lock(lock_key, timeout=5, blocking_timeout=5)
try:
    await lock.acquire()
    # 锁内的操作
    pass
finally:
    try:
        await lock.release()
    except Exception as release_error:
        logger.error(f"释放锁失败: {release_error}")
```

### 长期解决方案
考虑将 aioredis 升级到 redis-py 4.2.0 或更高版本，该版本已经合并了 aioredis 的功能并修复了这个问题。升级方法：

1. 安装新版本：`pip install redis>=4.2.0`
2. 修改导入方式：`from redis import asyncio as aioredis`

### 修复日期
2025-03-10

## 历程

### 2022-02-10 以前
 - 略

### 2022-02-11 
- 完成预制订单查询接口
- 

### 2023-03-06
- 完成退费单据查询接口
  - 支持多条件查询（订单号、学生ID、退费类型、审核状态、申请日期范围等）
  - 支持按详情表字段（学生订单ID、订单ID）进行查询
  - 实现高性能分页查询
  - 返回退费单和对应的退费详情数据