"""
企业微信到ERP的同步项
"""
import asyncio
from datetime import datetime

from sqlalchemy import null, and_

import settings
from settings import logger
from models.models import ErpAccount
from modules.qy_wechat.base import QWechatBase
from settings import CF, INIT_PASSWORD
from utils.db.account_handler import <PERSON>ccount<PERSON><PERSON><PERSON>
from utils.db.db_handler import get_default_db
from utils.enum.enum_account import EmployeeStatus, SyncStatus

erp_account = CF.get_crud(ErpAccount)


async def address_sync(minute):
    """
    同步企微userid
    :param minute:
    :return:
    """
    # print('【任务】企微通讯录同步正在运行...')
    while True:
        qw_base = QWechatBase()  # 企微处理类
        async for db in get_default_db():
            data = await erp_account.get_many(db, raw=[
                # ErpAccount.qy_wechat_userid == null(),
                ErpAccount.sync_status == SyncStatus.WAITING.value
            ])
            if len(data) <= 0:
                await asyncio.sleep(minute * 60)
                continue
            logger.info(f"本次同步通讯录{len(data)}条")
            address_access_token = await qw_base.get_address_access_token()
            for user in data:
                qy_wechat_user = await qw_base.get_userid_by_phone(address_access_token, user.username)
                if not qy_wechat_user:
                    # print(f'正在同步：{user.username}, {user.nickname}, user不存在')
                    await erp_account.update_one(db, user.id, {
                        "sync_msg": "userid sync failed",
                        "sync_status": SyncStatus.FAILED.value,
                    }, commit=False)
                    continue
                qy_wechat_userid = qy_wechat_user['userid']
                openid_obj = await qw_base.get_openid_by_userid(address_access_token, qy_wechat_userid)
                open_id = openid_obj.get('openid') if openid_obj else ""
                print(f'正在同步：{user.username}, {user.employee_name}')

                await erp_account.update_one(db, user.id, {
                    "password": AccountHandler.get_password_hash(INIT_PASSWORD),
                    "openid": open_id,
                    "qy_wechat_userid": qy_wechat_userid,
                    "create_by": 4001,
                    "update_by": 4001,
                    "sync_msg": "success",
                    "sync_status": SyncStatus.SUCCESS.value,
                }, commit=False)
            try:
                await db.commit()
            except Exception as e:
                logger.warning(f'同步通讯录，更新表失败：{e}')
                await db.rollback()
        await asyncio.sleep(minute * 60)


async def account_qw_leave(minute):
    """
    同步企微离职员工
    """
    # print('【任务】离职员工检查正在运行...')
    while True:
        qw_base = QWechatBase()  # 企微处理类
        async for db in get_default_db():
            data = await erp_account.get_many(db, raw=[
                ErpAccount.employee_status == EmployeeStatus.IN_HANOVER.value,
                ErpAccount.employee_leave_date < datetime.now(settings.TIME_ZONE)
            ])
            if len(data) <= 0:
                await asyncio.sleep(minute * 60)
                continue
            logger.info(f"本次需删除企微员工{len(data)}位")
            address_access_token = await qw_base.get_address_access_token()
            for user in data:
                await qw_base.delete_user(address_access_token, user.qy_wechat_userid)
                await erp_account.update_one(db, user.id, {
                    "employee_status": EmployeeStatus.RESIGNED.value,
                }, commit=False)
            try:
                await db.commit()
            except Exception as e:
                logger.warning(f'离职失败：{e}')
                await db.rollback()
        await asyncio.sleep(minute * 60)
