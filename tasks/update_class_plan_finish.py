import asyncio
from datetime import datetime
from sqlalchemy import text
import settings
from utils.db.db_handler import get_default_db


async def update_class_plan_finish_task(time_interval: int = 60 * 15):  # 默认15分钟检查一次
    """
    定时更新课节完成状态任务
    使用原生SQL语句批量更新已结束但未标记完成的课节
    """
    while True:
        try:
            async for db in get_default_db():
                # 使用原生SQL语句批量更新
                update_sql = """
                UPDATE erp_class_plan 
                SET finish = 1, update_time = NOW() 
                WHERE end_time < NOW() 
                AND finish = 0 
                AND disable = 0
                """
                
                result = await db.execute(text(update_sql))
                affected_rows = result.rowcount
                
                if affected_rows > 0:
                    settings.logger.info(f'>>> 成功更新{affected_rows}个课节的完成状态')
                    await db.commit()
                else:
                    # settings.logger.info('>>> 暂无需要更新完成状态的课节')
                    pass
                
        except Exception as e:
            settings.logger.error(f"课节完成状态定时任务异常: {str(e)}")
        
        finally:
            await asyncio.sleep(time_interval)