import asyncio
from datetime import datetime, timedelta
from sqlalchemy import and_, or_
import settings
from app_human_resources.modules import get_attendance_report
from models.m_attendance import ErpCheckingReport
from models.models import ErpAccount
from settings import CF
from utils.db.db_handler import get_default_db
from utils.enum.enum_account import EmployeeStatus
from utils.other.DateHandler import get_last_ym

erp_account = CF.get_crud(ErpAccount)
ERP_CHECKING_REPORT = CF.get_crud(ErpCheckingReport)


async def backup_attendance_report(minute):
    """
    备份考勤报告数据
    :param minute:
    :return:
    """
    # print('【任务】考勤报告备份正在运行...')
    while True:
        async for db in get_default_db():
            employees = await erp_account.get_many(db, raw=[
                or_(
                    ErpAccount.employee_status.in_([
                        EmployeeStatus.EMPLOYED.value,  # 正式
                        EmployeeStatus.IN_HANOVER.value,  # 交接中
                        EmployeeStatus.PROBATION.value,  # 试用期
                    ]),
                    and_(
                        ErpAccount.employee_status == EmployeeStatus.RESIGNED.value,  # 已离职
                        ErpAccount.employee_leave_date > (datetime.now() - timedelta(days=30 * 2))  # 最近两个月内离职
                    )
                )
            ])
            last_ym = get_last_ym()
            if datetime.now(settings.TIME_ZONE).day < 10:
                await asyncio.sleep(minute * 60)
            # print(f'开始备份{last_ym}报告数据')
            exist_account_objs = await ERP_CHECKING_REPORT.get_many(db, {"ym": last_ym})
            exist_accounts = [i.account_id for i in exist_account_objs]
            count = 0
            for employee in employees:
                if employee.id in exist_accounts:
                    continue
                account_report = await get_attendance_report(db, last_ym, employee.id)
                if not account_report:
                    continue
                await ERP_CHECKING_REPORT.create(db, commit=False, **account_report[0])
                count += 1
            # print(f'本次更新{count}条')
            try:
                await db.commit()
            except Exception as e:
                settings.logger.warning(f'考勤归档，更新表失败：{e}')
                await db.rollback()

        await asyncio.sleep(minute * 60)
