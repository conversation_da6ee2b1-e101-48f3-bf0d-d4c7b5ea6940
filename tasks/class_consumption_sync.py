import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from sqlalchemy import text, and_, or_, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased

import settings
from models.m_finance import ErpClassConsumptionStatistic
from models.m_order import ErpOrderStudent, ErpOrder, ErpOrderOffer, ErpOrderRefundDetail
from models.m_class import ErpClass, ErpCourse, ErpClassChecking, ErpClassPlan
from models.m_student import ErpStudent
from models.m_office import ErpOfficeClassroom, ErpOfficeCenter
from models.m_finance import ErpFinanceTradePayment
from models.models import ErpAccount
from utils.db.db_handler import get_default_db
from utils.db.crud_handler import CRUD
from utils.enum.enum_order import OrderType, StudentState, TradeStatus, CheckStatus

# 初始化CRUD
erp_class_consumption_statistic = CRUD(ErpClassConsumptionStatistic)


async def sync_class_consumption_data(db: AsyncSession, batch_size: int = 1000, is_full_sync: bool = False):
    """
    同步课消统计数据
    
    Args:
        db: 数据库会话
        batch_size: 批量处理大小
        is_full_sync: 是否全量同步，False为增量同步
    """
    start_time = datetime.now()
    sync_type = "全量同步" if is_full_sync else "增量同步"
    
    try:
        print(f"\n{'='*60}")
        print(f"开始执行课消统计数据{sync_type}")
        print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"批处理大小: {batch_size}")
        print(f"{'='*60}")
        
        settings.logger.info(f">>> 开始{sync_type}课消统计数据，批处理大小: {batch_size}")
        
        # 获取需要同步的数据
        if is_full_sync:
            # 全量同步：清空表后重新同步所有数据
            print("🔄 正在清空统计表...")
            await db.execute(text("TRUNCATE TABLE erp_class_consumption_statistic"))
            await db.commit()
            print("✅ 统计表已清空")
            settings.logger.info(">>> 已清空课消统计表")
            
            # 获取所有符合条件的学生订单
            conditions = [
                ErpOrderStudent.order_class_type == OrderType.COURSE.value,
                ErpOrderStudent.student_state.in_([
                    StudentState.NORMAL.value,
                    StudentState.TRANSFER_IN.value  # 只包含转入(4)，不包含转出(5)
                ]),
                ErpOrderStudent.disable == 0
            ]
        else:
            # 增量同步：只同步最近更新的数据
            print("🔍 正在查询最后同步时间...")
            
            # 获取最后同步时间
            last_sync_time_result = await db.execute(
                select(func.max(ErpClassConsumptionStatistic.last_sync_time))
            )
            last_sync_time = last_sync_time_result.scalar()
            
            if not last_sync_time:
                # 如果没有同步时间，同步最近7天的数据
                last_sync_time = datetime.now() - timedelta(days=7)
                print(f"⚠️  未找到上次同步时间，将同步最近7天的数据")
            else:
                print(f"📅 上次同步时间: {last_sync_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            settings.logger.info(f">>> 增量同步，上次同步时间: {last_sync_time}")
            
            conditions = [
                ErpOrderStudent.order_class_type == OrderType.COURSE.value,
                ErpOrderStudent.student_state.in_([
                    StudentState.NORMAL.value,
                    StudentState.TRANSFER_IN.value  # 只包含转入(4)，不包含转出(5)
                ]),
                ErpOrderStudent.disable == 0,
                or_(
                    ErpOrderStudent.update_time > last_sync_time,
                    ErpOrder.update_time > last_sync_time,
                    ErpClassPlan.update_time > last_sync_time
                )
            ]
        
        # 首先获取总数量
        print("📊 正在统计需要处理的数据总量...")
        total_count = await get_order_student_count(db, conditions)
        
        print(f"📈 发现需要处理的记录总数: {total_count}")
        settings.logger.info(f">>> 需要处理的记录总数: {total_count}")
        
        if total_count == 0:
            print("✅ 没有需要同步的数据")
            settings.logger.info(">>> 没有需要同步的数据")
            return
        
        # 分批处理数据
        offset = 0
        total_processed = 0
        batch_number = 0
        
        print(f"\n🚀 开始分批处理数据...")
        
        while True:
            batch_number += 1
            batch_start_time = datetime.now()
            
            # 获取一批基础数据（只查询ErpOrderStudent）
            base_order_students = await get_base_order_students(db, conditions, batch_size, offset)
            
            if not base_order_students:
                break
            
            print(f"\n📦 第 {batch_number} 批:")
            print(f"   ├─ 数据范围: {offset + 1} - {offset + len(base_order_students)}")
            print(f"   ├─ 批次大小: {len(base_order_students)} 条")
            
            # 处理这批数据
            processed_count = await process_batch_data_optimized(db, base_order_students, is_full_sync, batch_number)
            total_processed += processed_count
            
            # 计算进度
            progress_percent = (total_processed / total_count) * 100
            batch_end_time = datetime.now()
            batch_duration = (batch_end_time - batch_start_time).total_seconds()
            
            print(f"   ├─ 处理成功: {processed_count} 条")
            print(f"   ├─ 处理耗时: {batch_duration:.2f} 秒")
            print(f"   ├─ 总进度: {total_processed}/{total_count} ({progress_percent:.1f}%)")
            
            # 估算剩余时间
            if total_processed > 0:
                elapsed_time = (batch_end_time - start_time).total_seconds()
                avg_speed = total_processed / elapsed_time
                remaining_records = total_count - total_processed
                estimated_remaining_time = remaining_records / avg_speed if avg_speed > 0 else 0
                print(f"   └─ 预计剩余时间: {estimated_remaining_time:.0f} 秒")
            
            settings.logger.info(f">>> 第{batch_number}批处理完成: {processed_count}/{len(base_order_students)} 条成功, 总进度: {total_processed}/{total_count} ({progress_percent:.1f}%)")
            
            # 处理完一个批次后提交
            try:
                await db.commit()
                print(f"   └─ 批次数据已提交到数据库")
            except Exception as commit_error:
                await db.rollback()
                error_msg = f"第{batch_number}批数据提交失败: {str(commit_error)}"
                print(f"   ❌ {error_msg}")
                settings.logger.error(f">>> {error_msg}")
                # 提交失败时，将该批次的成功记录数从总数中减去
                total_processed -= processed_count
                processed_count = 0
            
            offset += batch_size
            
            # 避免长时间占用数据库连接
            await asyncio.sleep(0.1)
        
        # 计算总耗时
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()
        
        print(f"\n{'='*60}")
        print(f"✅ 课消统计数据{sync_type}完成!")
        print(f"📊 处理统计:")
        print(f"   ├─ 总记录数: {total_count}")
        print(f"   ├─ 成功处理: {total_processed}")
        print(f"   ├─ 失败记录: {total_count - total_processed}")
        print(f"   ├─ 处理批次: {batch_number}")
        print(f"   ├─ 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   ├─ 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   └─ 总耗时: {total_duration:.2f} 秒")
        print(f"{'='*60}\n")
        
        settings.logger.info(f">>> 课消统计数据{sync_type}完成，共处理 {total_processed}/{total_count} 条记录，耗时 {total_duration:.2f} 秒")
        
    except Exception as e:
        await db.rollback()
        error_msg = f"课消统计数据{sync_type}失败: {str(e)}"
        print(f"\n❌ {error_msg}")
        settings.logger.error(f">>> {error_msg}")
        raise


async def get_order_student_count(db: AsyncSession, conditions: List) -> int:
    """获取符合条件的学生订单数量"""
    try:
        query = (
            select(func.count(ErpOrderStudent.id.distinct()))
            .select_from(ErpOrderStudent)
            .outerjoin(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
            .outerjoin(ErpClassPlan, ErpClassPlan.class_id == ErpOrderStudent.class_id)
            .where(and_(*conditions))
        )
        result = await db.execute(query)
        return result.scalar() or 0
    except Exception as e:
        settings.logger.error(f">>> 获取学生订单数量失败: {str(e)}")
        return 0


async def get_base_order_students(db: AsyncSession, conditions: List, limit: int, offset: int) -> List:
    """获取基础学生订单数据"""
    try:
        query = (
            select(
                ErpOrderStudent.id.label('order_student_id'),
                ErpOrderStudent.class_id,
                ErpOrderStudent.stu_id,
                ErpOrderStudent.student_state,
                ErpOrderStudent.total_hours,
                ErpOrderStudent.complete_hours,
                ErpOrderStudent.create_by,
                ErpOrderStudent.update_by,
                ErpOrderStudent.create_time,
                ErpOrderStudent.update_time
            )
            .select_from(ErpOrderStudent)
            .outerjoin(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
            .outerjoin(ErpClassPlan, ErpClassPlan.class_id == ErpOrderStudent.class_id)
            .where(and_(*conditions))
            .order_by(ErpOrderStudent.id.desc())
            .distinct()
            .limit(limit)
            .offset(offset)
        )
        result = await db.execute(query)
        return result.fetchall()
    except Exception as e:
        settings.logger.error(f">>> 获取基础学生订单数据失败: {str(e)}")
        return []



async def get_batch_related_data(db: AsyncSession, base_order_students: List) -> Dict:
    """批量获取所有相关数据，减少数据库查询次数"""
    try:
        # 收集所有需要查询的ID
        stu_ids = list(set([row.stu_id for row in base_order_students]))
        class_ids = list(set([row.class_id for row in base_order_students]))
        order_student_ids = [row.order_student_id for row in base_order_students]
        account_ids = list(set([row.create_by for row in base_order_students] + [row.update_by for row in base_order_students]))
        
        # 批量查询学生信息
        students = {}
        if stu_ids:
            student_query = select(ErpStudent.id, ErpStudent.stu_name).where(ErpStudent.id.in_(stu_ids))
            student_result = await db.execute(student_query)
            students = {row.id: {'stu_name': row.stu_name} for row in student_result.fetchall()}
        
        # 批量查询班级和课程信息
        classes = {}
        classroom_ids = []
        if class_ids:
            class_query = (
                select(
                    ErpClass.id,
                    ErpClass.class_name,
                    ErpClass.classroom_id,
                    ErpCourse.course_name
                )
                .select_from(ErpClass)
                .outerjoin(ErpCourse, ErpCourse.id == ErpClass.course_id)
                .where(ErpClass.id.in_(class_ids))
            )
            class_result = await db.execute(class_query)
            for row in class_result.fetchall():
                classes[row.id] = {
                    'class_name': row.class_name,
                    'course_name': row.course_name,
                    'classroom_id': row.classroom_id
                }
                if row.classroom_id:
                    classroom_ids.append(row.classroom_id)
        
        # 批量查询教室和教学点信息
        classrooms = {}
        if classroom_ids:
            classroom_query = (
                select(
                    ErpOfficeClassroom.id,
                    ErpOfficeClassroom.room_name.label('classroom_name'),
                    ErpOfficeCenter.id.label('center_id'),
                    ErpOfficeCenter.center_name
                )
                .select_from(ErpOfficeClassroom)
                .outerjoin(ErpOfficeCenter, ErpOfficeCenter.id == ErpOfficeClassroom.center_id)
                .where(ErpOfficeClassroom.id.in_(classroom_ids))
            )
            classroom_result = await db.execute(classroom_query)
            for row in classroom_result.fetchall():
                classrooms[row.id] = {
                    'classroom_name': row.classroom_name,
                    'center_id': row.center_id,
                    'center_name': row.center_name
                }
        
        # 批量查询财务信息
        financials = {}
        if order_student_ids:
            # 订单基础信息
            order_query = (
                select(
                    ErpOrder.order_student_id,
                    func.sum(ErpOrder.total_income).label('total_order_amount'),
                    func.sum(ErpOrder.discount).label('discount'),
                    func.min(ErpOrder.create_time).label('order_create_time'),
                    func.min(ErpOrder.create_by).label('order_create_by')
                )
                .select_from(ErpOrder)
                .where(ErpOrder.order_student_id.in_(order_student_ids))
                .group_by(ErpOrder.order_student_id)
            )
            order_result = await db.execute(order_query)
            for row in order_result.fetchall():
                financials[row.order_student_id] = {
                    'total_order_amount': row.total_order_amount,
                    'discount': row.discount or 0,
                    'order_create_time': row.order_create_time,
                    'order_create_by': row.order_create_by,
                    'payment_order_no': None,
                    'refund_money': 0
                }
            
            # 处理转班订单：查询父订单的财务信息
            # 只查询转入订单（student_state=4），转出订单不参与统计
            transfer_order_query = (
                select(
                    ErpOrderStudent.id.label('order_student_id'),
                    ErpOrderStudent.p_id,
                    ErpOrderStudent.student_state
                )
                .where(
                    and_(
                        ErpOrderStudent.id.in_(order_student_ids),
                        ErpOrderStudent.p_id.isnot(None),
                        ErpOrderStudent.p_id != 0,
                        # 只包括转入(4)状态的订单，转出(5)不参与统计
                        ErpOrderStudent.student_state == StudentState.TRANSFER_IN.value  # 4 - 已转入
                    )
                )
            )
            transfer_order_result = await db.execute(transfer_order_query)
            transfer_orders = transfer_order_result.fetchall()
            
            if transfer_orders:
                parent_order_student_ids = [row.p_id for row in transfer_orders]
                
                # 查询父订单的财务信息
                parent_financial_query = (
                    select(
                        ErpOrder.order_student_id,
                        func.sum(ErpOrder.total_income).label('total_order_amount'),
                        func.sum(ErpOrder.discount).label('discount'),
                        func.min(ErpOrder.create_time).label('order_create_time'),
                        func.min(ErpOrder.create_by).label('order_create_by')
                    )
                    .select_from(ErpOrder)
                    .where(ErpOrder.order_student_id.in_(parent_order_student_ids))
                    .group_by(ErpOrder.order_student_id)
                )
                parent_financial_result = await db.execute(parent_financial_query)
                parent_financials = {
                    row.order_student_id: {
                        'total_order_amount': row.total_order_amount,
                        'discount': row.discount or 0,
                        'order_create_time': row.order_create_time,
                        'order_create_by': row.order_create_by or 4001,  # 使用系统默认ID 4001
                        'payment_order_no': None,
                        'refund_money': 0
                    }
                    for row in parent_financial_result.fetchall()
                }
                
                # 将父订单的财务信息附加到转班订单上
                for transfer_row in transfer_orders:
                    child_order_student_id = transfer_row.order_student_id
                    parent_order_student_id = transfer_row.p_id
                    
                    if parent_order_student_id in parent_financials:
                        parent_financial = parent_financials[parent_order_student_id]
                        if child_order_student_id in financials:
                            # 更新转班订单的财务信息为父订单的财务信息
                            financials[child_order_student_id].update({
                                'total_order_amount': parent_financial['total_order_amount'],
                                'discount': parent_financial['discount'],
                                'order_create_time': parent_financial['order_create_time'],
                                'order_create_by': parent_financial['order_create_by']
                            })
                        else:
                            # 如果financials中没有这个转班订单，创建新的记录
                            financials[child_order_student_id] = {
                                'total_order_amount': parent_financial['total_order_amount'],
                                'discount': parent_financial['discount'],
                                'order_create_time': parent_financial['order_create_time'],
                                'order_create_by': parent_financial['order_create_by'],
                                'payment_order_no': None,
                                'refund_money': 0
                            }
            
            # 支付信息 - 包括转班订单的父订单支付信息
            all_order_student_ids = list(set(order_student_ids + (parent_order_student_ids if 'parent_order_student_ids' in locals() else [])))
            payment_query = (
                select(
                    ErpOrder.order_student_id,
                    ErpFinanceTradePayment.payment_order_no
                )
                .select_from(ErpOrder)
                .outerjoin(ErpOrderOffer, ErpOrderOffer.id == ErpOrder.offer_id)
                .outerjoin(ErpFinanceTradePayment, ErpFinanceTradePayment.payment_order_no == ErpOrderOffer.order_no)
                .where(ErpOrder.order_student_id.in_(all_order_student_ids))
                .distinct()
            )
            payment_result = await db.execute(payment_query)
            payment_info = {row.order_student_id: row.payment_order_no for row in payment_result.fetchall()}
            
            # 更新支付信息到financials中
            for order_student_id, payment_order_no in payment_info.items():
                if order_student_id in financials:
                    financials[order_student_id]['payment_order_no'] = payment_order_no
            
            # 为转班订单设置父订单的支付信息
            if 'transfer_orders' in locals():
                for transfer_row in transfer_orders:
                    child_order_student_id = transfer_row.order_student_id
                    parent_order_student_id = transfer_row.p_id
                    if parent_order_student_id in payment_info and child_order_student_id in financials:
                        financials[child_order_student_id]['payment_order_no'] = payment_info[parent_order_student_id]
            
            # 退款信息 - 包括转班订单的父订单退款信息
            refund_query = (
                select(
                    ErpOrder.order_student_id,
                    func.sum(ErpOrderRefundDetail.refund_money).label('refund_money')
                )
                .select_from(ErpOrderRefundDetail)
                .join(ErpOrder, ErpOrder.id == ErpOrderRefundDetail.order_id)
                .where(ErpOrder.order_student_id.in_(all_order_student_ids))
                .group_by(ErpOrder.order_student_id)
            )
            refund_result = await db.execute(refund_query)
            refund_info = {row.order_student_id: row.refund_money or 0 for row in refund_result.fetchall()}
            
            # 更新退款信息到financials中
            for order_student_id, refund_money in refund_info.items():
                if order_student_id in financials:
                    financials[order_student_id]['refund_money'] = refund_money
            
            # 为转班订单设置父订单的退款信息
            if 'transfer_orders' in locals():
                for transfer_row in transfer_orders:
                    child_order_student_id = transfer_row.order_student_id
                    parent_order_student_id = transfer_row.p_id
                    if parent_order_student_id in refund_info and child_order_student_id in financials:
                        financials[child_order_student_id]['refund_money'] = refund_info[parent_order_student_id]
        
        # 添加财务信息中的创建人ID到账户查询列表
        financial_create_by_ids = [data.get('order_create_by') for data in financials.values() if data.get('order_create_by')]
        account_ids.extend(financial_create_by_ids)
        account_ids = list(set(filter(None, account_ids)))  # 去重并过滤None值
        
        # 批量查询账户信息
        accounts = {}
        if account_ids:
            account_query = (
                select(ErpAccount.id, ErpAccount.employee_name)
                .where(ErpAccount.id.in_(account_ids))
            )
            account_result = await db.execute(account_query)
            accounts = {row.id: row.employee_name for row in account_result.fetchall()}
        
        return {
            'students': students,
            'classes': classes,
            'classrooms': classrooms,
            'financials': financials,
            'accounts': accounts
        }
        
    except Exception as e:
        settings.logger.error(f">>> 批量获取关联数据失败: {str(e)}")
        return {
            'students': {},
            'classes': {},
            'classrooms': {},
            'financials': {},
            'accounts': {}
        }


async def process_batch_data_optimized(db: AsyncSession, base_order_students: List, is_full_sync: bool, batch_number: int = 0) -> int:
    """优化后的批量数据处理"""
    processed_count = 0
    failed_count = 0
    
    # 批量获取关联数据，减少数据库查询次数
    batch_data = await get_batch_related_data(db, base_order_students)
    
    for index, base_row in enumerate(base_order_students, 1):
        try:
            # 从批量数据中获取相关信息
            order_student_id = base_row.order_student_id
            student_info = batch_data['students'].get(base_row.stu_id, {'stu_name': None})
            class_info = batch_data['classes'].get(base_row.class_id, {'class_name': None, 'course_name': None, 'classroom_id': None})
            classroom_info = batch_data['classrooms'].get(class_info['classroom_id'], {'classroom_name': None, 'center_id': None, 'center_name': None})
            financial_info = batch_data['financials'].get(order_student_id, {
                'total_order_amount': None, 'discount': 0, 'order_create_time': None, 
                'order_create_by': 4001, 'payment_order_no': None, 'refund_money': 0
            })
            
            # 获取创建人和更新人姓名，对于转班订单使用父订单的创建人
            order_create_by = financial_info.get('order_create_by', base_row.create_by) or 4001
            create_by_name = batch_data['accounts'].get(order_create_by, "系统")
            update_by_name = batch_data['accounts'].get(base_row.update_by, "系统")
            
            # 计算课消相关数据
            unit_price = 0
            class_consumption_amount = 0
            
            if base_row.total_hours and base_row.total_hours > 0 and financial_info['total_order_amount']:
                unit_price = float(financial_info['total_order_amount']) / float(base_row.total_hours)
                if base_row.complete_hours:
                    class_consumption_amount = unit_price * float(base_row.complete_hours)
            
            # 获取最后课消时间
            last_consumption_time = await get_last_consumption_time(db, base_row.order_student_id)
            
            if is_full_sync:
                # 全量同步：直接插入
                consumption_record = ErpClassConsumptionStatistic(
                    order_student_id=base_row.order_student_id,
                    class_id=base_row.class_id,
                    stu_id=base_row.stu_id,
                    stu_name=student_info['stu_name'],
                    class_name=class_info['class_name'],
                    course_name=class_info['course_name'],
                    center_id=classroom_info['center_id'],
                    center_name=classroom_info['center_name'],
                    classroom_id=class_info['classroom_id'],
                    classroom_name=classroom_info['classroom_name'],
                    payment_order_no=financial_info['payment_order_no'],
                    total_order_amount=financial_info['total_order_amount'],
                    discount=financial_info['discount'] or 0,
                    refund_money=financial_info['refund_money'] or 0,
                    total_hours=base_row.total_hours,
                    complete_hours=base_row.complete_hours,
                    unit_price=unit_price,
                    class_consumption_amount=class_consumption_amount,
                    student_state=base_row.student_state,
                    order_create_time=financial_info['order_create_time'],
                    last_consumption_time=last_consumption_time,
                    create_by=order_create_by,
                    update_by=base_row.update_by,
                    last_sync_time=datetime.now()
                )
                db.add(consumption_record)
            else:
                # 增量同步：更新或插入
                existing_record = await erp_class_consumption_statistic.get_one(
                    db, order_student_id=base_row.order_student_id
                )
                
                if existing_record:
                    # 更新现有记录
                    existing_record.stu_name = student_info['stu_name']
                    existing_record.class_name = class_info['class_name']
                    existing_record.course_name = class_info['course_name']
                    existing_record.center_id = classroom_info['center_id']
                    existing_record.center_name = classroom_info['center_name']
                    existing_record.classroom_id = class_info['classroom_id']
                    existing_record.classroom_name = classroom_info['classroom_name']
                    existing_record.payment_order_no = financial_info['payment_order_no']
                    existing_record.total_order_amount = financial_info['total_order_amount']
                    existing_record.discount = financial_info['discount'] or 0
                    existing_record.refund_money = financial_info['refund_money'] or 0
                    existing_record.total_hours = base_row.total_hours
                    existing_record.complete_hours = base_row.complete_hours
                    existing_record.unit_price = unit_price
                    existing_record.class_consumption_amount = class_consumption_amount
                    existing_record.student_state = base_row.student_state
                    existing_record.order_create_time = financial_info['order_create_time']
                    existing_record.last_consumption_time = last_consumption_time
                    existing_record.update_by = base_row.update_by
                    existing_record.update_time = datetime.now()
                    existing_record.last_sync_time = datetime.now()
                    existing_record.data_version += 1
                else:
                    # 插入新记录
                    consumption_record = ErpClassConsumptionStatistic(
                        order_student_id=base_row.order_student_id,
                        class_id=base_row.class_id,
                        stu_id=base_row.stu_id,
                        stu_name=student_info['stu_name'],
                        class_name=class_info['class_name'],
                        course_name=class_info['course_name'],
                        center_id=classroom_info['center_id'],
                        center_name=classroom_info['center_name'],
                        classroom_id=class_info['classroom_id'],
                        classroom_name=classroom_info['classroom_name'],
                        payment_order_no=financial_info['payment_order_no'],
                        total_order_amount=financial_info['total_order_amount'],
                        discount=financial_info['discount'] or 0,
                        refund_money=financial_info['refund_money'] or 0,
                        total_hours=base_row.total_hours,
                        complete_hours=base_row.complete_hours,
                        unit_price=unit_price,
                        class_consumption_amount=class_consumption_amount,
                        student_state=base_row.student_state,
                        order_create_time=financial_info['order_create_time'],
                        last_consumption_time=last_consumption_time,
                        create_by=order_create_by,
                        update_by=base_row.update_by,
                        last_sync_time=datetime.now()
                    )
                    db.add(consumption_record)
            
            processed_count += 1
            
            # 每处理10条记录输出一次进度（仅在批次较大时）
            if len(base_order_students) > 50 and index % 10 == 0:
                print(f"      └─ 批次内进度: {index}/{len(base_order_students)} ({index/len(base_order_students)*100:.1f}%)")
            
        except Exception as e:
            failed_count += 1
            error_msg = f"第{batch_number}批第{index}条记录处理失败，order_student_id: {base_row.order_student_id}, 错误: {str(e)}"
            print(f"      ⚠️  {error_msg}")
            settings.logger.error(f">>> {error_msg}")
            continue
    
    if failed_count > 0:
        print(f"      ⚠️  批次处理完成，失败 {failed_count} 条记录")
    
    return processed_count


async def get_last_consumption_time(db: AsyncSession, order_student_id: int) -> Optional[datetime]:
    """获取最后课消时间"""
    try:
        query = (
            select(func.max(ErpClassPlan.start_time))
            .select_from(ErpClassChecking)
            .join(ErpClassPlan, ErpClassChecking.class_plan_id == ErpClassPlan.id)
            .where(
                and_(
                    ErpClassChecking.order_student_id == order_student_id,
                    ErpClassChecking.check_status != CheckStatus.ABSENT.value,
                    ErpClassChecking.disable == 0,
                    ErpClassPlan.disable == 0
                )
            )
        )
        result = await db.execute(query)
        return result.scalar()
    except Exception as e:
        settings.logger.error(f">>> 获取最后课消时间失败，order_student_id: {order_student_id}, 错误: {str(e)}")
        return None


async def class_consumption_sync_task(time_interval: int = 60 * 30):  # 默认30分钟同步一次
    """
    课消统计定时同步任务
    
    Args:
        time_interval: 同步间隔（秒）
    """
    settings.logger.info(">>> 启动课消统计定时同步任务")
    
    # 首次启动时进行一次全量同步
    first_run = True
    
    while True:
        try:
            async for db in get_default_db():
                if first_run:
                    # 检查表是否为空，如果为空则进行全量同步
                    count_result = await db.execute(
                        select(func.count(ErpClassConsumptionStatistic.id))
                    )
                    record_count = count_result.scalar()
                    
                    if record_count == 0:
                        settings.logger.info(">>> 检测到课消统计表为空，开始全量同步")
                        await sync_class_consumption_data(db, is_full_sync=True)
                    else:
                        settings.logger.info(f">>> 课消统计表已有 {record_count} 条记录，开始增量同步")
                        await sync_class_consumption_data(db, is_full_sync=False)
                    
                    first_run = False
                else:
                    # 后续运行进行增量同步
                    await sync_class_consumption_data(db, is_full_sync=False)
                    
        except Exception as e:
            settings.logger.error(f">>> 课消统计定时同步任务异常: {str(e)}")
        
        finally:
            # 等待下次同步
            await asyncio.sleep(time_interval)


# 手动触发全量同步的函数
async def manual_full_sync():
    """手动触发全量同步"""
    settings.logger.info(">>> 手动触发课消统计全量同步")
    
    async for db in get_default_db():
        await sync_class_consumption_data(db, is_full_sync=True)
        
    settings.logger.info(">>> 手动全量同步完成")


if __name__ == "__main__":
    # 可以直接运行此脚本进行手动同步
    asyncio.run(manual_full_sync()) 