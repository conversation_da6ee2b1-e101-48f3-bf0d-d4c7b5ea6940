import asyncio
import copy
import random
from datetime import datetime

from app_teach.crud import get_student_by_class_id
from models.m_class import ErpClass, ErpClassRenewRules, ErpCourse, ErpCourseTextbook
from models.m_order import ErpOrderStudent, ErpOrder, ErpOrderOffer
from models.m_student import ErpStudent
from models.m_discount import ErpStudentDiscountCoupon, ErpStudentCouponCourse, ErpStudentDiscountFixed
import settings
from sqlalchemy import select, and_
from utils.db.db_handler import get_default_db, get_redis
from utils.enum.enum_order import StudentState, OfferState, OrderState, OfferType, OrderType, DiscountStatus
from utils.other.money_handler import MoneyHandler
from app_order.crud import class_info_by_classids

erp_class = settings.CF.get_crud(ErpClass)
erp_order_student = settings.CF.get_crud(ErpOrderStudent)
erp_class_renew_rules = settings.CF.get_crud(ErpClassRenewRules)
erp_course = settings.CF.get_crud(ErpCourse)
erp_order = settings.CF.get_crud(ErpOrder)
erp_order_offer = settings.CF.get_crud(ErpOrderOffer)
erp_student = settings.CF.get_crud(ErpStudent)
erp_course_textbook = settings.CF.get_crud(ErpCourseTextbook)
erp_student_discount_coupon = settings.CF.get_crud(ErpStudentDiscountCoupon)
erp_student_coupon_course = settings.CF.get_crud(ErpStudentCouponCourse)
erp_student_discount_fixed = settings.CF.get_crud(ErpStudentDiscountFixed)


async def verify_renew_rule(db, rule_obj):
    """
    验证续报规则
    """
    term_ids = set()
    rule_obj = await erp_class_renew_rules.get_by_id(db, rule_obj.id)
    current_class_obj = await erp_class.get_by_id(db, rule_obj.current_class_id)
    if not current_class_obj:
        rule_obj.new_msg = f"当前班级: {rule_obj.current_class_id}不存在"
        rule_obj.run_status = 2   
        await db.commit()
        return
    current_course_obj = await erp_course.get_by_id(db, current_class_obj.course_id)
    term_ids.add(current_course_obj.term_id)
    next_class_obj = await erp_class.get_by_id(db, rule_obj.next_class_id)
    if not next_class_obj:
        rule_obj.new_msg = f"下一班级: {rule_obj.next_class_id}不存在"
        rule_obj.run_status = 2   
        await db.commit()
        return
    next_course_obj = await erp_course.get_by_id(db, next_class_obj.course_id)
    if next_course_obj.term_id in term_ids:
        rule_obj.new_msg = f"下一班级: {rule_obj.next_class_id}与当前班级: {rule_obj.current_class_id}期段冲突,无法续报"
        rule_obj.run_status = 2   
        await db.commit()
        return
    term_ids.add(next_course_obj.term_id)
    next2_class_obj = await erp_class.get_by_id(db, rule_obj.next2_class_id)
    if not next2_class_obj:
        rule_obj.new_msg = f"下下一班级: {rule_obj.next2_class_id}不存在"
        rule_obj.run_status = 2   
        await db.commit()
        return 
    next2_course_obj = await erp_course.get_by_id(db, next2_class_obj.course_id)
    if next2_course_obj.term_id in term_ids:
        rule_obj.new_msg = f"下下一班级: {rule_obj.next2_class_id}与前面两个班级期段冲突,无法续报"
        rule_obj.run_status = 2   
        await db.commit()
        return
    
    return True


def round_money(amount: float, precision: int = 2):
    """金额精度控制，默认保留2位小数，四舍五入"""
    from decimal import Decimal, ROUND_HALF_UP
    return Decimal(str(amount)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)


async def calculate_discount(db, stu_id, class_obj, sale_price):
    """
    计算优惠金额 - 优先使用固定折扣，其次使用优惠券
    返回: (discount, discount_id)
    """
    discount = 0
    discount_id = 0
    
    # 获取固定折扣
    discount_fixed = await erp_student_discount_fixed.get_one(db, stu_id=stu_id)
    if discount_fixed and discount_fixed.discount_rate > 0:
        # 优先使用固定折扣
        sale_price_decimal = MoneyHandler.to_decimal(sale_price)
        discount_rate_decimal = MoneyHandler.to_decimal(discount_fixed.discount_rate)
        discount = MoneyHandler.to_float(MoneyHandler.multiply(sale_price_decimal, discount_rate_decimal))
        return discount, discount_id
    
    # 获取可用优惠券（未使用固定折扣时）
    discount_coupons = await erp_student_discount_coupon.get_many(
        db,
        {"stu_id": stu_id, "status": DiscountStatus.AVAILABLE.value}
    )
    
    if not discount_coupons:
        return discount, discount_id
    
    # 过滤过期优惠券
    valid_coupons = []
    for coupon in discount_coupons:
        if coupon.expired_time > datetime.now():
            valid_coupons.append(coupon)
        else:
            # 更新已过期优惠券状态
            coupon.status = DiscountStatus.EXPIRED.value
    
    if not valid_coupons:
        return discount, discount_id
    
    # 分离通用优惠券和课程专用优惠券
    universal_coupons = [c for c in valid_coupons if c.is_universal > 0]
    course_coupons = [c for c in valid_coupons if c.is_universal == 0]
    
    # 获取课程专用优惠券对应的课程
    course_coupon_ids = [c.id for c in course_coupons]
    if course_coupon_ids:
        course_coupon_map = await erp_student_coupon_course.get_many(db, raw=[
            ErpStudentCouponCourse.coupon_id.in_(course_coupon_ids)
        ])
        
        # 查找当前课程是否有对应的优惠券
        for course_coupon in course_coupon_map:
            if course_coupon.course_id == class_obj.course_id:
                # 找到对应课程的优惠券
                for coupon in course_coupons:
                    if coupon.id == course_coupon.coupon_id and MoneyHandler.is_less_than(MoneyHandler.to_decimal(coupon.limit_money), MoneyHandler.to_decimal(sale_price)) or MoneyHandler.is_equal(MoneyHandler.to_decimal(coupon.limit_money), MoneyHandler.to_decimal(sale_price)):
                        return MoneyHandler.to_float(coupon.amount), coupon.id
    
    # 如果没有找到课程专用优惠券，尝试使用通用优惠券
    # 按优惠金额从大到小排序
    universal_coupons.sort(key=lambda x: MoneyHandler.to_float(x.amount), reverse=True)
    
    for coupon in universal_coupons:
        if MoneyHandler.is_less_than(MoneyHandler.to_decimal(coupon.limit_money), MoneyHandler.to_decimal(sale_price)) or MoneyHandler.is_equal(MoneyHandler.to_decimal(coupon.limit_money), MoneyHandler.to_decimal(sale_price)):
            return MoneyHandler.to_float(coupon.amount), coupon.id
    
    return discount, discount_id


async def create_class_order(db, class_id, stu_id, offer_id, is_renew=1):
    """
    创建班级订单（包含讲义）
    
    Args:
        db: 数据库会话
        class_id: 班级ID
        stu_id: 学生ID
        offer_id: 报价单ID
        is_renew: 是否续报订单
        
    Returns:
        float: 订单总金额
    """
    # 获取班级信息
    class_obj = await erp_class.get_by_id(db, class_id)
    if not class_obj:
        settings.logger.error(f"班级不存在: {class_id}")
        return 0
    
    # 计算优惠
    discount, discount_id = await calculate_discount(db, stu_id, class_obj, class_obj.sale_price)
    
    # 创建学生课程订单
    order_student = await erp_order_student.create(db, commit=False, **{
        "class_id": class_id,
        "stu_id": stu_id,
        "student_state": StudentState.WAIT_PAY.value,
        "total_hours": class_obj.number_of_lessons or 0,
        "complete_hours": 0,
        "is_renew": is_renew,  # 标记为续费单
        "create_by": 0,
        "update_by": 0,
    })
    
    # 计算价格
    original_price_decimal = MoneyHandler.to_decimal(class_obj.original_price)
    sale_price_decimal = MoneyHandler.to_decimal(class_obj.sale_price)
    discount_decimal = MoneyHandler.to_decimal(discount)
    
    total_receivable = original_price_decimal
    total_income = MoneyHandler.subtract(sale_price_decimal, discount_decimal)
    platform_tax_rate = MoneyHandler.to_decimal(settings.CMB_PLATFORM_TAX or 0)
    platform_tax = MoneyHandler.multiply(total_income, platform_tax_rate)
    
    # 创建订单
    await erp_order.create(db, commit=False, **{
        "order_student_id": order_student.id,
        "offer_id": offer_id,
        "trade_way": 1,  # 线上交易
        "class_price": class_obj.original_price,
        "lesson_price": MoneyHandler.divide(sale_price_decimal, MoneyHandler.to_decimal(class_obj.number_of_lessons or 1)) if class_obj.number_of_lessons and class_obj.number_of_lessons > 0 else MoneyHandler.to_decimal(0),
        "total_receivable": total_receivable,
        "total_income": total_income,
        "discount": discount,
        "discount_id": discount_id,
        "platform_tax": platform_tax,
        "order_state": OrderState.WAIT_PAY.value,
        "campus_id": 1,
        "order_class_type": OrderType.COURSE.value,
        "insert_plan_index": 0,
        "join_type": 3,  # 续费订单
        "is_first_buy": 0,
        'unit': 1,  # 按期收费
        'buy_num': 1,
        "create_by": 0,
        "update_by": 0,
    })
    
    # 如果有绑定讲义，创建讲义订单
    textbook_income = Decimal('0.00')
    if class_obj.bound_textbook_id and class_obj.bound_textbook_id > 0:
        textbook_obj = await erp_course_textbook.get_by_id(db, class_obj.bound_textbook_id)
        textbook_sale_price = MoneyHandler.to_decimal(textbook_obj.sale_price) if textbook_obj else Decimal('0.00')
        if textbook_obj and MoneyHandler.is_greater_than(textbook_sale_price, 0):
            # 创建学生讲义订单
            textbook_order_student = await erp_order_student.create(db, commit=False, **{
                "class_id": class_obj.bound_textbook_id,
                "order_class_type": OrderType.MATERIAL.value,
                "stu_id": stu_id,
                "student_state": StudentState.WAIT_PAY.value,
                "total_hours": 0,
                "complete_hours": 0,
                "is_renew": 0,
                "create_by": 0,
                "update_by": 0,
            })
            
            # 计算讲义平台税
            textbook_platform_tax = MoneyHandler.multiply(textbook_sale_price, platform_tax_rate)
            
            # 创建讲义订单
            await erp_order.create(db, commit=False, **{
                "order_student_id": textbook_order_student.id,
                "offer_id": offer_id,
                "trade_way": 1,  # 线上交易
                "class_price": textbook_obj.sale_price,
                "lesson_price": textbook_obj.sale_price,
                "total_receivable": textbook_sale_price,
                "total_income": textbook_sale_price,
                "platform_tax": textbook_platform_tax,
                "order_class_type": OrderType.MATERIAL.value,
                "order_state": OrderState.WAIT_PAY.value,
                "campus_id": 1,
                'unit': 1,  # 按本
                'buy_num': 1,
                "create_by": 0,
                "update_by": 0,
            })
            
            # 添加讲义金额到总金额
            textbook_income = textbook_sale_price
    
    return MoneyHandler.to_float(MoneyHandler.add(total_income, textbook_income))


async def create_offer_for_class(db, class_id, stu_id, rule_id, class_name=""):
    """
    为单个班级创建报价单
    
    Args:
        db: 数据库会话
        class_id: 班级ID
        stu_id: 学生ID
        rule_id: 续报规则ID
        class_name: 班级名称（用于区分报价单）
        
    Returns:
        int: 报价单ID
    """
    now = datetime.now()
    effective_start = now.strftime("%Y-%m-%d %H:%M:%S")
    effective_end = now.replace(year=now.year + 1).strftime("%Y-%m-%d %H:%M:%S")
    order_no = datetime.now().strftime('%Y%m%d%H%M%S%f')[:17] + (str(stu_id) or '0000') + str(random.randint(1000, 9999))
    
    # 创建报价单
    offer_obj = await erp_order_offer.create(db, commit=False, **{
        "name": f"{order_no}{class_name}续报单",
        "effective_start": effective_start,
        "effective_end": effective_end,
        "total_original_price": 0,  # 后面计算
        "total_sale_price": 0,  # 后面计算
        "total_discount_price": 0,
        "integral_money": 0,
        "ewallet_money": 0,
        "inner_remark": f"自动续报 - 规则ID: {rule_id} - 班级ID: {class_id}",
        "out_remark": f"系统自动生成的续报单 - {class_name}",
        "cash_voucher": "",
        "cash_pay_remark": "",
        "create_by": 0,  # 系统创建
        "update_by": 0,
        "campus_id": 1,
        "order_no": order_no,
        "offer_type": OfferType.RENEW_ORDER.value,
        "offer_state": OfferState.NOT_PAY.value,
        "stu_id": stu_id,
        "order_from": 1,  # ERP创建
        "is_push": 1,  # 自动推送
        "rule_id": rule_id,
    })
    
    # 创建班级订单
    total_sale_price = await create_class_order(db, class_id, stu_id, offer_obj.id)
    
    # 更新报价单金额
    offer_obj.total_sale_price = total_sale_price
    
    return offer_obj.id


async def create_renew_order_for_class(rule_obj):
    """
    创建续报单 - 为每个班级创建独立的报价单
    """
    
    async for db in get_default_db():
        try:
            settings.logger.info(f"创建续报单: {rule_obj.id}")
            result = await verify_renew_rule(db, rule_obj)
            if not result:
                settings.logger.error(f"续报规则验证失败: {rule_obj.new_msg}")
                return

            # 查询出当前班级所有学生
            current_class_students = await get_student_by_class_id(db, rule_obj.current_class_id)
            if not current_class_students:
                settings.logger.info(f"当前班级: {rule_obj.current_class_id}没有学生")
                rule_obj.new_msg = f"当前班级: {rule_obj.current_class_id}没有学生"
                rule_obj.run_status = 2
                await db.commit()
                return

            # 获取班级名称（用于区分报价单）
            next_class = await erp_class.get_by_id(db, rule_obj.next_class_id)
            next2_class = await erp_class.get_by_id(db, rule_obj.next2_class_id)
            next_class_name = next_class.class_name if next_class else "下一班级"
            next2_class_name = next2_class.class_name if next2_class else "下下一班级"
            
            # 为每个学生创建两个独立的续报订单
            offer_ids = []
            
            # 为每个学生创建续报订单
            for student in current_class_students:
                # 获取学生信息
                stu_obj = await erp_student.get_one(db, id=student['stu_id'])
                if not stu_obj:
                    settings.logger.error(f"学生不存在: {student['stu_id']}")
                    continue
                
                # 为第一个班级创建报价单
                next_offer_id = await create_offer_for_class(
                    db, rule_obj.next_class_id, student['stu_id'], rule_obj.id, next_class_name
                )
                
                # 为第二个班级创建报价单
                next2_offer_id = await create_offer_for_class(
                    db, rule_obj.next2_class_id, student['stu_id'], rule_obj.id, next2_class_name
                )
                
                # 保存报价单ID到Redis，用于后续推送
                offer_ids.append((student['stu_id'], next_offer_id, next2_offer_id))
            
            # 更新续报规则状态
            rule_obj.run_status = 1  # 已运行
            rule_obj.created_order = 1  # 已创建订单
            rule_obj.update_time = datetime.now()
            
            # 提交事务
            await db.commit()
            settings.logger.info(f"续报单创建成功，规则ID: {rule_obj.id}，学生数量: {len(current_class_students)}")
            
            # 将订单ID保存到Redis并推送订单通知（使用异步任务，避免阻塞当前任务）
            from app_order.modules import push_order_modules
            async for redis_client in get_redis():
                for stu_id, next_offer_id, next2_offer_id in offer_ids:
                    # 保存订单ID到Redis
                    next_key = f"renew_order_{rule_obj.id}_{stu_id}_next"
                    next2_key = f"renew_order_{rule_obj.id}_{stu_id}_next2"
                    await redis_client.set(next_key, str(next_offer_id), ex=86400)  # 保存1天
                    await redis_client.set(next2_key, str(next2_offer_id), ex=86400)  # 保存1天
                    
                    # 异步推送订单
                    asyncio.create_task(push_order_modules(next_offer_id))
                    asyncio.create_task(push_order_modules(next2_offer_id))
        
        except Exception as e:
            await db.rollback()
            settings.logger.error(f"创建续报单失败: {str(e)}")
            rule_obj.new_msg = f"创建续报单失败: {str(e)}"
            rule_obj.run_status = 2  # 运行失败
            await db.commit()


async def non_order_renew_rule(db):
    """
    查询未创建订单的续报规则
    """
    selects = [
        ErpClassRenewRules.id,
        ErpClassRenewRules.current_class_id,
        ErpClassRenewRules.current_teacher_id,
        ErpClassRenewRules.term_id,
        ErpClassRenewRules.next_class_id,
        ErpClassRenewRules.next2_class_id,
        ErpClassRenewRules.start_time,
        ErpClassRenewRules.end_time,
        ErpClassRenewRules.signup_start,
        ErpClassRenewRules.created_order
    ]
    
    conditions = [
        ErpClassRenewRules.created_order != 1,
        ErpClassRenewRules.disable == 0,
        ErpClassRenewRules.run_status == 0,   # 未运行的
        ErpClassRenewRules.end_time > datetime.now(),
        ErpClassRenewRules.start_time < datetime.now(),
    ]
    stmt = select(*selects).where(and_(*conditions))
    result = await db.execute(stmt)
    return result.fetchall()


async def auto_renew(time_interval: int = 30):
    """
    续报规则自动创建续报单
    """
    while True:
        # 查询未创建订单的规则
        async for db in get_default_db():
            non_order_renew_rule_objs = await non_order_renew_rule(db)
            if not non_order_renew_rule_objs:
                # settings.logger.info('>>> 暂无可应用规则')
                await asyncio.sleep(time_interval)
                continue
            settings.logger.info(f'>>> 查询到{len(non_order_renew_rule_objs)}条可应用规则')
            for non_order_renew_rule_obj in non_order_renew_rule_objs:
                # 创建续报单
                this_rule_obj = copy.deepcopy(non_order_renew_rule_obj)
                asyncio.create_task(create_renew_order_for_class(this_rule_obj))
                await asyncio.sleep(0.5)
            await asyncio.sleep(time_interval)
