"""
财务报表日报生成任务
每日凌晨生成营收报表和简易报表的缓存数据
"""
import asyncio
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional
from decimal import Decimal

from sqlalchemy import text, select, and_, func, case
from sqlalchemy.ext.asyncio import AsyncSession

import settings
from models.m_finance_report import ErpFinanceIncomeReportDaily, ErpFinanceSimpleReportDaily, ErpFinanceReportGenerationLog
from models.m_class import ErpClass, ErpCourse
from models.m_teacher import ErpAccountTeacher
from models.m_finance import ErpFinanceCostType
from utils.db.db_handler import get_default_db
from utils.db.crud_handler import CRUD
from app_finance.modules import finance_income_report_module, finance_simple_report_module


# CRUD instances
finance_income_report_daily_crud = CRUD(ErpFinanceIncomeReportDaily)
finance_simple_report_daily_crud = CRUD(ErpFinanceSimpleReportDaily) 
report_generation_log_crud = CRUD(ErpFinanceReportGenerationLog)


async def log_generation_start(db: AsyncSession, report_type: str, report_date: date) -> int:
    """记录报表生成开始"""
    log_data = {
        "report_type": report_type,
        "report_date": report_date,
        "generation_start_time": datetime.now(),
        "status": "running",
        "create_by": 4001,  # 系统用户
        "update_by": 4001
    }
    log_obj = await report_generation_log_crud.create(db, commit=True, **log_data)
    return log_obj.id


async def log_generation_end(db: AsyncSession, log_id: int, status: str, records_processed: int = 0, error_message: str = None):
    """记录报表生成结束"""
    log_obj = await report_generation_log_crud.get_by_id(db, log_id)
    if log_obj:
        end_time = datetime.now()
        duration = (end_time - log_obj.generation_start_time).seconds if log_obj.generation_start_time else 0
        
        log_obj.generation_end_time = end_time
        log_obj.generation_duration = duration
        log_obj.status = status
        log_obj.records_processed = records_processed
        log_obj.error_message = error_message
        log_obj.update_by = 4001
        
        await db.commit()


async def generate_finance_income_report_daily(target_date: date = None):
    """
    生成营收报表日报数据
    
    Args:
        target_date: 目标日期，默认为昨天
    """
    if target_date is None:
        target_date = date.today() - timedelta(days=1)
    
    async for db in get_default_db():
        log_id = await log_generation_start(db, "income_report", target_date)
        
        try:
            settings.logger.info(f">>> 开始生成营收报表日报数据 - {target_date}")
            
            # 清理当日已有数据
            await db.execute(
                text("DELETE FROM erp_finance_income_report_daily WHERE report_date = :report_date"),
                {"report_date": target_date}
            )
            await db.commit()
            
            # 获取所有班级的营收数据
            # 这里复用现有的 finance_income_report_module 逻辑
            condition = {
                # 可以根据需要添加日期筛选条件
                'start_date_begin': target_date.strftime('%Y-%m-%d'),
                'start_date_end': target_date.strftime('%Y-%m-%d')
            }
            
            # 分批处理数据以避免内存问题
            batch_size = 100
            offset = 0
            total_processed = 0
            
            while True:
                # 获取一批班级数据
                class_data = await finance_income_report_module(
                    db, 
                    page=offset // batch_size + 1, 
                    page_size=batch_size, 
                    condition={}  # 获取所有班级数据
                )
                
                if not class_data:
                    break
                
                # 批量插入数据
                insert_data = []
                for class_item in class_data:
                    # 计算营收指标
                    try:
                        # 基础数据
                        course_income_receivable = Decimal(str(class_item.get('course_income_receivable', 0) or 0))
                        discount_amount = Decimal(str(class_item.get('discount_amount', 0) or 0))
                        course_refund = Decimal(str(class_item.get('course_refund', 0) or 0))
                        lecture_fee_income = Decimal(str(class_item.get('lecture_fee_income', 0) or 0))
                        lecture_fee_refund = Decimal(str(class_item.get('lecture_fee_refund', 0) or 0))
                        actual_teacher_class_fee = Decimal(str(class_item.get('actual_teacher_class_fee', 0) or 0))
                        other_expenses = Decimal(str(class_item.get('other_expenses', 0) or 0))
                        
                        # 计算衍生指标
                        course_income_actual = course_income_receivable - discount_amount - course_refund
                        lecture_fee_net = lecture_fee_income - lecture_fee_refund
                        
                        consumed_amount = Decimal(str(class_item.get('consumed_amount', 0) or 0))
                        unconsumed_amount = course_income_actual - consumed_amount
                        
                        current_gross_profit = course_income_actual + lecture_fee_net - actual_teacher_class_fee
                        current_actual_profit = current_gross_profit - other_expenses
                        
                        # 利润率计算
                        total_income = course_income_actual + lecture_fee_net
                        current_gross_profit_rate = (current_gross_profit / total_income) if total_income > 0 else Decimal('0')
                        current_actual_profit_rate = (current_actual_profit / total_income) if total_income > 0 else Decimal('0')
                        
                        # 平均利润计算
                        completed_class_times = class_item.get('completed_class_times', 0) or 0
                        student_count = class_item.get('student_count', 0) or 0
                        
                        average_profit_per_class = (current_actual_profit / completed_class_times) if completed_class_times > 0 else Decimal('0')
                        average_profit_per_student = (current_actual_profit / student_count) if student_count > 0 else Decimal('0')
                        
                        # 预期指标计算
                        total_class_times = class_item.get('total_class_times', 0) or 0
                        expected_course_total_income = course_income_actual + unconsumed_amount + lecture_fee_net
                        expected_teacher_class_fee = (actual_teacher_class_fee * total_class_times / completed_class_times) if completed_class_times > 0 else actual_teacher_class_fee
                        expected_profit = expected_course_total_income - expected_teacher_class_fee - other_expenses
                        expected_profit_rate = (expected_profit / expected_course_total_income) if expected_course_total_income > 0 else Decimal('0')
                        expected_average_profit_per_class = (expected_profit / total_class_times) if total_class_times > 0 else Decimal('0')
                        expected_average_profit_per_student = (expected_profit / student_count) if student_count > 0 else Decimal('0')
                        
                        insert_data.append({
                            "report_date": target_date,
                            "class_id": class_item.get('class_id'),
                            "class_name": class_item.get('class_name'),
                            "class_type": class_item.get('class_type'),
                            "period": class_item.get('period'),
                            "start_date": class_item.get('start_date'),
                            "class_status": class_item.get('class_status'),
                            "course_type": class_item.get('course_type'),
                            "course_name": class_item.get('course_name'),
                            "teacher_id": class_item.get('teacher_id'),
                            "teacher_name": class_item.get('teacher_name'),
                            "student_count": student_count,
                            "total_class_times": total_class_times,
                            "completed_class_times": completed_class_times,
                            "course_income_receivable": course_income_receivable,
                            "discount_amount": discount_amount,
                            "course_refund": course_refund,
                            "course_income_actual": course_income_actual,
                            "lecture_fee_income": lecture_fee_income,
                            "lecture_fee_refund": lecture_fee_refund,
                            "unconsumed_amount": unconsumed_amount,
                            "consumed_amount": consumed_amount,
                            "course_advisor_commission": Decimal(str(class_item.get('course_advisor_commission', 0) or 0)),
                            "actual_teacher_class_fee": actual_teacher_class_fee,
                            "current_gross_profit": current_gross_profit,
                            "current_gross_profit_rate": current_gross_profit_rate,
                            "other_expenses": other_expenses,
                            "current_actual_profit": current_actual_profit,
                            "current_actual_profit_rate": current_actual_profit_rate,
                            "average_profit_per_class": average_profit_per_class,
                            "average_profit_per_student": average_profit_per_student,
                            "expected_course_total_income": expected_course_total_income,
                            "expected_teacher_class_fee": expected_teacher_class_fee,
                            "expected_profit": expected_profit,
                            "expected_profit_rate": expected_profit_rate,
                            "expected_average_profit_per_class": expected_average_profit_per_class,
                            "expected_average_profit_per_student": expected_average_profit_per_student,
                            "create_by": 4001,
                            "update_by": 4001
                        })
                    except Exception as e:
                        settings.logger.error(f"处理班级 {class_item.get('class_id')} 营收数据时出错: {str(e)}")
                        continue
                
                # 批量插入
                if insert_data:
                    await finance_income_report_daily_crud.create_many(db, insert_data, commit=True)
                    total_processed += len(insert_data)
                    settings.logger.info(f">>> 已处理营收报表数据: {total_processed} 条")
                
                offset += batch_size
                
                # 防止无限循环
                if len(class_data) < batch_size:
                    break
            
            await log_generation_end(db, log_id, "success", total_processed)
            settings.logger.info(f">>> 营收报表日报生成完成 - {target_date}, 共处理 {total_processed} 条记录")
            
        except Exception as e:
            await log_generation_end(db, log_id, "failed", 0, str(e))
            settings.logger.error(f">>> 营收报表日报生成失败 - {target_date}: {str(e)}")
            raise


async def generate_finance_simple_report_daily(target_date: date = None):
    """
    生成简易报表日报数据
    
    Args:
        target_date: 目标日期，默认为昨天
    """
    if target_date is None:
        target_date = date.today() - timedelta(days=1)
    
    async for db in get_default_db():
        log_id = await log_generation_start(db, "simple_report", target_date)
        
        try:
            settings.logger.info(f">>> 开始生成简易报表日报数据 - {target_date}")
            
            # 清理当日已有数据
            await db.execute(
                text("DELETE FROM erp_finance_simple_report_daily WHERE report_date = :report_date"),
                {"report_date": target_date}
            )
            await db.commit()
            
            # 获取年份用于简易报表查询
            year = target_date.year
            
            # 复用现有的 finance_simple_report_module 逻辑
            simple_report_data = await finance_simple_report_module(db, year)
            
            total_processed = 0
            insert_data = []
            
            # 处理各个标签大类的数据
            for label_category, cost_types in simple_report_data.items():
                for cost_type_data in cost_types:
                    try:
                        # 处理父级分类
                        insert_data.append({
                            "report_date": target_date,
                            "cost_type_id": cost_type_data.get('id'),
                            "cost_type_name": cost_type_data.get('name'),
                            "parent_id": cost_type_data.get('parent_id'),
                            "is_parent": cost_type_data.get('is_parent', False),
                            "label_category": label_category,
                            "january": Decimal(str(cost_type_data.get('january', 0) or 0)),
                            "february": Decimal(str(cost_type_data.get('february', 0) or 0)),
                            "march": Decimal(str(cost_type_data.get('march', 0) or 0)),
                            "april": Decimal(str(cost_type_data.get('april', 0) or 0)),
                            "may": Decimal(str(cost_type_data.get('may', 0) or 0)),
                            "june": Decimal(str(cost_type_data.get('june', 0) or 0)),
                            "july": Decimal(str(cost_type_data.get('july', 0) or 0)),
                            "august": Decimal(str(cost_type_data.get('august', 0) or 0)),
                            "september": Decimal(str(cost_type_data.get('september', 0) or 0)),
                            "october": Decimal(str(cost_type_data.get('october', 0) or 0)),
                            "november": Decimal(str(cost_type_data.get('november', 0) or 0)),
                            "december": Decimal(str(cost_type_data.get('december', 0) or 0)),
                            "quarter1": Decimal(str(cost_type_data.get('quarter1', 0) or 0)),
                            "quarter2": Decimal(str(cost_type_data.get('quarter2', 0) or 0)),
                            "quarter3": Decimal(str(cost_type_data.get('quarter3', 0) or 0)),
                            "quarter4": Decimal(str(cost_type_data.get('quarter4', 0) or 0)),
                            "half_year1": Decimal(str(cost_type_data.get('half_year1', 0) or 0)),
                            "half_year2": Decimal(str(cost_type_data.get('half_year2', 0) or 0)),
                            "annual": Decimal(str(cost_type_data.get('annual', 0) or 0)),
                            "total": Decimal(str(cost_type_data.get('total', 0) or 0)),
                            "summary": cost_type_data.get('summary', ''),
                            "create_by": 4001,
                            "update_by": 4001
                        })
                        
                        # 处理子级分类
                        if cost_type_data.get('children'):
                            for child_data in cost_type_data['children']:
                                insert_data.append({
                                    "report_date": target_date,
                                    "cost_type_id": child_data.get('id'),
                                    "cost_type_name": child_data.get('name'),
                                    "parent_id": cost_type_data.get('id'),
                                    "is_parent": False,
                                    "label_category": label_category,
                                    "january": Decimal(str(child_data.get('january', 0) or 0)),
                                    "february": Decimal(str(child_data.get('february', 0) or 0)),
                                    "march": Decimal(str(child_data.get('march', 0) or 0)),
                                    "april": Decimal(str(child_data.get('april', 0) or 0)),
                                    "may": Decimal(str(child_data.get('may', 0) or 0)),
                                    "june": Decimal(str(child_data.get('june', 0) or 0)),
                                    "july": Decimal(str(child_data.get('july', 0) or 0)),
                                    "august": Decimal(str(child_data.get('august', 0) or 0)),
                                    "september": Decimal(str(child_data.get('september', 0) or 0)),
                                    "october": Decimal(str(child_data.get('october', 0) or 0)),
                                    "november": Decimal(str(child_data.get('november', 0) or 0)),
                                    "december": Decimal(str(child_data.get('december', 0) or 0)),
                                    "quarter1": Decimal(str(child_data.get('quarter1', 0) or 0)),
                                    "quarter2": Decimal(str(child_data.get('quarter2', 0) or 0)),
                                    "quarter3": Decimal(str(child_data.get('quarter3', 0) or 0)),
                                    "quarter4": Decimal(str(child_data.get('quarter4', 0) or 0)),
                                    "half_year1": Decimal(str(child_data.get('half_year1', 0) or 0)),
                                    "half_year2": Decimal(str(child_data.get('half_year2', 0) or 0)),
                                    "annual": Decimal(str(child_data.get('annual', 0) or 0)),
                                    "total": Decimal(str(child_data.get('total', 0) or 0)),
                                    "summary": child_data.get('summary', ''),
                                    "create_by": 4001,
                                    "update_by": 4001
                                })
                                
                    except Exception as e:
                        settings.logger.error(f"处理费用类型 {cost_type_data.get('id')} 数据时出错: {str(e)}")
                        continue
            
            # 批量插入数据
            if insert_data:
                # 分批插入避免SQL语句过长
                batch_size = 100
                for i in range(0, len(insert_data), batch_size):
                    batch = insert_data[i:i + batch_size]
                    await finance_simple_report_daily_crud.create_many(db, batch, commit=True)
                    total_processed += len(batch)
                    settings.logger.info(f">>> 已处理简易报表数据: {total_processed} 条")
            
            await log_generation_end(db, log_id, "success", total_processed)
            settings.logger.info(f">>> 简易报表日报生成完成 - {target_date}, 共处理 {total_processed} 条记录")
            
        except Exception as e:
            await log_generation_end(db, log_id, "failed", 0, str(e))
            settings.logger.error(f">>> 简易报表日报生成失败 - {target_date}: {str(e)}")
            raise


async def finance_report_generation_task():
    """
    财务报表日报生成主任务
    每日凌晨2点运行，生成前一天的报表数据
    """
    while True:
        try:
            current_time = datetime.now()
            # 检查是否是凌晨2点
            if current_time.hour == 2 and current_time.minute < 5:
                target_date = (current_time - timedelta(days=1)).date()
                
                settings.logger.info(f">>> 开始执行财务报表日报生成任务 - {target_date}")
                
                # 生成营收报表日报
                await generate_finance_income_report_daily(target_date)
                
                # 生成简易报表日报
                await generate_finance_simple_report_daily(target_date)
                
                settings.logger.info(f">>> 财务报表日报生成任务完成 - {target_date}")
                
                # 等待到第二天，避免重复执行
                await asyncio.sleep(3600)  # 等待1小时
            else:
                # 非执行时间，每10分钟检查一次
                await asyncio.sleep(600)
                
        except Exception as e:
            settings.logger.error(f"财务报表日报生成任务异常: {e}")
            # 发生异常时等待30分钟后重试
            await asyncio.sleep(1800)


async def manual_generate_finance_reports(target_date: date = None, report_types: List[str] = None):
    """
    手动触发财务报表生成
    
    Args:
        target_date: 目标日期，默认为昨天
        report_types: 报表类型列表，可选 ['income_report', 'simple_report']，默认生成所有
    """
    if target_date is None:
        target_date = date.today() - timedelta(days=1)
        
    if report_types is None:
        report_types = ['income_report', 'simple_report']
    
    settings.logger.info(f">>> 手动触发财务报表生成 - {target_date}, 报表类型: {report_types}")
    
    try:
        if 'income_report' in report_types:
            await generate_finance_income_report_daily(target_date)
            
        if 'simple_report' in report_types:
            await generate_finance_simple_report_daily(target_date)
            
        settings.logger.info(f">>> 手动财务报表生成完成 - {target_date}")
        return True
        
    except Exception as e:
        settings.logger.error(f">>> 手动财务报表生成失败 - {target_date}: {str(e)}")
        return False


if __name__ == "__main__":
    # 用于测试
    import asyncio
    async def test():
        await manual_generate_finance_reports()
    
    asyncio.run(test())