"""
签到价格更新任务
处理转班情况的递归查询，更新签到表的价格字段
计算价格后设置flag=2标记，避免重复计算
"""
import asyncio
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, List
from sqlalchemy import select, update, and_, func
from sqlalchemy.ext.asyncio import AsyncSession

import settings
from utils.db.db_handler import get_default_db
from models.m_order import ErpOrder, ErpOrderStudent
from models.m_class import ErpClassChecking


async def find_root_order_student(db: AsyncSession, order_student_id: int) -> Optional[ErpOrderStudent]:
    """
    递归查询p_id直到找到p_id为0的根订单学生记录
    
    Args:
        db: 数据库会话
        order_student_id: 学生订单ID
        
    Returns:
        根订单学生记录，如果找不到返回None
    """
    visited_ids = set()  # 防止循环引用
    current_id = order_student_id
    
    while current_id and current_id not in visited_ids:
        visited_ids.add(current_id)
        
        # 查询当前订单学生记录
        result = await db.execute(
            select(ErpOrderStudent)
            .where(
                and_(
                    ErpOrderStudent.id == current_id,
                    ErpOrderStudent.disable == 0
                )
            )
        )
        order_student = result.scalar_one_or_none()
        
        if not order_student:
            settings.logger.warning(f"找不到学生订单记录: {current_id}")
            return None
            
        # 如果p_id为0或None，说明找到了根订单
        if not order_student.p_id or order_student.p_id == 0:
            return order_student
            
        # 继续查找父订单
        current_id = order_student.p_id
        
    settings.logger.warning(f"递归查询p_id时出现循环引用，起始ID: {order_student_id}")
    return None


async def get_order_info_for_price_calculation(db: AsyncSession, root_order_student_id: int) -> Optional[Dict]:
    """
    根据根订单学生ID获取用于价格计算的订单信息
    
    Args:
        db: 数据库会话
        root_order_student_id: 根订单学生ID
        
    Returns:
        包含total_income和buy_num的字典，如果找不到返回None
    """
    # 查询对应的订单记录
    result = await db.execute(
        select(ErpOrder.total_income, ErpOrder.buy_num)
        .where(
            and_(
                ErpOrder.order_student_id == root_order_student_id,
                ErpOrder.disable == 0
            )
        )
    )
    order_row = result.first()
    
    if not order_row:
        settings.logger.warning(f"找不到根订单学生ID对应的订单记录: {root_order_student_id}")
        return None
        
    total_income = order_row.total_income or Decimal('0')
    buy_num = order_row.buy_num or 0
    
    if buy_num <= 0:
        settings.logger.warning(f"订单buy_num无效: {buy_num}, 根订单学生ID: {root_order_student_id}")
        return None
        
    return {
        'total_income': total_income,
        'buy_num': buy_num,
        'unit_price': round(total_income / Decimal(buy_num), 2)
    }


async def update_checking_price_for_order_student(db: AsyncSession, order_student_id: int) -> int:
    """
    更新指定学生订单的所有签到记录价格
    
    Args:
        db: 数据库会话
        order_student_id: 学生订单ID
        
    Returns:
        更新的记录数量
    """
    try:
        # 1. 递归查找根订单
        root_order_student = await find_root_order_student(db, order_student_id)
        if not root_order_student:
            settings.logger.error(f"无法找到根订单，学生订单ID: {order_student_id}")
            return 0
            
        # 2. 获取订单信息计算价格
        order_info = await get_order_info_for_price_calculation(db, root_order_student.id)
        if not order_info:
            settings.logger.error(f"无法获取订单价格信息，根订单学生ID: {root_order_student.id}")
            return 0
            
        unit_price = order_info['unit_price']
        # settings.logger.info(f"计算得出单价: {unit_price}, 学生订单ID: {order_student_id}, 根订单ID: {root_order_student.id}")
        
        # 3. 更新所有相关的签到记录价格和flag标记
        result = await db.execute(
            update(ErpClassChecking)
            .where(
                and_(
                    ErpClassChecking.order_student_id == order_student_id,
                    ErpClassChecking.disable == 0
                )
            )
            .values(price=unit_price, flag=2, update_time=datetime.now())
        )
        
        updated_count = result.rowcount
        if updated_count > 0:
            await db.commit()
            settings.logger.info(f"成功更新 {updated_count} 条签到记录价格(flag=2)，学生订单ID: {order_student_id}, 单价: {unit_price}")
        else:
            settings.logger.info(f"没有找到需要更新的签到记录，学生订单ID: {order_student_id}")
            
        return updated_count
        
    except Exception as e:
        await db.rollback()
        settings.logger.error(f"更新签到价格失败，学生订单ID: {order_student_id}, 错误: {str(e)}")
        raise


async def update_checking_price_batch(db: AsyncSession, batch_size: int = 500) -> int:
    """
    批量更新签到记录价格
    
    Args:
        db: 数据库会话
        batch_size: 批次大小
        
    Returns:
        总共更新的记录数量
    """
    total_updated = 0
    offset = 0
    
    settings.logger.info(f"开始批量更新签到价格，批次大小: {batch_size}")
    
    while True:
        # 查询需要更新价格的签到记录（price为0或NULL且flag!=2的记录）
        result = await db.execute(
            select(ErpClassChecking.order_student_id)
            .where(
                and_(
                    ErpClassChecking.disable == 0,
                    func.coalesce(ErpClassChecking.price, 0) == 0,
                    func.coalesce(ErpClassChecking.flag, 0) != 2
                )
            )
            .order_by(ErpClassChecking.id.desc())
            .distinct()
            .limit(batch_size)
            .offset(offset)
        )
        
        order_student_ids = [row[0] for row in result.fetchall()]
        
        if not order_student_ids:
            break
            
        settings.logger.info(f"处理批次 {offset//batch_size + 1}，处理 {len(order_student_ids)} 个学生订单")
        
        # 逐个更新学生订单的签到价格
        batch_updated = 0
        for order_student_id in order_student_ids:
            try:
                count = await update_checking_price_for_order_student(db, order_student_id)
                batch_updated += count
                
                # 避免数据库压力过大
                await asyncio.sleep(0.01)
                
            except Exception as e:
                settings.logger.error(f"更新学生订单 {order_student_id} 价格失败: {str(e)}")
                continue
                
        total_updated += batch_updated
        offset += batch_size
        
        settings.logger.info(f"批次处理完成，本批次更新 {batch_updated} 条记录")
        
        # 如果本批次处理的记录数少于批次大小，说明已经处理完所有记录
        if len(order_student_ids) < batch_size:
            break
            
    settings.logger.info(f"批量更新签到价格完成，总共更新 {total_updated} 条记录")
    return total_updated


async def checking_price_update_task(time_interval: int = 60 * 60):  # 默认1小时检查一次
    """
    签到价格更新定时任务
    
    Args:
        time_interval: 检查间隔（秒）
    """
    settings.logger.info(">>> 启动签到价格更新定时任务")
    
    while True:
        try:
            async for db in get_default_db():
                # 查询有多少条签到记录需要更新价格
                count_result = await db.execute(
                    select(func.count(ErpClassChecking.id))
                    .where(
                        and_(
                            ErpClassChecking.disable == 0,
                            func.coalesce(ErpClassChecking.price, 0) == 0,
                            func.coalesce(ErpClassChecking.flag, 0) != 2
                        )
                    )
                )
                need_update_count = count_result.scalar()
                
                if need_update_count == 0:
                    # settings.logger.info(">>> 暂无需要更新价格的签到记录")
                    pass
                else:
                    settings.logger.info(f">>> 发现 {need_update_count} 条签到记录需要更新价格")
                    
                    # 批量更新价格
                    updated_count = await update_checking_price_batch(db, batch_size=200)
                    settings.logger.info(f">>> 签到价格更新完成，共更新 {updated_count} 条记录")
                    
        except Exception as e:
            settings.logger.error(f">>> 签到价格更新定时任务异常: {str(e)}")
        
        finally:
            # 等待下次检查
            await asyncio.sleep(time_interval)


# 为了兼容现有的导入，保留这个函数名
async def update_checking_price_for_order_student_sync(db: AsyncSession, order_student_id: int) -> int:
    """
    兼容性函数，调用异步版本
    """
    return await update_checking_price_for_order_student(db, order_student_id)


if __name__ == '__main__':
    """
    测试脚本，可以直接运行进行测试
    """
    async def test_update():
        async for db in get_default_db():
            # 测试批量更新
            count = await update_checking_price_batch(db, batch_size=10)
            print(f"测试完成，更新了 {count} 条记录")
    
    asyncio.run(test_update()) 