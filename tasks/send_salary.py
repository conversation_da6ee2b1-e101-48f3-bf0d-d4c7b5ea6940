import asyncio
from datetime import datetime

import settings
from models.models import ErpSalaryDetail
from modules.queue.redis_queue import RedisQueue
from modules.qy_wechat.base import QWechatBase
from settings import logger, CF
from utils.db.db_handler import get_default_db

ERP_SALARY_DETAIL = CF.get_crud(ErpSalaryDetail)


async def service_of_send_salary():
    """
    工资发放通知队列
    :return:
    """
    # print('【任务】薪资消息队列正在运行...')
    r_key = 'erp_salary_email'
    while True:
        try:
            qw_base = QWechatBase()  # 企微处理类
            redis_queue = RedisQueue()  # 消息队列处理类
            item = await redis_queue.consume_message(r_key)
            
            # 如果队列为空，等待一段时间再继续
            if item is None:
                await asyncio.sleep(5)
                continue
                
            logger.info(f'>>> 消息队列处理中: {item}')
            access_token = await qw_base.get_access_token()
            flag, resp = await qw_base.send_email(access_token,
                                                  item.get('userids'),
                                                  item.get('subject'),
                                                  item.get('content'),
                                                  item.get('attachment_list'))
            if item.get('salary_detail_id'):
                async for db in get_default_db():
                    if not flag:
                        await ERP_SALARY_DETAIL.update_one(db, item.get('salary_detail_id'), {
                            "send_error": 1,
                            "send_error_msg": resp,
                            "send_time": datetime.now(settings.TIME_ZONE),
                        })
                        continue
                    await ERP_SALARY_DETAIL.update_one(db, item.get('salary_detail_id'), {
                        "is_send": 1,
                        "send_error": 0,
                        "send_error_msg": "",
                        "send_time": datetime.now(settings.TIME_ZONE),
                    })
        except Exception as e:
            logger.error(f"Redis backend error and reconnect: {e}")
            await asyncio.sleep(5)  # 发生异常时等待一段时间再继续
            continue
