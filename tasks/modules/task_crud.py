from sqlalchemy.ext.asyncio import AsyncSession

import settings


async def async_student_plus(db: AsyncSession):
    await db.execute(f"""
    INSERT INTO `erp2.0`.erp_student_plus (stu_id, create_time, update_time, create_by, update_by, disable,complete_exam)
            SELECT 
                a.StuId,
                NOW(),
                NOW(),
                4001,
                4001,
                0,
                0
            FROM `uat_reborn_think`.rb_student a 
            WHERE a.CreateTime > '{settings.LIMIT_DATE}'
            AND a.StuId NOT IN (
                SELECT stu_id 
                FROM `erp2.0`.erp_student_plus
            );
    
    """)
    await db.commit()
