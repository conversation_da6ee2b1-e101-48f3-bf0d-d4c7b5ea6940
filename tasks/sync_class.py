"""
同步ERP和CLASSIN的班级数据任务
"""
import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any

from models.m_class import ErpClass, ErpClassPlan, ErpCourse
from models.m_student import ErpStudent
from models.models import ErpAccount
from models.m_teacher import ErpAccountTeacher
from models.m_order import ErpOrderStudent
import settings
from utils.db.db_handler import get_default_db
from utils.enum.enum_approval import ClassAuditStatus
from utils.enum.enum_class import ClassStatus
from sqlalchemy import or_, select, and_
from modules.classin.classinApiHandler import ClassInSDK
from utils.enum.enum_order import OrderType, StudentState
from hashlib import md5

erp_class = settings.CF.get_crud(ErpClass)
erp_course = settings.CF.get_crud(ErpCourse)
erp_class_plan = settings.CF.get_crud(ErpClassPlan)
erp_student = settings.CF.get_crud(ErpStudent)
erp_order_student = settings.CF.get_crud(ErpOrderStudent)
erp_account = settings.CF.get_crud(ErpAccount)
erp_account_teacher = settings.CF.get_crud(ErpAccountTeacher)

# 初始化ClassIn SDK
classin_sdk = ClassInSDK()



async def sync_class_data_to_classin(class_id: int=None):
    """
    同步班级数据到classin， 这里用class_id为了方便其他地方调用

    """
    settings.logger.info(f"开始同步班级数据到classin: class_id={class_id}")
    async for db in get_default_db():
        class_obj = await erp_class.get_one(db, id=class_id)
        if not class_obj:
            settings.logger.error(f"未获取到班级数据: class_id={class_id}")
            return
        settings.logger.info(f"获取到班级数据: class_name={class_obj.class_name}")
        # 获取课程数据
        course_obj = await erp_course.get_one(db, id=class_obj.course_id)
        if not course_obj:
            settings.logger.error(f"未获取到课程数据: course_id={class_obj.course_id}")
            return
        if int(course_obj.grade_id) in (1, ):   # 小学1v1 - 2人, 其他1v0 - 1人
            seat_num = 2
        else:
            seat_num = 1
        try:
            # 1. 获取班级相关信息
            # 获取教师信息
            teacher = await erp_account_teacher.get_one(db, id=class_obj.teacher_id)
            if not teacher:
                settings.logger.error(f"未获取到教师信息: teacher_id={class_obj.teacher_id}")
                class_obj.classin_sync = 2  # 同步失败
                class_obj.update_time = datetime.now()
                await db.commit()
                return

            teacher_account = await erp_account.get_one(db, id=teacher.account_id)
            if not teacher_account:
                settings.logger.error(f"未获取到教师账号信息: teacher_id={teacher.account_id}")
                class_obj.classin_sync = 2  # 同步失败
                class_obj.update_time = datetime.now()
                await db.commit()
                return
            
            # 获取班级计划（课节）
            class_plans = await erp_class_plan.get_many(db, raw=[
                ErpClassPlan.class_id == class_obj.id,
                ErpClassPlan.disable == 0
            ])
            if class_plans and int(class_plans[0].classin_id) > 0:   # 如果班级计划已同步到classin，则跳过同步
                settings.logger.info(f"检测到存在班级计划已同步到classin, 跳过同步:class_id={class_obj.id}")
                return
            start_timestamp = None
            end_timestamp = None
            if class_plans:

                # 2. 创建ClassIn课程
                # 获取第一个和最后一个课节的时间，作为课程的开始和结束时间
                class_plans.sort(key=lambda x: x.start_time)
                first_plan = class_plans[0]
                last_plan = class_plans[-1]

                # 转换为ClassIn需要的时间戳格式
                start_timestamp = classin_sdk.get_time_stamp_from_date(first_plan.start_time)
                end_timestamp = classin_sdk.get_time_stamp_from_date(last_plan.end_time)

            # 先获取教师uid
            teacher_uid = teacher.classin_uid
            if not teacher_uid:
                # 创建教师, 如果classIn已经存在该教师，会返回教师uid
                teacher_response = await classin_sdk.user.register_user(
                    telephone=teacher_account.username,
                    nickname=teacher_account.employee_name,
                    add_to_school_member=2
                )
                teacher_uid = teacher_response.get('data') or 0   # 不管是新建还是已存在，这里都会返回教师uid
            # 更新教师uid
            teacher.classin_uid = teacher_uid
            teacher.classin_sync = 1
            teacher.update_time = datetime.now()
            # 1. 创建课程
            course_response = await classin_sdk.classroom.addCourse(
                course_name=class_obj.class_name,
                mainTeacherUid=teacher_uid,
                start_time=start_timestamp,
                end_time=end_timestamp,
                )
            settings.logger.info(f"创建ClassIn课程响应: {course_response}")

            # 更新班级classin信息
            class_obj.classin_id = course_response.get('data')
            class_obj.update_time = datetime.now()
            class_obj.classin_sync = 1

            # 获取课程ID
            course_id = str(course_response['data'])
            settings.logger.info(f"创建ClassIn课程成功，课程ID: {course_id}")

            # 2. 创建单元,默认创建"课堂实录"单元，课节放在"课堂实录"单元下

            unit_response = await classin_sdk.lms.create_unit(
                course_id=class_obj.classin_id,
                name="课堂实录",
                publish_flag=2,
                content="课堂实录存放所有课节的录课视频"
            )
            settings.logger.info(f"创建单元响应: {unit_response}")
            unit_id = unit_response.get('data').get('unitId') or 0
            if not unit_id:
                settings.logger.error(f"创建单元失败: {unit_response}")
                return
            
            
            # 3. 创建课节, 在单元下创建课堂活动
            for i, plan in enumerate(class_plans):
                try:
                    plan_start_timestamp = classin_sdk.get_time_stamp_from_date(plan.start_time)
                    plan_end_timestamp = classin_sdk.get_time_stamp_from_date(plan.end_time)
                    lesson_name = f"{class_obj.class_name}-课节{i+1}"
                    activity_response = await classin_sdk.lms.create_class_activity(
                        course_id=class_obj.classin_id,
                        unit_id=unit_id,
                        name=lesson_name,
                        start_time=plan_start_timestamp,
                        end_time=plan_end_timestamp,
                        teacher_uid=teacher_uid,
                        seat_num=seat_num,
                        is_hd=0,
                        is_auto_onstage=0,
                        live_state=1,
                        record_type=0,
                        record_state=1,
                        open_state=1
                        
                    )
                    plan.classin_activity_id = activity_response.get('data').get('activityId') or 0
                    plan.classin_id = activity_response.get('data').get('classId') or 0
                    plan.classin_name = activity_response.get('data').get('name') or ''
                    plan.classin_live_url = activity_response.get('data').get('live_url') or ''
                    plan.classin_live_info = activity_response.get('data').get('live_info') or {}
                    plan.classin_unit_id = unit_id
                    plan.update_time = datetime.now()
                except Exception as e:
                    settings.logger.error(f"创建课节失败: {e}")
                    continue
            settings.logger.info(f"同步班级{class_obj.class_name}数据到ClassIn成功")
            await db.commit()

            return course_id

        except Exception as e:
            settings.logger.error(f"同步班级数据到ClassIn出错: {str(e)}")
            return False



async def get_non_sync_class_data():
    """
    获取未同步的班级数据
    """
    async for db in get_default_db():
        class_objs = await erp_class.get_many(db, raw=[
            or_(
                ErpClass.classin_sync == 0,
                ErpClass.classin_sync == None,
            ),
            ErpClass.audit_status == ClassAuditStatus.PASS.value,
            ErpClass.class_status.in_([ClassStatus.NotStart.value, ClassStatus.Started.value]),
            ErpClass.disable == 0,

        ])
    return class_objs


# 主班级数据同步
async def sync_class_data():
    """
    同步班级数据和课节数据
    """
    non_sync_class_data = await get_non_sync_class_data()
    if not non_sync_class_data:
        return
    class_ids = [class_obj.id for class_obj in non_sync_class_data]
    if not class_ids:
        return
    # 有数据则同步
    for class_id in class_ids:
        await sync_class_data_to_classin(class_id)



async def remove_student_data_from_classin(order_student_id: int):
    """
    移除学生数据从classin
    调用 delete_course_student
    """
    settings.logger.info(f"开始移除学生数据从classin: order_student_id={order_student_id}")
    async for db in get_default_db():
        # 1. 获取学生订单信息
        order_student_obj = await erp_order_student.get_one(db, id=order_student_id)
        if not order_student_obj:
            settings.logger.error(f"未获取到学生订单数据: order_student_id={order_student_id}")
            return False
            
        # 2. 检查学生状态，只处理退班和转出的学生
        if order_student_obj.student_state not in [StudentState.REFUND.value, StudentState.TRANSFER_OUT.value]:
            settings.logger.warning(f"学生状态不是退班或转出，跳过移除: order_student_id={order_student_id}, student_state={order_student_obj.student_state}")
            return False
            
        # 3. 检查是否已经同步过ClassIn
        if not order_student_obj.class_sync or order_student_obj.class_sync != 1:
            settings.logger.info(f"学生未同步到ClassIn，无需移除: order_student_id={order_student_id}")
            return True
            
        # 4. 获取ClassIn相关信息
        classin_stu_uid = order_student_obj.classin_stu_uid
        classin_course_id = order_student_obj.classin_course_id
        
        if not classin_stu_uid or not classin_course_id:
            settings.logger.warning(f"缺少ClassIn信息，无法移除学生: order_student_id={order_student_id}, classin_stu_uid={classin_stu_uid}, classin_course_id={classin_course_id}")
            return False
            
        # 5. 获取学生信息用于日志
        student_obj = await erp_student.get_one(db, id=order_student_obj.stu_id)
        student_name = student_obj.stu_name if student_obj else "未知学生"
        
        # 6. 获取班级信息用于日志
        class_obj = await erp_class.get_one(db, id=order_student_obj.class_id)
        class_name = class_obj.class_name if class_obj else "未知班级"
        
        try:
            # 7. 调用ClassIn API移除学生
            delete_resp = await classin_sdk.classroom.delete_course_student(
                course_id=str(classin_course_id),
                student_uid=str(classin_stu_uid),
                identity=1  # 1-学生，2-旁听
            )
            
            settings.logger.info(f"调用ClassIn删除学生API响应: {delete_resp}")
            
            # 8. 检查API响应结果
            if delete_resp.get('error_info', {}).get('errno') in (1, 163):  # 1-成功，163-学生不存在
                # 9. 更新本地数据库状态
                order_student_obj.class_sync = 0  # 标记为未同步状态
                order_student_obj.classin_stu_uid = 0  # 清空ClassIn学生UID
                order_student_obj.classin_course_id = 0  # 清空ClassIn课程ID
                order_student_obj.update_time = datetime.now()
                
                await db.commit()
                
                settings.logger.info(f"成功移除学生{student_name}从ClassIn课程{class_name}: order_student_id={order_student_id}")
                return True
            else:
                settings.logger.error(f"ClassIn删除学生失败: order_student_id={order_student_id}, 响应={delete_resp}")
                return False
                
        except Exception as e:
            settings.logger.error(f"移除学生数据从ClassIn异常: order_student_id={order_student_id}, 学生={student_name}, 班级={class_name}, 错误={str(e)}")
            return False


async def sync_student_data_to_classin(order_student_id: int):
    """
    同步学生数据到classin,并更新学生表
    """
    print(f"开始同步学生数据到classin: {order_student_id}")
    async for db in get_default_db():
        order_student_obj = await erp_order_student.get_one(db, id=order_student_id)
        if not order_student_obj:
            print(f"未获取到学生数据: {order_student_id}")
            return
        # 获取班级信息
        class_obj = await erp_class.get_one(db, id=order_student_obj.class_id)
        if not class_obj:
            print(f"未获取到班级信息: {order_student_obj.class_id}")
            return
        print(f"获取到班级信息: {class_obj.classin_id}")
        # 确认班级信息是否已经创建
        classin_course_id = class_obj.classin_id
        if class_obj.classin_sync is None or int(class_obj.classin_sync) == 0:
            settings.logger.info(f"检测到班级信息未同步到classin, 开始同步班级信息: {order_student_obj.class_id}")
            classin_course_id = await sync_class_data_to_classin(order_student_obj.class_id)
            if not classin_course_id:
                return
            
        # 获取学生信息
        student_obj = await erp_student.get_one(db, id=order_student_obj.stu_id)
        if not student_obj:
            print(f"未获取到学生信息: {order_student_obj.stu_id}")
            return
            
        classin_stu_uid = student_obj.classin_uid
        if student_obj.classin_uid is None or int(student_obj.classin_uid) <= 0:
            settings.logger.info(f"检测到学生信息未同步到classin, 开始同步学生信息: {order_student_obj.stu_id}")
            student_response = await classin_sdk.user.register_user(
                    telephone=student_obj.stu_username,
                    nickname=student_obj.stu_name,
                    add_to_school_member=1
                )
            student_uid = student_response.get('data') or 0   # 不管是新建还是已存在，这里都会返回教师uid
            print(f"创建/获取学生成功，学生uid: {student_uid}")
            # 更新学生uid
            student_obj.classin_uid = student_uid
            student_obj.update_time = datetime.now()
            student_obj.classin_sync = 1
            classin_stu_uid = student_uid
        
        # 添加学生到课程
        add_resp = await classin_sdk.classroom.add_course_student(
            course_id=classin_course_id,
            student_uid=classin_stu_uid,
            student_name=student_obj.stu_name,
            identity=1   # 1-学生，2-旁听
        )
        # print(f"添加学生到课程响应: {add_resp}, 可略过")
        if add_resp.get('error_info').get('errno') in (1, 163):
            order_student_obj.classin_stu_uid = classin_stu_uid
            order_student_obj.classin_course_id = classin_course_id
            order_student_obj.update_time = datetime.now()
            order_student_obj.class_sync = 1
        else:
            order_student_obj.class_sync = 2  # 添加失败
            order_student_obj.classin_stu_uid = -1  # 添加失败
            order_student_obj.classin_course_id = -1 # 添加失败
            settings.logger.warning(f"添加学生到课程响应: {add_resp}, 需要检查，非新建或已存在")

        await db.commit()
        

async def get_out_student_data():
    """
    获取退班和转出的学生数据
    """
    async for db in get_default_db():
        order_student_objs = await erp_order_student.get_many(db, raw=[
            ErpOrderStudent.student_state.in_([StudentState.REFUND.value, StudentState.TRANSFER_OUT.value, StudentState.CANCEL.value]),
            ErpOrderStudent.class_sync == 1,
        ])
    return order_student_objs
        

# 获取未同步的学生对应课程信息
async def get_non_sync_order_student_data():
    """
    获取未同步的学生对应课程信息
    """
    async for db in get_default_db():
        order_student_objs = await erp_order_student.get_many(db, raw=[
            or_(
                ErpOrderStudent.class_sync == 0,
                ErpOrderStudent.classin_stu_uid == 0,
                ErpOrderStudent.classin_course_id == 0,
                ErpOrderStudent.classin_stu_uid == None,
                ErpOrderStudent.classin_course_id == None,
            ),
            ErpOrderStudent.create_time > '2025-06-01 00:00:00',
            ErpOrderStudent.disable == 0,
            ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value]),   # 正常和转班学生才进行同步
            ErpOrderStudent.class_id > 0,
            ErpOrderStudent.stu_id > 0,
            ErpOrderStudent.total_hours > 0,
            ErpOrderStudent.order_class_type == OrderType.COURSE.value,
            
        ])
    return order_student_objs


# 同步学生数据
async def sync_student_data():
    """
    同步学生数据
    """
    # 查询未同步的学生对应课程信息
    non_sync_order_student_data = await get_non_sync_order_student_data()
    if not non_sync_order_student_data:
        return
    # 有数据则同步
    for order_student_data in non_sync_order_student_data:
        await sync_student_data_to_classin(order_student_data.id)

#退班和转出的需要移除classin学生
async def remove_student_data():
    """
    移除classin学生
    """
    out_student_objs = await get_out_student_data()
    if not out_student_objs:
        # settings.logger.info("没有需要移除的学生数据")
        return
    
    settings.logger.info(f"开始处理{len(out_student_objs)}个需要移除的学生")
    success_count = 0
    fail_count = 0
    
    for out_student_obj in out_student_objs:
        result = await remove_student_data_from_classin(out_student_obj.id)
        if result:
            success_count += 1
        else:
            fail_count += 1
            async for db in get_default_db():
                exist_out_student_obj = await erp_order_student.get_one(db, id=out_student_obj.id)
                if exist_out_student_obj:
                    exist_out_student_obj.class_sync = 3  # 移除失败
                    exist_out_student_obj.classin_stu_uid = 0  # 移除失败
                    exist_out_student_obj.classin_course_id = 0 # 移除失败
                    settings.logger.error(f"处理学生移除失败: order_student_id={out_student_obj.id}, classin resp")
                await db.commit()
    settings.logger.info(f"移除学生数据完成: 成功={success_count}, 失败={fail_count}")



async def main_of_sync_class(interval: int = 60):
    """
    同步ERP和CLASSIN的班级数据任务
    - 同步班级数据和课节数据 sync_class_data
    - 同步学生数据 sync_student_data
    - 移除退班和转出的学生数据 remove_student_data
    """
    while True:
        try:
            # 同步班级数据和课节数据，审核通过但是未同步到classin的班级
            await sync_class_data()    
            # 同步学生数据,正常单但是classin未同步的学生
            await sync_student_data()
            # 移除退班和转出的学生数据
            await remove_student_data()
        except Exception as e:
            settings.logger.error(f"同步班级数据任务异常: {str(e)}")
        
        await asyncio.sleep(interval)

if __name__ == "__main__":
    asyncio.run(main_of_sync_class())