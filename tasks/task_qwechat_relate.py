import asyncio

from models.m_wechat import ErpQwechatContact
from modules.qy_wechat.qy_wechat_relate import QyWechatRelate
from settings import CF
from utils.db.db_handler import get_default_db


erp_qwechat_contact = CF.get_crud(ErpQwechatContact)

async def sync_teacher_students():
    """
    同步教师学生
    """
    async for db in get_default_db():
        non_processed_objs = await erp_qwechat_contact.get_many(db, raw=[
            ErpQwechatContact.stu_id == 0,
            ErpQwechatContact.disable == 0
        ])
        for non_processed_obj in non_processed_objs:
            external_userid = non_processed_obj.external_userid
            print(external_userid)
            # # 调用获取客户详情接口
            # qy_wechat_relate = QyWechatRelate()
            # result = await qy_wechat_relate.get_external_contact_users([external_userid])
            # print(result)


async def task_of_qwechat_relate():
    """
    企微相关任务
    """
    while True:
        await sync_teacher_students()
        await asyncio.sleep(5*60)