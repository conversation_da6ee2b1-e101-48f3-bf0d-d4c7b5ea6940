import asyncio
from datetime import datetime
from sqlalchemy import and_
import settings
from models.m_order import ErpOrderOffer, ErpOrder, ErpOrderStudent
from models.m_student import ErpStudent
from models.m_discount import ErpStudentDiscountCoupon
from utils.db.db_handler import get_default_db
from utils.enum.enum_order import OfferState, OrderState, StudentState, DiscountStatus
from utils.db.crud_handler import CRUD

erp_order_offer = CRUD(ErpOrderOffer)
erp_order = CRUD(ErpOrder)
erp_order_student = CRUD(ErpOrderStudent)
erp_student = CRUD(ErpStudent)
erp_student_discount_coupon = CRUD(ErpStudentDiscountCoupon)


async def get_expired_offers(db):
    """
    查询过期的未付款报价单
    """
    now = datetime.now()
    conditions = [
        ErpOrderOffer.effective_end < now,  # 已过期
        ErpOrderOffer.offer_state == OfferState.NOT_PAY.value,  # 未付款状态
        ErpOrderOffer.disable == 0  # 未删除
    ]
    
    expired_offers = await erp_order_offer.get_many(db, raw=conditions)
    return expired_offers


async def close_expired_offer(db, offer_obj):
    """
    关闭单个过期报价单
    """
    try:
        settings.logger.info(f"开始关闭过期报价单 - ID: {offer_obj.id}, 订单号: {offer_obj.order_no}")
        
        # 1. 更新报价单状态为已失效
        offer_obj.offer_state = OfferState.INVALID.value  # 4-已失效
        offer_obj.update_time = datetime.now()
        
        # 2. 获取关联的订单信息
        orders = await erp_order.get_many(db, {"offer_id": offer_obj.id})
        
        # 3. 更新订单状态为已取消
        for order in orders:
            order.order_state = OrderState.CANCEL.value
            order.update_time = datetime.now()
        
        # 4. 获取订单关联的学生订单并更新状态
        if orders:
            order_student_ids = [order.order_student_id for order in orders]
            order_students = await erp_order_student.get_many(db, raw=[
                ErpOrderStudent.id.in_(order_student_ids)
            ])
            
            for order_student in order_students:
                order_student.student_state = StudentState.CANCEL.value
                order_student.update_time = datetime.now()
        
        # 5. 返还电子钱包余额（如果使用了）
        if offer_obj.ewallet_money > 0:
            # 查找学生记录并返还电子钱包余额
            student_obj = await erp_student.get_one(db, id=offer_obj.stu_id)
            if student_obj:
                # 返还电子钱包余额
                current_amount = student_obj.stu_wallet_amount or 0
                student_obj.stu_wallet_amount = current_amount + float(offer_obj.ewallet_money)
                student_obj.update_time = datetime.now()
                settings.logger.info(f"返还电子钱包余额 - 学生ID: {offer_obj.stu_id}, 金额: {offer_obj.ewallet_money}")
        
        # 6. 返还优惠券（如果使用了）
        # 查找该报价单相关的已使用优惠券
        used_coupons = await erp_student_discount_coupon.get_many(db, raw=[
            ErpStudentDiscountCoupon.stu_id == offer_obj.stu_id,
            ErpStudentDiscountCoupon.status == DiscountStatus.USED.value
        ])
        
        # 通过订单中的discount_id来确定具体使用了哪个优惠券
        for order in orders:
            if order.discount_id and order.discount_id > 0:
                # 查找对应的优惠券并恢复为可用状态
                coupon_obj = await erp_student_discount_coupon.get_one(db, id=order.discount_id)
                if coupon_obj and coupon_obj.status == DiscountStatus.USED.value:
                    # 检查优惠券是否还在有效期内
                    if coupon_obj.expired_time > datetime.now():
                        coupon_obj.status = DiscountStatus.AVAILABLE.value
                        coupon_obj.update_time = datetime.now()
                        settings.logger.info(f"恢复优惠券状态 - 优惠券ID: {coupon_obj.id}, 学生ID: {offer_obj.stu_id}")
                    else:
                        # 优惠券已过期，设置为过期状态
                        coupon_obj.status = DiscountStatus.EXPIRED.value
                        coupon_obj.update_time = datetime.now()
                        settings.logger.info(f"优惠券已过期，设置为过期状态 - 优惠券ID: {coupon_obj.id}")
        
        settings.logger.info(f"过期报价单关闭成功 - ID: {offer_obj.id}, 学生ID: {offer_obj.stu_id}")
        return True
        
    except Exception as e:
        settings.logger.error(f"关闭过期报价单失败 - ID: {offer_obj.id}, 错误: {str(e)}")
        return False


async def close_expired_offers_task(time_interval: int = 60 * 30):  # 默认30分钟检查一次
    """
    定时关闭过期报价单任务
    """
    while True:
        try:
            async for db in get_default_db():
                # 查询过期的报价单
                expired_offers = await get_expired_offers(db)
                
                if not expired_offers:
                    # settings.logger.info('>>> 暂无过期报价单')
                    await asyncio.sleep(time_interval)
                    continue
                
                settings.logger.info(f'>>> 查询到{len(expired_offers)}个过期报价单需要关闭')
                
                success_count = 0
                failed_count = 0
                
                for offer_obj in expired_offers:
                    try:
                        success = await close_expired_offer(db, offer_obj)
                        if success:
                            success_count += 1
                        else:
                            failed_count += 1
                        
                        # 处理间隔，避免数据库压力过大
                        await asyncio.sleep(0.1)
                        
                    except Exception as e:
                        failed_count += 1
                        settings.logger.error(f"处理过期报价单异常 - ID: {offer_obj.id}, 错误: {str(e)}")
                
                # 提交数据库事务
                try:
                    await db.commit()
                    settings.logger.info(f"过期报价单处理完成 - 成功: {success_count}, 失败: {failed_count}")
                except Exception as e:
                    await db.rollback()
                    settings.logger.error(f"过期报价单处理事务提交失败: {str(e)}")
                
        except Exception as e:
            settings.logger.error(f"过期报价单定时任务异常: {str(e)}")
        
        finally:
            await asyncio.sleep(time_interval) 