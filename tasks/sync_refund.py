# 退费任务检测
import asyncio
import json
import time

from sqlalchemy import select, and_

from models.m_finance import ErpFinanceTradePayment, ErpFinanceTradeRefund
from models.m_mall import MallMerchantConfig
from models.m_order import ErpOrder, ErpOrderOffer, ErpOrderRefund, ErpOrderRefundDetail, ErpOrderStudent
from models.m_student import ErpStudent
from modules.cmb.c_merchant_bank.handler import MerchantBank
import settings
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db, get_redis
from utils.enum.enum_approval import AuditState
from utils.enum.enum_order import  OfferState, OrderState,  RefundDetailState,RefundType, TradeStatus
import redis
from redis.exceptions import LockError

erp_order_refund = CRUD(ErpOrderRefund)
erp_order_refund_detail = CRUD(ErpOrderRefundDetail)

erp_finance_trade_payment = CRUD(ErpFinanceTradePayment)
mall_merchant_config = CRUD(MallMerchantConfig)
erp_finance_trade_refund = CRUD(ErpFinanceTradeRefund)
erp_order_offer = CRUD(ErpOrderOffer)
erp_order = CRUD(ErpOrder)
erp_order_student = CRUD(ErpOrderStudent)
erp_student = CRUD(ErpStudent)

async def refund_async_trade(refund_order_no):
    """
    退款结果查询并更新交易
    
    Args:
        refund_order_no: 退款订单号（字符串格式）
    
    Returns:
        bool: 查询结果，True表示退款状态已确定
    """
    try:
        async for db in get_default_db():
            start_time = time.time()
            settings.logger.info(f"开始查询退款状态: {refund_order_no}")

            # 查询退款单，检查当前状态
            erp_finance_trade_refund_obj = await erp_finance_trade_refund.get_one(db, refund_order_no=refund_order_no)
            if not erp_finance_trade_refund_obj:
                settings.logger.info(f"未查询到退款单, 退出同步轮询，单号: {refund_order_no}")
                return True
            
            # 提前获取需要的数据，避免后续产生懒加载请求
            saved_merchant_id = erp_finance_trade_refund_obj.merchant_id
            saved_trade_status = erp_finance_trade_refund_obj.trade_status
            
            # 如果退款单状态不为挂起或退款中，退出查询
            if saved_trade_status not in [TradeStatus.PENDING.value, TradeStatus.REFUNDING.value]:
                settings.logger.info(f"退款单状态不为PENDING/REFUNDING,退出同步轮询, 退款单号: {refund_order_no}")
                return True

            mc = await mall_merchant_config.get_one(db, Id=saved_merchant_id)
            if not mc:
                settings.logger.info(f"未找到商户配置, 退出同步轮询，商户ID: {saved_merchant_id}, 单号: {refund_order_no}")
                return True
            
            client = await MerchantBank.create(mc)
            resp = await client.cmb_refund_query(refund_order_no)
            settings.logger.info(f"-- refund_async_trade -- 退款查询响应: {resp}, 退款单号: {refund_order_no}")
            
            # 处理查询结果
            if resp.get('returnCode') == 'SUCCESS':
                if resp['respCode'] == 'SUCCESS':
                    settings.logger.info(f'【主动查询到退款】{resp}')
                    biz_content = resp['biz_content']
                    biz_content = json.loads(biz_content)
                    cmb_trade_state = biz_content.get('tradeState')
                    cmb_order_id = biz_content.get('cmbOrderId')
                    third_order_id = biz_content.get('thirdOrderId')
                    
                    # 处理不同的交易状态
                    if cmb_trade_state == 'S':  # 退款成功
                        # 更新退款单
                        erp_finance_trade_refund_obj.comments = f'已完成退款'
                        
                        erp_finance_trade_refund_obj.trade_status = TradeStatus.SUCCESS.value
                        erp_finance_trade_refund_obj.cmb_order_id = cmb_order_id
                        erp_finance_trade_refund_obj.third_order_id = third_order_id
                        if 'txnTime' in biz_content:
                            erp_finance_trade_refund_obj.cmb_pay_time = biz_content['txnTime']
                        if 'tradeType' in biz_content:
                            erp_finance_trade_refund_obj.cmb_trade_type = biz_content['tradeType']
                        if 'merId' in biz_content:
                            erp_finance_trade_refund_obj.merchant_id = mc.Id
                        if 'refundAmt' in biz_content:
                            refund_amt = float(biz_content['refundAmt'])/100
                            erp_finance_trade_refund_obj.money_refund = refund_amt
                        
                        settings.logger.info(f"退款成功: 退款单号: {refund_order_no}, 详情: {biz_content}")
                        
                        # 更新申请单
                        refund_detail_obj = await erp_order_refund_detail.get_one(db, refund_order_no=refund_order_no)
                        refund_detail_obj.refund_money = float(biz_content['refundAmt'])/100
                        refund_detail_obj.pay_time = biz_content['txnTime']
                        refund_detail_obj.refund_state = RefundDetailState.SUCCESS.value

                        # 更新退款申请表
                        order_refund_obj = await erp_order_refund.get_by_id(db, refund_detail_obj.refund_id)
                        order_refund_obj.refund_money = float(order_refund_obj.refund_money or 0) + float(biz_content['refundAmt'])/100
                        
                        order_obj = await erp_order.get_by_id(db, refund_detail_obj.order_id)
                        order_obj.refund = float(order_obj.refund or 0) + float(biz_content['refundAmt'])/100
                        # 学生订单表
                        order_student_obj = await erp_order_student.get_by_id(db,refund_detail_obj.order_student_id)
                        # 减掉相应的退还课节，确保不为负数
                        current_hours = float(order_student_obj.total_hours or 0)
                        refund_hours = float(refund_detail_obj.refund_num or 0)
                        order_student_obj.total_hours = max(0, current_hours - refund_hours)  # 确保不为负数
                        # 申请返还的电子钱包金额
                        refund_ewallet_money = refund_detail_obj.refund_ewallet
                        if refund_ewallet_money:
                            # 原订单使用的电子钱包金额
                            order_obj = await erp_order.get_by_id(db, refund_detail_obj.order_id)
                            if order_obj.ewallet_money < refund_ewallet_money:
                                settings.logger.warning(f"申请返还的电子钱包金额{refund_ewallet_money}大于订单使用的电子钱包金额{order_obj.ewallet_money}, 单号: {refund_order_no}, 未返还金额{refund_ewallet_money - order_obj.ewallet_money}")
                                refund_ewallet_money = order_obj.ewallet_money


                            # 查询学生订单并更新电子钱包金额
                            
                            student_obj = await erp_student.get_by_id(db, order_student_obj.stu_id)
                            student_obj.stu_wallet_amount = float(student_obj.stu_wallet_amount or 0) + float(refund_ewallet_money or 0)
                        # try:
                        # 获取报价单信息
                        offer_obj = await erp_order_offer.get_one(db, order_no=refund_detail_obj.order_no)
                        if offer_obj:
                            offer_obj = await erp_order_offer.get_by_id(db, offer_obj.id)
                            
                            try:
                                # 获取订单信息，用于判断退款金额是否等于订单金额
                                order_obj = await erp_order.get_by_id(db, refund_detail_obj.order_id)
                                if not order_obj:
                                    settings.logger.error(f"未找到订单信息: {refund_detail_obj.order_id}, 退款单号: {refund_order_no}")
                                    offer_obj.offer_state = OfferState.PARTIAL_REFUND.value
                                    return

                                # 计算订单已退款总金额
                                stmt = select(
                                    ErpOrderRefundDetail.refund_money
                                ).where(
                                    and_(
                                        ErpOrderRefundDetail.order_id == refund_detail_obj.order_id,
                                        ErpOrderRefundDetail.refund_state == RefundDetailState.SUCCESS.value
                                    )
                                )
                                result = await db.execute(stmt)
                                refund_records = result.fetchall()
                                
                                # 计算已退款总额，包括当前退款
                                current_refund_amount = float(biz_content['refundAmt'])/100
                                previous_refund_amount = sum([float(record.refund_money or 0) for record in refund_records])
                                total_refund_amount = previous_refund_amount + current_refund_amount
                                order_pay_amount = float(order_obj.total_income or 0)
                                
                                settings.logger.info(f"退款计算: 订单号: {refund_detail_obj.order_no}, "
                                                    f"当前退款: {current_refund_amount}, "
                                                    f"之前已退: {previous_refund_amount}, "
                                                    f"总退款: {total_refund_amount}, "
                                                    f"订单金额: {order_pay_amount}")
                                
                                # 判断是否为全额退款（允许0.01误差）
                                if abs(total_refund_amount - order_pay_amount) <= 0.01:
                                    # 全额退款
                                    offer_obj.offer_state = OfferState.FULL_REFUND.value
                                    settings.logger.info(f"全额退款完成: 订单号: {refund_detail_obj.order_no}")
                                else:
                                    # 部分退款 
                                    offer_obj.offer_state = OfferState.PARTIAL_REFUND.value
                                    settings.logger.info(f"部分退款完成: 订单号: {refund_detail_obj.order_no}")
                                    
                            except Exception as inner_e:
                                settings.logger.error(f"计算退款金额时发生异常: {refund_order_no}, 错误: {str(inner_e)}")
                                # 默认为部分退款
                                offer_obj.offer_state = OfferState.PARTIAL_REFUND.value
                                    
                        # except LockError as e:
                        #     settings.logger.error(f"获取报价单状态锁失败: {e}")
                        # except Exception as e:
                        #     settings.logger.error(f"退款成功，但更新报价单状态失败: {e}")
                        
                        await db.commit()
                        return True
                        
                    elif cmb_trade_state == 'F':  # 退款失败
                        failure_reason = biz_content.get('failureReason', '未知原因')
                        erp_finance_trade_refund_obj.trade_status = TradeStatus.FAIL.value
                        erp_finance_trade_refund_obj.cmb_order_id = cmb_order_id
                        erp_finance_trade_refund_obj.third_order_id = third_order_id
                        erp_finance_trade_refund_obj.comments = failure_reason
                        
                        # 更新退款详情状态为失败，并增加退款次数
                        refund_detail_obj = await erp_order_refund_detail.get_one(db, refund_order_no=refund_order_no)
                        if refund_detail_obj:
                            refund_detail_obj.refund_state = RefundDetailState.FAIL.value
                            refund_detail_obj.refund_times += 1  # 招行确认退款失败，增加重试次数
                        
                        settings.logger.warning(f"退款失败: 退款单号: {refund_order_no}, 原因: {failure_reason}")
                        await db.commit()
                        return True
                        
                    elif cmb_trade_state == 'P':  # 退款中
                        erp_finance_trade_refund_obj.trade_status = TradeStatus.REFUNDING.value
                        erp_finance_trade_refund_obj.cmb_order_id = cmb_order_id
                        erp_finance_trade_refund_obj.third_order_id = third_order_id
                        
                        settings.logger.info(f"退款处理中: 退款单号: {refund_order_no}")
                        await db.commit()
                        return False
                    else:
                        # 未知状态，也需要增加重试次数
                        refund_detail_obj = await erp_order_refund_detail.get_one(db, refund_order_no=refund_order_no)
                        if refund_detail_obj:
                            refund_detail_obj.refund_state = RefundDetailState.FAIL.value
                            refund_detail_obj.refund_times += 1  # 未知状态也增加重试次数
                        
                        settings.logger.warning(f"退款查询返回未知状态: 退款单号: {refund_order_no}, 状态: {cmb_trade_state}, 响应: {resp}")
                        await db.commit()
                        return True
                else:
                    # 查询业务失败，需要增加重试次数
                    refund_detail_obj = await erp_order_refund_detail.get_one(db, refund_order_no=refund_order_no)
                    if refund_detail_obj:
                        refund_detail_obj.refund_state = RefundDetailState.FAIL.value
                        refund_detail_obj.refund_times += 1  # 查询业务失败，增加重试次数
                    
                    settings.logger.warning(f"退款查询业务失败: 退款单号: {refund_order_no}, 响应: {resp}")
                    await db.commit()
                    return True
            elif resp.get('returnCode') == 'FAIL' and resp.get('errCode') == 'ORDERID_NOT_EXIST':
                # 订单不存在，明确失败，增加重试次数
                refund_detail_obj = await erp_order_refund_detail.get_one(db, refund_order_no=refund_order_no)
                if refund_detail_obj:
                    refund_detail_obj.refund_state = RefundDetailState.FAIL.value
                    refund_detail_obj.refund_times += 1  # 订单不存在，增加重试次数
                
                settings.logger.warning(f"退款订单不存在: 退款单号: {refund_order_no}")
                erp_finance_trade_refund_obj.trade_status = TradeStatus.FAIL.value
                erp_finance_trade_refund_obj.comments = f"退款单号不存在: {resp.get('errMsg', '未知错误')}"
                await db.commit()
                return True
            else:
                # 其他失败情况，也需要增加重试次数
                refund_detail_obj = await erp_order_refund_detail.get_one(db, refund_order_no=refund_order_no)
                if refund_detail_obj:
                    refund_detail_obj.refund_state = RefundDetailState.FAIL.value
                    refund_detail_obj.refund_times += 1  # 接口调用失败，增加重试次数
                
                settings.logger.warning(f"退款查询接口调用失败: 退款单号: {refund_order_no}, 响应: {resp}")
                await db.commit()
                return True
    except Exception as e:
        settings.logger.error(f"退款查询过程中发生异常: {refund_order_no}, 错误: {str(e)}")
        # 异常情况下也需要增加重试次数
        try:
            async for db in get_default_db():
                refund_detail_obj = await erp_order_refund_detail.get_one(db, refund_order_no=refund_order_no)
                if refund_detail_obj:
                    refund_detail_obj.refund_state = RefundDetailState.FAIL.value
                    refund_detail_obj.refund_times += 1  # 异常情况增加重试次数
                    await db.commit()
        except Exception as inner_e:
            settings.logger.error(f"更新退款次数时发生异常: {refund_order_no}, 错误: {str(inner_e)}")
        return False




async def after_refund_check(refund_order_no):
    """
    退款申请完成后进行退款查询
    {【招行要求】建议商户30秒查一次，查询10分钟仍为P状态则停止}
    
    Args:
        refund_order_no: 退款订单号（字符串格式）
    """
    try:
        settings.logger.info(f"开始轮询查询退款状态: {refund_order_no}")
        
        total_time = 60 * 10  # 设定总轮询时间为10分钟
        interval = 30  # 每次间隔30秒
        
        elapsed_time = 0
        query_count = 0
        
        while elapsed_time < total_time:
            query_count += 1
            settings.logger.info(f"第{query_count}次查询退款订单状态: {refund_order_no}, 已等待: {elapsed_time}秒")
            
            flag = False
            # try:
                # 确保在正确的异步上下文中执行数据库操作
            flag = await refund_async_trade(refund_order_no)
            
            # 如果退款状态已确定，则退出循环
            if flag:
                settings.logger.info(f"退款状态已确定，结束轮询: {refund_order_no}, 耗时: {elapsed_time}秒")
                return
            # except Exception as e:
            #     settings.logger.error(f"第{query_count}次查询退款状态异常: {refund_order_no}, 错误: {str(e)}")
                # 继续下一次轮询
            
            # 等待下一次查询
            await asyncio.sleep(interval)
            elapsed_time += interval
        
        settings.logger.warning(f"退款查询超时: {refund_order_no}, 在{total_time / 60}分钟内状态未确定")
        
    except Exception as e:
        settings.logger.error(f"轮询查询退款状态整体异常: {refund_order_no}, 错误: {str(e)}")



async def apply_refund_by_refund_order_no(refund_order_no: str):
    """
    根据退费单号，退费
    """
    try:
        # 查询退费详情
        refund_detail_obj = None
        async for db in get_default_db():
            refund_detail_obj = await erp_order_refund_detail.get_one(db, refund_order_no=refund_order_no)
            if not refund_detail_obj:
                settings.logger.info(f'>>> 退费单据{refund_order_no}没有退费详情')
                return
            # 申请金额为0，则直接退费成功
            if refund_detail_obj.apply_money is None or refund_detail_obj.apply_money <= 0:
                settings.logger.info(f'>>> 退费单据{refund_order_no}，退费申请金额为0')
                refund_detail_obj.refund_state = RefundDetailState.SUCCESS.value
                # refund_detail_obj.comments = '退费申请金额为0'
                refund_detail_obj.refund_money = 0
                if refund_detail_obj.refund_ewallet is not None and refund_detail_obj.refund_ewallet > 0:
                    # 查询学生订单并更新电子钱包金额
                    order_student_obj = await erp_order_student.get_by_id(db,refund_detail_obj.order_student_id)
                    student_obj = await erp_student.get_by_id(db, order_student_obj.stu_id)
                    student_obj.stu_wallet_amount = float(student_obj.stu_wallet_amount) + float(refund_detail_obj.refund_ewallet)
                    refund_msg = '退费申请金额为0, 返还电子钱包金额'
                else:
                    refund_msg = '申请0其他情况'
                # 更新退款单
                erp_trade_refund_obj = await erp_finance_trade_refund.get_one(db, refund_order_no=refund_order_no)
                if erp_trade_refund_obj:
                    erp_trade_refund_obj.trade_status = TradeStatus.SUCCESS.value
                    erp_trade_refund_obj.comments = refund_msg
                    erp_trade_refund_obj.money_refund = 0
                        
                        
                await db.commit()
                return
            
            # 申请金额不为0，则需要发起退款
            # 重要修改：提前获取所有需要的数据，避免在异步任务中访问ORM对象
            saved_refund_order_no = str(refund_detail_obj.refund_order_no)
            saved_order_no = str(refund_detail_obj.order_no)
            saved_apply_money = float(refund_detail_obj.apply_money)
            saved_refund_type = int(refund_detail_obj.refund_type)
            
            # 检查退款类型
            if saved_refund_type == RefundType.REFUND.value:
                print(f'退费详情id:{refund_detail_obj.id}，付款单{saved_order_no}，退费单{saved_refund_order_no}, 学生订单号：{refund_detail_obj.order_student_id}，订单号：{refund_detail_obj.order_id}，退费数量{refund_detail_obj.refund_num}，退费单价{refund_detail_obj.unit_price}，退费申请金额{saved_apply_money}')
                
                # 查询付款单
                payment_obj = await erp_finance_trade_payment.get_one(db, payment_order_no=saved_order_no)
                if not payment_obj:
                    msg = f'>>> 退费单据{saved_refund_order_no}，付款单{saved_order_no}不存在'
                    settings.logger.info(msg)
                    refund_detail_obj.refund_state = RefundDetailState.FAIL.value
                    # refund_detail_obj.comments = msg  # 注释掉：ErpOrderRefundDetail表没有comments字段
                    refund_detail_obj.refund_times += 1
                    await db.commit()
                    return False
                
                # 同样提前获取支付对象的所有需要的数据
                saved_trade_status = payment_obj.trade_status
                saved_money_pay = float(payment_obj.money_pay)
                saved_merchant_id = payment_obj.merchant_id
                saved_offer_id = payment_obj.offer_id
                saved_stu_id = payment_obj.stu_id
                saved_trade_type = payment_obj.trade_type
                
                if saved_trade_status != TradeStatus.SUCCESS.value:
                    msg = f'>>> 退费单据{saved_refund_order_no}，付款单{saved_order_no}，订单状态不允许退款'
                    settings.logger.info(msg)
                    refund_detail_obj.refund_state = RefundDetailState.FAIL.value
                    # refund_detail_obj.comments = msg  # 注释掉：ErpOrderRefundDetail表没有comments字段
                    refund_detail_obj.refund_times += 1
                    await db.commit()
                    return False
                
                # 验证退款金额是否小于支付金额
                if saved_apply_money > saved_money_pay:
                    msg = f'>>> 退费单据{saved_refund_order_no}，付款单{saved_order_no}，退款金额应小于支付金额'
                    settings.logger.info(msg)
                    refund_detail_obj.refund_state = RefundDetailState.FAIL.value
                    # refund_detail_obj.comments = msg  # 注释掉：ErpOrderRefundDetail表没有comments字段
                    refund_detail_obj.refund_times += 1
                    await db.commit()
                    return False
                
                mc = await mall_merchant_config.get_one(db, Id=saved_merchant_id)
                if not mc:
                    msg = f'>>> 退费单据{saved_refund_order_no}，付款单{saved_order_no}，商户配置不存在'
                    settings.logger.info(msg)
                    refund_detail_obj.refund_state = RefundDetailState.FAIL.value
                    # refund_detail_obj.comments = msg  # 注释掉：ErpOrderRefundDetail表没有comments字段
                    refund_detail_obj.refund_times += 1
                    await db.commit()
                    return False
                
                trade_refund_obj = await erp_finance_trade_refund.get_one(db, refund_order_no=saved_refund_order_no)
                if not trade_refund_obj:
                    trade_refund_obj = await erp_finance_trade_refund.create(db, commit=False, **{
                        "payment_order_no": saved_order_no,
                        "refund_order_no": saved_refund_order_no,
                        "offer_id": saved_offer_id,
                        "money": saved_apply_money,
                        "money_refund": 0,
                        "stu_id": saved_stu_id,
                        "trade_type": saved_trade_type,
                        "trade_status": TradeStatus.LOCAL_CREATED.value,
                        "merchant_id": saved_merchant_id,
                        "order_student_id": refund_detail_obj.order_student_id,
                    })
                
                client = await MerchantBank.create(mc)
                try:
                    resp = await client.cmb_refund_apply(saved_order_no, saved_refund_order_no, saved_money_pay, saved_apply_money)
                except Exception as e:
                    settings.logger.error(f"退款请求网络异常: {str(e)}")
                    trade_refund_obj.trade_status = TradeStatus.FAIL.value
                    trade_refund_obj.comments = f"网络请求异常: {str(e)}"
                    await db.commit()
                    return  # 发生异常时直接返回，不再继续执行
                
                # 不管是否成功，退款次数+1
                refund_detail_obj.refund_times += 1

                if resp.get('returnCode') == 'SUCCESS' and resp.get('respCode') == 'SUCCESS':
                    settings.logger.info(f'【退款申请成功】招行返回信息{resp}')
                    trade_refund_obj.trade_status = TradeStatus.PENDING.value
                    trade_refund_obj.comments = '退款申请中'
                    # 确保事务先提交
                    await db.commit()
                    asyncio.create_task(after_refund_check(saved_refund_order_no))
                else:
                    settings.logger.info(f'【发起退款】失败响应：{resp}')
                    trade_refund_obj.trade_status = TradeStatus.FAIL.value
                    trade_refund_obj.comments = resp.get('respMsg', '退款发起失败')
                    await db.commit()
            elif saved_refund_type == RefundType.CARRYOVER.value:
                # 结转
                print(f'结转{saved_refund_order_no}')
            else:
                settings.logger.info(f'>>> 退费单据{saved_refund_order_no}，退费类型错误{saved_refund_type}')
                return
    except Exception as e:
        settings.logger.error(f"退款流程整体异常: {refund_order_no}, 错误: {str(e)}")
        raise e

async def get_wait_refund_detail(db):
    """
    获取待退费单据,或退款失败需要再次退费的单据
    """
    selects = [
        ErpOrderRefundDetail.id,
        ErpOrderRefundDetail.refund_order_no,
        ErpOrderRefundDetail.refund_id,
        ErpOrderRefundDetail.order_no,
        ErpOrderRefundDetail.refund_type,
        ErpOrderRefundDetail.apply_money,
        ErpOrderRefundDetail.refund_num,
        ErpOrderRefundDetail.unit_price,
        ErpOrderRefundDetail.order_student_id,
        ErpOrderRefundDetail.order_id,
        ErpOrderRefundDetail.refund_times,
        ErpOrderRefund.audit_state
    ]
    conditions = [
        ErpOrderRefundDetail.refund_state.in_([RefundDetailState.PENDING.value, RefundDetailState.FAIL.value]),
        ErpOrderRefund.audit_state == AuditState.PASS.value,
        ErpOrderRefundDetail.refund_times < 6,   # 退款总次数小于6次可再次发起退款
        ErpOrderRefundDetail.refund_way == 1,   # 原路退款才自动发起
    ]
    stmt = (
        select(*selects)
        .select_from(ErpOrderRefundDetail)
        .outerjoin(ErpOrderRefund, ErpOrderRefund.id == ErpOrderRefundDetail.refund_id)
        .where(and_(*conditions))
        .order_by(ErpOrderRefundDetail.create_time)
    )
    # if page and page_size:
    #     stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()

async def task_of_sync_refund(time_interval: int = 30):
    """
    退费任务检测
    """
    # 查询审核通过的退费单据
    

    while True:
        # 查询审核通过的退费单据
        async for db in get_default_db():
            refund_detail_objs = await get_wait_refund_detail(db)
            if not refund_detail_objs:
                # settings.logger.info('>>> 没有可退费单据')
                await asyncio.sleep(time_interval)
                continue
            settings.logger.info(f'>>> 查询到{len(refund_detail_objs)}条审核通过的原路退费单据')
            for refund_detail_obj in refund_detail_objs:
                refund_obj = await erp_order_refund.get_by_id(db, refund_detail_obj.refund_id)
                if refund_obj.audit_state == AuditState.PASS.value:
                    asyncio.create_task(apply_refund_by_refund_order_no(refund_detail_obj.refund_order_no))
                await asyncio.sleep(0.5)
            await asyncio.sleep(time_interval)

    

