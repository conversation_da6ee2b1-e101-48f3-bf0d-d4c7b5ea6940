import asyncio
from datetime import datetime
from app_finance.modules import bill_account_change
import decimal
from models.m_class import ErpClass, ErpCourse, ErpCourseTextbook
from models.m_finance import ErpBankAccount, ErpFinanceTradePayment, ErpFinanceTradeRefund
from models.m_mall import MallMerchantConfig
from models.m_order import ErpOrder, ErpOrderOffer, ErpOrderRefund, ErpOrderRefundDetail, ErpOrderStudent
from models.m_student import ErpStudent
from models.m_workflow import ErpReceipt, ErpReceiptFinance, ErpReceiptDetail
from public_api.crud import get_workflow_cost_type_related
import settings
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db
from utils.enum.enum_approval import AuditState, CostTypeBind, RelatedObjType
from utils.enum.enum_order import BankAccountType, IEType, ItemType, OrderState, FinanceAuditState, OrderType, RefundDetailState, TradeStatus
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_


erp_finance_trade_payment = CRUD(ErpFinanceTradePayment)
erp_finance_trade_refund = CRUD(ErpFinanceTradeRefund)
mall_merchant_config = CRUD(MallMerchantConfig)
erp_bank_account = CRUD(ErpBankAccount)
erp_order = CRUD(ErpOrder)
erp_receipt = CRUD(ErpReceipt)
erp_receipt_finance = CRUD(ErpReceiptFinance)
erp_receipt_detail = CRUD(ErpReceiptDetail)
erp_order_refund_detail = CRUD(ErpOrderRefundDetail)
erp_order_refund = CRUD(ErpOrderRefund)

async def get_order_by_order_no(db:AsyncSession, order_no: str):
    """
    查询订单
    """
    
    from sqlalchemy import case
    selects = [
        ErpOrder.id,
        ErpOrder.buy_num,
        ErpOrder.order_state,
        ErpOrder.class_price,
        ErpOrderOffer.id,
        ErpOrderOffer.order_no,
        ErpOrder.order_class_type, 
        ErpOrder.total_income,
        ErpOrder.buy_num,
        case(
            [
                (ErpOrder.order_class_type == OrderType.COURSE.value, ErpClass.class_name),
                (ErpOrder.order_class_type == OrderType.MATERIAL.value, ErpCourseTextbook.name)
            ],
            else_=""
        ).label('item_name'),
        ErpOrderStudent.class_id.label('obj_id'),
        ErpStudent.stu_name
    ]
    conditions = [
        ErpOrderOffer.order_no == order_no,
        # ErpOrder.order_state == OrderState.PAID.value,   # 这里不检测是否已付款
        ErpOrder.disable==0
    ]

    stmt = (
            select(*selects)
            .select_from(ErpOrder)
            .outerjoin(ErpOrderOffer, ErpOrder.offer_id == ErpOrderOffer.id)
            .outerjoin(ErpOrderStudent, ErpOrder.order_student_id == ErpOrderStudent.id)
            .outerjoin(ErpStudent, ErpOrderOffer.stu_id == ErpStudent.id)
            .outerjoin(ErpClass, and_(ErpOrderStudent.class_id == ErpClass.id, ErpOrder.order_class_type == OrderType.COURSE.value))
            .outerjoin(ErpCourseTextbook, and_(ErpOrderStudent.class_id == ErpCourseTextbook.id, ErpOrder.order_class_type == OrderType.MATERIAL.value))
            
            
            .where(and_(*conditions))
        )
    result = await db.execute(stmt)
    return result.fetchall()



# 查询退款但未生成财务单的退款单
async def wait_finance_order_refunded(db:AsyncSession):
    """
    查询退款但未生成财务单的退款单
    """
    selects = [
       ErpFinanceTradeRefund.id,
       ErpFinanceTradeRefund.refund_order_no,
       ErpFinanceTradeRefund.merchant_id,
       ErpFinanceTradeRefund.stu_id,
       ErpFinanceTradeRefund.money_refund,
       ErpFinanceTradeRefund.cmb_pay_time,
       ErpFinanceTradeRefund.order_student_id,
       ErpOrderRefundDetail.refund_num,
       ErpOrderRefundDetail.total_money,
       ErpOrderRefundDetail.order_id,
       ErpOrderRefund.id.label('refund_id'),
       ErpFinanceTradeRefund.trade_status,
       ErpOrderRefund.audit_state
    ]
    conditions = [
       ErpFinanceTradeRefund.trade_status == TradeStatus.SUCCESS.value,
       ErpOrderRefund.audit_state == AuditState.PASS.value,
       or_(ErpFinanceTradeRefund.receipt_id == 0, 
           ErpFinanceTradeRefund.receipt_id == None),
       ErpOrderRefundDetail.refund_times <= 6
    ]
    stmt = (
            select(*selects)
            .select_from(ErpFinanceTradeRefund)
            .outerjoin(ErpOrderRefundDetail, ErpFinanceTradeRefund.refund_order_no == ErpOrderRefundDetail.refund_order_no)
            .outerjoin(ErpOrderRefund, ErpOrderRefundDetail.refund_id == ErpOrderRefund.id)
            .where(and_(*conditions))
        )
    result = await db.execute(stmt)
    return result.fetchall()


# 从退款表更新财务单
async def update_finance_by_trade_refund():
    """
    从退款表更新财务单据 erp_finance_trade_refund -> (erp_receipt + erp_receipt_finance)
    检查到招行退款状态 -> 需要更新表单
    - erp_receipt 流程表： 更新状态 
    - erp_receipt_finance 财务表： 更新状态
    """
    # print('检查退款单')
    async for db in get_default_db():
        erp_finance_trade_refund_objs = await wait_finance_order_refunded(db)
        print(f'退款单数量: {len(erp_finance_trade_refund_objs)}')
        # 查询退款商户号
        mall_merchant_configs = await mall_merchant_config.get_many(db)
        cmb_merchant_id_map = {int(mc.Id): mc for mc in mall_merchant_configs}

        # 获取虚拟账户号
        virtual_account_objs = await erp_bank_account.get_many(db, raw=[
            ErpBankAccount.account_type == BankAccountType.PUBLIC.value,
        ])
        virtual_account_map = {va.cmb_merchant_id: va for va in virtual_account_objs}
        if not erp_finance_trade_refund_objs:
            return

    # 轮询创建财务单
    async for db in get_default_db():
        for erp_finance_trade_refund_obj in erp_finance_trade_refund_objs:
            current_mc = cmb_merchant_id_map.get(int(erp_finance_trade_refund_obj.merchant_id))
            if not current_mc:
                continue
            # 重新从数据库获取对象，确保在当前会话中
            trade_refund_obj = await erp_finance_trade_refund.get_by_id(db, erp_finance_trade_refund_obj.id)

            refund_detail_obj = await erp_order_refund_detail.get_one(db, refund_order_no=erp_finance_trade_refund_obj.refund_order_no)
            if not refund_detail_obj:
                settings.logger.error(f'退款单{erp_finance_trade_refund_obj.refund_order_no}没有退款明细')
                continue
            # 更新退款明细表实际到账金额
            refund_detail_obj.act_money = float(refund_detail_obj.act_money or 0) + float(erp_finance_trade_refund_obj.money_refund or 0)
            # 先查询退款单据
            receipt_obj = await erp_receipt.get_one(db, refund_id=refund_detail_obj.refund_id)
            if not receipt_obj:
                settings.logger.error(f'退款单{erp_finance_trade_refund_obj.refund_order_no}没有退款单据')
                refund_detail_obj.refund_state = RefundDetailState.FAIL.value
                refund_detail_obj.refund_times += 1
                await db.commit()
                continue
            receipt_finance_obj = await erp_receipt_finance.get_one(db, receipt_id=receipt_obj.id)
            receipt_finance_obj.trade_money = float(receipt_finance_obj.trade_money) + float(erp_finance_trade_refund_obj.money_refund)
            trade_refund_obj.receipt_id = receipt_obj.id
            trade_refund_obj.update_time = datetime.now()  # 更新时间戳
            db.add(trade_refund_obj)  # 标记对象为已修改
            await db.commit()


# 从付款表更新财务单
async def update_finance_by_trade_payment():
    """
    从付款表更新财务单 erp_finance_trade_payment -> (erp_receipt + erp_receipt_finance)
    """
    # print('检查财务单')
    erp_finance_trade_payment_objs = None
    async for db in get_default_db():
        erp_finance_trade_payment_objs = await erp_finance_trade_payment.get_many(db, raw=[
            ErpFinanceTradePayment.trade_status.in_([TradeStatus.SUCCESS.value]),
            and_(ErpFinanceTradePayment.merchant_id !=None,
                 ErpFinanceTradePayment.merchant_id > 0),
            or_(ErpFinanceTradePayment.receipt_id == 0, 
                ErpFinanceTradePayment.receipt_id == None),    # 未创建财务单
            ErpFinanceTradePayment.create_time > '2025-01-01'
        ])
        print(f'付款单数量: {len(erp_finance_trade_payment_objs)}')
        
        # 查询收款商户号
        mall_merchant_configs = await mall_merchant_config.get_many(db)
        cmb_merchant_id_map = {int(mc.Id): mc for mc in mall_merchant_configs}
        # 获取虚拟账户号
        virtual_account_objs = await erp_bank_account.get_many(db, raw=[
            ErpBankAccount.account_type == BankAccountType.PUBLIC.value,
        ])
        virtual_account_map = {va.cmb_merchant_id: va for va in virtual_account_objs}
    if not erp_finance_trade_payment_objs:
        return

    # 轮询创建财务单
    async for db in get_default_db():
        for erp_finance_trade_payment_obj in erp_finance_trade_payment_objs:
            # print(f'付款单{erp_finance_trade_payment_obj.payment_order_no}')
            # 重新从数据库获取对象，确保在当前会话中
            trade_payment_obj = await erp_finance_trade_payment.get_by_id(db, erp_finance_trade_payment_obj.id)
            # print(f'付款单{erp_finance_trade_payment_obj.merchant_id}')
            current_mc = cmb_merchant_id_map.get(int(erp_finance_trade_payment_obj.merchant_id))
            if not current_mc:
                print(f'商户号{erp_finance_trade_payment_obj.merchant_id}不存在')
                continue
            # 商户号  
            merchant_no = current_mc.CmbMerId
            # 商户名称
            bank_account = virtual_account_map.get(merchant_no)
            cmb_account_type = '微信' if current_mc.Type == 1 else '支付宝'

            # 通过order_no查询所有订单
            erp_order_objs = await get_order_by_order_no(db, order_no=trade_payment_obj.payment_order_no)
            if not erp_order_objs:
                print(f'订单{trade_payment_obj.payment_order_no}不存在')
                continue
            # 先查询是否已存在付款单的单据
            receipt_obj = await erp_receipt.get_one(db, payment_id=trade_payment_obj.id)
            if not receipt_obj:
                # 创建单据基本信息
                receipt_data = {
                    "apply_reason": f"{erp_order_objs[0].stu_name}{trade_payment_obj.create_time.strftime('%Y-%m-%d')}课程收入-{trade_payment_obj.payment_order_no}",   # 学生+日期+课程收入
                    "related_obj_id": trade_payment_obj.stu_id,
                    "related_obj_type": RelatedObjType.INTERNAL_STUDENT.value,
                    "related_obj_name": erp_order_objs[0].stu_name,
                    "workflow_id": 12,    # 课程收入流程
                    "apply_remark": "课程收入-系统自动审核通过",
                    "audit_state": AuditState.PASS.value,  # 自动通过
                    "is_auto": 1,  # 自动通过
                    "create_by": 4001,
                    "update_by": 4001,
                    "payment_id": trade_payment_obj.id,
                }
                receipt_obj = await erp_receipt.create(db, commit=False, **receipt_data)
                # print(f'创建单据{receipt_obj.id}, 付款单{trade_payment_obj.id}')
            
            # 检查是否已存在财务单
            receipt_finance_obj = await erp_receipt_finance.get_one(db, receipt_id=receipt_obj.id)
            if not receipt_finance_obj:
                # 创建财务信息
                finance_data = {
                    "receipt_id": receipt_obj.id,
                    "order_no": trade_payment_obj.payment_order_no,
                    "bank_account_id": bank_account.id if bank_account else 0,
                    "bank_account_name": f'{bank_account.account_alias} - {cmb_account_type}' if bank_account else "",
                    "apply_money": trade_payment_obj.money_income,
                    "trade_money": trade_payment_obj.money_income,
                    "ie_type": IEType.INCOME.value,  # 收入
                    "desc": "课程收入-系统自动审核通过",
                    "pre_pay_time": trade_payment_obj.create_time.strftime('%Y-%m-%d') if trade_payment_obj.create_time else datetime.now().strftime('%Y-%m-%d'),
                    "cashier_id": 0,  # 无出纳
                    "create_by": 4001,
                    "update_by": 4001,
                    }
                receipt_finance_obj = await erp_receipt_finance.create(db, commit=False, **finance_data)
            # 更新单据关联财务ID# 更新单据关联财务ID
            receipt_obj.relate_finance_id = receipt_finance_obj.id
            # print(f'创建财务单{receipt_obj.id}， 付款单{trade_payment_obj.payment_order_no}')
            # 创建明细信息，对每个订单创建一条明细记录
            for index, erp_order in enumerate(erp_order_objs):
                # 检查是否已存在财务明细
                receipt_detail_obj = await erp_receipt_detail.get_one(db, order_id=erp_order.id)
                # 确定费用类型是课程收入还是讲义收入
                if erp_order.order_class_type == OrderType.COURSE.value:
                    cost_type_obj = await get_workflow_cost_type_related(db, cost_type_bind=CostTypeBind.CourseCashIncome.value)
                else:
                    cost_type_obj = await get_workflow_cost_type_related(db, cost_type_bind=CostTypeBind.TextbookIncome.value)

                if not receipt_detail_obj:
                    # 创建财务明细
                    # 避免除零错误，当buy_num为0时，单价设为0
                    buy_num = decimal.Decimal(erp_order.buy_num or 0)
                    total_income = decimal.Decimal(erp_order.total_income or 0)
                    item_unit_price = round(total_income / buy_num, 2) if buy_num > 0 else decimal.Decimal('0.00')
                    
                    detail_data = {
                        "receipt_id": receipt_obj.id,
                        "item_type": ItemType.FINANCE.value,  # 财务明细
                        "item_name": erp_order.item_name,
                        "item_num": erp_order.buy_num,
                        "item_unit_price": item_unit_price,
                        "item_total_price": round(total_income, 2),
                        "cost_type_id": cost_type_obj.default_cost_type_id,
                        "cost_type_name": cost_type_obj.finance_cost_type_name,
                        "amount": round(float(erp_order.total_income), 2),
                        "remark": "自动创建课程收入单据",
                        "sort_no": index + 1,
                        "create_by": 4001,
                        "update_by": 4001,
                        "order_id": erp_order.id,
                    }
                    await erp_receipt_detail.create(db, commit=False, **detail_data)
                    # 收入单还需要增加账户余额
                    try:
                        if bank_account and bank_account.id > 0:  # 确保有有效的银行账户
                            # 增加银行账户余额
                            bank_account.balance = decimal.Decimal(bank_account.balance or 0) + decimal.Decimal(trade_payment_obj.money_income or 0)
                            bank_account.update_by = 20000
                            bank_account.update_time = datetime.now()
                            # 更新银行账户日志
                            try:
                                await bill_account_change(
                                    db, 
                                    bank_account.id, 
                                    1,  # 1表示增加余额
                                    erp_order.total_income, 
                                    "收入入账", 
                                    4001, 
                                    commit=False,
                                    receipt_id=receipt_obj.id
                                )
                            except Exception as e:
                                settings.logger.error(f'收入单记录账户变动失败:单据{receipt_obj.id}，账户id:{bank_account.id}，错误:{str(e)}')
                    except Exception as e:
                        settings.logger.error(f'收入单增加账户余额失败:单据{receipt_obj.id}，错误:{str(e)}')
            # print(f'更新付款单关联的财务单ID{receipt_obj.id}')
            # 更新付款单关联的财务单ID
            trade_payment_obj.receipt_id = receipt_obj.id
            trade_payment_obj.update_time = datetime.now()  # 更新时间戳
            print(f'更新付款单关联的财务单ID:{trade_payment_obj.id} -> {trade_payment_obj.payment_order_no}')
            await db.commit()




async def run_finance_task(time_interval: int = 60):
    """
    财务任务检测, 更新付款单和退款单的财务相关单据
    """
    while True:
        try:
            # 更新审核通过的付款单 -> 财务单收入
            await update_finance_by_trade_payment()
            # 更新审核通过的退款单 -> 财务单支出
            await update_finance_by_trade_refund()
        except Exception as e:
            settings.logger.error(f'财务任务检测异常: {e}')
        finally:
            await asyncio.sleep(time_interval)

