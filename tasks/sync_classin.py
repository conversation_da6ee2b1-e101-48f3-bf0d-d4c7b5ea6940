

import asyncio
import copy
from models.m_eeo import ErpEeoPhoneNumberChange
from models.m_student import ErpStudent
from settings import CF
import settings
from utils.db.db_handler import get_default_db


async def sync_phone_modify():
    """
    同步手机号修改
    """
    erp_eeo_phone_number_change = CF.get_crud(ErpEeoPhoneNumberChange)
    erp_student = CF.get_crud(ErpStudent)
    async for db in get_default_db():
        non_processed_objs = await erp_eeo_phone_number_change.get_many(db, raw=[
            ErpEeoPhoneNumberChange.processed == 0,
        ])
        for non_processed_obj in non_processed_objs:
            new_phone = non_processed_obj.telephone
            uid = non_processed_obj.uid
            exist_student = await erp_student.get_one(db, classin_uid=uid)
            if not exist_student:
                non_processed_obj.processed = 1
                non_processed_obj.msg = '学生不存在'
                continue
            if exist_student.stu_username == new_phone:
                non_processed_obj.processed = 1
                non_processed_obj.msg = '手机号未修改'
                settings.logger.info(f'>>> 手机号{old_phone}未修改，跳过')
                continue
            old_phone = copy.deepcopy(exist_student.stu_username)
            exist_student.stu_username = new_phone
            non_processed_obj.processed = 1
            msg = f'手机号 {old_phone} -> {new_phone} 修改成功'
            non_processed_obj.msg = msg
            settings.logger.info(msg)
        await db.commit()

async def task_of_sync_classin(interval: int = 60):
    """
    同步classin数据
    """
    while True:
        await sync_phone_modify()
        await asyncio.sleep(interval)