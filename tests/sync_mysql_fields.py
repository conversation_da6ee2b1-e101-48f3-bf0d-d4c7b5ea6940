TABLE_MAPPINGS = {
    # "rb_class_log": {
    #     "target_table": "erp_class_log",
    #     "column_mapping": {
    #         "CLogId": "id",
    #         "LogType": "log_type",
    #         "LogContent": "log_content",
    #         "ClassId": "class_id",
    #         "School_Id": "campus_id",
    #         "CreateBy": "create_by",
    #         "CreateTime": "create_time",
    #     },
    #     "extra_columns": {
    #         "update_time": "NOW()",
    #         "disable": "0",
    #     },
    #     "primary_key": "CLogId",
    # },
    # "rb_student_tempinvitation": {
    #     "target_table": "erp_class_transfer_record",
    #     "column_mapping": {
    #         "Id": "id",
    #         "ClassId": "class_id",
    #         # "OrderId": "order_id",
    #         "OrderGuestId": "order_guest_id",
    #         "ClassPlanId": "class_plan_id",
    #         "ClassTimeId": "class_time_id",
    #         "School_Id": "campus_id",
    #         "CreateBy": "create_by",
    #         "CreateTime": "create_time",
    #         "UpdateBy": "update_by",
    #         # "UpdateTime": "update_time",
    #         "Status": "disable",  # 这个字段在新列表中没有对应项
    #         "InvitationType": "transfer_type",
    #         "StuId": "stu_id",
    #         "SourcePlanId": "source_plan_id",
    #         "SourceClassId": "source_class_id"
    #     },
    #     "extra_columns": {
    #         "update_time": "NOW()",
    #     },
    #     "primary_key": "Id",
    # },
    # "rb_class": {
    #     "target_table": "erp_class",
    #     "column_mapping": {
    #         "ClassId": "id",
    #         "ClassName": "class_name",
    #         "CouseId": "course_id",  # 注意：原字段可能有拼写错误，应为 "CourseId"
    #         "Teacher_Id": "teacher_id",
    #         "CreateBy": "create_by",
    #         "CreateTime": "create_time",
    #         "UpdateBy": "update_by",
    #         "Status": "disable",  # 假设 "Status" 对应 "disable"
    #         "ClassPersion": "class_capacity",  # 注意：原字段可能有拼写错误，应为 "ClassPerson"
    #         "OpenTime": "start_date",
    #         "EndOrderTime": "miniprogram_end_enrollment_time",
    #         "ClassRoomId": "classroom_id",
    #         "CourseTimes": "planning_class_times",
    #         "WeChatId": "qwechat_id",
    #         "ClassInCourseID": "classin_id",
    #         "Mall_Is_Show": "is_shelf_miniprogram",
    #         "HourFeeRate": "hourly_tuition_ratio",
    #         "EnrollPersion": "pre_enrollment",  # 注意：原字段可能有拼写错误，应为 "EnrollPerson"
    #         "StartOrderTime": "miniprogram_start_enrollment_time",
    #     },
    #     "extra_columns": {
    #         "update_time": "NOW()",
    #     },
    #     "primary_key": "ClassId",
    # },
    # "rb_course": {
    #     "target_table": "erp_course",
    #     "column_mapping": {
    #         "CourseId": "id",
    #         "CoverImg": "course_cover",
    #         "CourseName": "course_name",
    #         "CourseIntro": "course_introduction_page",
    #         "CateId": "category_id",
    #         "CreateBy": "create_by",
    #         "CreateTime": "create_time",
    #         "UpdateBy": "update_by",
    #         "Status": "disable",  # 假设 "Status" 对应 "disable"
    #         "OriginalPrice": "original_price",
    #         "SellPrice": "sale_price",
    #         "ClassHours": "number_of_lessons",
    #         "CourseRate": "course_coefficient",
    #         "CourseSubject": "subject_id",
    #         "ShiftTerm": "term_id",
    #         "ShiftGrade": "grade_id",
    #         "ShiftClassType": "type_id",
    #         "IsByTerm": "is_term_plan",
    #         "CanRefund": "allow_refund",
    #         "IsRepeatOrder": "allow_repeated_purchase",
    #     },
    #     "extra_columns": {
    #         "update_time": "NOW()",
    #     },
    #     "primary_key": "CourseId",
    # },
    "rb_student": {
        "target_table": "erp_student",
        "column_mapping": {
            # "StuId": "id",
            # "StuName": "stu_name",
            # "StuBirth": "stu_birth",
            # "StuSex": "stu_gender",
            # "StuIcon": "stu_avatar",
            # "AreaName": "stu_area",
            # "StuAddress": "stu_address",
            # "StuIDCard": "stu_idcard",
            # "BaseCondition": "stu_school_name",
            # "E_WalletMoney": "stu_wallet_amount",
            # "Serial": "stu_serial",
            # "ClassInUID": "classin_uid",
            # "UpdateTime": "update_time",
            # "CreateTime": "create_time",
            # "IsDisable": "disable",
            # "StuRealMobile": "stu_username",
            # "SchoolClass": "stu_grade",
            "SchoolSource": "how_known"
        },
        "extra_columns": {
            "classin_sync": "0",
            "mall_user_id": "0",
            "wechat_open_id": "0",
            "campus_id": "1",
        },
        "primary_key": "StuId",
        "value_transformations": {
            "stu_gender": "StuSex + 1"  # 定义值转换规则
        }
    },
    # "rb_order": {
    #     "target_table": "erp_order",
    #     "column_mapping": {
    #         "OrderId": "id",
    #         "OfferId": "offer_id",
    #         "TradeWay": "trade_way",
    #         "Class_Price": "class_price",
    #         "Unit_Price": "lesson_price",
    #         "PreferPrice": "total_receivable",
    #         "Income": "total_income",
    #         "Refund": "refund",
    #         "DiscountMoney": "discount",
    #         "PlatformTax": "platform_tax",
    #         "OrderState": "order_state",
    #         "School_Id": "campus_id",
    #         "RenewOrderId": "renew_order_id",
    #         "OrderType": "order_class_type",
    #         "StartClassHours": "insert_plan_index",
    #         "JoinType": "join_type",
    #         "SourceOrderId": "parent_order_id",
    #         "CreateBy": "create_by",
    #         "CreateTime": "create_time",
    #         "UpdateBy": "update_by",
    #         "UpdateTime": "update_time",
    #         "OrderUnit": "unit",
    #         "Times": "buy_num"
    #     },
    #     "extra_columns": {
    #         "disable": "0",
    #         "is_first_buy": "0"
    #     },
    #     "primary_key": "OrderId",
    #     "value_transformations": {
    #         "order_state": "CASE WHEN OrderState = 1 THEN 1 WHEN OrderState = 2 THEN 2 ELSE 3 END",
    #         "order_class_type": "CASE WHEN OrderType = 1 THEN 1 ELSE 3 END"
    #     }
    # },
    #
    # "rb_order_main": {
    #     "target_table": "erp_order_offer",
    #     "column_mapping": {
    #         "Id": "id",
    #         "Name": "name",
    #         "EffectiveStart": "effective_start",
    #         "EffectiveEnd": "effective_end",
    #         "TotalOriginalPrice": "total_original_price",
    #         "TotalPrice": "total_sale_price",
    #         "TotalDiscountPrice": "total_discount_price",
    #         "CreateBy": "create_by",
    #         "CreateTime": "create_time",
    #         "UpdateBy": "update_by",
    #         "UpdateTime": "update_time",
    #         "School_Id": "campus_id",
    #         "OrderId": "order_no",
    #         "OfferType": "offer_type",
    #         "StuId": "stu_id",
    #         "OfferState": "offer_state",
    #         "IntegralPrice": "integral_money",
    #         "ElectronicWallet": "ewallet_money",
    #         "IsPush": "is_push",
    #         "CashVoucher": "cash_voucher",
    #         "CashPayRemark": "cash_pay_remark",
    #         "InnerRemark": "inner_remark",
    #         "OutRemark": "out_remark",
    #         "OrderFrom": "order_from",
    #     },
    #     "extra_columns": {
    #         "disable": "0"
    #     },
    #     "primary_key": "Id"
    # },
    # # 这里erp_order_student搭配数据库更新stu_id语句
    # "rb_order_guest": {
    #     "target_table": "erp_order_student",
    #     "column_mapping": {
    #         "Id": "id",
    #         "OrderId": "order_id",
    #         "ClassId": "class_id",
    #         "GuestState": "student_state",
    #         "CreateBy": "create_by",
    #         "CreateTime": "create_time",
    #         "UpdateBy": "update_by",
    #         "UpdateTime": "update_time",
    #         "TotalHours": "total_hours",
    #         "CompleteHours": "complete_hours",
    #         "Status": "disable",
    #         "RenewState": "is_renew",
    #         "CanSelfChangeClass": "self_change_class"
    #     },
    #     "extra_columns": {
    #         "contract_id": "0"
    #     },
    #     "primary_key": "Id"
    # }
}
