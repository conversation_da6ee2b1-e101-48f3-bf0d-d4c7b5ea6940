# 表映射配置
import asyncio
from datetime import datetime
from sqlalchemy import text
import settings
from tests.sync_mysql_fields import TABLE_MAPPINGS
from tasks.modules.task_crud import async_student_plus
from utils.db.db_handler import get_default_db


async def get_last_sync_time(db, table_name):
    """获取最后同步时间"""
    query = f"SELECT MAX(create_time) FROM {table_name}"
    result = await db.execute(text(query))
    last_sync_time = result.scalar()
    return last_sync_time or datetime.min


async def sync_table(db, source_table, mapping):
    """同步单个表的数据"""
    last_sync_time = await get_last_sync_time(db, mapping["target_table"])
    # print(f'last_sync_time: {last_sync_time}')

    # 构建查询，处理值转换
    columns = []
    for source_col, target_col in mapping["column_mapping"].items():
        if target_col in mapping.get("value_transformations", {}):
            # 如果列有转换规则，使用转换表达式
            transform_expr = mapping["value_transformations"][target_col]
            columns.append(f"{transform_expr} as {source_col}")
        else:
            # 否则直接使用原列名
            columns.append(source_col)

    columns_str = ", ".join(columns)

    query = f"""
    SELECT {columns_str}
    FROM `uat_reborn_think`.{source_table}
    WHERE CreateTime > :last_sync_time
    ORDER BY CreateTime ASC
    LIMIT 1000
    """

    result = await db.execute(text(query), {"last_sync_time": last_sync_time})
    rows = result.fetchall()

    if not rows:
        return 0

    # 构建插入语句
    target_columns = ", ".join(mapping["column_mapping"].values())
    extra_columns = ", ".join(mapping["extra_columns"].keys())
    placeholders = ", ".join([f":{col}" for col in mapping["column_mapping"].values()])
    extra_values = ", ".join(mapping["extra_columns"].values())

    insert_query = f"""
    INSERT INTO {mapping['target_table']} ({target_columns}, {extra_columns})
    VALUES ({placeholders}, {extra_values})
    ON DUPLICATE KEY UPDATE
    {', '.join([f"{col} = VALUES({col})" for col in mapping["column_mapping"].values() if col != mapping["primary_key"]])}
    """

    # 执行插入
    count = 0
    for row in rows:
        data = {target_col: getattr(row, source_col) for source_col, target_col in mapping["column_mapping"].items()}
        await db.execute(text(insert_query), data)
        count += 1

    return count


async def sync_all_tables(minute):
    """同步所有配置的表"""
    # print('【任务】数据新老表同步正在运行...')
    show_log = True
    while True:
        async for db in get_default_db():
            total_count = 0
            for source_table, mapping in TABLE_MAPPINGS.items():
                try:
                    count = await sync_table(db, source_table, mapping)
                    total_count += count
                    if show_log:
                        print(f'sync {source_table} to {mapping["target_table"]} {count} rows')
                except Exception as e:
                    settings.logger.warning(f'同步 {source_table} 失败：{e}')
                    await db.rollback()
                    continue

            try:
                await db.commit()
                show_log = False
                print(f'本次共同步 {total_count} 条班级日志')
            except Exception as e:
                settings.logger.warning(f'提交事务失败：{e}')
                await db.rollback()

        await asyncio.sleep(minute * 60)


async def sync_plus_pool(minute):
    """同步公海池"""
    print('【任务】公海池同步正在运行...')
    while True:
        async for db in get_default_db():
            await async_student_plus(db)

        await asyncio.sleep(minute * 60)

# FastAPI 应用启动时调用此函数
# @app.on_event("startup")
# async def start_sync_task():
#     asyncio.create_task(sync_all_tables(10))  # 每10分钟同步一次
