# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, Date, DateTime, Float, JSON, String, Table, Text, text
from sqlalchemy.dialects.mysql import INTEGER, LONGTEXT, VARCHAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpApprovalCc(Base):
    __tablename__ = 'erp_approval_cc'

    id = Column(INTEGER(11), primary_key=True)
    process_id = Column(INTEGER(11))
    cc_type = Column(INTEGER(11), comment='抄送类型：1 直接上级、2 连续多级上级、3 指定成员、4 指定岗位')
    cc_value = Column(String(255), comment='抄送人账号，存储连续多级上级的级数或指定成员ID、岗位的ID')
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpApprovalNode(Base):
    __tablename__ = 'erp_approval_node'

    id = Column(INTEGER(11), primary_key=True)
    process_id = Column(INTEGER(11), comment='关联的流程Id')
    node_desc = Column(String(255), comment='节点名称或描述')
    order = Column(INTEGER(4), comment='节点顺序')
    action = Column(INTEGER(4), comment='节点动作: 1  常规 2 财务 3 出纳')
    approval_type = Column(INTEGER(4), comment='审批人类型：1连续多级上级、2 指定成员、3 指定岗位')
    approval_value = Column(String(255), comment='审批人账号，存储连续多级上级的级数或指定成员ID、岗位的ID')
    approval_mode = Column(String(255), comment='审批模式：1 或签 2 会签')
    involve_accounting = Column(INTEGER(4), comment='是否涉及出入账')
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpApprovalProcess(Base):
    __tablename__ = 'erp_approval_process'

    id = Column(INTEGER(11), primary_key=True)
    process_name = Column(String(255))
    process_type = Column(INTEGER(4), comment='1 收入 2 支出 3 收支相抵 4 不入账')
    process_desc = Column(String(1000))
    cost_type_ids = Column(JSON)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpApprovalProcessCampus(Base):
    __tablename__ = 'erp_approval_process_campus'

    id = Column(INTEGER(11), primary_key=True)
    campus_id = Column(INTEGER(4))
    process_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpApprovalRecord(Base):
    __tablename__ = 'erp_approval_record'

    id = Column(INTEGER(11), primary_key=True)
    process_id = Column(INTEGER(11))
    node_id = Column(INTEGER(11))
    initiator = Column(INTEGER(11), comment='发起人')
    process_status = Column(INTEGER(4), comment='1 待审批 2 已通过 3 已拒绝')
    comments = Column(Text)
    receipt_id = Column(INTEGER(11))
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    auditor = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    together_auditors = Column(JSON, comment='会签人员')


class ErpApprovalAccountRole(Base):
    __tablename__ = 'erp_approval_account_role'

    id = Column(INTEGER(11), primary_key=True)
    role_id = Column(INTEGER(11))
    account_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpApprovalRole(Base):
    __tablename__ = 'erp_approval_role'

    id = Column(INTEGER(11), primary_key=True)
    role_name = Column(String(255))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpApprovalCcRecord(Base):
    __tablename__ = 'erp_approval_cc_record'

    id = Column(INTEGER(11), primary_key=True)
    receipt_id = Column(INTEGER(11))
    to = Column(INTEGER(44))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


# class ErpReceipt(Base):
#     __tablename__ = 'erp_receipt'

#     id = Column(INTEGER(11), primary_key=True)
#     order_id = Column(INTEGER(11), comment='订单号', default=0)
#     campus_id = Column(INTEGER(11), comment='校区', default=0)
#     related_personnel = Column(String(255), comment='相关人员')
#     # receipt_type = Column(INTEGER(4))
#     cost_type_id = Column(INTEGER(4))
#     money = Column(DECIMAL(10, 2))
#     dept_id = Column(INTEGER(11), comment='部门')
#     receipt_status = Column(INTEGER(4), comment='单据状态0 暂存 1审核中 2通过 3驳回 4作废')
#     pay_money = Column(DECIMAL(10, 2), comment='实际支付金额')
#     fee_rate = Column(DECIMAL(10, 2), comment='其他费率')
#     desc = Column(String(1000), comment='详细信息')
#     parent_id = Column(INTEGER(11), comment='关联财务单据')
#     invoice_type = Column(INTEGER(4), comment='发票类型 1 专票 2 普票')
#     invoice_money = Column(DECIMAL(10, 2), comment='发票金额')
#     invoice_remark = Column(String(255), comment='发票备注')
#     wallet_money = Column(DECIMAL(10, 2), comment='电子钱包支付金额')
#     update_by = Column(INTEGER(11))
#     create_by = Column(INTEGER(11))
#     attachment = Column(JSON, comment='附件')
#     payment_id = Column(INTEGER(4), comment='付款对象')
#     payment_account = Column(String(125), comment='付款账户')
#     payment_account_type = Column(INTEGER(4), comment=' 1 员工 2 供应商 3 兼职 4 学生')
#     payment_bank = Column(String(255), comment='付款账户银行')
#     payment_account_number = Column(String(255), comment='付款账户号')
#     estimated_payment_date = Column(Date, comment='预计付款日期')
#     cashier = Column(INTEGER(11), comment='出纳')
#     financial_auditor = Column(INTEGER(11), comment='财务审核')
#     process_id = Column(INTEGER(11), comment='流程号')

#     application_reason = Column(String(255), comment='申请事由')

#     create_time = Column(DateTime, default=datetime.now)
#     update_time = Column(DateTime, default=datetime.now)
#     disable = Column(INTEGER(4), default=0)


class ErpReceiptDetail(Base):
    __tablename__ = 'erp_receipt_detail'

    id = Column(INTEGER(11), primary_key=True)
    receipt_id = Column(INTEGER(11), comment='关联单据')
    fee_desc = Column(String(255), comment='费用说明')
    amount = Column(INTEGER(4), comment='数量')
    unit_price = Column(DECIMAL(10, 4), comment='单价')
    currency = Column(String(45), comment='币种')
    remark = Column(String(255), comment='备注')
    # application_reason = Column(String(255), comment='申请事由')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    trade_type = Column(INTEGER(4), comment='1 调拨收入 2 调拨付款')
    trade_way = Column(INTEGER(4), comment='1 银行 2 平台 3 现金 4 资金池')
    trade_money = Column(DECIMAL(10, 2), comment='交易金额')
    account_type = Column(INTEGER(4), comment='1 公账 2 私账 3 现金账户 4 微信支付宝 5 虚拟账户')
    bill_account_id = Column(INTEGER(11), comment='账户号')
    attachment = Column(JSON, comment='附件')
    serial_no = Column(String(255), comment='流水号')
    source = Column(String(255), comment='流水号')
    type = Column(INTEGER(4))
    cost_type_id = Column(INTEGER(11))
    money = Column(DECIMAL(10, 2), comment='金额')

