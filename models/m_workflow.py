# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, Date, DateTime, Float, JSON, String, Table, Text, text
from sqlalchemy.dialects.mysql import INTEGER, LONGTEXT, VARCHAR, BIGINT, TINYINT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata



class ErpReceipt(Base):
    __tablename__ = 'erp_receipt'
    __table_args__ = {'comment': '单据表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    apply_reason = Column(String(255), comment='申请原因')
    related_obj_id = Column(BIGINT(20), comment='关联对象ID')
    related_obj_type = Column(INTEGER(4), comment='关联对象类型')
    apply_remark = Column(String(500), comment='申请备注')
    audit_state = Column(TINYINT(1), server_default=text("'0'"), comment='审核状态：0暂存 1待审 2通过 3驳回 4取消')
    workflow_instance_id = Column(BIGINT(20), comment='绑定流程实例', default=0)
    workflow_id = Column(BIGINT(20), comment='绑定流程定义', default=0)
    dept_id = Column(BIGINT(20), comment='部门ID', default=0)
    dept_name = Column(String(100), comment='部门名称')
    is_auto = Column(TINYINT(1), server_default=text("'0'"), comment='是否自动审核通过', default=0)
    attachment = Column(JSON, comment='附件')
    relate_finance_id = Column(BIGINT(20), comment='关联财务单号')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11), comment='创建人')
    update_by = Column(INTEGER(11), comment='更新人')
    disable = Column(TINYINT(1), default=0)
    refund_id = Column(BIGINT(20), default=0, comment='不为空则为退费单ID')
    payment_id = Column(String(125),default='', comment='不为空则为付款单id')
    related_obj_name = Column(String(125),default='', comment='不为空则为关联对象名称')
    class_id = Column(BIGINT(20), default=0, comment='不为空则为开班ID')
    # receipt_type = Column(INTEGER(4), comment='单据类型')
    is_transfer = Column(TINYINT(1), default=0, comment='是否调拨单 1 是 0 否')
    expect_time = Column(DateTime, comment='期望时间:交付/以及其他类型的期望日期')


class ErpReceiptFinance(Base):
    __tablename__ = 'erp_receipt_finance'
    __table_args__ = {'comment': '财务单表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    receipt_id = Column(BIGINT(20), nullable=False, comment='关联单据ID')
    order_no = Column(String(50), comment='订单号')
    bank_account_id = Column(BIGINT(20), comment='关联账户ID')
    bank_account_name = Column(String(100), comment='关联账户名')
    apply_money = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='申请金额')
    trade_money = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='实际金额')
    ie_type = Column(TINYINT(1), comment='单据类型：1 退费单 2 采购单 3 报销单 4 支出单')
    # cost_type_id = Column(BIGINT(20), comment='费用类型ID')
    desc = Column(String(500), comment='描述')
    # template_id = Column(BIGINT(20), comment='模板ID')
    pre_pay_time = Column(Date, comment='预计付款日期')
    cashier_id = Column(BIGINT(20), server_default=text("'0'"), comment='出纳状态：0未审核 >0已审核（审核人ID）')
    financer_id = Column(BIGINT(20), server_default=text("'0'"), comment='财务审核：0未审核 >0已审核（审核人ID）')
    invoice_type = Column(TINYINT(1), comment='发票类型：1专票 2普票')
    invoice_money = Column(DECIMAL(10, 2), comment='发票金额')
    invoice_remark = Column(String(255), comment='发票备注')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(BIGINT(20), comment='创建人')
    update_by = Column(BIGINT(20), comment='更新人')
    disable = Column(TINYINT(1), default=0)
    # refund_order_no = Column(String(125), comment='退费订单号')




class ErpReceiptDetail(Base):
    __tablename__ = 'erp_receipt_detail'
    __table_args__ = {'comment': '通用明细表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    receipt_id = Column(BIGINT(20), nullable=False, comment='关联单据ID')
    item_type = Column(TINYINT(1), nullable=False, comment='明细类型:1单据明细 2财务明细')
    item_name = Column(String(100), comment='项目名称')
    item_num = Column(INTEGER(11), comment='数量')
    item_unit_price = Column(DECIMAL(10, 2), comment='单价')
    item_total_price = Column(DECIMAL(10, 2), comment='总价')
    cost_type_id = Column(BIGINT(20), comment='费用类型ID')
    cost_type_name = Column(String(50), comment='费用类型名称')
    amount = Column(DECIMAL(10, 2), comment='金额')
    tax_rate = Column(DECIMAL(5, 2), comment='税率')
    tax_amount = Column(DECIMAL(10, 2), comment='税额')
    remark = Column(String(255), comment='备注/描述')
    sort_no = Column(INTEGER(11), default=0)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(BIGINT(20), comment='创建人')
    update_by = Column(BIGINT(20), comment='更新人')
    attachment = Column(JSON, comment='附件')
    order_id = Column(BIGINT(20),default=0, comment='关联订单ID')
    item_source = Column(String(255), comment='来源')
    # 采购项id(>0即教学点采购项)
    purchasing_id = Column(BIGINT(20),default=0, comment='教学点采购项id')
    # 调拨单信息
    transfer_type = Column(BIGINT(20),default=0, comment='>0为调拨单， 1 付款方 2 收款方')
    transfer_income = Column(BIGINT(20),default=0, comment='是否汇兑收益/损失')
    transfer_comments = Column(String(255), comment='调拨备注')
    transfer_date = Column(Date, comment='调拨日期')
    transfer_account_id = Column(BIGINT(20),default=0, comment='付/收款对象')
    transfer_account_type = Column(BIGINT(20),default=0, comment='账户类型')
    transfer_account_number = Column(String(255), comment='付/收款对象账号')
    transfer_way_id = Column(BIGINT(20),default=0, comment='收/付款方式')
    disable = Column(TINYINT(1), default=0, comment='是否删除：0否 1是')



class ErpWorkflowDef(Base):
    __tablename__ = 'erp_workflow_def'
    __table_args__ = {'comment': '流程定义表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    workflow_desc = Column(String(50), nullable=False, comment='流程描述')
    workflow_name = Column(String(100), nullable=False, comment='流程名称')
    workflow_type = Column(INTEGER(4), comment='流程类型')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='状态：0禁用 1启用')
    remark = Column(String(255), comment='备注')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11), comment='创建人')
    update_by = Column(INTEGER(11), comment='更新人')
    disable = Column(INTEGER(4), default=0)




class ErpWorkflowInstance(Base):
    __tablename__ = 'erp_workflow_instance'
    __table_args__ = {'comment': '流程实例表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    workflow_id = Column(BIGINT(20), nullable=False, comment='流程定义ID')
    workflow_code = Column(String(50), comment='流程编码')
    workflow_name = Column(String(100), comment='流程名称')
    business_id = Column(BIGINT(20), comment='业务单据ID')
    business_type = Column(String(50), comment='业务单据类型')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='状态：0草稿 1进行中 2已完成 3已取消 4已驳回')
    current_node_id = Column(BIGINT(20), comment='当前节点ID')
    current_node_name = Column(String(100), comment='当前节点名称')
    submit_time = Column(DateTime, comment='提交时间')
    finish_time = Column(DateTime, comment='完成时间')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    create_by = Column(INTEGER(11), comment='创建人')
    update_by = Column(INTEGER(11), comment='更新人')
    disable = Column(INTEGER(4), default=0)


class ErpWorkflowNode(Base):
    __tablename__ = 'erp_workflow_node'
    __table_args__ = {'comment': '流程节点表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    workflow_id = Column(BIGINT(20), nullable=False, comment='流程定义ID')
    node_desc = Column(String(50), nullable=False, comment='节点描述')
    node_name = Column(String(100), nullable=False, comment='节点名称')
    node_type = Column(TINYINT(1), server_default=text("'1'"), comment='节点类型：1审批节点 2抄送节点')
    approver_type = Column(TINYINT(1), comment='审批人类型：1指定人 2指定角色 3部门主管 4连续多级上级 5指定岗位')
    approver_ids = Column(String(500), comment='审批人ID列表（逗号分隔）')
    approver_names = Column(String(500), comment='审批人名称列表（逗号分隔）')
    sort_no = Column(INTEGER(11), server_default=text("'0'"), comment='排序号')
    is_countersign = Column(TINYINT(1), server_default=text("'0'"), comment='是否会签：0否 1是')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11), comment='创建人')
    update_by = Column(INTEGER(11), comment='更新人')
    node_action = Column(INTEGER(4), comment='节点动作')
    is_finance_related = Column(TINYINT(1), server_default=text("'0'"), comment='是否涉及出入账：0否 1是')
    is_continuous_approval = Column(TINYINT(1), server_default=text("'0'"), comment='是否连续多级上级：0否 1是')
    disable = Column(INTEGER(4), default=0)


class ErpPaymentObj(Base):
    __tablename__ = 'erp_payment_obj'

    id = Column(INTEGER(11), primary_key=True)
    obj_name = Column(String(255))
    obj_type = Column(INTEGER(4))
    account_type = Column(String(255))
    obj_related_id = Column(INTEGER(11))
    account_no = Column(String(125))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4), default=0)
    remark = Column(String(255))
    bank_name = Column(String(255))
    obj_related_name = Column(String(125))
    phone = Column(String(125))


class ErpPaymentObjType(Base):
    __tablename__ = 'erp_payment_obj_type'

    id = Column(INTEGER(4), primary_key=True)
    type_name = Column(String(45))
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    update_time = Column(DateTime, default=datetime.now)
    create_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)



class ErpWorkflowRecord(Base):
    __tablename__ = 'erp_workflow_record'
    __table_args__ = {'comment': '审批记录表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    instance_id = Column(BIGINT(20), nullable=False, comment='流程实例ID')
    node_id = Column(BIGINT(20), nullable=False, comment='节点ID')
    node_name = Column(String(100), comment='节点名称')
    approver_id = Column(BIGINT(20), comment='审批人ID')
    approver_name = Column(String(50), comment='审批人姓名')
    approve_status = Column(TINYINT(1), server_default=text("'0'"), comment='审批状态：0待审批 1同意 2驳回 3转交 4撤销')
    approve_time = Column(DateTime, comment='审批时间')
    approve_opinion = Column(String(500), comment='审批意见')
    sort_no = Column(INTEGER(11), server_default=text("'0'"), comment='排序号')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpWorkflowInstanceReviewer(Base):
    __tablename__ = 'erp_workflow_instance_reviewer'
    __table_args__ = {'comment': '流程实例审批人表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    instance_id = Column(BIGINT(20), nullable=False, comment='流程实例ID')
    node_id = Column(BIGINT(20), nullable=False, comment='节点ID')
    reviewer_id = Column(BIGINT(20), nullable=False, comment='审批人ID')
    reviewer_name = Column(String(50), comment='审批人姓名')
    status = Column(TINYINT(1), server_default=text("'0'"), comment='状态：0待审批 1已同意 2已驳回')
    review_time = Column(DateTime, comment='审批时间')
    comments = Column(String(500), comment='审批意见')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpWorkflowCostType(Base):
    __tablename__ = 'erp_workflow_cost_type'

    id = Column(INTEGER(11), primary_key=True)
    workflow_id = Column(INTEGER(11))
    cost_type_id = Column(INTEGER(11))
    cost_type_name = Column(String(255))
    # cost_type_bind = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    # bind_type_name = Column(String(45), comment='绑定类型名称 例如 采购单 支出单 报销单 退费单')


class ErpCostTypeBind(Base):
    __tablename__ = 'erp_cost_type_bind'

    id = Column(INTEGER(4), primary_key=True)
    receipt_category_name = Column(String(255))
    workflow_id = Column(INTEGER(4))
    default_cost_type_id = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)