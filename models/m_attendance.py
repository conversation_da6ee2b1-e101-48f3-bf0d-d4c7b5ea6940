from datetime import datetime
from sqlalchemy import Column, DECIMAL, DateTime, Float, String, Text, text, Date, JSON
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, VARCHAR, LONGTEXT
from sqlalchemy.ext.declarative import declarative_base

import settings

Base = declarative_base()


class ErpCheckingReport(Base):
    __tablename__ = 'erp_checking_report'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11), nullable=False)
    ym = Column(INTEGER(11), nullable=False)
    employee_name = Column(String(255), nullable=False)
    employee_number = Column(String(50), nullable=False)
    salary_base = Column(DECIMAL(10, 2), nullable=False)
    salary_performance = Column(DECIMAL(10, 2), nullable=False)
    late_within_10_min = Column(INTEGER(11), nullable=False)
    late_10_to_30_min = Column(INTEGER(11), nullable=False)
    absent_without_notice = Column(INTEGER(11), nullable=False)
    early_leave = Column(INTEGER(11), nullable=False)
    forgot_to_check_in = Column(INTEGER(11), nullable=False)
    sick_leave = Column(INTEGER(11), nullable=False)
    personal_leave = Column(INTEGER(11), nullable=False)
    overtime = Column(INTEGER(11), nullable=False)
    other_paid_leave = Column(INTEGER(11), nullable=False)
    enterprise_name = Column(String(255), nullable=False)
    forgot_to_check_in_deduction = Column(DECIMAL(10, 2), nullable=False)
    late_deduction = Column(DECIMAL(10, 2), nullable=False)
    early_leave_deduction = Column(DECIMAL(10, 2), nullable=False)
    absent_without_notice_deduction = Column(DECIMAL(10, 2), nullable=False)
    personal_leave_deduction = Column(DECIMAL(10, 2), nullable=False)
    sick_leave_deduction = Column(DECIMAL(10, 2), nullable=False)
    sick_leave_addition = Column(DECIMAL(10, 2), nullable=False)
    actual_attendance_days = Column(DECIMAL(5, 2), nullable=False)
    actual_salary = Column(DECIMAL(10, 2), nullable=False)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
