# coding: utf-8
from datetime import datetime

import email.policy
from sqlalchemy import Column, DECIMAL, DateTime, String, Text, text, JSON, Float
from sqlalchemy.dialects.mssql import TINYINT
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, YEAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpClass(Base):
    __tablename__ = 'erp_class'

    id = Column(INTEGER(11), primary_key=True)
    course_id = Column(INTEGER(11), nullable=False)
    class_name = Column(String(255), nullable=False)
    class_capacity = Column(INTEGER(11), nullable=False)
    pre_enrollment = Column(INTEGER(11), nullable=False)
    teacher_id = Column(INTEGER(11), nullable=False)
    start_date = Column(DateTime, nullable=False)
    classroom_id = Column(INTEGER(11), nullable=False)
    use_standard_full_rate = Column(TINYINT(), nullable=False, server_default=text("'1'"))
    planning_class_times = Column(INTEGER(11), nullable=False)
    scheduling_method = Column(TINYINT(), nullable=False, server_default=text("'1'"))
    is_shelf_miniprogram = Column(TINYINT(), nullable=False, server_default=text("'1'"))
    miniprogram_start_enrollment_time = Column(DateTime)
    miniprogram_end_enrollment_time = Column(DateTime)
    qwechat_id = Column(String(255))
    qwechat_name = Column(String(255))
    classin_id = Column(INTEGER(11), default=0)
    enrollment_conditions = Column(Text)
    classin_sync = Column(TINYINT(), nullable=False, server_default=text("'0'"))
    hourly_tuition_ratio = Column(DECIMAL(5, 2), nullable=False, server_default=text("'0.00'"))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    disable = Column(INTEGER(4), default=0)
    class_status = Column(INTEGER(4), default=0)
    audit_status = Column(INTEGER(4), default=0, comment='0 未审核 1 已通过 2 已驳回')
    comments = Column(String(2000))
    qr_code = Column(String(255))
    assistant_teacher_id = Column(INTEGER(11), default=0, comment='助教老师id')


class ErpCourse(Base):
    __tablename__ = 'erp_course'

    id = Column(INTEGER(11), primary_key=True)
    course_name = Column(String(255), nullable=False)
    year = Column(YEAR(4), nullable=False)
    term_id = Column(INTEGER(11), nullable=False)
    subject_id = Column(INTEGER(11), nullable=False)
    grade_id = Column(INTEGER(11), nullable=False)
    p_grade_id = Column(INTEGER(11), default=0)
    type_id = Column(INTEGER(11), nullable=False)
    category_id = Column(INTEGER(11), nullable=False)
    course_cover = Column(String(255))
    course_introduction_page = Column(String(255))
    allow_refund = Column(TINYINT(), nullable=False, server_default=text("'0'"))
    allow_repeated_purchase = Column(TINYINT(), nullable=False, server_default=text("'0'"))
    cost_calculation_plan = Column(TINYINT(), nullable=False)
    course_coefficient = Column(DECIMAL(10, 2))
    pricing_plan = Column(TINYINT(), nullable=False)
    original_price = Column(DECIMAL(10, 2))
    sale_price = Column(DECIMAL(10, 2))
    number_of_lessons = Column(INTEGER(11))
    bound_textbook_id = Column(INTEGER(11))
    # outline_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    disable = Column(INTEGER(4), default=0)
    is_enable = Column(INTEGER(4), default=1)
    is_term_plan = Column(INTEGER(4), default=1)


class ErpCourseOutline(Base):
    __tablename__ = 'erp_course_outline'

    id = Column(INTEGER(11), primary_key=True)
    outline_name = Column(String(45))
    outline_desc = Column(String(255))
    course_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    disable = Column(INTEGER(4), default=0)


class ErpCourseCategory(Base):
    __tablename__ = 'erp_course_category'

    id = Column(INTEGER(11), primary_key=True)
    category_name = Column(String(125))
    category_desc = Column(String(255))
    category_objective = Column(String(255))
    stu_type_desc = Column(String(255))
    grade_id = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    is_enable = Column(INTEGER(4), default=0)


class ErpCourseTerm(Base):
    __tablename__ = 'erp_course_term'

    id = Column(INTEGER(11), primary_key=True)
    # term_type = Column(String(4))
    term_name = Column(String(255))
    year = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


# class ErpClassLog(Base):
#     __tablename__ = 'erp_class_log'

#     id = Column(INTEGER(11), primary_key=True, comment='日志编号')
#     log_type = Column(INTEGER(11), server_default=text("'0'"), comment='日志类型(1-新增学员）')
#     log_content = Column(Text, comment='日志内容')
#     class_id = Column(INTEGER(11), server_default=text("'0'"), comment='班级编号')
#     campus_id = Column(INTEGER(11), server_default=text("'0'"), comment='校区')
#     create_time = Column(DateTime, default=datetime.now)
#     update_time = Column(DateTime, default=datetime.now)
#     create_by = Column(INTEGER(11))
#     update_by = Column(INTEGER(11))
#     disable = Column(INTEGER(4), default=0)

class ErpClassLog(Base):
    __tablename__ = 'erp_class_log'

    id = Column(INTEGER(11), primary_key=True)
    objective_id = Column(INTEGER(11))
    log_content = Column(String(500))
    log_type = Column(INTEGER(4))
    log_name = Column(String(125))
    create_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpStudyClassReport(Base):
    __tablename__ = 'erp_study_class_report'

    id = Column(INTEGER(11), primary_key=True)
    study_report_id = Column(INTEGER(11))
    class_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpStudyReport(Base):
    __tablename__ = 'erp_study_report'

    id = Column(INTEGER(11), primary_key=True)
    report_name = Column(String(255))
    desc = Column(MEDIUMTEXT)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpCourseLog(Base):
    __tablename__ = 'erp_course_log'

    id = Column(INTEGER(11), primary_key=True)
    log_content = Column(String(500))
    log_type = Column(INTEGER(4))
    log_name = Column(String(125))
    objective_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpCourseTextbook(Base):
    __tablename__ = 'erp_course_textbook'

    id = Column(INTEGER(11), primary_key=True)
    name = Column(String(125))
    unit = Column(String(45))
    origin_price = Column(DECIMAL(10, 2))
    sale_price = Column(DECIMAL(10, 2))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpClassPlan(Base):
    __tablename__ = 'erp_class_plan'

    id = Column(INTEGER(11), primary_key=True)
    class_id = Column(INTEGER(11))
    room_id = Column(INTEGER(11))
    teacher_id = Column(INTEGER(11))
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    finish = Column(INTEGER(4), default=0)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    time_duration = Column(Float(4))
    classin_id = Column(INTEGER(11), default=0)

    classin_activity_id = Column(INTEGER(11), default=0)
    classin_name = Column(String(255), default='')
    classin_live_url = Column(String(255), default='')
    classin_live_info = Column(JSON, default={})
    classin_unit_id = Column(INTEGER(11), default=0)
    



class ErpClassChecking(Base):
    __tablename__ = 'erp_class_checking'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    class_id = Column(INTEGER(11))
    class_plan_id = Column(INTEGER(11))
    check_status = Column(INTEGER(4))
    order_student_id = Column(INTEGER(11))
    teacher_id = Column(INTEGER(11))
    class_room_id = Column(INTEGER(11))
    stu_type = Column(INTEGER(4), comment='1 正常 2 转班')
    is_online = Column(INTEGER(4), comment='1 线上')
    time_duration = Column(Float(10))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(5), default=0)
    price = Column(DECIMAL(10, 2), default=0, comment='签到价格')
    flag = Column(INTEGER(4), default=0, comment='0 无 1其他标记 2 已计算价格')




class ErpClassTransfor(Base):
    __tablename__ = 'erp_class_transfor'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    old_order_student_id = Column(INTEGER(11),  nullable=False)
    new_order_student_id = Column(INTEGER(11), nullable=False)
    old_class_id = Column(INTEGER(11),  nullable=False)
    new_class_id = Column(INTEGER(11),  nullable=False)
    transfor_reason = Column(String(255))
    transfor_num = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    disable = Column(INTEGER(4), default=0)
    is_inner = Column(INTEGER(4), default=0)



class ErpClassRescheduling(Base):
    __tablename__ = 'erp_class_rescheduling'

    id = Column(INTEGER(11), primary_key=True)
    order_student_id = Column(INTEGER(11))
    old_plan_id = Column(INTEGER(11))
    new_plan_id = Column(INTEGER(11))
    reschedule_reason = Column(String(255))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    disable = Column(INTEGER(4), default=0)
    is_inner = Column(INTEGER(4), default=0)



class ErpClassChangeRulesPaid(Base):
    __tablename__ = 'erp_class_change_rules_paid'
    __table_args__ = {'comment': '班型转换规则表'}

    id = Column(INTEGER(11), primary_key=True, comment='主键ID')
    p_grade_id = Column(INTEGER(11), nullable=False, comment='年级大类ID: 98小学,99初中,100高中')
    grade_id = Column(INTEGER(11), nullable=False, comment='源年级ID')
    subject_id = Column(INTEGER(11), nullable=False, comment='源学科ID')
    cate_id = Column(INTEGER(11), nullable=False, comment='源班型ID')
    target_cate_ids = Column(String(255), nullable=False, comment='目标班型IDs,逗号分隔')
    target_subject_ids = Column(String(255), nullable=False, comment='目标学科IDs,逗号分隔')
    target_grade_ids = Column(String(255), nullable=False, comment='目标年级IDs,逗号分隔')
    description = Column(String(255), comment='规则描述')
    is_enabled = Column(INTEGER(4), nullable=False, default=1, comment='是否启用:1启用,0禁用')
    create_by = Column(INTEGER(11), comment='创建者ID')
    update_by = Column(INTEGER(11), comment='更新者ID')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpClassChangeRules(Base):
    __tablename__ = 'erp_class_change_rules'
    __table_args__ = {'comment': '未付费转班规则'}

    id = Column(INTEGER(11), primary_key=True, comment='主键ID')
    p_grade_id = Column(INTEGER(11), nullable=False, comment='年级大类ID')
    grade_id = Column(INTEGER(11), nullable=False, comment='源年级ID')
    subject_id = Column(INTEGER(11), nullable=False, comment='源学科ID')
    cate_id = Column(INTEGER(11), nullable=False, comment='源班型ID')
    target_cate_ids = Column(String(255), nullable=False, comment='目标班型IDs,逗号分隔')
    target_subject_ids = Column(String(255), nullable=False, comment='目标学科IDs,逗号分隔')
    target_grade_ids = Column(String(255), nullable=False, comment='目标年级IDs,逗号分隔')
    description = Column(String(255), comment='规则描述')
    is_enabled = Column(INTEGER(4), nullable=False, default=1, comment='是否启用:1启用,0禁用')
    create_by = Column(INTEGER(11), comment='创建者ID')
    update_by = Column(INTEGER(11), comment='更新者ID')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)




class ErpClassRenewRules(Base): 
    __tablename__ = 'erp_class_renew_rules'
    __table_args__ = {'comment': '续报规则表'}

    id = Column(INTEGER(11), primary_key=True, comment='主键ID')
    create_by = Column(INTEGER(11), comment='创建者ID')
    update_by = Column(INTEGER(11), comment='更新者ID')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    current_class_id = Column(INTEGER(11))
    current_teacher_id = Column(INTEGER(11))
    term_id = Column(INTEGER(11))
    next_class_id = Column(INTEGER(11))
    next2_class_id = Column(INTEGER(11))
    start_time = Column(DateTime, comment='规则生效时间，也是自动生成订单的时间')
    end_time = Column(DateTime, comment='规则截止时间，报名也截止')
    signup_start = Column(DateTime, comment='可报名开始时间')
    created_order = Column(INTEGER(4), comment='是否已生成订单', default=0)
    new_msg = Column(String(500), comment='新消息')
    run_status = Column(INTEGER(4),  default=0, comment='运行状态 0 未运行 1 成功 2 失败')


class ErpClassRescheduleRules(Base):
    __tablename__ = 'erp_class_reschedule_rules'
    __table_args__ = {'comment': '调课规则'}

    id = Column(INTEGER(11), primary_key=True, comment='主键ID')
    p_grade_id = Column(INTEGER(11), nullable=False, comment='年级大类ID')
    grade_id = Column(INTEGER(11), nullable=False, comment='源年级ID')
    subject_id = Column(INTEGER(11), nullable=False, comment='源学科ID')
    cate_id = Column(INTEGER(11), nullable=False, comment='源班型ID')
    target_cate_ids = Column(String(255), nullable=False, comment='目标班型IDs,逗号分隔')
    target_subject_ids = Column(String(255), nullable=False, comment='目标学科IDs,逗号分隔')
    target_grade_ids = Column(String(255), nullable=False, comment='目标年级IDs,逗号分隔')
    description = Column(String(255), comment='规则描述')
    is_enabled = Column(INTEGER(4), nullable=False, default=1, comment='是否启用:1启用,0禁用')
    create_by = Column(INTEGER(11), comment='创建者ID')
    update_by = Column(INTEGER(11), comment='更新者ID')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)






class ErpExam(Base):
    __tablename__ = 'erp_exam'

    id = Column(INTEGER(11), primary_key=True)
    exam_name = Column(String(255), comment='考试名称')
    class_id = Column(INTEGER(11), server_default=text("'0'"), comment='关联class_id')
    disable = Column(INTEGER(4), default=0, comment='删除状态')
    create_by = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    create_time = Column(DateTime, default=datetime.now)
    update_by = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    update_time = Column(DateTime, default=datetime.now)
    type = Column(INTEGER(11), server_default=text("'1'"), comment='类型  1正常考试   2分组试题   3统计分析')
    analysis_type = Column(INTEGER(11), server_default=text("'1'"), comment='是否生成统计表的1-8题统计分析   1是  其他否')
    upload_type = Column(INTEGER(4), comment='1 诊断 其他待定')
    grade_type = Column(String(45))


class ErpExamQuestions(Base):
    __tablename__ = 'erp_exam_questions'

    id = Column(INTEGER(11), primary_key=True)
    exam_id = Column(INTEGER(11), server_default=text("'0'"), comment='考试ID')
    sort = Column(INTEGER(11), server_default=text("'0'"), comment='题号')
    difficulty = Column(String(255), comment='难度')
    knowledge_point = Column(String(255), comment='知识点')
    score = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='得分')
    group_name = Column(String(255), comment='分组名称')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpExamScore(Base):
    __tablename__ = 'erp_exam_score'

    id = Column(INTEGER(11), primary_key=True)
    exam_id = Column(INTEGER(11), server_default=text("'0'"), comment='考试ID')
    stu_name = Column(String(255), comment='导入的名称')
    stu_id = Column(INTEGER(11), server_default=text("'0'"), comment='学生StuID')
    content = Column(MEDIUMTEXT, comment='得分明细  json格式')
    t_score = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='总分')
    rank = Column(INTEGER(11), server_default=text("'0'"), comment='排名')
    rank_rate = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='百分比排名')
    exam_score = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='考试总分')
    match_class = Column(String(125))
    teacher_comments = Column(String(555))
    update_time = Column(DateTime)
    match_system = Column(String(45))
    disable = Column(INTEGER(4), default=0)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)