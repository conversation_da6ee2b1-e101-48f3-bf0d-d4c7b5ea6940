# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, DateTime, String, Text, text, JSON, Float
from sqlalchemy.dialects.mssql import TINYINT
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, YEAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpOfficeCenter(Base):
    __tablename__ = 'erp_office_center'

    id = Column(INTEGER(11), primary_key=True)
    center_name = Column(String(125))
    address = Column(String(125))
    phone = Column(String(45))
    latitude = Column(Float(10))
    longitude = Column(Float(10))
    school_spell = Column(String(45))
    qrcode = Column(String(255))
    sort = Column(INTEGER(4), default=0)
    center_cover_img = Column(String(255))


    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    center_cover_img = Column(String(255))
    center_type = Column(INTEGER(4), default=1)


class ErpOfficeClassroom(Base):
    __tablename__ = 'erp_office_classroom'

    id = Column(INTEGER(11), primary_key=True)
    center_id = Column(INTEGER(11))
    room_name = Column(String(255))
    room_cover_img = Column(String(255))
    room_square = Column(Float(6))
    room_length = Column(Float(6))
    room_width = Column(Float(6))
    stu_cap_max = Column(INTEGER(4))
    stu_cap_comfort = Column(INTEGER(4))
    window_to = Column(String(45))
    quiet_level = Column(String(45))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    inner_type = Column(INTEGER(4), default=0)


class ErpOfficeClassroomProblem(Base):
    __tablename__ = 'erp_office_classroom_problem'

    id = Column(INTEGER(11), primary_key=True)
    room_id = Column(INTEGER(11))
    content = Column(String(255))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    


class ErpOfficeFixedAssets(Base):
    __tablename__ = 'erp_office_fixed_assets'

    id = Column(INTEGER(11), primary_key=True)
    type = Column(INTEGER(4))
    name = Column(String(45))
    amount = Column(INTEGER(6))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    room_id = Column(INTEGER(11))
    serial_no = Column(String(45))
    status = Column(INTEGER(4), default=0)
    unit = Column(String(20))


class ErpOfficePurchasingList(Base):
    __tablename__ = 'erp_office_purchasing_list'

    id = Column(INTEGER(11), primary_key=True)
    room_id = Column(INTEGER(11))
    type = Column(INTEGER(4))
    name = Column(String(45))
    unit = Column(String(45))
    num = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    receipt_detail_id = Column(INTEGER(11), default=0)


class ErpOfficeConsumable(Base):
    __tablename__ = 'erp_office_consumable'

    id = Column(INTEGER(11), primary_key=True)
    title = Column(String(125))
    center_id = Column(INTEGER(11))
    num = Column(INTEGER(11))
    receive_datetime = Column(DateTime)
    pages = Column(INTEGER(4))
    size = Column(String(45))
    color = Column(String(45))
    binding = Column(String(45))
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    stock_status = Column(INTEGER(4), comment='入库状态 0 未入库 1 库存', default=0)
    # parent_id = Column(INTEGER(11), default=0)
    # is_supplement = Column(INTEGER(4), default=0)


class ErpOfficeConsumableSupplement(Base):
    __tablename__ = 'erp_office_consumable_supplement'

    id = Column(INTEGER(11), primary_key=True)
    consumable_id = Column(INTEGER(11))
    consumable_type = Column(INTEGER(4), comment='补充类型 1 加印（缺） 2 加印（需） 3 普通印刷')
    num = Column(INTEGER(4), comment='补充数量')
    supplement_status = Column(INTEGER(4), comment='补充状态 0 已完成或忽略 1 已提交的任务')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)



class ErpOfficeWarehouse(Base):
    __tablename__ = 'erp_office_warehouse'

    id = Column(INTEGER(11), primary_key=True)
    type = Column(INTEGER(4))
    name = Column(String(45))
    amount = Column(INTEGER(6))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    center_id = Column(INTEGER(11))
    serial_no = Column(String(45))
    status = Column(INTEGER(4), comment='1 使用中 2 异常 3 报废 4 遗失')
    unit = Column(String(20))

# 仓库日志
class ErpOfficeWarehouseLog(Base):
    __tablename__ = 'erp_office_warehouse_log'

    id = Column(INTEGER(11), primary_key=True)
    warehouse_id = Column(INTEGER(11))
    content = Column(String(500))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    disable = Column(INTEGER(4), default=0)