# coding: utf-8
from datetime import datetime
from email.policy import default

from sqlalchemy import Column, DECIMAL, DateTime, String, Text, text, JSON, Float
from sqlalchemy.dialects.mssql import TINYINT
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, YEAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpStudentPlus(Base):
    __tablename__ = 'erp_student_plus'

    id = Column(INTEGER(11), primary_key=True)
    stu_id = Column(INTEGER(11))
    training_plan = Column(String(45))
    consultant_status = Column(INTEGER(4), default=0)
    consultant_id = Column(INTEGER(11))
    parental_appeal = Column(String(255))
    character = Column(String(45))
    is_multi_child = Column(INTEGER(4))
    real_guardian = Column(String(45))
    major_concern_point = Column(String(45))
    reject_point = Column(String(45))
    famous_teacher_effect = Column(String(45))
    other_comments = Column(String(255))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    disable = Column(INTEGER(4), default=0)
    complete_exam = Column(INTEGER(4), default=0)
    course_labels = Column(JSON)


class ErpStudentConsultant(Base):
    __tablename__ = 'erp_student_consultant'
    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    disable = Column(INTEGER(4), default=0)
    current_point = Column(INTEGER(4), default=0)
    consultant_qr = Column(String(500))
    nick_name = Column(String(50))
    enterprise_image = Column(String(125))


class ErpOfflineExam(Base):
    __tablename__ = 'erp_offline_exam'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    appointment_time = Column(DateTime)
    appointment_center_id = Column(INTEGER(4))
    paper_name = Column(String(255))
    stu_score = Column(Float(10))
    paper_score = Column(Float(10))
    exam_result = Column(String(255))
    plus_id = Column(INTEGER(11))
    attachment = Column(JSON)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    disable = Column(INTEGER(4), default=0)

    # as_dict
    def as_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}
