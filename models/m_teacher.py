from datetime import datetime
from sqlalchemy import Column, DECIMAL, DateTime, Float, String, Text, text, Date, JSON
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, VARCHAR, LONGTEXT
from sqlalchemy.ext.declarative import declarative_base

import settings

Base = declarative_base()


class ErpAccountTeacher(Base):
    __tablename__ = 'erp_account_teacher'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    teacher_grade = Column(INTEGER(4))
    teacher_subject = Column(INTEGER(4))
    teacher_certification = Column(String(125))
    teacher_fee = Column(Float(10))

    teacher_avatar = Column(String(225))
    teacher_image = Column(String(225))
    teacher_qr_img = Column(String(225))
    teacher_desc = Column(String(1000))
    teacher_tag = Column(JSON)
    is_show = Column(INTEGER(4), default=0)

    class_fee = Column(Float(10))
    class_fee_type = Column(INTEGER(4))

    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    classin_uid = Column(INTEGER(11))
    classin_sync = Column(INTEGER(4), default=0)



class ErpAccountTeacherLog(Base):
    __tablename__ = 'erp_account_teacher_log'

    id = Column(INTEGER(11), primary_key=True)
    teacher_id = Column(INTEGER(11))
    content = Column(String(500))
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    update_time = Column(DateTime, default=datetime.now)
    create_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
