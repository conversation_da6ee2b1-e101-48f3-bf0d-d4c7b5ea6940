# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, DateTime, String, text, Float, JSON, Date
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpOrder(Base):
    __tablename__ = 'erp_order'
    __table_args__ = {'comment': 'ERP 订单表'}

    id = Column(INTEGER(11), primary_key=True)
    offer_id = Column(INTEGER(11), server_default=text("'0'"), comment='报价单id')
    order_student_id = Column(INTEGER(11), server_default=text("'0'"), comment='学生订单id')
    trade_way = Column(INTEGER(11), server_default=text("'0'"), comment='交易方式 1线上交易 2现金付款')
    class_price = Column(DECIMAL(12, 2), comment='课程单价')
    lesson_price = Column(DECIMAL(12, 2), comment='课节单价')
    total_receivable = Column(DECIMAL(12, 2), comment='总应收')
    total_income = Column(DECIMAL(12, 2), comment='总实收')
    refund = Column(DECIMAL(12, 2), server_default=text("'0.00'"), comment='退款金额')
    discount = Column(DECIMAL(12, 2), server_default=text("'0.00'"), comment='优惠金额')
    platform_tax = Column(DECIMAL(12, 2), server_default=text("'0.00'"), comment='平台税金')
    order_state = Column(INTEGER(11), server_default=text("'0'"), comment='订单状态 1正常 2退学 3其他')
    campus_id = Column(INTEGER(11), server_default=text("'1'"), comment='校区')
    renew_order_id = Column(INTEGER(11), server_default=text("'0'"), comment='续费订单编号')
    order_class_type = Column(INTEGER(11), server_default=text("'0'"), comment='订单类型 1课程 3 讲义')
    insert_plan_index = Column(INTEGER(11), server_default=text("'0'"), comment='插班开始课节 0正常单 x 插班单')
    join_type = Column(INTEGER(11), server_default=text("'1'"), comment='订单报入类型(1-正常报入，2-插班报入，3-续费订单，4-转班订单，5-分拆订单)')
    is_first_buy = Column(INTEGER(11), server_default=text("'0'"), comment='是否首次下单 0否 1是')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    unit = Column(INTEGER(4))
    buy_num = Column(INTEGER(4))
    disable = Column(INTEGER(4), default=0)
    ewallet_money = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='使用电子钱包金额')
    discount_id = Column(INTEGER(11))


class ErpOrderOffer(Base):
    __tablename__ = 'erp_order_offer'

    id = Column(INTEGER(11), primary_key=True, comment='主键编号')
    name = Column(String(255), server_default=text("''"), comment='报价单名称')
    effective_start = Column(DateTime, comment='有效期开始时间')
    effective_end = Column(DateTime, comment='有效期结束时间')
    total_original_price = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总原价')
    total_sale_price = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总金额')
    total_discount_price = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='优惠券总额')
    campus_id = Column(INTEGER(11), server_default=text("'0'"), comment='校区')
    order_no = Column(String(200), server_default=text("''"), comment='订单编号')
    offer_type = Column(INTEGER(11), server_default=text("'0'"),
                        comment='报价单类型(1-新购订单 2-续费订单)')
    stu_id = Column(INTEGER(11), server_default=text("'0'"), comment='学员编号')
    offer_state = Column(INTEGER(11), server_default=text("'0'"),
                         comment='付款状态(0-未推送，1-已推送，2-未付款，3-已付款，4-已关闭，5-已失效)')
    integral_money = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='积分兑换金额')
    ewallet_money = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='使用电子钱包金额')
    is_push = Column(INTEGER(10), server_default=text("'0'"), comment='是否推送(0-未推送，1-已推送)')
    cash_voucher = Column(String(2000), comment='现金付款凭证')
    cash_pay_remark = Column(String(255), comment='现金付款备注')
    inner_remark = Column(String(500), server_default=text("''"), comment='对内备注(内部显示)')
    out_remark = Column(String(500), server_default=text("''"), comment='对外备注(公众号H5支付显示)')
    order_from = Column(INTEGER(4), server_default=text("'1'"), comment='订单来源 1 ERP 2 新版小课堂')

    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    disable = Column(INTEGER(4), default=0)
    rule_id = Column(INTEGER(11), comment='规则id, 规则自动创建的报价单会生成此字段')
    cash_receive_way = Column(INTEGER(4), default=0, comment='现金收款方式 1 现金 默认0线上收款')


class ErpOrderStudent(Base):
    __tablename__ = 'erp_order_student'

    id = Column(INTEGER(11), primary_key=True)
    # order_id = Column(INTEGER(11), server_default=text("'0'"), comment='订单id')
    class_id = Column(INTEGER(11), server_default=text("'0'"), comment='班级id')
    stu_id = Column(INTEGER(11))
    student_state = Column(INTEGER(11), server_default=text("'0'"), comment='状态   1正常   2退学')
    total_hours = Column(DECIMAL(10, 1), server_default=text("'0.0'"), comment='总课时')
    complete_hours = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='完成课时')
    contract_id = Column(INTEGER(11), server_default=text("'0'"), comment='合同编号')
    is_renew = Column(INTEGER(11), server_default=text("'0'"), comment='是否是续费订单')
    self_change_class = Column(INTEGER(4), server_default=text("'0'"), comment='自主调课次数')
    order_class_type = Column(INTEGER(4), server_default=text("'0'"), comment='订单类型 1 课程 3 讲义')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    disable = Column(INTEGER(4), default=0)
    p_id = Column(INTEGER(11),  comment='父订单id')
    class_sync = Column(INTEGER(4), default=0, comment='班级同步状态 0未同步 1已同步')
    classin_course_id = Column(INTEGER(11), comment='classin课程id', default=0)
    classin_stu_uid = Column(INTEGER(11), comment='classin学生uid', default=0)
    is_online = Column(INTEGER(4), default=2, comment='是否在线 1线上 2 线下')



# class ErpFinanceTradePayment(Base):
#     __tablename__ = 'erp_finance_trade_payment'

#     id = Column(INTEGER(11), primary_key=True)
#     create_time = Column(DateTime, default=datetime.now)
#     update_time = Column(DateTime, default=datetime.now)
#     disable = Column(INTEGER(4), default=0)
#     payment_order_no = Column(String(125), comment='支付生成单号')
#     trade_type = Column(INTEGER(4), comment='1 微信 2 支付宝')
#     money_pay = Column(Float(10), comment='发起金额')
#     money_income = Column(Float(10), comment='实际收入金额')
#     openid = Column(String(125), comment='用户openid')
#     trade_status = Column(INTEGER(4), comment='1 挂起 2 成功 3 失败 4 已关闭')
#     merchant_id = Column(String(125), comment='商户编号')
#     cmb_order_id = Column(String(225), comment='招行订单号')
#     cmb_pay_time = Column(DateTime, comment='招行交易时间')
#     cmb_trade_type = Column(String(255), comment='招行交易类型 JSAPI')
#     third_order_id = Column(String(255), comment='三方流水号')
#     stu_id = Column(INTEGER(11))
#     offer_id = Column(INTEGER(11))
#     campus_id = Column(INTEGER(11))


# class ErpFinanceTradeRefund(Base):
#     __tablename__ = 'erp_finance_trade_refund'

#     id = Column(INTEGER(11), primary_key=True)
#     create_time = Column(DateTime, default=datetime.now)
#     update_time = Column(DateTime, default=datetime.now)
#     disable = Column(INTEGER(4), default=0)
#     payment_order_no = Column(String(125), comment='支付生成单号')
#     refund_order_no = Column(String(125), comment='退款生成单号')
#     trade_type = Column(INTEGER(4), comment='1 微信 2 支付宝')
#     money = Column(Float(10), comment='发起金额')
#     money_refund = Column(Float(10), comment='实际退款金额')
#     openid = Column(String(125), comment='用户openid')
#     trade_status = Column(INTEGER(4), comment='1 挂起 2 成功 3 失败 4 已关闭')
#     merchant_id = Column(String(125), comment='商户编号')
#     cmb_order_id = Column(String(225), comment='招行订单号')
#     cmb_pay_time = Column(DateTime, comment='招行交易时间')
#     cmb_trade_type = Column(String(255), comment='招行交易类型 JSAPI')
#     third_order_id = Column(String(255), comment='三方流水号')
#     comments = Column(String(255))
#     stu_id = Column(INTEGER(11), comment='关联学生')
#     offer_id = Column(INTEGER(11), comment='报价单号')
#     campus_id = Column(INTEGER(11), comment='关联校区')
#     receipt_id = Column(INTEGER(11), comment='关联单据')



class MallOrder(Base):
    __tablename__ = 'mall_order'

    Id = Column(INTEGER(11), primary_key=True, comment='小课堂订单信息id')
    ShoppingAddressDetail = Column(String(125), comment='收货地址')
    ShoppingMobile = Column(String(45), comment='收货电话')
    ShoppingConsignee = Column(String(45), comment='收货人')
    BuyMsg = Column(String(255), comment='订单备注')
    OfferId = Column(INTEGER(11), comment='报价单Id')
    OrderNo = Column(String(125), comment='订单号')
    MerchantsNo = Column(String(125), comment='商户单号')
    StuId = Column(INTEGER(11))
    CreateTime = Column(DateTime, default=datetime.now)
    SnapShot = Column(JSON, comment='交易快照')
    ShoppingAddressProvince = Column(String(45))
    ShoppingAddressCity = Column(String(45))
    ShoppingAddressDistrict = Column(String(45))
    Status = Column(INTEGER(11), server_default=text("'0'"))
    PayState = Column(INTEGER(11), server_default=text("'0'"))
    UpdateTime = Column(DateTime, default=datetime.now)
    CmbOrderId = Column(String(125), comment='招行id')
    CmbTradeState = Column(String(45), comment='定时任务状态')
    PayOpenId = Column(String(45), comment='支付的微信openid')
    PayType = Column(String(45), comment='支付平台')
    thirdOrderId = Column(String(125), comment='三方订单号')
    OrderFrom = Column(INTEGER(4), server_default=text("'2'"))
    MerchantConfigId = Column(INTEGER(11), comment=' 订单支付商户')



class ErpOrderRefund(Base):
    __tablename__ = 'erp_order_refund'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    refund_reason = Column(String(255))
    stu_id = Column(INTEGER(11))
    refund_remark = Column(String(255))
    audit_state = Column(INTEGER(4), default=0, comment='0 草稿 1 审核中 2 通过 3 驳回 4 取消')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    apply_date = Column(Date)
    apply_money = Column(DECIMAL(10, 2))
    refund_money = Column(DECIMAL(10, 2))
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    refund_type = Column(String(255), comment='1 退费 2 结转')
    receipt_id = Column(INTEGER(11))
    attachment = Column(JSON)
    receipt_id = Column(INTEGER(11), comment='关联单据ID')


class ErpOrderRefundDetail(Base):
    __tablename__ = 'erp_order_refund_detail'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    refund_id = Column(INTEGER(11), )
    order_no = Column(String(125), )
    refund_order_no = Column(String(125), )
    order_student_id = Column(INTEGER(11), )
    order_id = Column(INTEGER(11), )
    unit_price = Column(DECIMAL(10, 2))
    refund_num = Column(INTEGER(4))
    total_money = Column(DECIMAL(10, 2))
    unit = Column(String(45))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    apply_money = Column(DECIMAL(10, 2))
    refund_money = Column(DECIMAL(10, 2))
    # carryover_money = Column(DECIMAL(10, 2))
    pay_time = Column(DateTime)
    disable = Column(INTEGER(4), default=0)
    refund_state = Column(INTEGER(4), default=0)
    refund_type = Column(INTEGER(4), default=1)
    refund_ewallet = Column(DECIMAL(10, 2))
    refund_way = Column(INTEGER(4), default=1)
    cost_type_id = Column(INTEGER(11))
    refund_times = Column(INTEGER(4), default=0)
    cost_type_name = Column(String(125))
    sort_no = Column(INTEGER(4))
    act_money = Column(DECIMAL(10, 2), default=0, comment='实际退款金额')
    
