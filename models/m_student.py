# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, Date, DateTime, Float, JSON, String, Table, Text, text
from sqlalchemy.dialects.mysql import INTEGER, LONGTEXT, VARCHAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpStudent(Base):
    __tablename__ = 'erp_student'

    id = Column(INTEGER(11), primary_key=True)
    stu_username = Column(String(125), comment='手机号')
    stu_name = Column(String(125), comment='学生姓名')
    # stu_status = Column(INTEGER(4), comment='学生状态')
    stu_birth = Column(DateTime, comment='出生日期')
    stu_gender = Column(INTEGER(4), comment='性别 1 男 2 女')
    stu_avatar = Column(String(555), comment='头像或形象照')
    stu_area = Column(String(45), comment='城市')
    stu_address = Column(String(255), comment='地址')
    stu_grade = Column(String(125), comment='账号建立时的年级')
    stu_idcard = Column(String(125), comment='身份证')
    stu_school_name = Column(String(125), comment='就读学校')
    stu_wallet_amount = Column(Float(10), comment='钱包额度')
    stu_serial = Column(String(125), comment='学号')
    how_known = Column(String(125), comment='如何知道进阶')
    wechat_open_id = Column(String(125), comment='微信openid')
    classin_uid = Column(INTEGER(11), comment='classin uid')
    classin_sync = Column(INTEGER(4), comment='是否同步classin')
    update_time = Column(DateTime, default=datetime.now)
    create_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    mall_user_id = Column(INTEGER(11))
    is_blacklist = Column(INTEGER(4), default=0)



class ErpStudentWechat(Base):
    __tablename__ = 'erp_student_wechat'

    id = Column(INTEGER(11), primary_key=True)
    stu_id = Column(INTEGER(11))
    wechat_open_id = Column(String(125))
    update_time = Column(DateTime, default=datetime.now)
    create_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)