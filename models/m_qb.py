# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, Date, DateTime, Float, JSON, String, Table, text
from sqlalchemy.dialects.mysql import DATETIME, INTEGER, LONGTEXT, VARCHAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpQbAccountPermission(Base):
    __tablename__ = 'erp_qb_account_permission'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    account_type = Column(INTEGER(4))
    subject_ids = Column(JSON)
    grade_ids = Column(JSON)
    create_by = Column(INTEGER(4), default=20000)
    update_by = Column(INTEGER(4), default=20000)
    permission_ids = Column(JSON)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)



class ErpQbPermission(Base):
    __tablename__ = 'erp_qb_permission'

    id = Column(INTEGER(11), primary_key=True)
    permission_name = Column(String(45), comment='题库权限名')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    create_by = Column(INTEGER(4), default=20000)
    update_by = Column(INTEGER(4), default=20000)


class ErpGuest(Base):
    __tablename__ = 'erp_guest'

    id = Column(INTEGER(11), primary_key=True)
    guest_name = Column(String(45))
    username = Column(String(45))
    password = Column(String(255))
    create_by = Column(INTEGER(4), default=20000)
    update_by = Column(INTEGER(4), default=20000)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
