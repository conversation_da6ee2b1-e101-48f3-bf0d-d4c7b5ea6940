
from sqlalchemy import Column, DECIMAL, DateTime, Float, String, Text, text, JSON
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, VARCHAR, LONGTEXT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()



class RbOrder(Base):
    __tablename__ = 'rb_order'

    OrderId = Column(INTEGER(11), primary_key=True)
    ClassId = Column(INTEGER(11), server_default=text("'0'"), comment='班级id')
    TradeWay = Column(INTEGER(11), server_default=text("'0'"), comment='交易方式  1线上交易   2现金付款')
    Class_Price = Column(DECIMAL(18, 2), comment='单价')
    Unit_Price = Column(DECIMAL(18, 2), comment='成交单价')
    GuestNum = Column(INTEGER(11), comment='报名人数')
    PreferPrice = Column(DECIMAL(18, 2), comment='应收总额')
    Income = Column(DECIMAL(18, 2), comment='已收金额')
    Refund = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='退款金额')
    DiscountMoney = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='优惠金额')
    PlatformTax = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='平台税金')
    OrderState = Column(INTEGER(11), server_default=text("'0'"), comment='订单状态  1正常   2退学  3取消   枚举')
    OrderSource = Column(INTEGER(11), server_default=text("'0'"), comment='订单来源    枚举')
    EnterID = Column(INTEGER(11), server_default=text("'0'"), comment='业务员')
    OrderForm = Column(INTEGER(11), server_default=text("'1'"), comment='订单来自于  电脑  手机区分   枚举')
    CommissionMoney = Column(DECIMAL(18, 2), nullable=False, server_default=text("'-1.00'"), comment='提成金额')
    ExtraRewardMoney = Column(DECIMAL(18, 2), comment='额外奖励金额')
    ExtraDeductMoney = Column(DECIMAL(18, 2), comment='额外扣除金额')
    IsCommissionGive = Column(INTEGER(11), server_default=text("'0'"), comment='提成是否发放   1是  0否')
    SaleRemark = Column(String(600, 'utf8mb4_unicode_ci'), comment='销售备注')
    TeacherRemark = Column(String(600, 'utf8mb4_unicode_ci'), comment='教务备注')
    RectorRemark = Column(String(600, 'utf8mb4_unicode_ci'), comment='校长备注')
    DirectorRemark = Column(String(600, 'utf8mb4_unicode_ci'), comment='经理备注')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校编号')
    Dept_Id = Column(INTEGER(11), server_default=text("'0'"), comment='部门id')
    CreateBy = Column(INTEGER(11), comment='创建人')
    CreateTime = Column(DateTime, comment='创建时间')
    UpdateBy = Column(INTEGER(11), comment='修改人')
    UpdateTime = Column(DateTime, comment='修改时间')
    RenewOrderId = Column(INTEGER(11), server_default=text("'0'"), comment='续费订单编号')
    OfferId = Column(INTEGER(11), server_default=text("'0'"), comment='报价单id')
    OrderType = Column(INTEGER(11), server_default=text("'0'"), comment='订单类型(见枚举)')
    SourceId = Column(INTEGER(11), server_default=text("'0'"),
                      comment='来源编号(OrderType=1,SourceId=SourceId),(OrderType=2,3,SourceId=留学就业产品对应编号)')
    HelpEnterId = Column(INTEGER(11), server_default=text("'0'"), comment='协助人员编号(协助老师编号)')
    GeneralOccupation = Column(String(50, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='一般同行名称')
    EduOccupation = Column(String(50, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='教育同行名称')
    IsLessPrice = Column(INTEGER(11), server_default=text("'0'"), comment='是否少价(1-是)')
    LessPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='少价金额')
    OrderNature = Column(INTEGER(11), server_default=text("'0'"),
                         comment='(教育订单使用)订单性质(1-直通车,2-代收代付,3-返佣)')
    OldPreferPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"),
                            comment='(教育订单使用)订单性质(1-直通车,2-代收代付,3-返佣)')
    CourseId = Column(INTEGER(11), server_default=text("'0'"), comment='课程编号')
    IsChaBan = Column(INTEGER(11), server_default=text("'0'"), comment='是否插班报入(1-是)')
    StartClassHours = Column(INTEGER(11), server_default=text("'0'"), comment='插班开始课时')
    CommissionType = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='销售返佣类型  0百分比   1固定值')
    CommissionRate = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='提成比例/固定值')
    CommissionTypeTH = Column(DECIMAL(18, 2), server_default=text("'0.00'"),
                              comment='销售返佣类型  0百分比   1固定值（同行的）')
    CommissionRateTH = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='提成比例/固定值（同行的）')
    CommissionBack = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='提成补交')
    CommissionRecord = Column(String(600, 'utf8mb4_unicode_ci'), comment='提成记录')
    CommissionRemark = Column(String(600, 'utf8mb4_unicode_ci'), comment='提成备注： 记录下单时  使用的 比例')
    ExtraCommissionMoney = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='额外提成已发放金额')
    IsCommissionGiveOK = Column(INTEGER(11), server_default=text("'0'"), comment='提成是否发放完毕   1是  0否')
    EffectTime = Column(DateTime, comment='入班日期')
    EffectStatus = Column(INTEGER(11), server_default=text("'0'"), comment='生效状态(0-未生效，1-生效中，2-完成)')
    UpOrderId = Column(INTEGER(11), server_default=text("'0'"), comment='前置订单编号')
    VisitorReserveId = Column(INTEGER(11), server_default=text("'0'"), comment='预约单号')
    LXConfirmDate = Column(DateTime, comment='留学确认时间（确认后 才能发提成  才进报表）')
    IsRenewOrder = Column(INTEGER(11), server_default=text("'0'"), comment='是否续班(1-是)')
    JoinType = Column(INTEGER(11), server_default=text("'1'"),
                      comment='订单报入类型(1-正常报入，2-插班报入，3-续费订单，4-转班订单，5-分拆订单)')
    TargetJoinType = Column(INTEGER(11), server_default=text("'1'"), comment='新订单报入类型')
    SourceOrderId = Column(INTEGER(11), server_default=text("'0'"), comment='原订单编号')
    TargetOrderId = Column(INTEGER(11), server_default=text("'0'"), comment='目标订单编号')
    FirstOrderId = Column(INTEGER(11), server_default=text("'0'"), comment='最开始拆分订单的订单编号')
    IsBackClass = Column(INTEGER(11), server_default=text("'0'"), comment='是否已回归原班(1-是)')
    PerLessMoney = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='每人少价金额')
    PerDiscountMoney = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='每人优惠金额')
    LXConfirmState = Column(INTEGER(11), server_default=text("'0'"), comment='留学订单确认状态   1确认')
    LXConfirmEmpId = Column(INTEGER(11), server_default=text("'0'"), comment='确认人')
    ExchangeNum = Column(INTEGER(11), server_default=text("'0'"), comment='兑换奖品人数')
    ExchangeMoney = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='兑换奖品金额')
    ExchangeType = Column(INTEGER(11), server_default=text("'0'"), comment='兑换奖品类型(1-人数，2-金额)')
    CustomerId = Column(INTEGER(11), server_default=text("'0'"), comment='同业ID')
    PayState = Column(INTEGER(11), server_default=text("'0'"), comment='付款状态   1已付款   2未付款')
    PayDate = Column(DateTime, comment='付款时间')
    CourseConsultantId = Column(INTEGER(11), server_default=text("'0'"), comment='课程顾问ID')
    OrderIdentify = Column(INTEGER(11), server_default=text("'0'"), comment='订单标识    1产品下单   2客户转订单')
    B2CRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='直客首次报名优惠比例')
    B2CReNewRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='直客续费优惠比例')
    B2BRebateRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='一般同行首次报名返佣比例')
    B2BReNewRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='一般同行续费返佣比例')
    SchoolRebateRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='校代同行首次返佣比例')
    SchoolReNewRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='校代同行续费返佣比例')
    TransIntroductceRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='转介首次报名返佣比例')
    TransIntroductceReNewRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='转介续费返佣比例')
    InnerRecommendRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='內推首次报名返佣比例')
    InnerRecommendReNewRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='內推续费返佣比例')
    CommissionReType = Column(INTEGER(11), server_default=text("'1'"), comment='返佣类型   1按课耗反    2付款返佣')
    ScrollSchoolId = Column(INTEGER(11), server_default=text("'0'"), comment='滚动开班所属校区')
    StudyBroadRebateMoney = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='留学订单返佣金额')
    GUIDStr = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='订单GUID编号')
    OrderCode = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='订单流水编号')
    BookMoney = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='教材费')
    Times = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='期数')
    ClientPayMoney = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='客人线上付款')
    ClientCashMoney = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='客人现金付款')
    ClientWalletMoney = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='客人电子钱包付款')
    OrderUnit = Column(INTEGER(10), server_default=text("'0'"), comment='单位(1-期,2-次,3-本）')
    BackClassDate = Column(DateTime, comment='具体退课日期')
    IsTimesOrder = Column(INTEGER(11), server_default=text("'0'"), comment='是否续课次订单(1-是)')
    TimesOrderId = Column(INTEGER(11), server_default=text("'0'"), comment='原订单Id')
    TimesOrderRemark = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='续课次订单备注')
    RClassId = Column(INTEGER(11), server_default=text("'0'"), comment='关联班级编号')
    IsOnlineStu = Column(INTEGER(11), server_default=text("'0'"), comment='是否在线学习(在线学习不占用班级名额)')
    ReceiveBookType = Column(INTEGER(11), server_default=text("'0'"),
                             comment='领取讲义类型(0-未领取(可退费),1-已领取(不能退费),2-已退回(可退费)')
    OrderFrom = Column(INTEGER(4), comment='订单来源')




class RbOrderMain(Base):
    __tablename__ = 'rb_order_main'

    Id = Column(INTEGER(11), primary_key=True, comment='主键编号')
    Name = Column(String(255, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='报价单名称')
    EffectiveStart = Column(DateTime, comment='有效期开始时间')
    EffectiveEnd = Column(DateTime, comment='有效期结束时间')
    CustomerType = Column(INTEGER(11), server_default=text("'0'"), comment='客户类型(1-新客户,2-续班客户)')
    CustomerSource = Column(INTEGER(11), server_default=text("'0'"),
                            comment='客户来源(1-学校介绍,2-朋友介绍,3-网络广告)')
    TotalOriginalPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总原价')
    TotalPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总金额')
    TotalDiscountPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='优惠劵总额')
    CustomerStatus = Column(INTEGER(11), server_default=text("'0'"), comment='客户确认状态(1-待确认,2-已确认)')
    CustomerInfo = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='客户微信信息')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, comment='修改时间')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校编号')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态(1-删除)')
    CustomerName = Column(String(255, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='客户名称')
    CustomerTel = Column(String(255, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='客户电话')
    JoinNum = Column(INTEGER(11), server_default=text("'0'"), comment='客户人数')
    SerialNum = Column(String(50, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='流水号')
    OrderId = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='订单编号')
    ApplyReason = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='申请原因')
    OfferType = Column(INTEGER(11), server_default=text("'0'"),
                       comment='报价单类型(1-校管家后台预制订单，2-校管家商城预制订单，3-新系统预制订单)')
    StuId = Column(INTEGER(11), server_default=text("'0'"), comment='学员编号')
    OfferState = Column(INTEGER(11), server_default=text("'0'"),
                        comment='付款状态(0-未推送，1-已推送，2-未付款，3-已付款，4-已关闭，5-已失效)')
    PayNo = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='付款流水号')
    PayType = Column(INTEGER(11), server_default=text("'0'"), comment='支付方式(1-微信公众号，2-支付宝收款码，)')
    PayTime = Column(DateTime, comment='付款时间')
    PayMoney = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='付款金额(首次实交金额)')
    IntegralPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='积分兑换金额')
    ElectronicWallet = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='电子钱包')
    GUIDStr = Column(VARCHAR(255), server_default=text("''"), comment='GUID编号')
    IsPush = Column(INTEGER(10), server_default=text("'0'"), comment='是否推送(0-未推送，1-已推送)')
    Mall_Order_Id = Column(INTEGER(11), server_default=text("'0'"), comment='商城订单编号')
    CashVoucher = Column(String(2000, 'utf8mb4_unicode_ci'), comment='现金付款凭证')
    CashPayRemark = Column(String(255, 'utf8mb4_unicode_ci'), comment='现在付款备注')
    InnerRemark = Column(String(500, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='对内备注(内部显示)')
    OutRemark = Column(String(500, 'utf8mb4_unicode_ci'), server_default=text("''"),
                       comment='对外备注(公众号H5支付显示)')
    OrderFrom = Column(INTEGER(4), comment='订单来源')

