# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, Date, DateTime, Float, JSON, String, Table, Text, text
from sqlalchemy.dialects.mysql import INTEGER, LONGTEXT, VARCHAR, MEDIUMTEXT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class RbClass(Base):
    __tablename__ = 'rb_class'

    ClassId = Column(INTEGER(11), primary_key=True, comment='主键(班级ID)')
    ClassName = Column(String(50, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='班级名称')
    CouseId = Column(INTEGER(11), server_default=text("'0'"), comment='课程Id')
    Teacher_Id = Column(INTEGER(11), server_default=text("'0'"), comment='讲师Id')
    Assist_Id = Column(INTEGER(11), server_default=text("'0'"), comment='助教Id')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校编号')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态(0-正常,1-禁用)')
    ClassPersion = Column(INTEGER(11), server_default=text("'0'"), comment='招生人数')
    OpenTime = Column(DateTime, comment='开班时间')
    EndOrderTime = Column(DateTime, comment='截止报名时间')
    OriginalPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='原价')
    SellPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='售价')
    IsStepPrice = Column(INTEGER(11), server_default=text("'0'"), comment='是否阶梯价(1-是)')
    ClassRoomId = Column(INTEGER(11), server_default=text("'0'"), comment='教室编号')
    IsOpenCommission = Column(INTEGER(11), server_default=text("'0'"), comment='是否开启提成设置(1-是)')
    CommissionType = Column(INTEGER(11), server_default=text("'0'"), comment='提成类型(1-人头，2-百分比）')
    CommissionValue = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='提成值')
    ClassHours = Column(INTEGER(11), server_default=text("'0'"), comment='课时')
    ClassStyle = Column(INTEGER(11), server_default=text("'0'"), comment='排课方式(1-周，2-月，3-固定日期，4-约课)')
    ClassStatus = Column(INTEGER(11), server_default=text("'0'"), comment='班级状态')
    InnerRemark = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='对内备注')
    OutRemark = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='对外备注')
    CompleteProgress = Column(INTEGER(11), server_default=text("'0'"), comment='完成进度')
    ClassType = Column(INTEGER(11), server_default=text("'0'"), comment='班级类型(1-学生班，2-社会班)')
    DefaultTimeJson = Column(String(4000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='默认时间字符串')
    DateJson = Column(String(4000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='默认选中的日期、周')
    EndClassDate = Column(DateTime, comment='结课日期')
    IsDeduction = Column(INTEGER(11), server_default=text("'0'"), comment='是否可抵扣(1-是)')
    IsSubscribe = Column(INTEGER(11), server_default=text("'0'"), comment='App是否可预约(1-是)')
    Point = Column(INTEGER(11), server_default=text("'0'"), comment='App预约点数')
    CourseClassType = Column(INTEGER(11), server_default=text("'0'"), comment='分类类型')
    StudentNumType = Column(INTEGER(11), server_default=text("'0'"), comment='上课人数')
    ClassHourMinute = Column(INTEGER(11), server_default=text("'45'"), comment='课时分钟数')
    ClassNo = Column(String(20, 'utf8mb4_unicode_ci'), comment='班级编码')
    ClassLetterNum = Column(INTEGER(11), server_default=text("'0'"), comment='字母排序')
    ClassScrollType = Column(INTEGER(11), server_default=text("'0'"), comment='开班类型   1正常班   2滚动班')
    ScrollMonth = Column(String(255, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='滚动开班月份')
    GUIDStr = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='班级GUID编号')
    CourseTimes = Column(INTEGER(11), server_default=text("'0'"), comment='计划排课次数')
    DetailInfo = Column(MEDIUMTEXT, comment='图文')
    CoverImage = Column(String(1000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='封面图')
    CourseDescImage = Column(String(500, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='课程详情图')
    AuditStatus = Column(INTEGER(11), server_default=text("'0'"), comment='审核状态')
    WeChatId = Column(String(50, 'utf8mb4_unicode_ci'), comment='企微群ID')
    WeChatName = Column(String(255, 'utf8mb4_unicode_ci'), comment='群聊名称')
    WeChatConfigId = Column(String(50, 'utf8mb4_unicode_ci'), comment='进群配置ID')
    Mall_Goods_Id = Column(INTEGER(11), server_default=text("'0'"), comment='商城表RB_Goods的Id')
    ClassInCourseID = Column(INTEGER(11), server_default=text("'0'"), comment='classIn 课程 ID')
    Mall_Is_Show = Column(INTEGER(11), server_default=text("'1'"), comment='商城是否显示此班级(1-显示，0-不显示)')
    MallImage = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='商城图片')
    HourFeeRate = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='老师课时费发放比例')
    EnrollPersion = Column(INTEGER(11), server_default=text("'0'"), comment='预招人数')
    StartOrderTime = Column(DateTime, comment='小程序上架时间')


class RbCourse(Base):
    __tablename__ = 'rb_course'

    CourseId = Column(INTEGER(11), primary_key=True, comment='主键(课程编号)')
    CoverImg = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='课程封面图')
    CourseName = Column(Text(collation='utf8mb4_unicode_ci'), comment='课程名称')
    CourseIntro = Column(Text(collation='utf8mb4_unicode_ci'), comment='课程介绍')
    CateId = Column(INTEGER(11), server_default=text("'0'"), comment='课程分类编号')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
                        comment='更新时间')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校Id')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='状态')
    Teacher_Ids = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='教师编号[逗号分隔]')
    IsShowChapterNo = Column(INTEGER(11), server_default=text("'1'"), comment='是否显示章节编号(1-显示，0-不显示)')
    CourseFeature = Column(Text(collation='utf8mb4_unicode_ci'), comment='课程特色')
    OriginalPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='原价')
    SellPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='售价')
    IsOpenStepPrice = Column(INTEGER(11), server_default=text("'0'"), comment='是否开启阶梯定价(1-开启)')
    IsRenew = Column(INTEGER(11), server_default=text("'0'"), comment='是否可续费课程(1-是)')
    RenewOgPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='续费原价')
    RenewSlPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='续费售价')
    Saleplat = Column(String(50, 'utf8mb4_unicode_ci'), server_default=text("''"),
                      comment='上架端口(1-内部销售，2-学员App，3-网课端）')
    IsKCourse = Column(INTEGER(11), server_default=text("'0'"), comment='是否是K12课程(1-是)')
    ClassHours = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='课时')
    MallGoodsId = Column(INTEGER(11), server_default=text("'0'"), comment='甲鹤小程序商品id')
    CourseRate = Column(INTEGER(11), server_default=text("'0'"), comment='课程等级')
    CourseSubject = Column(INTEGER(11), server_default=text("'0'"), comment='所属科目')
    B2BIcon = Column(String(255, 'utf8mb4_unicode_ci'), comment='同业图标')
    B2BBackground = Column(String(255, 'utf8mb4_unicode_ci'), comment='背景颜色（前端无法实现自动识别时启用）')
    CourseEmphasis = Column(String(255, 'utf8mb4_unicode_ci'), comment='课程重点， 多个英文逗号分隔  有枚举')
    B2CRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='直客首次报名优惠比例')
    B2CReNewRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='直客续费优惠比例')
    B2BRebateRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='一般同行首次报名返佣比例')
    B2BReNewRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='一般同行续费返佣比例')
    SchoolRebateRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='校代同行首次返佣比例')
    SchoolReNewRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='校代同行续费返佣比例')
    TransIntroductceRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='转介返佣比例')
    TransIntroductceReNewRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='转介续费返佣比例')
    InnerRecommendRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='內推返佣比例')
    InnerRecommendReNewRatio = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='內推续费返佣比例')
    CommissionReType = Column(INTEGER(11), server_default=text("'1'"), comment='返佣类型   1按课耗反    2付款返佣')
    B2BRebateRatioType = Column(INTEGER(11), server_default=text("'0'"), comment='一般同行首次报名返佣类型')
    B2BRebateRatioValue = Column(DECIMAL(18, 2), server_default=text("'0.00'"),
                                 comment='一般同行首次报名返佣课程比例值')
    B2BReNewRatioType = Column(INTEGER(11), server_default=text("'0'"), comment='一般同行续费返佣类型')
    B2BReNewRatioValue = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='一般同行续费返佣课程比例值')
    SchoolRebateRatioType = Column(INTEGER(11), server_default=text("'0'"), comment='校代同行首次返佣类型')
    SchoolRebateRatioValue = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='校代同行首次返佣课程比例值')
    SchoolReNewRatioType = Column(INTEGER(11), server_default=text("'0'"), comment='校代同行续费返佣类型')
    SchoolReNewRatioValue = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='校代同行续费返佣课程比例值')
    TransIntroductceRatioType = Column(INTEGER(11), server_default=text("'0'"), comment='转介首次报名返佣类型')
    TransIntroductceRatioValue = Column(DECIMAL(18, 2), server_default=text("'0.00'"),
                                        comment='转介首次报名返佣课程比例值')
    TransIntroductceReNewRatioType = Column(INTEGER(11), server_default=text("'0'"), comment='转介续费返佣类型')
    TransIntroductceReNewRatioValue = Column(DECIMAL(18, 2), server_default=text("'0.00'"),
                                             comment='转介续费返佣课程比例值')
    InnerRecommendRatioType = Column(INTEGER(11), server_default=text("'0'"), comment='內推首次报名返佣类型')
    InnerRecommendRatioValue = Column(DECIMAL(18, 2), server_default=text("'0.00'"),
                                      comment='內推首次报名返佣课程比例值')
    InnerRecommendReNewRatioType = Column(INTEGER(11), server_default=text("'0'"), comment='內推续费返佣类型')
    InnerRecommendReNewRatioValue = Column(DECIMAL(18, 2), server_default=text("'0.00'"),
                                           comment='內推续费返佣课程比例值')
    B2CRbRatio = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='直客首次报名返佣比例')
    B2CRbRatioType = Column(INTEGER(11), server_default=text("'0'"), comment='直客首次报名返佣类型')
    B2CRbRatioValue = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='直客首次报名返佣类型值')
    B2CRNRatio = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='直客续费返佣比例')
    B2CRNRatioType = Column(INTEGER(11), server_default=text("'0'"), comment='直客续费返佣类型')
    B2CRNRatioValue = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='直客续费返佣类型值')
    ContractInfo = Column(MEDIUMTEXT, comment='合同补充协议')
    IsScrollClass = Column(INTEGER(11), server_default=text("'***********'"), comment='是否会滚动开班   1是')
    ScrollMinNum = Column(INTEGER(11), server_default=text("'0'"), comment='最小上课人数')
    ScrollMaxNum = Column(INTEGER(11), server_default=text("'0'"), comment='最大上课人数')
    CourseTimeId = Column(INTEGER(11), server_default=text("'0'"), comment='适配上课时段ID (滚动开班)')
    OpenBankLevel = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='开放题库等级')
    FreeCoffeeNum = Column(INTEGER(11), server_default=text("'0'"), comment='赠送咖啡劵数量')
    AddHoursMoney = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='课程增加  课时费')
    ChineseHours = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='中教课时')
    ForeignHours = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='外教课时')
    GUIDStr = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='课程GUID编号')
    ShiftTerm = Column(INTEGER(11), server_default=text("'0'"), comment='期段')
    ShiftGrade = Column(INTEGER(11), server_default=text("'0'"), comment='年级')
    ShiftClassType = Column(INTEGER(11), server_default=text("'0'"), comment='班型')
    IsOneToOne = Column(INTEGER(11), server_default=text("'0'"), comment='课程类型(0-集体班,1-一对一,2-一对多)')
    NumberOfStudents = Column(INTEGER(11), server_default=text("'0'"), comment='上课人数(IsOneToOne=2时使用)')
    Unit = Column(INTEGER(11), server_default=text("'0'"), comment='计费形式(1-小时，2-次)')
    IsDynamicConsume = Column(INTEGER(11), server_default=text("'0'"), comment='动态课消(1-是)')
    ConsumeAmount = Column(INTEGER(11), server_default=text("'0'"), comment='每排一次课的消课次数')
    IsByTerm = Column(INTEGER(11), server_default=text("'0'"), comment='是否按期收费(1-是)')
    CourseAmountPerTerm = Column(INTEGER(11), server_default=text("'0'"), comment='每期数量')
    UnitPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='标准单价')
    UnitPriceByTerm = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='每期单价')
    CanRefund = Column(INTEGER(11), server_default=text("'0'"), comment='退费/结转(1-允许)')
    TotalTimes = Column(INTEGER(11), server_default=text("'0'"), comment='次数')
    GoodIds = Column(String(1000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='绑定讲义编号')
    IsRepeatOrder = Column(INTEGER(11), server_default=text("'0'"), comment='同一课程是否允许重复报名(1-允许重复报名)')
    ShiftParentGrade = Column(INTEGER(11), server_default=text("'0'"), comment='年级父级Id【小学，初中,高中】')
    IsOnline = Column(INTEGER(11), server_default=text("'0'"), comment='是否是线上课【1-线上课】')
    CourseCoverImg = Column(String(100), server_default=text("'0'"), comment='课程封面')

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}


class RbCourseSubject(Base):
    __tablename__ = 'rb_course_subject'

    Id = Column(INTEGER(11), primary_key=True)
    SubjectName = Column(String(255, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='科目名称')
    AliasName = Column(String(255, 'utf8mb4_unicode_ci'), comment='科目别名')
    SubjectIcon = Column(String(255, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='科目图标')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
                        comment='更新时间')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校Id')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='状态')
    GUIDStr = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='课程分类GUID编号')
    AliasNameReview = Column(String(255, 'utf8mb4_unicode_ci'), comment='审核用的科目别名')

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}


class RbCourseCategory(Base):
    __tablename__ = 'rb_course_category'

    CateId = Column(INTEGER(11), primary_key=True, comment='主键（课程分类编号）')
    ParentId = Column(INTEGER(11), server_default=text("'0'"), comment='父级编号')
    CateName = Column(String(255, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='课程分类名称')
    SortNum = Column(INTEGER(11), server_default=text("'0'"), comment='排序')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校Id')
    CourseSubject = Column(INTEGER(11), server_default=text("'0'"), comment='所属科目')
    GUIDStr = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='课程分类GUID编号')
    Mall_Product_Category_Id = Column(INTEGER(11), server_default=text("'0'"), comment='商城表RB_Product_Category的Id')

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}


class RbCourseConfig(Base):
    __tablename__ = 'rb_course_config'

    Id = Column(INTEGER(11), primary_key=True, comment='主键编号')
    ConfigType = Column(INTEGER(11), comment='配置类型')
    ConfigName = Column(String(500), server_default=text("''"), comment='配置名称')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校编号')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态(0-正常,1-禁用)')
    GUIDStr = Column(String(255), server_default=text("''"), comment='GUID编号')
    ConfigDesc = Column(String(500), comment='描述')
    Mall_Product_Category_Id = Column(INTEGER(11), server_default=text("'0'"), comment='商城表RB_Product_Category的Id')
    IsCalcRenew = Column(INTEGER(11), server_default=text("'0'"), comment='是否计入续报(1-统计,0-不统计)')
    IsCalcHourFee = Column(INTEGER(11), server_default=text("'0'"), comment='是否计入课时费(1-统计,0-不统计)')
    ParentId = Column(INTEGER(11), server_default=text("'0'"), comment='父级节点编号')
    IsStaticReport = Column(INTEGER(11), server_default=text("'0'"), comment='是否统计营收报表(1-统计)')

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}


class RbTeacher(Base):
    __tablename__ = 'rb_teacher'

    TId = Column(INTEGER(11), primary_key=True, comment='主键(教师编号)')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校Id')
    TeacherName = Column(String(50, 'utf8mb4_unicode_ci'), comment='讲师姓名')
    TeacherTel = Column(String(30, 'utf8mb4_unicode_ci'), comment='讲师手机号码')
    TeacherHead = Column(String(200, 'utf8mb4_unicode_ci'), comment='讲师头像')
    TeacherIcon = Column(String(200, 'utf8mb4_unicode_ci'), comment='讲师形象照')
    TeacherSay = Column(String(100, 'utf8mb4_unicode_ci'), comment='讲师营销语')
    TeacherIntro = Column(MEDIUMTEXT, comment='讲师介绍')
    Status = Column(INTEGER(11))
    AuditStatus = Column(INTEGER(255), server_default=text("'0'"), comment='审核状态(1-审核中,2-审核通过,3-审核不通过)')
    AuditRemark = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='审核备注')
    IsShow = Column(INTEGER(255), server_default=text("'0'"), comment='显示状态(1-显示，0-隐藏)')
    IsRecommend = Column(INTEGER(255), server_default=text("'0'"), comment='推荐状态(1-已推荐，0-未推荐)')
    SortNum = Column(INTEGER(11), server_default=text("'0'"), comment='排序')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    TeachTag = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='教师标签')
    Dept_Id = Column(INTEGER(11), server_default=text("'0'"), comment='部门编号')
    Post_Id = Column(INTEGER(11), server_default=text("'0'"), comment='岗位编号')
    IDCard = Column(String(50, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='身份证号码')
    Sex = Column(INTEGER(11), server_default=text("'0'"), comment='性别(0-男,1-女)')
    EntryTime = Column(DateTime, comment='入职时间')
    Address = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='地址')
    BirthDate = Column(DateTime, comment='生日')
    LeaveStatus = Column(INTEGER(11), server_default=text("'0'"), comment='在职状态(1-在职，2-兼职，3-临时工，4-离职)')
    LeaveTime = Column(DateTime, comment='离职时间')
    Education = Column(INTEGER(11), server_default=text("'0'"), comment='学历')
    BaseStuNum = Column(INTEGER(11), server_default=text("'0'"), comment='带班基础人数')
    Email = Column(String(60, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='邮箱')
    BaseHourFee = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='基础课时费')
    Nationality = Column(String(50, 'utf8mb4_unicode_ci'), comment='国籍')
    ForeignersUrl = Column(String(255, 'utf8mb4_unicode_ci'), comment='国籍图片')
    Specialty = Column(String(500, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='老师特长')
    BaseHoursEnabled = Column(INTEGER(11), server_default=text("'1'"), comment='是否启用基础课时   1是')
    EnableTime = Column(String(20, 'utf8mb4_unicode_ci'), comment='启用时间 (存到月)')
    UserRole = Column(INTEGER(11), server_default=text("'0'"), comment='用户角色(1-市场人员，2-课程顾问)')
    IsTenCccUser = Column(INTEGER(11), server_default=text("'0'"), comment='电话客服（0，不是，1是）')
    BaseHoursAdd = Column(INTEGER(11), server_default=text("'0'"), comment='增加的基础课时')
    GUIDStr = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='员工GUID编号')
    ClassInUID = Column(INTEGER(11), server_default=text("'0'"), comment='classIn对应账号ID')
    ClassInTID = Column(INTEGER(11), server_default=text("'0'"), comment='机构和老师的关系 ID')
    TeacherQrCode = Column(String(255, 'utf8mb4_unicode_ci'), comment='教师二维码')

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}


class RbStudentTempinvitation(Base):
    __tablename__ = 'rb_student_tempinvitation'

    Id = Column(INTEGER(11), primary_key=True, comment='主键(调课编号)')
    ClassId = Column(INTEGER(11), server_default=text("'0'"), comment='班级编号')
    CourseId = Column(INTEGER(11), server_default=text("'0'"), comment='课程编号')
    OrderId = Column(INTEGER(11), comment='订单编号')
    OrderGuestId = Column(INTEGER(11), server_default=text("'0'"), comment='订单学员编号')
    ClassPlanId = Column(INTEGER(11), server_default=text("'0'"), comment='上课计划Id')
    ClassTimeId = Column(INTEGER(11), server_default=text("'0'"), comment='计划时间id')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校编号')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, comment='更新时间')
    Remarks = Column(String(2000, 'utf8mb4_unicode_ci'), comment='备注')
    Status = Column(INTEGER(11), comment='状态')
    InvitationType = Column(INTEGER(11), comment='邀请类型(1-新增学员，2-移除学员)')
    StuId = Column(INTEGER(11), server_default=text("'0'"), comment='学生编号')
    SourcePlanId = Column(INTEGER(11), server_default=text("'0'"), comment='原上课计划Id')
    SourceClassId = Column(INTEGER(11), server_default=text("'0'"), comment='目标班级编号')


class RbClassPlan(Base):
    __tablename__ = 'rb_class_plan'

    ClassPlanId = Column(INTEGER(11), primary_key=True, comment='计划编号')
    ClassId = Column(INTEGER(11), server_default=text("'0'"), comment='班级编号')
    ClassDate = Column(DateTime, comment='上课日期')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='状态')
    ClassRoomId = Column(INTEGER(11), server_default=text("'0'"), comment='教室编号')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校编号')
    TeacherId = Column(INTEGER(11), server_default=text("'0'"), comment='教师编号')
    BeiKeStatus = Column(INTEGER(11), server_default=text("'0'"), comment='0-未备课，1-已备课')
    FanKuiStatus = Column(INTEGER(11), server_default=text("'0'"), comment='0-未反馈，1-已反馈')
    ParentFanKuiStatus = Column(INTEGER(11), server_default=text("'0'"), comment='主管反馈状态0-未反馈，1-已反馈')
    RepeatPlanIds = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"),
                           comment='上课计划重复的计划编号')
    PlanType = Column(INTEGER(11), server_default=text("'0'"), comment='计划类型  1正常班   2滚动开班')
    CourseId = Column(INTEGER(11), server_default=text("'0'"), comment='课程ID')
    GUIDStr = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='GUID编号')
    Finished = Column(INTEGER(11), server_default=text("'0'"), comment='上课状态(1-已上课，0-未上课)')


class RbClassLog(Base):
    __tablename__ = 'rb_class_log'

    CLogId = Column(INTEGER(11), primary_key=True, comment='日志编号')
    LogType = Column(INTEGER(11), server_default=text("'0'"), comment='日志类型(1-新增学员）')
    LogContent = Column(Text(collation='utf8mb4_unicode_ci'), comment='日志内容')
    ClassId = Column(INTEGER(11), server_default=text("'0'"), comment='班级编号')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='校区编号')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, comment='创建时间')
