from sqlalchemy import Column, DECIMAL, DateTime, Float, String, text, VARCHAR
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class RbStudent(Base):
    __tablename__ = 'rb_student'

    StuId = Column(INTEGER(11), primary_key=True, comment='主键(学生Id)')
    StuName = Column(String(255, 'utf8mb4_unicode_ci'), comment='学生昵称')
    StuTel = Column(String(255, 'utf8mb4_unicode_ci'), comment='学生电话')
    StuIcon = Column(String(255, 'utf8mb4_unicode_ci'), comment='学生头像')
    StuSex = Column(INTEGER(11), server_default=text("'0'"), comment='学生性别 (0-男,1-女)')
    StuBirth = Column(DateTime, comment='学生出生日期')
    ProviceId = Column(INTEGER(11), server_default=text("'0'"), comment='省份Id')
    CityId = Column(INTEGER(11), server_default=text("'0'"), comment='城市Id')
    AreaId = Column(INTEGER(11), server_default=text("'0'"), comment='区县Id')
    ProvinceName = Column(String(45, 'utf8mb4_unicode_ci'), comment='省份')
    CityName = Column(String(45, 'utf8mb4_unicode_ci'), comment='城市')
    AreaName = Column(String(45, 'utf8mb4_unicode_ci'), comment='区县')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校编号')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    IsDisable = Column(INTEGER(11), server_default=text("'1'"), comment='是否禁用(0-禁用,1-启用)')
    StuStatus = Column(INTEGER(11), server_default=text("'1'"), comment='1-未学习，2-已退课，3-学习中')
    Interest = Column(INTEGER(11), server_default=text("'0'"), comment='兴趣爱好')
    JapanBaseInfo = Column(INTEGER(11), server_default=text("'0'"), comment='日语基础信息')
    CustomerId = Column(INTEGER(11), server_default=text("'0'"), comment='同业客户编号')
    StuProfession = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='职业')
    StuEducation = Column(INTEGER(11), server_default=text("'0'"), comment='最高学历')
    StuPurpose = Column(INTEGER(11), server_default=text("'0'"), comment='学习目的')
    StuSource = Column(INTEGER(11), server_default=text("'0'"), comment='客人来源')
    StuAddress = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='学员地址')
    StuContract = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='联系人')
    StuContractMobile = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='联系电话')
    StuIDCard = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='身份证')
    StuIDCardAddress = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='身份证居住地')
    CreateType = Column(INTEGER(11), server_default=text("'0'"), comment='录入方式(1-销售录入，2-同行录入)')
    StuStage = Column(INTEGER(11), server_default=text("'0'"), comment='客户阶段')
    StuChannel = Column(INTEGER(11), server_default=text("'0'"), comment='收客渠道(见枚举)')
    PlatformName = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='第三方平台名称')
    StuSourceId = Column(INTEGER(11), server_default=text("'0'"), comment='来源编号')
    QQ = Column(String(50, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='QQ号')
    WeChatNo = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='微信号')
    StuType = Column(INTEGER(11), server_default=text("'0'"), comment='客户类型')
    StuNeeds = Column(INTEGER(11), server_default=text("'0'"), comment='客户需求')
    StuRealMobile = Column(MEDIUMTEXT, comment='学员真实电话号码')
    ConsultDate = Column(DateTime, comment='咨询日期')
    PlanPrice = Column(String(200, 'utf8mb4_unicode_ci'), comment='规划课程及报价')
    BaseCondition = Column(String(600, 'utf8mb4_unicode_ci'), comment='基本情况')
    DemandPoint = Column(String(600, 'utf8mb4_unicode_ci'), comment='需求点')
    ResistPoint = Column(String(600, 'utf8mb4_unicode_ci'), comment='抗拒点')
    ConsultingResults = Column(String(600, 'utf8mb4_unicode_ci'), comment='咨询结果')
    FirstEnrollDate = Column(DateTime, comment='首次报名时间')
    AdvisorStatus = Column(INTEGER(11), server_default=text("'0'"), comment='课程顾问跟进状态')
    AdvisorWinRate = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='课程顾问胜率')
    AdvisorExpectDate = Column(String(50, 'utf8mb4_unicode_ci'), server_default=text("''"),
                               comment='课程顾问期望达成协议日期')
    GUIDStr = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='学员GUID编号')
    SchoolClass = Column(String(255, 'utf8mb4_unicode_ci'), comment='公立学校就读班级')
    SchoolSource = Column(String(255, 'utf8mb4_unicode_ci'), comment='如何知道进阶思维')
    E_WalletMoney = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='电子钱包余额')
    Serial = Column(String(20, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='学号')
    ClassInAccount = Column(String(255, 'utf8mb4_unicode_ci'), comment='ClassIn账号')
    ClassInUID = Column(INTEGER(11), server_default=text("'0'"), comment='classIn对应账号ID')
    GradeType = Column(INTEGER(11), server_default=text("'0'"), comment='学员年级')
    Remark = Column(String(500, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='备注')


class RbAccount(Base):
    __tablename__ = 'rb_account'

    Id = Column(INTEGER(11), primary_key=True, comment='主键(账号编号)')
    Account = Column(String(50, 'utf8mb4_unicode_ci'), comment='账号')
    Password = Column(String(100, 'utf8mb4_unicode_ci'), comment='密码')
    AccountType = Column(INTEGER(11), server_default=text("'0'"), comment='账号类型(1-管理端，2,-教师端，3-助教，4-学生)')
    AccountId = Column(INTEGER(11), server_default=text("'0'"), comment='关联Id')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, comment='修改时间')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校编号')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态')
    AnnualLeaveDay = Column(Float(11, True), server_default=text("'0.0'"), comment='年假天数')
    DirectSupervisor = Column(INTEGER(11), server_default=text("'0'"), comment='直接主管 （OKR专用）')
    OpenId = Column(String(50, 'utf8mb4_unicode_ci'), comment='微信唯一识别码')
    ActivationStatus = Column(INTEGER(11), server_default=text("'0'"), comment='0-未激活，1-已激活')
    WorkUserId = Column(String(50, 'utf8mb4_unicode_ci'), comment='企业微信ID')
    UnionId = Column(String(11, 'utf8mb4_unicode_ci'), comment='微信UnionId')
    IsWorkTransfer = Column(INTEGER(11), server_default=text("'0'"), comment='是否已客户转移  1是')

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}


class RbAccountHk(Base):
    __tablename__ = 'rb_account_hk'

    UniqueId = Column(INTEGER(11), primary_key=True, comment='主键ID')
    Id = Column(INTEGER(11), server_default=text("'0'"), comment='对应TeacherId 或者  StuId')
    Account = Column(VARCHAR(255), comment='登录账号')
    Password = Column(String(255), comment='密码')
    AccountType = Column(INTEGER(11), comment='类型  1老师  2学生')
    AccountId = Column(INTEGER(11), server_default=text("'0'"), comment='对应 老师/学生UID')
    Status = Column(INTEGER(11), comment='删除状态')
    OpenId = Column(VARCHAR(50), comment='微信唯一识别码')
    UnionId = Column(VARCHAR(11), comment='微信UnionId')
