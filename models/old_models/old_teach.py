
from sqlalchemy import Column, DECIMAL, DateTime, Float, String, Text, text, JSON
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, VARCHAR, LONGTEXT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class RbTeacher(Base):
    __tablename__ = 'rb_teacher'

    TId = Column(INTEGER(11), primary_key=True, comment='主键(教师编号)')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校Id')
    TeacherName = Column(String(50, 'utf8mb4_unicode_ci'), comment='讲师姓名')
    TeacherTel = Column(String(30, 'utf8mb4_unicode_ci'), comment='讲师手机号码')
    TeacherHead = Column(String(200, 'utf8mb4_unicode_ci'), comment='讲师头像')
    TeacherIcon = Column(String(200, 'utf8mb4_unicode_ci'), comment='讲师形象照')
    TeacherSay = Column(String(100, 'utf8mb4_unicode_ci'), comment='讲师营销语')
    TeacherIntro = Column(MEDIUMTEXT, comment='讲师介绍')
    Status = Column(INTEGER(11))
    AuditStatus = Column(INTEGER(255), server_default=text("'0'"), comment='审核状态(1-审核中,2-审核通过,3-审核不通过)')
    AuditRemark = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='审核备注')
    IsShow = Column(INTEGER(255), server_default=text("'0'"), comment='显示状态(1-显示，0-隐藏)')
    IsRecommend = Column(INTEGER(255), server_default=text("'0'"), comment='推荐状态(1-已推荐，0-未推荐)')
    SortNum = Column(INTEGER(11), server_default=text("'0'"), comment='排序')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    TeachTag = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='教师标签')
    Dept_Id = Column(INTEGER(11), server_default=text("'0'"), comment='部门编号')
    Post_Id = Column(INTEGER(11), server_default=text("'0'"), comment='岗位编号')
    IDCard = Column(String(50, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='身份证号码')
    Sex = Column(INTEGER(11), server_default=text("'0'"), comment='性别(0-男,1-女)')
    EntryTime = Column(DateTime, comment='入职时间')
    Address = Column(String(200, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='地址')
    BirthDate = Column(DateTime, comment='生日')
    LeaveStatus = Column(INTEGER(11), server_default=text("'0'"), comment='在职状态(1-在职，2-兼职，3-临时工，4-离职)')
    LeaveTime = Column(DateTime, comment='离职时间')
    Education = Column(INTEGER(11), server_default=text("'0'"), comment='学历')
    BaseStuNum = Column(INTEGER(11), server_default=text("'0'"), comment='带班基础人数')
    Email = Column(String(60, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='邮箱')
    BaseHourFee = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='基础课时费')
    Nationality = Column(String(50, 'utf8mb4_unicode_ci'), comment='国籍')
    ForeignersUrl = Column(String(255, 'utf8mb4_unicode_ci'), comment='国籍图片')
    Specialty = Column(String(500, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='老师特长')
    BaseHoursEnabled = Column(INTEGER(11), server_default=text("'1'"), comment='是否启用基础课时   1是')
    EnableTime = Column(String(20, 'utf8mb4_unicode_ci'), comment='启用时间 (存到月)')
    UserRole = Column(INTEGER(11), server_default=text("'0'"), comment='用户角色(1-市场人员，2-课程顾问)')
    IsTenCccUser = Column(INTEGER(11), server_default=text("'0'"), comment='电话客服（0，不是，1是）')
    BaseHoursAdd = Column(INTEGER(11), server_default=text("'0'"), comment='增加的基础课时')
    GUIDStr = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='员工GUID编号')
    ClassInUID = Column(INTEGER(11), server_default=text("'0'"), comment='classIn对应账号ID')
    ClassInTID = Column(INTEGER(11), server_default=text("'0'"), comment='机构和老师的关系 ID')
    TeacherQrCode = Column(String(255, 'utf8mb4_unicode_ci'), comment='教师二维码')

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}
