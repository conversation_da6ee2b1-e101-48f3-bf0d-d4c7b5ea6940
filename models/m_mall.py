# coding: utf-8
from sqlalchemy import Column, DECIMAL, Date, DateTime, Float, JSON, String, Table, Text, text
from sqlalchemy.dialects.mysql import INTEGER, LONGTEXT, MEDIUMTEXT, VARCHAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class MallAccount(Base):
    __tablename__ = 'mall_account'

    UserId = Column(INTEGER(11), primary_key=True)
    Uid = Column(INTEGER(11), comment='rb_account表Id')
    StuId = Column(INTEGER(11), comment='学生表')
    OpenId = Column(String(45), comment='微信小程序小课堂openId')
    CreateTime = Column(DateTime)
    DeliveryAddress = Column(String(1000), comment='收货地址')
    AvatarImg = Column(String(255), comment='头像')
    PhoneNumber = Column(String(45), comment='电话')
    RealName = Column(String(45), comment='真实姓名')


class MallComplain(Base):
    __tablename__ = 'mall_complain'

    Id = Column(INTEGER(11), primary_key=True, comment='投诉主键编号')
    ComplainCode = Column(String(50), server_default=text("''"), comment='投诉编号')
    ComplainTypeId = Column(INTEGER(11), server_default=text("'0'"), comment='投诉类型')
    ComplainObject = Column(String(500), server_default=text("''"), comment='投诉对象')
    ComplainContent = Column(String(2000), server_default=text("''"), comment='投诉内容')
    SeverityLevel = Column(INTEGER(11), server_default=text("'0'"), comment='严重程度')
    DisposeStatus = Column(INTEGER(11), server_default=text("'0'"), comment='处理状态')
    NeedResponse = Column(INTEGER(4), comment='是否需要回复')
    ComplainResponse = Column(String(2000), server_default=text("''"), comment='回复')
    NextPersionId = Column(INTEGER(11), server_default=text("'0'"), comment='当前处理人员')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态(0-正常,1-禁用)')


class MallComplainConfig(Base):
    __tablename__ = 'mall_complain_config'

    Id = Column(INTEGER(11), primary_key=True, comment='投诉类型编号')
    Name = Column(String(255), server_default=text("''"), comment='投诉配置名称')
    NextUserId = Column(INTEGER(11), server_default=text("'0'"), comment='初始处理人')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态(0-正常,1-禁用)')
    Viewer = Column(String(255))


class MallMerchantConfig(Base):
    __tablename__ = 'mall_merchant_config'

    Id = Column(INTEGER(11), primary_key=True)
    CmbMerId = Column(String(125))
    CmbUserId = Column(String(125))
    CmdAppId = Column(String(125))
    CmdAppSecret = Column(String(125))
    AppId = Column(String(45))
    Sk = Column(LONGTEXT)
    Pk = Column(LONGTEXT)
    NotifyUrl = Column(String(255))
    CmbBaseUrl = Column(String(255))
    AppSecret = Column(String(255))
    Type = Column(INTEGER(4), comment='1 微信 2 支付宝')
    Status = Column(String(255))
    Enable = Column(INTEGER(4))
    MerchantName = Column(String(45))
    Env = Column(String(45))
    AlipaySk = Column(LONGTEXT)
    AlipayPk = Column(LONGTEXT)


class MallOrder(Base):
    __tablename__ = 'mall_order'

    Id = Column(INTEGER(11), primary_key=True, comment='小课堂订单信息id')
    ShoppingAddressDetail = Column(String(125), comment='收货地址')
    ShoppingMobile = Column(String(45), comment='收货电话')
    ShoppingConsignee = Column(String(45), comment='收货人')
    BuyMsg = Column(String(255), comment='订单备注')
    OfferId = Column(INTEGER(11), comment='报价单Id')
    OrderNo = Column(String(125), comment='订单号')
    MerchantsNo = Column(String(125), comment='商户单号')
    StuId = Column(INTEGER(11))
    CreateTime = Column(DateTime)
    SnapShot = Column(JSON, comment='交易快照')
    ShoppingAddressProvince = Column(String(45))
    ShoppingAddressCity = Column(String(45))
    ShoppingAddressDistrict = Column(String(45))
    Status = Column(INTEGER(11), server_default=text("'0'"))
    PayState = Column(INTEGER(11), server_default=text("'0'"))
    UpdateTime = Column(DateTime)
    CmbOrderId = Column(String(125), comment='招行id')
    CmbTradeState = Column(String(45), comment='定时任务状态')
    PayOpenId = Column(String(45), comment='支付的微信openid')
    PayType = Column(String(45), comment='支付平台')
    thirdOrderId = Column(String(125), comment='三方订单号')
    OrderFrom = Column(INTEGER(4), server_default=text("'2'"))
    MerchantConfigId = Column(INTEGER(11), comment=' 订单支付商户')


class MallProductsBak(Base):
    __tablename__ = 'mall_products_bak'

    ProductId = Column(INTEGER(11), primary_key=True)
    Name = Column(String(255), comment='商品名称')
    CarouselImage = Column(String(4000), comment='轮播图  json 格式  第一张为封面图')
    VideoAddress = Column(String(255), comment='视频地址')
    CustomShareTitles = Column(String(255), comment='自定义分享标题')
    ProductStatus = Column(INTEGER(11), server_default=text("'0'"), comment='商品状态   1销售中  2下架中')
    InventoryNum = Column(INTEGER(11), server_default=text("'0'"), comment='库存数量')
    SellingPrice = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='售价')
    OriginalPrice = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='原价')
    Unit = Column(String(20), comment='单位')
    SalesNum = Column(INTEGER(11), server_default=text("'0'"), comment='已售出数量')
    ProductNumbers = Column(String(50), comment='商品货号')
    IntegralPresentType = Column(INTEGER(11), server_default=text("'0'"), comment='赠送类型  1固定值  2百分比')
    ProductDetails = Column(Text, comment='商品详情')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态')
    TenantId = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='商户号')
    MallBaseId = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='小程序id')
    CreateDate = Column(DateTime)
    UpdateDate = Column(DateTime)
    productType = Column(INTEGER(11), server_default=text("'0'"), comment='商品类型  枚举')
    Advertising = Column(String(2000), comment='广告词')
    SubName = Column(String(255), comment='副标题')
    ShelvesDate = Column(DateTime, comment='自动上架时间')
    DownDate = Column(DateTime, comment='自动下架时间')
    CourseLable = Column(String(1000), comment='课程标签')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    GoodsEduType = Column(INTEGER(11), server_default=text("'0'"),
                          comment='教育商品类型   0 正常商品   1咖啡劵   2耗材类  3教室类')
    MaterialId = Column(INTEGER(11), server_default=text("'0'"), comment='耗材对应的ID')
    EduJsonData = Column(MEDIUMTEXT, comment='教育相关信息JSON对象')
    EduGradeId = Column(INTEGER(11), server_default=text("'0'"), comment='进阶小课堂年级Id')
    EduClassTypeId = Column(INTEGER(11), server_default=text("'0'"), comment='进阶小课堂班型Id')
    EduTeacherId = Column(INTEGER(11), server_default=text("'0'"), comment='进阶小课堂老师Id')
    IsShow = Column(INTEGER(11), server_default=text("'1'"), comment='是否显示商品(1-显示，0-不显示)')


class MallServiceGuarantee(Base):
    __tablename__ = 'mall_service_guarantee'

    GuaranteeId = Column(INTEGER(11), primary_key=True, comment='服务保障表id')
    Title = Column(String(45), comment='标题')
    Desc = Column(LONGTEXT, comment='描述')
    Status = Column(INTEGER(4), server_default=text("'0'"), comment='状态 1-删除')
    CreatTime = Column(DateTime)
    Type = Column(INTEGER(4), comment='1-购课服务；2-退费服务')


class MallSettingsAccount(Base):
    __tablename__ = 'mall_settings_account'

    TenantId = Column(INTEGER(11), primary_key=True, comment='商户号')
    Account = Column(String(20), comment='账号')
    Password = Column(String(50), comment='密码')
    Name = Column(String(255), comment='姓名/企业名称')
    MobilePhone = Column(String(20), comment='手机号码')
    WeChatNum = Column(String(100), comment='微信号')
    ApplyReason = Column(String(2000), comment='申请原因')
    IDCardPositiveImg = Column(String(255), comment='身份证正面图片')
    IDCardReverseImg = Column(String(255), comment='身份证背面图片')
    BusinessLicenseImg = Column(String(255), comment='营业执照图片')
    CreateDate = Column(DateTime)
    IsEffective = Column(INTEGER(255), server_default=text("'0'"), comment='是否永久有效(0-默认，1-永久有效)')
    AccountValidate = Column(DateTime, comment='账号有效期')
    CreateMiniPrograme = Column(INTEGER(11), server_default=text("'0'"), comment='可创建小程序数量')
    AccountStatus = Column(INTEGER(11), server_default=text("'0'"),
                           comment='账号状态(0-默认，1-提交申请，2-审核通过，3-审核拒绝）')


class MallSettingsCampus(Base):
    __tablename__ = 'mall_settings_campus'

    CampusId = Column(INTEGER(11), primary_key=True)
    CampusName = Column(String(45))
    Address = Column(String(255))
    Phone = Column(String(45))
    CampusType = Column(INTEGER(11), comment='图片类型  1 - 校区,2 - 默认客服')
    QrCode = Column(String(255))
    Latitude = Column(Float(10), comment='纬度')
    Longitude = Column(Float(10), comment='经度')
    SchoolPinyin = Column(String(255))


class MallSettingsImg(Base):
    __tablename__ = 'mall_settings_img'

    ImgId = Column(INTEGER(11), primary_key=True)
    ImgType = Column(INTEGER(4), comment='图片类型 1 - 首页封面（师资） ,2 - 首页banner, 3 - 课程列表页banner，')
    ImgUrl = Column(String(255))
    Comment = Column(String(255), comment='备注')
    CreateTime = Column(DateTime)
    PathUrl = Column(String(255), comment='跳转链接')


class MallSettingsMiniprogram(Base):
    __tablename__ = 'mall_settings_miniprogram'

    SettingId = Column(INTEGER(11), primary_key=True, comment='Id')
    ProgramName = Column(String(255), comment='程序名称')
    CreateDate = Column(DateTime, comment='创建时间')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='状态(0-正常，1-禁用)')
    MiniAppId = Column(String(100), comment='小程序AppId')
    MiniAppSecret = Column(String(100), comment='小程序AppSecret')
    WeChatPayMerchants = Column(String(50), comment='微信支付商户号')
    WeChatApiSecret = Column(String(100), comment='微信支付Api密钥')
    WeChatPayCertificateUrl = Column(String(500), comment='支付证书路径')
    WeChatPayCertificate = Column(String(1000), comment='微信支付证书')
    WeChatPayPrivateKey = Column(String(1000), comment='微信支付私钥')
    WechatPublicQr = Column(String(255), comment='公众号二维码')
    ServiceQr = Column(String(255), comment='服务二维码')
    About = Column(LONGTEXT, comment='关于进阶')


class MallShoppingCart(Base):
    __tablename__ = 'mall_shopping_cart'

    Id = Column(INTEGER(11), primary_key=True)
    ClassId = Column(INTEGER(11))
    UserId = Column(INTEGER(11))
    Status = Column(INTEGER(4), comment='转态：1-删除；0-正常')
    CreateTime = Column(DateTime, comment='创建时间')
