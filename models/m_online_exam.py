from datetime import datetime
from sqlalchemy import Column, DECIMAL, DateTime, Float, String, Text, text, Date, JSON
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, VARCHAR, LONGTEXT
from sqlalchemy.ext.declarative import declarative_base

import settings

Base = declarative_base()


class ErpOnlinePaper(Base):
    __tablename__ = 'erp_online_paper'

    id = Column(INTEGER(11), primary_key=True)
    paper_name = Column(String(255))
    create_by = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    is_active = Column(INTEGER(4))
    subject_id = Column(INTEGER(4))
    grade_id = Column(INTEGER(4))
    is_lock = Column(INTEGER(4))
    p_grade_id = Column(INTEGER(4))
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    passing_score = Column(Float)
    total_score = Column(Float)


class ErpOnlinePaperQuestion(Base):
    __tablename__ = 'erp_online_paper_question'

    id = Column(INTEGER(11), primary_key=True)
    paper_id = Column(INTEGER(11))
    question_id = Column(INTEGER(11))
    sort = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpOnlineQuestion(Base):
    __tablename__ = 'erp_online_question'

    id = Column(INTEGER(11), primary_key=True)
    content = Column(LONGTEXT, comment='题目内容')
    difficulty_level = Column(INTEGER(4), comment='难度')
    score = Column(Float(10), comment='分值')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    create_by = Column(INTEGER(4))
    knowledge_point = Column(String(255), comment='知识点')
    question_type = Column(INTEGER(4), comment='题目类型')
    analysis = Column(LONGTEXT, comment='解析')


class ErpOnlineQuestionOption(Base):
    __tablename__ = 'erp_online_question_option'

    id = Column(INTEGER(11), primary_key=True)
    question_id = Column(INTEGER(11), comment='试题id')
    option_content = Column(LONGTEXT)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    correct = Column(INTEGER(4))
    answer = Column(LONGTEXT)
    option_score = Column(Float(10), comment='选项分值')


class ErpOnlineStuPaper(Base):
    __tablename__ = 'erp_online_stu_paper'

    id = Column(INTEGER(11), primary_key=True)
    paper_id = Column(INTEGER(11))
    erp_stu_id = Column(INTEGER(11), comment='erp的学生id')
    current_process = Column(INTEGER(4), comment='第几道')
    all_process = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpOnlineStuScore(Base):
    __tablename__ = 'erp_online_stu_score'

    id = Column(INTEGER(11), primary_key=True)
    paper_stu_id = Column(INTEGER(11))
    paper_question_id = Column(INTEGER(11))
    choice_option_id = Column(INTEGER(4))
    is_correct = Column(INTEGER(4))
    stu_score = Column(Float)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    answer = Column(String(255))


class ErpOnlinePaperCourse(Base):
    __tablename__ = 'erp_online_paper_course'

    id = Column(INTEGER(11), primary_key=True, index=True)
    paper_id = Column(INTEGER(11))
    min_score = Column(INTEGER(4))
    max_score = Column(INTEGER(4))
    course_name = Column(String(255))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    teacher_msg = Column(String(255))
