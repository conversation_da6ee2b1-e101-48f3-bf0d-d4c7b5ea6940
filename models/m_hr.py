from datetime import datetime
from sqlalchemy import Column, DECIMAL, DateTime, Float, String, Text, text, Date, JSON
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, VARCHAR, LONGTEXT
from sqlalchemy.ext.declarative import declarative_base

import settings

Base = declarative_base()


class ErpHrPlan(Base):
    __tablename__ = 'erp_hr_plan'

    id = Column(INTEGER(11), primary_key=True)
    title = Column(String(255))
    create_by = Column(INTEGER(11))
    desired_num = Column(INTEGER(11))
    entry_num = Column(INTEGER(11), default=0)
    employee_type = Column(INTEGER(4))
    push_date = Column(Date)
    desc = Column(String(1000))
    end_date = Column(Date)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpHrAccount(Base):
    __tablename__ = 'erp_hr_account'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    hr_plan_id = Column(INTEGER(11))
    introducer = Column(INTEGER(11))
    source = Column(String(45))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    comments = Column(String(255), comment='备注')



class ErpHrTalentPool(Base):
    __tablename__ = 'erp_hr_talent_pool'

    id = Column(INTEGER(11), primary_key=True)
    name = Column(String(45), comment='姓名')
    interview_time = Column(DateTime, comment='面试时间')
    hr_plan_id = Column(INTEGER(11), comment='招聘计划id')
    is_accept = Column(INTEGER(4), comment='是否入职')
    comments = Column(String(255), comment='备注')
    attachment = Column(JSON, comment='附件或简历，列表')
    update_by = Column(INTEGER(11), comment='更新人')
    create_by = Column(INTEGER(11), comment='创建人')
    update_time = Column(DateTime, default=datetime.now, comment='更新时间')
    create_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
