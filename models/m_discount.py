# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, DateTime, String, text, Float
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpStudentCouponCourse(Base):
    __tablename__ = 'erp_student_coupon_course'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    coupon_id = Column(INTEGER(11))
    course_id = Column(INTEGER(11), nullable=False)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)

class ErpStudentDiscountCoupon(Base):
    __tablename__ = 'erp_student_discount_coupon'

    id = Column(INTEGER(11), primary_key=True)
    stu_id = Column(INTEGER(11))
    amount = Column(DECIMAL(10, 2))
    expired_time = Column(DateTime)
    limit_money = Column(DECIMAL(10, 2))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    disable = Column(INTEGER(4), default=0)
    status = Column(INTEGER(4), comment='0 未激活 1 可用（未使用） 2 已使用 3已失效')
    is_universal = Column(INTEGER(4), comment='是否通用')


class ErpStudentDiscountFixed(Base):
    __tablename__ = 'erp_student_discount_fixed'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    stu_id = Column(INTEGER(11))
    discount_rate = Column(Float(6))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    disable = Column(INTEGER(4), default=0)
