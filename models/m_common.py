# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, DateTime, String, Text, text, JSON, Float
from sqlalchemy.dialects.mssql import TINYINT
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, YEAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpLog(Base):
    __tablename__ = 'erp_log'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    action = Column(String(45))
    content = Column(JSON)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    log_type = Column(INTEGER(4))
    obj_id = Column(INTEGER(11))


class ErpClassinConfig(Base):
    __tablename__ = 'erp_classin_config'

    Id = Column(INTEGER(11), primary_key=True)
    SID = Column(String(255), comment='classIn API_ID')
    SECRET = Column(String(255), comment='秘钥')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='归属集团ID')
    ClassroomSettingId = Column(INTEGER(11), server_default=text("'0'"), comment='教室设置 ID （课程使用）')
    AllowAddFriend = Column(INTEGER(11), server_default=text("'0'"),
                            comment='是否允许班级成员在群里互相添加好友，0=不允许，1=允许   (课程使用)')
    AllowStudentModifyNickname = Column(INTEGER(11), server_default=text("'0'"),
                                        comment='是否允许学生在群里修改其班级昵称，0=不允许，1=允许  (课程使用)')
    TeachMode = Column(INTEGER(11), server_default=text("'1'"), comment='教学模式，1=在线教室，2=智慧教室')
    IsAutoOnstage = Column(INTEGER(11), server_default=text("'0'"),
                           comment='0=自动，1=不自动，默认为0，所有非1的数字,都会当成0处理\t学生进入教室是否自动上台')
    SeatNum = Column(INTEGER(11), server_default=text("'6'"), comment='默认为6，最大上限值调整为12\t学生上台数')
    IsHd = Column(INTEGER(11), server_default=text("'0'"),
                  comment='0=非高清，1=高清，2=全高清，默认为0，除了1和2，所有非0的数字,都会当成0处理\t是否高清\t目前仅支持 1V1 或 1V6 高清、全高清')
    IsDc = Column(INTEGER(11), server_default=text("'0'"),
                  comment='双摄模式，是否开启副摄像头，0=不开启，3=开启全高清副摄像头')
    Record = Column(INTEGER(11), server_default=text("'1'"), comment='录课(0 关闭，1 开启)')
    RecordScene = Column(INTEGER(11), server_default=text("'0'"), comment='录制现场(0 关闭，1 开启)')
    Live = Column(INTEGER(11), server_default=text("'0'"), comment='网页直播(0 关闭，1 开启)')
    Replay = Column(INTEGER(11), server_default=text("'0'"), comment='网页回放(0 关闭，1 开启)')
    WatchByLogin = Column(INTEGER(11), server_default=text("'0'"),
                          comment='只允许登录ClassIn账号后才可观看，未登录不可观看，0=未开启，1=开启')
    AllowUnloggedChat = Column(INTEGER(11), server_default=text("'0'"),
                               comment='允许未登录用户参与直播聊天和点赞，0=不允许，1=允许')
    Enable = Column(INTEGER(11), server_default=text("'2'"), comment='是否启用  1是  其他否')
