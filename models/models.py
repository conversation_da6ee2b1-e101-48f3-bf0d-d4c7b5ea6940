from datetime import datetime
from sqlalchemy import Column, DECIMAL, DateTime, Float, String, Text, text, Date, JSON
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, VARCHAR, LONGTEXT
from sqlalchemy.ext.declarative import declarative_base

import settings

Base = declarative_base()


class ErpAccount(Base):
    __tablename__ = 'erp_account'

    id = Column(INTEGER(11), primary_key=True)
    username = Column(String(125))
    password = Column(String(255))
    employee_number = Column(String(11), comment='员工号')
    employee_name = Column(String(255))
    employee_idcard = Column(String(125), comment='身份证')
    employee_gender = Column(INTEGER(11), comment='性别，1 男 2 女')
    employee_education = Column(INTEGER(11), comment='教育程度')
    employee_hire_date = Column(DateTime, comment='受雇日期', default=datetime.now)
    employee_leave_date = Column(DateTime, comment='离职日期')
    employee_birth = Column(DateTime, comment='生日')
    employee_city = Column(String(45), comment='所在城市')
    employee_address = Column(String(255), comment='住址')
    employee_major = Column(String(255), comment='专业')
    leave_reason = Column(INTEGER(4))
    leave_reason_detail = Column(String(255))
    avatar = Column(String(255))
    successor_account_id = Column(INTEGER(11))
    attachment = Column(JSON)
    employee_emergency = Column(String(125), comment='紧急联系电话')
    employee_status = Column(INTEGER(4), comment='1 在职 2 离职交接中 3 离职 4 试用')
    employee_type = Column(INTEGER(4), comment='1 全职 2 兼职')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    qy_wechat_openid = Column(String(125), comment='微信公众号openid')
    qy_wechat_userid = Column(String(125), comment='企微userid')
    qy_wechat_nickname = Column(String(125))
    qy_wechat_position = Column(String(45))
    qy_wechat_direct_leader = Column(JSON)
    qy_wechat_department = Column(JSON)
    disable = Column(INTEGER(4), default=0)
    sync_status = Column(INTEGER(4), default=1, comment='1 等待同步 2 同步成功 3 同步失败')
    sync_msg = Column(String(255), comment='同步消息')
    is_teacher = Column(INTEGER(11), comment='是否是老师')
    level_id = Column(INTEGER(11), comment='职级编号')


class ErpAccountRole(Base):
    __tablename__ = 'erp_account_role'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    role_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpCheckingRecords(Base):
    __tablename__ = 'erp_checking_records'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    employee_number = Column(String(45), comment='员工姓名')
    employee_name = Column(String(255), comment='员工姓名')
    ym = Column(INTEGER(6), comment='年月')
    check_date = Column(Date, comment='考勤日期')
    day_period = Column(INTEGER(4), comment='1 上午 2 下午')
    check_value = Column(String(45), comment='考勤结果')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpSalaryBase(Base):
    __tablename__ = 'erp_salary_base'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    enterprise_id = Column(INTEGER(11), default=0)
    salary_base = Column(Float(10))
    salary_performance = Column(Float(10))
    bank_card_number = Column(String(125))
    bank_sub_name = Column(String(125))
    bank_city = Column(String(125))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    type = Column(INTEGER(4), default=1)
    to_type = Column(INTEGER(4), default=1)
    to_name = Column(String(125))


class ErpSalaryChangeLog(Base):
    __tablename__ = 'erp_salary_change_log'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    change_type = Column(INTEGER(4), default=2, comment='1 调整基础工资 2 调整绩效工资')
    salary_type = Column(INTEGER(4), default=1, comment='1 普通薪资 2 课时费')
    prev_salary = Column(DECIMAL(10, 2))
    current_salary = Column(DECIMAL(10, 2))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))


class ErpEnterprise(Base):
    __tablename__ = 'erp_enterprise'

    id = Column(INTEGER(11), primary_key=True)
    enterprise_name = Column(String(125))
    enterprise_short_name = Column(String(45))
    enterprise_bank_account = Column(String(255))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpJobLevel(Base):
    __tablename__ = 'erp_job_level'

    id = Column(INTEGER(11), primary_key=True)
    level_name = Column(String(45))
    level_desc = Column(String(1000))
    level_type = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    level_item = Column(String(1000))


class ErpAccountLevel(Base):
    __tablename__ = 'erp_account_level'

    id = Column(INTEGER(11), primary_key=True)
    level_id = Column(INTEGER(11))
    account_id = Column(INTEGER(11))
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpPublicSettings(Base):
    __tablename__ = 'erp_public_settings'

    id = Column(INTEGER(11), primary_key=True)
    dict_key = Column(String(125))
    dict_value = Column(JSON)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpSalary(Base):
    __tablename__ = 'erp_salary'

    id = Column(INTEGER(11), primary_key=True)
    ym = Column(INTEGER(6))
    title = Column(String(125))
    salary_status = Column(INTEGER(4))
    comments = Column(String(225))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    admin_certified = Column(INTEGER(4), default=0)


class ErpSalaryDetail(Base):
    __tablename__ = 'erp_salary_detail'

    id = Column(INTEGER(11), primary_key=True)
    salary_id = Column(INTEGER(11))
    account_id = Column(INTEGER(11))
    enterprise_name = Column(String(125))
    actual_salary = Column(DECIMAL(10, 2))
    issued_salary = Column(DECIMAL(10, 2))
    detail = Column(JSON)
    is_send = Column(INTEGER(4))
    send_error = Column(INTEGER(4), default=0)
    send_error_msg = Column(String(225))
    send_time = Column(DateTime)
    is_check = Column(INTEGER(4))
    check_time = Column(DateTime)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpDepartment(Base):
    __tablename__ = 'erp_department'

    id = Column(INTEGER(11), primary_key=True, comment='部门编号(主键)')
    dept_name = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='部门名称')
    # dept_tel = Column(VARCHAR(50), server_default=text("''"), comment='联系电话')
    manager_id = Column(INTEGER(11), default=0, comment='部门负责人')
    parent_id = Column(INTEGER(11), server_default=text("'0'"), comment='上级部门编号')
    create_by = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    update_by = Column(INTEGER(11), server_default=text("'0'"), comment='更新人')
    # dept_level = Column(INTEGER(11), server_default=text("'0'"), comment='部门层级')
    dept_sort = Column(INTEGER(11), server_default=text("'0'"), comment='部门排序')
    # is_company = Column(INTEGER(11), server_default=text("'0'"), comment='是否问公司(0-不是,1-是)')
    qy_wechat_dept_id = Column(INTEGER(11), server_default=text("'0'"), comment='企业微信部门id')
    create_time = Column(DateTime, default=datetime.now())
    update_time = Column(DateTime, default=datetime.now())
    disable = Column(INTEGER(4), default=0)


class ErpAccountDepartment(Base):
    __tablename__ = 'erp_account_department'

    id = Column(INTEGER(11), primary_key=True, comment='用户部门表主键Id')
    account_id = Column(INTEGER(11), server_default=text("'0'"), comment='用户Id(RB_Account表Id)')
    account_type = Column(INTEGER(11), server_default=text("'1'"), comment='账号类型')
    dept_id = Column(INTEGER(11), server_default=text("'0'"), comment='部门Id')
    create_by = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    update_by = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    is_main_dept = Column(INTEGER(11), server_default=text("'0'"), comment='是否为主部门(1-是,0不是)')
    create_time = Column(DateTime, default=datetime.now())
    update_time = Column(DateTime, default=datetime.now())
    disable = Column(INTEGER(4), default=0)


class ErpChecking(Base):
    __tablename__ = 'erp_checking'

    id = Column(INTEGER(11), primary_key=True)
    ym = Column(String(255))
    title = Column(String(255))
    create_by = Column(INTEGER(11), comment='创建人')
    update_by = Column(INTEGER(11), comment='修改人')
    create_time = Column(DateTime, default=datetime.now())
    update_time = Column(DateTime, default=datetime.now())
    disable = Column(INTEGER(4), default=0)


class ErpRole(Base):
    __tablename__ = 'erp_role'

    id = Column(INTEGER(11), primary_key=True)
    role_name = Column(String(255))
    role_desc = Column(String(255))
    create_time = Column(DateTime, default=datetime.now())
    update_time = Column(DateTime, default=datetime.now())
    disable = Column(INTEGER(4), default=0)


class ErpRoleRoute(Base):
    __tablename__ = 'erp_role_route'

    id = Column(INTEGER(11), primary_key=True)
    role_id = Column(INTEGER(11))
    route_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now())
    update_time = Column(DateTime, default=datetime.now())
    disable = Column(INTEGER(4), default=0)


class ErpRoute(Base):
    __tablename__ = 'erp_route'

    id = Column(INTEGER(11), primary_key=True)
    route_name = Column(String(45))
    route_path = Column(String(255))
    identification = Column(String(42))
    parent_id = Column(INTEGER(11))
    meta = Column(JSON)
    component = Column(String(255))
    create_time = Column(DateTime, default=datetime.now())
    update_time = Column(DateTime, default=datetime.now())
    disable = Column(INTEGER(4), default=0)

    def as_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}


class ErpCampus(Base):
    __tablename__ = 'erp_campus'

    id = Column(INTEGER(11), primary_key=True)
    campus_name = Column(String(45))
    create_time = Column(DateTime, default=datetime.now())
    update_time = Column(DateTime, default=datetime.now())
    disable = Column(INTEGER(4), default=0)


class ErpCourseLabel(Base):
    __tablename__ = 'erp_course_label'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    label_id = Column(INTEGER(4))
    course_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now())
    update_time = Column(DateTime, default=datetime.now())
    disable = Column(INTEGER(4), default=0)
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))


class ErpLabel(Base):
    __tablename__ = 'erp_label'

    id = Column(INTEGER(11), primary_key=True)
    title = Column(String(45))
    create_time = Column(DateTime, default=datetime.now())
    update_time = Column(DateTime, default=datetime.now())
    disable = Column(INTEGER(4), default=0)
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
