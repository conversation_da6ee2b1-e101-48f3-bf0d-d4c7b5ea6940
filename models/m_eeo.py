# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, DateTime, String, text, Float, TIMESTAMP, JSON, TEXT, Integer, ForeignKey
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.mysql import TINYINT, BIGINT
from sqlalchemy.orm import relationship


Base = declarative_base()
metadata = Base.metadata


class ErpEeoExamScores(Base):
    __tablename__ = 'erp_eeo_exam_scores'

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(20), nullable=False)
    course_id = Column(String(20))
    course_name = Column(String(100))
    activity_id = Column(String(20))
    activity_name = Column(String(100))
    unit_id = Column(String(20))
    unit_name = Column(String(100))
    class_id = Column(String(20))
    student_uid = Column(String(20))
    student_name = Column(String(50))
    student_account = Column(String(50))
    teacher_uid = Column(String(20))
    teacher_name = Column(String(50))
    teacher_account = Column(String(50))
    score = Column(INTEGER(11))
    student_scoring_rate = Column(DECIMAL(10, 4))
    answer_duration = Column(INTEGER(11))
    submission_time = Column(BIGINT(20))
    correction_time = Column(BIGINT(20))
    disable = Column(TINYINT(1), server_default=text("'0'"), comment="删除状态：0正常，1已删除")
    created_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))


class ErpEeoHomeworkScores(Base):
    __tablename__ = 'erp_eeo_homework_scores'

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(20), nullable=False)
    course_id = Column(String(20))
    course_name = Column(String(100))
    activity_id = Column(String(20))
    activity_name = Column(String(100))
    unit_id = Column(String(20))
    unit_name = Column(String(100))
    student_uid = Column(String(20))
    student_name = Column(String(50))
    student_account = Column(String(50))
    teacher_uid = Column(String(20))
    teacher_name = Column(String(50))
    teacher_account = Column(String(50))
    score = Column(INTEGER(11))
    student_score = Column(String(20))
    student_scoring_rate = Column(DECIMAL(10, 4))
    review_details = Column(JSON)
    grading_plan = Column(String(20))
    submission_time = Column(BIGINT(20))
    correction_time = Column(BIGINT(20))
    disable = Column(TINYINT(1), server_default=text("'0'"), comment="删除状态：0正常，1已删除")
    created_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))


class ErpEeoHomeworkSubmit(Base):
    __tablename__ = 'erp_eeo_homework_submit'

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(20), nullable=False)
    course_id = Column(String(20))
    course_name = Column(String(100))
    activity_id = Column(String(20))
    activity_name = Column(String(100))
    unit_id = Column(String(20))
    unit_name = Column(String(100))
    student_uid = Column(String(20))
    student_name = Column(String(50))
    student_account = Column(String(50))
    teacher_uid = Column(String(20))
    teacher_name = Column(String(50))
    teacher_account = Column(String(50))
    files = Column(JSON)
    content = Column(TEXT)
    is_submit_late = Column(TINYINT(1), server_default=text("'0'"))
    is_revision = Column(TINYINT(1), server_default=text("'0'"))
    student_total = Column(INTEGER(11))
    submit_total = Column(INTEGER(11))
    submission_time = Column(BIGINT(20))
    disable = Column(TINYINT(1), server_default=text("'0'"), comment="删除状态：0正常，1已删除")
    created_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))


class ErpEeoMessageLogs(Base):
    __tablename__ = 'erp_eeo_message_logs'

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(20))
    cmd = Column(String(50), nullable=False)
    msg = Column(TEXT)
    safe_key = Column(String(100))
    time_stamp = Column(BIGINT(20))
    course_id = Column(String(20))
    course_name = Column(String(100))
    data = Column(JSON)
    processed = Column(TINYINT(1), default=0)
    created_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"))
    

class ErpEeoCourseRecordings(Base):
    __tablename__ = 'erp_eeo_course_recordings'

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(20), comment="机构ID")
    course_id = Column(String(20), comment="课程ID")
    file_id = Column(String(50), comment="文件ID")
    vurl = Column(String(255), comment="视频URL")
    vst = Column(BIGINT(20), comment="视频开始时间")
    vet = Column(BIGINT(20), comment="视频结束时间")
    duration = Column(INTEGER(11), comment="视频时长")
    size = Column(INTEGER(11), comment="文件大小")
    processed = Column(TINYINT(1), default=0, comment="是否处理：0未处理，1已处理")
    cid_ext = Column(String(50), comment="MP4数据来源 ClassRoom：教室；Camera.3：教师摄像头（现场）")
    class_id = Column(String(20), comment="班级ID")
    action_time = Column(BIGINT(20), comment="操作时间")
    disable = Column(TINYINT(1), server_default=text("'0'"), comment="删除状态：0正常，1已删除")
    created_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间")
    updated_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment="更新时间")

class ErpEeoExamScoresDetail(Base):
    __tablename__ = 'erp_eeo_exam_scores_detail'

    id = Column(INTEGER(11), primary_key=True)
    exam_score_id = Column(INTEGER(11), nullable=False)
    topic_id = Column(INTEGER(11), nullable=False)
    topic_type = Column(String(10))
    topic_max_score = Column(INTEGER(11))
    topic_score = Column(INTEGER(11))
    topic_result = Column(JSON)
    created_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))



class ErpEeoAnswerSheetScores(Base):
    """答题卡成绩表"""
    __tablename__ = "erp_eeo_answer_sheet_scores"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    sid = Column(String(32), nullable=True, comment="机构ID")
    course_id = Column(String(32), nullable=True, comment="课程ID")
    course_name = Column(String(128), nullable=True, comment="课程名称")
    activity_id = Column(String(32), nullable=True, comment="活动ID")
    activity_name = Column(String(128), nullable=True, comment="活动名称")
    unit_id = Column(String(32), nullable=True, comment="单元ID")
    unit_name = Column(String(128), nullable=True, comment="单元名称")
    class_id = Column(String(32), nullable=True, comment="班级ID")
    student_uid = Column(String(32), nullable=True, comment="学生UID")
    student_name = Column(String(64), nullable=True, comment="学生姓名")
    student_account = Column(String(64), nullable=True, comment="学生账号")
    teacher_uid = Column(String(32), nullable=True, comment="教师UID")
    teacher_name = Column(String(64), nullable=True, comment="教师姓名")
    teacher_account = Column(String(64), nullable=True, comment="教师账号")
    maximum_score = Column(Float, nullable=True, comment="最大分值")
    score = Column(Float, nullable=True, comment="得分")
    student_scoring_rate = Column(Float, nullable=True, comment="得分率")
    answer_duration = Column(Integer, nullable=True, comment="答题时长(秒)")
    submission_time = Column(Integer, nullable=True, comment="提交时间")
    correction_time = Column(Integer, nullable=True, comment="批改时间")
    disable = Column(TINYINT(1), server_default=text("'0'"), comment="删除状态：0正常，1已删除")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    details = relationship("ErpEeoAnswerSheetScoresDetail", back_populates="answer_sheet_score")


class ErpEeoAnswerSheetScoresDetail(Base):
    """答题卡成绩详情表"""
    __tablename__ = "erp_eeo_answer_sheet_scores_detail"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    answer_sheet_score_id = Column(Integer, ForeignKey("erp_eeo_answer_sheet_scores.id"), comment="答题卡成绩ID")
    topic_id = Column(Integer, nullable=True, comment="题目ID")
    topic_type = Column(String(32), nullable=True, comment="题目类型")
    topic_max_score = Column(Float, nullable=True, comment="题目最大分值")
    topic_score = Column(Float, nullable=True, comment="题目得分")
    topic_result = Column(TEXT, nullable=True, comment="题目结果")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    answer_sheet_score = relationship("ErpEeoAnswerSheetScores", back_populates="details")


class ErpEeoPhoneNumberChange(Base):
    """EEO手机号码更换记录表"""
    __tablename__ = "erp_eeo_phone_number_change"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    sid = Column(String(32), nullable=False, comment="机构ID")
    uid = Column(String(32), nullable=False, comment="用户UID")
    telephone = Column(String(32), nullable=False, comment="新手机号码")
    replace_time = Column(BIGINT(20), nullable=True, comment="替换时间")
    processed = Column(TINYINT(1), default=0, comment="处理状态：0未处理，1已处理")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    msg = Column(String(255), nullable=False, comment="备注")


class ErpEeoCourseInfoChange(Base):
    """EEO课程信息改动记录表"""
    __tablename__ = "erp_eeo_course_info_change"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    sid = Column(String(32), nullable=False, comment="机构ID")
    op_type = Column(Integer, nullable=False, comment="操作类型 1:增加，2:删除，3:修改")
    op_time = Column(BIGINT(20), nullable=False, comment="操作时间")
    uid = Column(String(32), nullable=False, comment="操作人UID")
    course_id = Column(String(32), nullable=False, comment="课程ID")
    op_source = Column(Integer, nullable=False, comment="操作来源 1:EEO后台，2:classIn客户端，4:机构API")
    course_name = Column(String(255), nullable=True, comment="课程名称")
    expiry_time = Column(BIGINT(20), nullable=True, comment="课程过期时间戳，0：为永久有效")
    course_type = Column(Integer, nullable=True, comment="课程类型，1=标准课、2=公开课、3=双师课")
    course_status = Column(Integer, nullable=True, comment="课程状态，1=还没开课、2=开课中、3=已结课、4=已删除")
    category = Column(String(255), nullable=True, comment="组织架构名称")
    category_id = Column(String(32), nullable=True, comment="组织架构ID")
    subject_id = Column(Integer, nullable=True, comment="课程学科")
    main_teacher_name = Column(String(64), nullable=True, comment="班主任姓名")
    main_teacher_uid = Column(String(32), nullable=True, comment="班主任UID")
    main_teacher_no = Column(String(64), nullable=True, comment="班主任工号")
    label_infos = Column(JSON, nullable=True, comment="标签信息")
    processed = Column(TINYINT(1), default=0, comment="处理状态：0未处理，1已处理")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")


class ErpEeoClassInfoChange(Base):
    """EEO课节信息改动记录表"""
    __tablename__ = "erp_eeo_class_info_change"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    sid = Column(String(32), nullable=False, comment="机构ID")
    op_type = Column(Integer, nullable=False, comment="操作类型 1:增加，2:删除，3:修改")
    op_time = Column(BIGINT(20), nullable=False, comment="操作时间")
    uid = Column(String(32), nullable=False, comment="操作人UID")
    course_id = Column(String(32), nullable=False, comment="课程ID")
    class_id = Column(String(32), nullable=False, comment="课节ID")
    op_source = Column(Integer, nullable=False, comment="操作来源 1:EEO后台，2:classIn客户端，4:机构API")
    class_name = Column(String(255), nullable=True, comment="课节名称")
    course_name = Column(String(255), nullable=True, comment="课程名称")
    begin_time = Column(BIGINT(20), nullable=True, comment="课节开始时间戳")
    end_time = Column(BIGINT(20), nullable=True, comment="课节结束时间戳")
    class_type = Column(Integer, nullable=True, comment="课节类型，1=标准课 2=公开课 3=双师课")
    class_status = Column(Integer, nullable=True, comment="课节状态，1=还没开课 2=上课中 3=上课结束 4=已取消")
    seat_num = Column(Integer, nullable=True, comment="上台人数")
    is_auto_onstage = Column(Integer, nullable=True, comment="是否自动上台，0=自动，1=不自动")
    teacher_name = Column(String(64), nullable=True, comment="授课教师姓名")
    teacher_uid = Column(String(32), nullable=True, comment="授课教师UID")
    teacher_no = Column(String(64), nullable=True, comment="授课教师工号")
    assistant_teacher_infos = Column(JSON, nullable=True, comment="联席教师信息")
    label_infos = Column(JSON, nullable=True, comment="标签信息")
    processed = Column(TINYINT(1), default=0, comment="处理状态：0未处理，1已处理")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")


class ErpEeoCourseStudentChange(Base):
    """EEO课程学生信息改动记录表"""
    __tablename__ = "erp_eeo_course_student_change"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    sid = Column(String(32), nullable=False, comment="机构ID")
    op_type = Column(Integer, nullable=False, comment="操作类型 1:增加，2:删除，3:修改")
    op_time = Column(BIGINT(20), nullable=False, comment="操作时间")
    uid = Column(String(32), nullable=False, comment="操作人UID")
    course_id = Column(String(32), nullable=False, comment="课程ID")
    op_source = Column(Integer, nullable=False, comment="操作来源 1:EEO后台，2:classIn客户端，4:机构API")
    course_name = Column(String(255), nullable=True, comment="课程名称")
    student_infos = Column(JSON, nullable=True, comment="学生信息")
    processed = Column(TINYINT(1), default=0, comment="处理状态：0未处理，1已处理")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")


class ErpEeoClassStudentChange(Base):
    """EEO课节学生信息改动记录表"""
    __tablename__ = "erp_eeo_class_student_change"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    sid = Column(String(32), nullable=False, comment="机构ID")
    op_type = Column(Integer, nullable=False, comment="操作类型 1:增加，2:删除，3:修改")
    op_time = Column(BIGINT(20), nullable=False, comment="操作时间")
    uid = Column(String(32), nullable=False, comment="操作人UID")
    course_id = Column(String(32), nullable=False, comment="课程ID")
    class_id = Column(String(32), nullable=False, comment="课节ID")
    op_source = Column(Integer, nullable=False, comment="操作来源 1:EEO后台，2:classIn客户端，4:机构API")
    class_name = Column(String(255), nullable=True, comment="课节名称")
    student_infos = Column(JSON, nullable=True, comment="学生信息")
    processed = Column(TINYINT(1), default=0, comment="处理状态：0未处理，1已处理")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")