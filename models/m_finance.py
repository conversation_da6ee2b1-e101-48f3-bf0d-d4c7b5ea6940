# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, Date, DateTime, Float, JSON, String, Table, Text, text
from sqlalchemy.dialects.mysql import INTEGER, LONGTEXT, VARCHAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpBankAccount(Base):
    __tablename__ = 'erp_bank_account'

    id = Column(INTEGER(4), primary_key=True)
    campus_id = Column(INTEGER(4))
    account_alias = Column(String(125))
    default_account_type = Column(INTEGER(4), comment='1 银行 2 现金 3 虚拟账户')
    account_type = Column(INTEGER(4), comment='关联账户类型表')
    account_number = Column(String(125), comment='账户号')
    bank_name = Column(String(45), comment='银行')
    bank_sub_name = Column(String(125), comment='开户支行')
    account_holder = Column(String(45), comment='持有人')
    balance = Column(Float(10), comment='余额')
    currency_type = Column(INTEGER(4), comment='1 人民币')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    is_active = Column(INTEGER(4), comment='是否激活')
    cmb_merchant_id = Column(String(125), comment='招行商户号')


class ErpBankAccountLog(Base):
    __tablename__ = 'erp_bank_account_log'

    id = Column(INTEGER(11), primary_key=True)
    bill_account_id = Column(INTEGER(11))
    change_type = Column(INTEGER(4), comment='1 增加 2 减少')
    amount = Column(Float(10), comment='数量')
    rest_balance = Column(Float(10), comment='剩余金额')
    desc = Column(String(1000), comment='描述')
    create_by = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    receipt_id = Column(INTEGER(11))


class ErpFinanceTradePayment(Base):
    __tablename__ = 'erp_finance_trade_payment'

    id = Column(INTEGER(11), primary_key=True)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    payment_order_no = Column(String(125), comment='支付生成单号')
    trade_type = Column(INTEGER(4), comment='1 微信 2 支付宝')
    money_pay = Column(Float(10), comment='发起金额')
    money_income = Column(Float(10), comment='实际收入金额')
    openid = Column(String(125), comment='用户openid')
    trade_status = Column(INTEGER(4), comment='1 挂起 2 成功 3 失败 4 已关闭')
    merchant_id = Column(INTEGER(11), comment='商户编号')
    cmb_order_id = Column(String(225), comment='招行订单号')
    cmb_pay_time = Column(DateTime, comment='招行交易时间')
    cmb_trade_type = Column(String(255), comment='招行交易类型 JSAPI')
    third_order_id = Column(String(255), comment='三方流水号')
    stu_id = Column(INTEGER(11))
    offer_id = Column(INTEGER(11))
    campus_id = Column(INTEGER(11))
    receipt_id = Column(INTEGER(11), default=0)


class ErpFinanceTradeRefund(Base):
    __tablename__ = 'erp_finance_trade_refund'

    id = Column(INTEGER(11), primary_key=True)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    payment_order_no = Column(String(125), comment='支付生成单号')
    refund_order_no = Column(String(125), comment='退款生成单号')
    trade_type = Column(INTEGER(4), comment='1 微信 2 支付宝')
    money = Column(Float(10), comment='发起金额')
    money_refund = Column(Float(10), comment='实际收入金额')
    openid = Column(String(125), comment='用户openid')
    trade_status = Column(INTEGER(4), comment='1 挂起 2 成功 3 失败 4 已关闭')
    merchant_id = Column(String(125), comment='商户编号')
    cmb_order_id = Column(String(225), comment='招行订单号')
    cmb_pay_time = Column(DateTime, comment='招行交易时间')
    cmb_trade_type = Column(String(255), comment='招行交易类型 JSAPI')
    third_order_id = Column(String(255), comment='三方流水号')
    comments = Column(String(255))
    stu_id = Column(INTEGER(11), default=0)
    offer_id = Column(INTEGER(11), default=0)
    campus_id = Column(INTEGER(11), default=0)
    receipt_id = Column(INTEGER(11), default=0)
    order_student_id = Column(INTEGER(11), default=0)
    receipt_id = Column(INTEGER(11), default=0)


class ErpFinanceCostType(Base):
    __tablename__ = 'erp_finance_cost_type'

    id = Column(INTEGER(11), primary_key=True)
    name = Column(String(255))
    type = Column(INTEGER(4), comment='1 收入 2 支出 3 收支相抵 4 其他')
    tag = Column(INTEGER(4), comment='标签大类，例如总成本，其他收入，其他支出')
    parent_id = Column(INTEGER(11))
    is_admin = Column(INTEGER(4), comment='是否是管理型节点')
    add_finance = Column(INTEGER(4), comment='是否计入营收')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)

    def as_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}


class ErpFinanceRole(Base):
    __tablename__ = 'erp_finance_role'

    id = Column(INTEGER(4), primary_key=True)
    type = Column(INTEGER(4))
    name = Column(String(45))
    account_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)

    def as_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}




# class ErpFinance(Base):
#     __tablename__ = 'erp_finance'

#     id = Column(INTEGER(11), primary_key=True)
#     order_no = Column(String(125), comment='订单号')
#     merchant_id = Column(INTEGER(11), comment='对象id')
#     merchant_name = Column(String(125), comment='对象名')
#     ie_type = Column(INTEGER(4), comment='1 收入 2 支出')
#     trade_money = Column(DECIMAL(10, 2))
#     trade_time = Column(DateTime)
#     create_by = Column(INTEGER(4), default=0)
#     update_by = Column(INTEGER(4), default=0)
#     create_time = Column(DateTime, default=datetime.now)
#     update_time = Column(DateTime, default=datetime.now)
#     audit_state = Column(INTEGER(4), default=0)
#     receipt_id = Column(INTEGER(11), default=0)
#     desc = Column(String(255))
#     template_id = Column(INTEGER(11))
#     is_public = Column(INTEGER(4), comment='1 私账 2 公账')
#     pre_pay_time = Column(DateTime, comment='预计付款日期')
#     cashier_id = Column(INTEGER(11), comment='出纳状态 0 未审核 >0 已审核（id为谁审核的）')
#     create_by_name = Column(String(45))
#     dept_id = Column(INTEGER(11))
#     dept_name = Column(String(45))
#     related_obj_id = Column(INTEGER(11))
#     disable = Column(INTEGER(4))
#     is_auto = Column(INTEGER(4), comment='是否自动通过')
#     cost_type_id = Column(INTEGER(11), comment='费用类型id')



# class ErpFinanceDetail(Base):
#     __tablename__ = 'erp_finance_detail'

#     id = Column(INTEGER(11), primary_key=True)
#     receipt_id = Column(INTEGER(11))
#     cost_type_id = Column(INTEGER(11))
#     total_price = Column(DECIMAL(10, 2))
#     unit_price = Column(DECIMAL(10, 2))
#     num = Column(INTEGER(11))
#     original_price = Column(DECIMAL(10, 2))
#     comments = Column(String(255))
#     create_by = Column(INTEGER(11), default=0)
#     update_by = Column(INTEGER(11), default=0)
#     create_time = Column(DateTime, default=datetime.now)
#     update_time = Column(DateTime, default=datetime.now)
#     disable = Column(INTEGER(4), default=0)
#     order_id = Column(INTEGER(11), default=0)
#     item_name = Column(String(255))




class ErpFinanceBudget(Base):
    __tablename__ = 'erp_finance_budget'

    id = Column(INTEGER(11), primary_key=True)
    yyyy = Column(INTEGER(4))
    month = Column(INTEGER(4))
    presale = Column(DECIMAL(12, 2), comment='预收')
    class_fee = Column(DECIMAL(12, 2), comment='课时费，预期课时费')
    estimated_profit = Column(DECIMAL(12, 2), comment='预计利润=预收-课时费')
    expenses = Column(DECIMAL(12, 2), comment='编辑费用支出')
    actual_profit = Column(DECIMAL(12, 2), comment='实际利润=预计-费用支出')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)



class ErpStudentEwalletLog(Base):
    __tablename__ = 'erp_student_ewallet_log'

    id = Column(INTEGER(11), primary_key=True)
    stu_id = Column(INTEGER(11))
    change_type = Column(INTEGER(4), comment='1 增加 2 减少')
    amount = Column(Float(10), comment='数量')
    desc = Column(String(1000), comment='描述')
    create_by = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    rest_balance = Column(Float(10), comment='剩余金额')
    receipt_id = Column(INTEGER(11), default=0, comment='关联单据')
    from_order_id = Column(INTEGER(11), default=0, comment='从哪个订单结转')

class ErpPerformanceRecords(Base):
    __tablename__ = 'erp_performance_records'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    yyyy = Column(INTEGER(4))
    quarter = Column(INTEGER(4))
    rating = Column(String(45))
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpBankAccountType(Base):
    __tablename__ = 'erp_bank_account_type'

    id = Column(INTEGER(11), primary_key=True)
    account_type_name = Column(String(45))
    is_public = Column(INTEGER(4))
    is_virtual = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpBankAccountReport(Base):
    __tablename__ = 'erp_bank_account_report'

    id = Column(INTEGER(11), primary_key=True)
    bill_account_id = Column(INTEGER(11))
    statistic_date = Column(Date)
    last_balance = Column(DECIMAL(10, 2))
    current_balance = Column(String(255))
    pay_money = Column(DECIMAL(10, 2))
    received_money = Column(DECIMAL(10, 2))
    wait_pay = Column(DECIMAL(10, 2))
    pay_num = Column(INTEGER(4))
    received_num = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpClassConsumptionStatistic(Base):
    """课消统计明细表"""
    __tablename__ = 'erp_class_consumption_statistic'

    id = Column(INTEGER(11), primary_key=True)
    order_student_id = Column(INTEGER(11), nullable=False, comment='学生订单ID')
    class_id = Column(INTEGER(11), nullable=False, comment='班级ID')
    stu_id = Column(INTEGER(11), nullable=False, comment='学生ID')
    stu_name = Column(String(255), comment='学生姓名')
    class_name = Column(String(255), comment='班级名称')
    course_name = Column(String(255), comment='课程名称')
    center_id = Column(INTEGER(11), comment='教学点ID')
    center_name = Column(String(255), comment='教学点名称')
    classroom_id = Column(INTEGER(11), comment='教室ID')
    classroom_name = Column(String(255), comment='教室名称')
    
    # 订单相关
    payment_order_no = Column(String(255), comment='支付单号')
    total_order_amount = Column(DECIMAL(12, 2), comment='订单总金额')
    discount = Column(DECIMAL(12, 2), comment='折扣金额')
    refund_money = Column(DECIMAL(12, 2), comment='退费金额')
    
    # 课时相关
    total_hours = Column(DECIMAL(10, 2), comment='总课时')
    complete_hours = Column(DECIMAL(10, 2), comment='已完成课时')
    unit_price = Column(DECIMAL(12, 2), comment='每课时单价')
    class_consumption_amount = Column(DECIMAL(12, 2), comment='课消金额')
    
    # 状态相关
    student_state = Column(INTEGER(4), comment='学生状态')
    
    # 时间相关
    order_create_time = Column(DateTime, comment='订单创建时间')
    last_consumption_time = Column(DateTime, comment='最后课消时间')
    
    # 系统字段
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(INTEGER(11), comment='创建者')
    update_by = Column(INTEGER(11), comment='更新者')
    disable = Column(INTEGER(4), default=0, comment='删除状态')
    
    # 数据版本，用于增量更新
    data_version = Column(INTEGER(11), default=1, comment='数据版本')
    last_sync_time = Column(DateTime, default=datetime.now, comment='最后同步时间')