# coding: utf-8
from datetime import datetime
from email.policy import default

from sqlalchemy import Column, DECIMAL, DateTime, String, Text, text, JSON, Float, Date
from sqlalchemy.dialects.mssql import TINYINT
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, YEAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpCompetitionLog(Base):
    __tablename__ = 'erp_competition_log'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    competition_stu_id = Column(INTEGER(11))
    content = Column(String(500))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpCompetitionSituation(Base):
    __tablename__ = 'erp_competition_situation'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    competition_stu_id = Column(INTEGER(11))
    part_type = Column(INTEGER(4))
    score = Column(Float(10))
    comments = Column(String(1000))
    suggestion = Column(String(1000))
    book_detail = Column(JSON)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpCompetitionStu(Base):
    __tablename__ = 'erp_competition_stu'

    id = Column(INTEGER(11), primary_key=True)
    stu_name = Column(String(45))
    stu_tel = Column(String(125))
    stu_grade = Column(String(45))
    stu_target = Column(String(125))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)




class ErpCompetitionCenterBooking(Base):
    __tablename__ = 'erp_competition_center_booking'

    id = Column(INTEGER(11), primary_key=True)
    booking_date = Column(Date)
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    stu_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpCompetitionCenterChecking(Base):
    __tablename__ = 'erp_competition_center_checking'

    id = Column(INTEGER(11), primary_key=True)
    booking_id = Column(INTEGER(11))
    check_status = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpCompetitionCenterStu(Base):
    __tablename__ = 'erp_competition_center_stu'

    id = Column(INTEGER(11), primary_key=True)
    stu_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4), default=0)


class ErpCompetitionCenterStuTarget(Base):
    __tablename__ = 'erp_competition_center_stu_target'

    id = Column(INTEGER(11), primary_key=True, autoincrement=True)
    stu_name = Column(String(45))
    stu_id = Column(INTEGER(11), nullable=False)
    stu_tel = Column(String(125))
    target_date = Column(DateTime)
    plan_period = Column(String(125))
    target_index = Column(INTEGER(4))
    target_value = Column(String(500))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4), default=0)
    comments = Column(String(500))