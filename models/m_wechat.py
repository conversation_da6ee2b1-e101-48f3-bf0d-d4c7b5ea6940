# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, DateTime, String, Text, text, JSON, Float, Date
from sqlalchemy.dialects.mssql import TINYINT
from sqlalchemy.dialects.mysql import INTEGER, MEDIUMTEXT, YEAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpQwechatContact(Base):
    """
    企微教师客户表
    """
    __tablename__ = 'erp_qwechat_contact'

    id = Column(INTEGER(11), primary_key=True)
    teacher_account_id = Column(INTEGER(11))
    teacher_id = Column(INTEGER(11))
    account_userid = Column(String(45))
    remark = Column(String(45))
    follow_create_time = Column(DateTime, comment='关注时间')
    add_way = Column(INTEGER(4))
    external_userid = Column(String(125))
    external_name = Column(String(45))
    external_type = Column(INTEGER(4))
    external_avatar = Column(String(255))
    external_gender = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    stu_id = Column(INTEGER(11), default=0)


class ErpQwechatContactConfig(Base):
    __tablename__ = 'erp_qwechat_contact_config'

    id = Column(INTEGER(11), primary_key=True)
    teacher_id = Column(INTEGER(11))
    teacher_account_id = Column(INTEGER(11))
    account_userid = Column(String(125))
    qr_code = Column(String(255))
    config_id = Column(String(125))
    stu_id = Column(INTEGER(11))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)


class ErpWechatClassuser(Base):
    """
    企微班级群关联学生表
    """
    __tablename__ = 'erp_wechat_classuser'

    id = Column(INTEGER(11), primary_key=True)
    class_id = Column(INTEGER(11), default=0, comment='班级ID')
    wechat_id = Column(String(255), default='', comment='企微群ID')
    external_user_id = Column(String(255), default='', comment='企微外部联系人UserId')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, comment='更新时间')
    state = Column(INTEGER(4), default=0, comment='状态 0在群 1已退群')
    disable = Column(INTEGER(4), default=0, comment='删除状态')


class ErpWechatExternaluser(Base):
    """
    企微外部用户关联表
    """
    __tablename__ = 'erp_wechat_externaluser'

    id = Column(INTEGER(11), primary_key=True)
    teacher_id = Column(INTEGER(11), default=0, comment='老师ID')
    teacher_account_id = Column(INTEGER(11), default=0, comment='老师账号ID')
    account_userid = Column(String(45), default='', comment='企微账号UserID')
    remark = Column(String(255), default='', comment='备注')
    follow_create_time = Column(DateTime, comment='关注时间')
    add_way = Column(INTEGER(4), default=0, comment='添加方式')
    stu_id = Column(INTEGER(11), default=0, comment='学生ID')
    external_user_id = Column(String(255), default='', comment='企微外部联系人UserId')
    customer_name = Column(String(255), default='', comment='客户微信昵称')
    external_name = Column(String(255), default='', comment='外部联系人姓名')
    external_type = Column(INTEGER(4), default=0, comment='外部联系人类型')
    external_avatar = Column(String(500), default='', comment='外部联系人头像')
    external_gender = Column(INTEGER(4), default=0, comment='外部联系人性别')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, comment='更新时间')
    disable = Column(INTEGER(4), default=0, comment='删除状态')


class ErpWechatChannelrecord(Base):
    """
    企微渠道活码客户记录表
    """
    __tablename__ = 'erp_wechat_channelrecord'

    id = Column(INTEGER(11), primary_key=True)
    channel_id = Column(INTEGER(11), default=0, comment='渠道活码ID')
    type = Column(INTEGER(4), default=0, comment='类型 1新增 2被客户删除 3删除')
    emp_id = Column(INTEGER(11), default=0, comment='员工ID')
    work_emp_id = Column(String(255), default='', comment='员工企业微信ID')
    external_user_id = Column(String(255), default='', comment='客户企业微信ID')
    customer_name = Column(String(255), default='', comment='客户名称')
    status = Column(INTEGER(4), default=0, comment='删除状态')
    create_by = Column(INTEGER(11), default=0, comment='创建人')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_by = Column(INTEGER(11), default=0, comment='修改人')
    update_time = Column(DateTime, default=datetime.now, comment='更新时间')
    disable = Column(INTEGER(4), default=0, comment='删除状态')


class ErpWechatChannel(Base):
    """
    企微渠道活码表
    """
    __tablename__ = 'erp_wechat_channel'

    id = Column(INTEGER(11), primary_key=True)
    wx_config_id = Column(String(255), default='', comment='企业微信联系我方式配置ID')
    wx_qrcode = Column(String(500), default='', comment='微信二维码')
    channel_group_id = Column(INTEGER(11), default=0, comment='分组ID')
    name = Column(String(255), default='', comment='活码名称')
    skip_verify = Column(INTEGER(4), default=1, comment='外部客户添加时是否无需验证 1需要')
    lable_ids = Column(Text, comment='标签IDs 英文逗号分隔')
    type = Column(INTEGER(4), default=1, comment='类型 1单人 2多人')
    special_date_enable = Column(INTEGER(4), default=0, comment='特殊日期启用 1是')
    emp_add_limit = Column(INTEGER(4), default=0, comment='员工添加上限 1开启')
    emp_backup = Column(Text, comment='备用人员ID 英文逗号分隔')
    welcome_enable = Column(INTEGER(4), default=0, comment='欢迎语启用 1是')
    welcome_week_enable = Column(INTEGER(4), default=0, comment='欢迎语周期启用 1是')
    welcome_special_enable = Column(INTEGER(4), default=0, comment='特殊时期欢迎语启用 1是')
    status = Column(INTEGER(4), default=0, comment='删除状态')
    create_by = Column(INTEGER(11), default=0, comment='创建人')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_by = Column(INTEGER(11), default=0, comment='修改人')
    update_time = Column(DateTime, default=datetime.now, comment='更新时间')
    remark = Column(String(500), default='', comment='备注')
    teacher_id = Column(INTEGER(11), default=0, comment='老师ID')
    stu_id = Column(INTEGER(11), default=0, comment='学生ID')
    class_id = Column(INTEGER(11), default=0, comment='就读班级ID')
    disable = Column(INTEGER(4), default=0, comment='删除状态')


class ErpWechatChannelweek(Base):
    """
    企微渠道周期表
    """
    __tablename__ = 'erp_wechat_channelweek'

    id = Column(INTEGER(11), primary_key=True)
    channel_id = Column(INTEGER(11), default=0, comment='渠道活码ID')
    type = Column(INTEGER(4), default=1, comment='类型 1标准周期 2特殊日期')
    week = Column(INTEGER(4), default=0, comment='周几')
    start_date = Column(DateTime, comment='特殊开始日期')
    end_date = Column(DateTime, comment='特殊结束日期')
    is_default = Column(INTEGER(4), default=0, comment='是否默认数据 1是')
    start_hours = Column(String(10), default='', comment='周期开始时间 精确到小时')
    end_hours = Column(String(10), default='', comment='周期结束时间 精确到小时')
    emp_ids = Column(Text, comment='员工IDs 英文逗号分隔')
    dept_ids = Column(Text, comment='部门IDs 英文逗号分隔')
    status = Column(INTEGER(4), default=0, comment='删除状态')
    create_by = Column(INTEGER(11), default=0, comment='创建人')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_by = Column(INTEGER(11), default=0, comment='修改人')
    update_time = Column(DateTime, default=datetime.now, comment='更新时间')
    remark = Column(String(500), default='', comment='备注')
    disable = Column(INTEGER(4), default=0, comment='删除状态')


class ErpWechatCheckin(Base):
    """
    企微打卡数据表
    """
    __tablename__ = 'erp_wechat_checkin'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11), default=0, comment='账号ID')
    date = Column(Date, comment='日期')
    rule = Column(String(255), default='', comment='打卡规则')
    schedule = Column(String(255), default='', comment='班次')
    first_time = Column(String(10), default='', comment='最早打卡时间 时分')
    last_time = Column(String(10), default='', comment='最晚打卡时间 时分')
    number = Column(INTEGER(11), default=0, comment='打卡次数')
    state = Column(INTEGER(4), default=0, comment='校准状态类型 1迟到 2早退 3缺卡 4旷工 5地点异常 6设备异常')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, comment='更新时间')
    dept_name = Column(String(255), default='', comment='部门名称')
    record_type = Column(INTEGER(4), default=1, comment='记录类型 1固定上下班 2外出 3按班次上下班 4自由签到 5加班 7无规则')
    day_type = Column(INTEGER(4), default=0, comment='日报类型 0工作日日报 1休息日日报')
    exception_count = Column(INTEGER(11), default=0, comment='当日此异常的次数')
    exception_duration = Column(INTEGER(11), default=0, comment='当日此异常的时长(分钟)')
    first_class_time = Column(String(10), default='', comment='第一节课上课时间')
    checkin_state = Column(INTEGER(4), default=1, comment='考勤状态 1正常 2课前迟到 3课后迟到 4早退 5缺卡 6旷工 7异常')
    disable = Column(INTEGER(4), default=0, comment='删除状态')


class ErpWechatApp(Base):
    """
    企微应用表
    """
    __tablename__ = 'erp_wechat_app'

    id = Column(INTEGER(11), primary_key=True)
    parent_id = Column(INTEGER(11), default=0, comment='父级ID')
    app_id = Column(String(255), default='', comment='应用ID')
    secret = Column(String(255), default='', comment='密钥')
    code = Column(String(255), default='', comment='功能代码')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, comment='更新时间')
    disable = Column(INTEGER(4), default=0, comment='删除状态')









