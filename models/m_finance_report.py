"""
财务报表相关数据模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, DECIMAL, Date, Boolean
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class ErpFinanceIncomeReportDaily(Base):
    """
    营收报表日报表
    """
    __tablename__ = 'erp_finance_income_report_daily'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    report_date = Column(Date, nullable=False, comment='报表日期')
    class_id = Column(Integer, nullable=False, comment='班级ID')
    class_name = Column(String(255), comment='班级名称')
    class_type = Column(String(50), comment='班型(长期/短期)')
    period = Column(String(100), comment='期段')
    start_date = Column(Date, comment='开班时间')
    class_status = Column(Integer, comment='班级状态')
    course_id = Column(Integer, comment='课程ID')
    course_type = Column(String(100), comment='课程类型')
    course_name = Column(String(255), comment='课程名称')
    teacher_id = Column(Integer, comment='授课教师ID')
    teacher_name = Column(String(100), comment='授课教师名称')
    student_count = Column(Integer, default=0, comment='学生数量')
    total_class_times = Column(Integer, default=0, comment='总课次')
    completed_class_times = Column(Integer, default=0, comment='已完成课次')
    course_income_receivable = Column(DECIMAL(15, 2), default=0.00, comment='课程收入应收')
    discount_amount = Column(DECIMAL(15, 2), default=0.00, comment='优惠金额')
    course_refund = Column(DECIMAL(15, 2), default=0.00, comment='课程退款')
    course_income_actual = Column(DECIMAL(15, 2), default=0.00, comment='课程收入实收')
    lecture_fee_income = Column(DECIMAL(15, 2), default=0.00, comment='讲义费收入')
    lecture_fee_refund = Column(DECIMAL(15, 2), default=0.00, comment='讲义费退费')
    unconsumed_amount = Column(DECIMAL(15, 2), default=0.00, comment='未课消金额')
    consumed_amount = Column(DECIMAL(15, 2), default=0.00, comment='已课消金额')
    course_advisor_commission = Column(DECIMAL(15, 2), default=0.00, comment='课程顾问提成')
    actual_teacher_class_fee = Column(DECIMAL(15, 2), default=0.00, comment='实际教师课时费')
    current_gross_profit = Column(DECIMAL(15, 2), default=0.00, comment='当前毛利')
    current_gross_profit_rate = Column(DECIMAL(5, 4), default=0.0000, comment='当前毛利率')
    other_expenses = Column(DECIMAL(15, 2), default=0.00, comment='其他支出')
    current_actual_profit = Column(DECIMAL(15, 2), default=0.00, comment='当前实际利润')
    current_actual_profit_rate = Column(DECIMAL(5, 4), default=0.0000, comment='当前实际利润率')
    average_profit_per_class = Column(DECIMAL(15, 2), default=0.00, comment='平均利润(每节)')
    average_profit_per_student = Column(DECIMAL(15, 2), default=0.00, comment='平均利润(每人次)')
    expected_course_total_income = Column(DECIMAL(15, 2), default=0.00, comment='预计课程总收入')
    expected_teacher_class_fee = Column(DECIMAL(15, 2), default=0.00, comment='预计教师课时费')
    expected_profit = Column(DECIMAL(15, 2), default=0.00, comment='预期利润')
    expected_profit_rate = Column(DECIMAL(5, 4), default=0.0000, comment='预期利润率')
    expected_average_profit_per_class = Column(DECIMAL(15, 2), default=0.00, comment='预期平均利润(每节)')
    expected_average_profit_per_student = Column(DECIMAL(15, 2), default=0.00, comment='预期平均利润(每人次)')
    
    # 通用字段
    disable = Column(Integer, default=0, comment='是否禁用 0-否 1-是')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(Integer, comment='创建人ID')
    update_by = Column(Integer, comment='更新人ID')


class ErpFinanceSimpleReportDaily(Base):
    """
    简易报表日报表
    """
    __tablename__ = 'erp_finance_simple_report_daily'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    report_date = Column(Date, nullable=False, comment='报表日期')
    cost_type_id = Column(Integer, nullable=False, comment='费用类型ID')
    cost_type_name = Column(String(255), comment='费用类型名称')
    parent_id = Column(Integer, comment='父级费用类型ID')
    is_parent = Column(Boolean, default=False, comment='是否为父级分类')
    label_category = Column(String(50), comment='标签大类(营业毛利/总成本/其他收入/其他支出)')
    
    # 月度金额
    january = Column(DECIMAL(15, 2), default=0.00, comment='一月金额')
    february = Column(DECIMAL(15, 2), default=0.00, comment='二月金额')
    march = Column(DECIMAL(15, 2), default=0.00, comment='三月金额')
    april = Column(DECIMAL(15, 2), default=0.00, comment='四月金额')
    may = Column(DECIMAL(15, 2), default=0.00, comment='五月金额')
    june = Column(DECIMAL(15, 2), default=0.00, comment='六月金额')
    july = Column(DECIMAL(15, 2), default=0.00, comment='七月金额')
    august = Column(DECIMAL(15, 2), default=0.00, comment='八月金额')
    september = Column(DECIMAL(15, 2), default=0.00, comment='九月金额')
    october = Column(DECIMAL(15, 2), default=0.00, comment='十月金额')
    november = Column(DECIMAL(15, 2), default=0.00, comment='十一月金额')
    december = Column(DECIMAL(15, 2), default=0.00, comment='十二月金额')
    
    # 季度金额  
    quarter1 = Column(DECIMAL(15, 2), default=0.00, comment='第一季度金额')
    quarter2 = Column(DECIMAL(15, 2), default=0.00, comment='第二季度金额')
    quarter3 = Column(DECIMAL(15, 2), default=0.00, comment='第三季度金额')
    quarter4 = Column(DECIMAL(15, 2), default=0.00, comment='第四季度金额')
    
    # 半年度金额
    half_year1 = Column(DECIMAL(15, 2), default=0.00, comment='上半年金额')
    half_year2 = Column(DECIMAL(15, 2), default=0.00, comment='下半年金额')
    
    # 年度汇总
    annual = Column(DECIMAL(15, 2), default=0.00, comment='全年金额')
    total = Column(DECIMAL(15, 2), default=0.00, comment='费用类型合计金额')
    summary = Column(Text, comment='费用类型摘要信息')
    
    # 通用字段
    disable = Column(Integer, default=0, comment='是否禁用 0-否 1-是')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(Integer, comment='创建人ID')
    update_by = Column(Integer, comment='更新人ID')


class ErpFinanceReportGenerationLog(Base):
    """
    报表生成日志
    """
    __tablename__ = 'erp_finance_report_generation_log'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    report_type = Column(String(50), nullable=False, comment='报表类型(income_report/simple_report)')
    report_date = Column(Date, nullable=False, comment='报表日期')
    generation_start_time = Column(DateTime, comment='生成开始时间')
    generation_end_time = Column(DateTime, comment='生成结束时间')
    generation_duration = Column(Integer, comment='生成耗时(秒)')
    status = Column(String(20), default='running', comment='生成状态(running/success/failed)')
    records_processed = Column(Integer, default=0, comment='处理记录数')
    error_message = Column(Text, comment='错误信息')
    
    # 通用字段
    disable = Column(Integer, default=0, comment='是否禁用 0-否 1-是')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(Integer, comment='创建人ID')
    update_by = Column(Integer, comment='更新人ID')