# coding: utf-8
from datetime import datetime

from sqlalchemy import Column, DECIMAL, Date, DateTime, Float, JSON, String, Table, Text, text
from sqlalchemy.dialects.mysql import INTEGER, LONGTEXT, VARCHAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpEeoCourseClass(Base):
    __tablename__ = 'erp_eeo_course_class'

    id = Column(INTEGER(11), primary_key=True)
    eeo_course_id = Column(INTEGER(11))
    eeo_class_id = Column(INTEGER(11))
    eeo_class_name = Column(String(125))
    class_begin_time = Column(DateTime)
    class_end_time = Column(DateTime)
    lesson_key = Column(String(45))
    eeo_course_name = Column(String(125))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    is_get_vod = Column(INTEGER(4))


class ErpEeoVodPlaylist(Base):
    __tablename__ = 'erp_eeo_vod_playlist'

    id = Column(INTEGER(11), primary_key=True)
    eeo_course_class_id = Column(INTEGER(11))
    eeo_file_id = Column(String(45))
    eeo_size = Column(String(45))
    eeo_update_time = Column(DateTime)
    eeo_end_time = Column(DateTime)
    eeo_play_set = Column(JSON)
    is_download = Column(INTEGER(4))
    is_check = Column(INTEGER(4))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now)
    disable = Column(INTEGER(4), default=0)
    download_status = Column(INTEGER(4))
    duration = Column(INTEGER(11))
    index = Column(INTEGER(4))
