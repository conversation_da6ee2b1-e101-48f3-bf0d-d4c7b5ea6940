import copy
from fastapi import APIRouter, Depends, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from app_competition.crud import get_stu_class
from app_competition.serialiazer import BatchBookingParams, BatchDeleteBookingParams, StudentAddCenterParams, BatchCheckInParams, UpdateCompetitionTargetParams
from app_user.jjyw_verify import UserDictJJYW, get_current_user
from models.m_competition import ErpCompetitionCenterBooking, ErpCompetitionCenterChecking, ErpCompetitionCenterStu, ErpCompetitionCenterStuTarget
import app_teach.crud as app_teach_crud
import app_competition.crud as app_competition_crud
from models.old_models.old_student import RbStudent
import settings
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db, get_uat_db
from utils.message.AliyunSmsHandler import send_sms
from utils.response.response_handler import ApiFailedResponse, ApiSuccessResponse
from utils.other.config_handler import get_config
from datetime import datetime
import pandas as pd
from io import BytesIO
from sqlalchemy import and_, or_


router = APIRouter(prefix="/center", tags=["竞赛中心"])

erp_competition_center_stu = CRUD(ErpCompetitionCenterStu)
erp_competition_center_booking = CRUD(ErpCompetitionCenterBooking)
erp_competition_center_checking = CRUD(ErpCompetitionCenterChecking)
erp_competition_center_stu_target = CRUD(ErpCompetitionCenterStuTarget)



@router.get(f"/student/info")
async def query_student_info(
        keyword: str,
        page: int = None,
        page_size: int = None,
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 关键词查询学生
    """
    data = await app_competition_crud.get_stu_info(uat_db, keyword, page, page_size)
    return await ApiSuccessResponse(data)



@router.get(f"/student/class")
async def query_student_class(
        stu_id: int,
        page: int = None,
        page_size: int = None,
        uat_db: AsyncSession = Depends(get_uat_db),
):
    """
    # 查询学生的报名记录
    """
    data = await get_stu_class(uat_db, stu_id, page, page_size)
    return await ApiSuccessResponse(data)


# 检测学生是否有权限进入竞赛中心
@router.get("/student/center_permission")
async def student_center_permission(
        uat_db: AsyncSession = Depends(get_uat_db),
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 检测学生是否有权限进入竞赛中心
    """
    stu_info = await app_teach_crud.get_stu_info(uat_db, jjyw_user.uid)
    stu_obj = await erp_competition_center_stu.get_one(default_db, stu_id=stu_info.StuId)
    if not stu_obj:
        return await ApiSuccessResponse(False, f'{jjyw_user.uid}-无权限')
    return await ApiSuccessResponse(True, f'{jjyw_user.uid}-有权限')


# 检测老师是否有权限进入竞赛中心
@router.get("/teacher/center_permission")
async def teacher_center_permission(
        uat_db: AsyncSession = Depends(get_uat_db),
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 检测老师是否有权限进入竞赛中心
    """
    uid = jjyw_user.uid
    if uid in conf.get('center_permission').get('teacher_id'):
        return await ApiSuccessResponse(True, f'{uid}-有权限')
    settings.logger.warning(f'{uid}-无权限')
    return await ApiSuccessResponse(False, f'{uid}-无权限')


# 添加学生到竞赛中心
@router.post("/teacher/add_student")
async def add_student_to_center(
        params: StudentAddCenterParams,
        uat_db: AsyncSession = Depends(get_uat_db),
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    添加学生到竞赛中心
    """
    rb_student = CRUD(RbStudent)
    exist_stu = await erp_competition_center_stu.get_one(default_db, stu_id=params.stu_id)
    if exist_stu:
        return await ApiFailedResponse(f'学生已存在')
    
    stu_obj = await erp_competition_center_stu.create(default_db, commit=False, stu_id=params.stu_id, create_by=jjyw_user.uid, update_by=jjyw_user.uid)
    stu_info = await rb_student.get_one(uat_db, StuId=params.stu_id)
    stu_info_dict = copy.deepcopy(stu_info)
    stu_obj = copy.deepcopy(stu_obj)
    await default_db.commit()
    try:
        # 测试发送自定义模板短信
        if stu_info_dict:
            resp = await send_sms(stu_info_dict.StuRealMobile, 'SMS_488365025', {
                "stu_name": stu_info_dict.StuName,
            })
            print('竞赛中心短信发送：', resp)
    except Exception as e:
        print(f"发送短信失败: {e}")
    return await ApiSuccessResponse(stu_obj)



# 查询所有学生
@router.get("/teacher/all_student")
async def all_student(
        page: int = 1,
        page_size: int =10,
        keyword: str=None,
        uat_db: AsyncSession = Depends(get_uat_db),
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    查询所有学生
    - keyword: 关键词, 这里要模糊查询学生姓名和手机号
    """
    rb_student = CRUD(RbStudent)
    
    if keyword:
        # 如果有关键词，先在RbStudent表中查询符合条件的学生
        stu_conditions = [
            or_(
                RbStudent.StuName.like(f"%{keyword}%"),
                RbStudent.StuRealMobile.like(f"%{keyword}%")
            )
        ]
        # 获取所有符合关键词条件的学生ID
        all_matched_students = await rb_student.get_many(uat_db, raw=stu_conditions)
        matched_stu_ids = [stu.StuId for stu in all_matched_students]
        
        if not matched_stu_ids:
            # 如果没有符合条件的学生，返回空结果
            return await ApiSuccessResponse({
                "data": [],
                "count": 0,
            })
        
        # 查询竞赛中心中且符合关键词的学生（带分页）
        stu_list = await erp_competition_center_stu.get_many_with_pagination(
            default_db, 
            page=page, 
            page_size=page_size,
            raw=[ErpCompetitionCenterStu.stu_id.in_(matched_stu_ids)]
        )
        
        # 统计符合条件的总数
        count = await erp_competition_center_stu.count(
            default_db,
            raw=[ErpCompetitionCenterStu.stu_id.in_(matched_stu_ids)]
        )
        
    else:
        # 如果没有关键词，直接查询所有竞赛中心的学生
        stu_list = await erp_competition_center_stu.get_many_with_pagination(default_db, page=page, page_size=page_size)
        count = await erp_competition_center_stu.count(default_db)
    
    # 获取学生ID列表
    stu_ids = [i.stu_id for i in stu_list]
    
    if not stu_ids:
        return await ApiSuccessResponse({
            "data": [],
            "count": count,
        })
    
    # 批量查询学生详细信息
    stu_objs = await rb_student.get_many(uat_db, raw=[RbStudent.StuId.in_(stu_ids)])
    
    # 创建学生信息映射便于查找
    stu_info_map = {stu.StuId: stu for stu in stu_objs}
    
    # 按照stu_list的顺序构建返回数据，保持分页顺序
    stu_data = []
    for center_stu in stu_list:
        stu_info = stu_info_map.get(center_stu.stu_id)
        if stu_info:
            stu_data.append({
                "stu_id": stu_info.StuId,
                "stu_name": stu_info.StuName,
                "stu_mobile": stu_info.StuRealMobile,
            })
    
    return await ApiSuccessResponse({
        "data": stu_data,
        "count": count,
    })



# 批量预约日期
@router.post("/student/batch_booking")
async def batch_booking(
        params: BatchBookingParams,
        uat_db: AsyncSession = Depends(get_uat_db),
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 批量预约日期
    ## 参数
    - booking_list: 预约日期列表
        - booking_date: 预约日期 格式: 2025-05-28
        - start_time: 预约开始时间 格式: 2025-05-28 10:00:00
        - end_time: 预约结束时间 格式: 2025-05-28 12:00:00
    - stu_id: 学生ID 可选 不传则使用当前用户
    """
    stu_info = await app_teach_crud.get_stu_info(uat_db, jjyw_user.uid)
    # 检测是否有时间冲突
    booking_list = await erp_competition_center_booking.get_many(default_db, {
        "stu_id": stu_info.StuId,
    })
    for item in params.booking_list:
        # 将字符串时间转换为datetime对象进行比较
        try:
            item_start_time = datetime.strptime(item.start_time, '%Y-%m-%d %H:%M:%S')
            item_end_time = datetime.strptime(item.end_time, '%Y-%m-%d %H:%M:%S')
            item_booking_date = datetime.strptime(item.booking_date, '%Y-%m-%d').date()
        except ValueError as e:
            return await ApiFailedResponse(f'时间格式错误: {str(e)}')
            
        for booking in booking_list:
            # 检查日期是否相同
            if booking.booking_date == item_booking_date:
                # 使用标准的时间段重叠检测算法
                # 两个时间段重叠的条件：item_start < booking_end AND item_end > booking_start
                if item_start_time < booking.end_time and item_end_time > booking.start_time:
                    return await ApiFailedResponse(f'时间冲突: {item.start_time}-{item.end_time} 与已有预约 {booking.start_time}-{booking.end_time} 冲突，请检查你的预约时段后再试')
    # 通过则创建批量预约
    for item in params.booking_list:
        await erp_competition_center_booking.create(default_db, commit=False, booking_date=item.booking_date, start_time=item.start_time, end_time=item.end_time, stu_id=stu_info.StuId)
    await default_db.commit()
    return await ApiSuccessResponse(True)

# 批量删除预约
@router.post("/teacher/batch_delete_booking")
async def batch_delete_booking(
        params: BatchDeleteBookingParams,
        uat_db: AsyncSession = Depends(get_uat_db),
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    批量删除预约
    """
    for booking_id in params.booking_id_list:
        await erp_competition_center_booking.delete_one(default_db, booking_id, commit=False)
    await default_db.commit()
    return await ApiSuccessResponse(True)





# 展示时间段内 我每天的预约情况
@router.get("/student/booking_list")
async def booking_list(
        start_time:str,
        end_time:str,   
        uat_db: AsyncSession = Depends(get_uat_db),
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    展示时间段内 我每天的预约情况
    返回格式: {日期: [预约列表]}
    """
    stu_info = await app_teach_crud.get_stu_info(uat_db, jjyw_user.uid)
    if not stu_info:
        return await ApiFailedResponse('学生信息不存在')
    booking_list = await erp_competition_center_booking.get_many(default_db, raw=[
        ErpCompetitionCenterBooking.booking_date.between(start_time, end_time),
        ErpCompetitionCenterBooking.stu_id == stu_info.StuId,
    ])
    
    # 按日期分组
    grouped_bookings = {}
    for booking in booking_list:
        booking_date_str = booking.booking_date.strftime('%Y-%m-%d') if booking.booking_date else None
        if booking_date_str:
            if booking_date_str not in grouped_bookings:
                grouped_bookings[booking_date_str] = []
            grouped_bookings[booking_date_str].append(booking)
    
    return await ApiSuccessResponse(grouped_bookings)

# 教师端口展示
# 1. 指定日期范围start_time,end_time 展示所有预约情况
# 2. 点击单个日期，需要展示该日期所有预约情况， 并且字段需要附加该booking_id的签到情况
# 3. 可以对[booking_id..]进行批量签到

# 教师端：按日期范围展示所有预约情况
@router.get("/teacher/booking_list")
async def teacher_booking_list(
        start_time: str,
        end_time: str,
        stu_id:int=None,
        uat_db: AsyncSession = Depends(get_uat_db),
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    教师端：指定日期范围展示所有预约情况
    返回格式: {日期: [预约列表]}
    """
    raw = [
        ErpCompetitionCenterBooking.start_time.between(start_time, end_time),
        ErpCompetitionCenterBooking.disable == 0
    ]
    if stu_id:
        raw.append(ErpCompetitionCenterBooking.stu_id == stu_id)    
    booking_list = await erp_competition_center_booking.get_many(default_db, raw=raw)
    
    # 收集所有学生ID
    stu_ids = list(set([booking.stu_id for booking in booking_list if booking.stu_id]))
    
    # 批量查询学生信息
    rb_student = CRUD(RbStudent)
    stu_objs = await rb_student.get_many(uat_db, raw=[RbStudent.StuId.in_(stu_ids)]) if stu_ids else []
    
    # 创建学生信息映射
    stu_info_map = {stu.StuId: {"name": stu.StuName, "mobile": stu.StuRealMobile} for stu in stu_objs}
    
    # 按日期分组，并添加学生信息
    grouped_bookings = {}
    for booking in booking_list:
        booking_date_str = booking.booking_date.strftime('%Y-%m-%d') if booking.booking_date else None
        if booking_date_str:
            if booking_date_str not in grouped_bookings:
                grouped_bookings[booking_date_str] = []
            
            # 获取学生信息
            stu_info = stu_info_map.get(booking.stu_id, {"name": None, "mobile": None})
            
            # 转换为字典并添加学生信息
            booking_dict = {
                "id": booking.id,
                "booking_date": booking.booking_date.strftime('%Y-%m-%d') if booking.booking_date else None,
                "start_time": booking.start_time.strftime('%Y-%m-%d %H:%M:%S') if booking.start_time else None,
                "end_time": booking.end_time.strftime('%Y-%m-%d %H:%M:%S') if booking.end_time else None,
                "stu_id": booking.stu_id,
                "stu_name": stu_info["name"],  # 添加学生姓名
                "stu_mobile": stu_info["mobile"],  # 添加学生电话
                "create_time": booking.create_time.strftime('%Y-%m-%d %H:%M:%S') if booking.create_time else None,
            }
            grouped_bookings[booking_date_str].append(booking_dict)
    
    return await ApiSuccessResponse(grouped_bookings)


# 教师端：展示单个日期的预约情况（带签到状态）
@router.get("/teacher/booking_detail_by_date")
async def teacher_booking_detail_by_date(
        booking_date: str,  # 格式: 2025-01-31
        uat_db: AsyncSession = Depends(get_uat_db),
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    教师端：展示指定日期的所有预约情况，附加签到状态
    """
    from sqlalchemy import and_
    from sqlalchemy.orm import joinedload
    
    # 查询指定日期的所有预约
    booking_list = await erp_competition_center_booking.get_many(default_db, raw=[
        ErpCompetitionCenterBooking.booking_date == booking_date,
        ErpCompetitionCenterBooking.disable == 0
    ])
    
    # 收集所有学生ID
    stu_ids = list(set([booking.stu_id for booking in booking_list if booking.stu_id]))
    
    # 批量查询学生信息
    rb_student = CRUD(RbStudent)
    stu_objs = await rb_student.get_many(uat_db, raw=[RbStudent.StuId.in_(stu_ids)]) if stu_ids else []
    
    # 创建学生信息映射
    stu_info_map = {stu.StuId: {"name": stu.StuName, "mobile": stu.StuRealMobile} for stu in stu_objs}
    
    # 为每个预约添加签到状态
    booking_with_checkin = []
    for booking in booking_list:
        # 查询该预约的签到记录
        checkin_record = await erp_competition_center_checking.get_one(
            default_db, 
            booking_id=booking.id,
            check_status=1,  # 签到状态为1
        )
        
        # 获取学生信息
        stu_info = stu_info_map.get(booking.stu_id, {"name": None, "mobile": None})
        
        # 转换为字典并添加签到状态
        booking_dict = {
            "id": booking.id,
            "booking_date": booking.booking_date.strftime('%Y-%m-%d') if booking.booking_date else None,
            "start_time": booking.start_time.strftime('%Y-%m-%d %H:%M:%S') if booking.start_time else None,
            "end_time": booking.end_time.strftime('%Y-%m-%d %H:%M:%S') if booking.end_time else None,
            "stu_id": booking.stu_id,
            "stu_name": stu_info["name"],  # 添加学生姓名
            "stu_mobile": stu_info["mobile"],  # 添加学生电话
            "create_time": booking.create_time.strftime('%Y-%m-%d %H:%M:%S') if booking.create_time else None,
            "is_checked_in": bool(checkin_record),  # 是否已签到
            "checkin_time": checkin_record.create_time.strftime('%Y-%m-%d %H:%M:%S') if checkin_record and checkin_record.create_time else None
        }
        booking_with_checkin.append(booking_dict)
    
    return await ApiSuccessResponse(booking_with_checkin)


# 教师端：批量签到
@router.post("/teacher/batch_checkin")
async def teacher_batch_checkin(
        params: BatchCheckInParams,
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    教师端：批量签到
    """
    success_count = 0
    failed_list = []
    
    for booking_id in params.booking_id_list:
        # 检查预约是否存在
        booking = await erp_competition_center_booking.get_one(default_db, id=booking_id, disable=0)
        if not booking:
            failed_list.append({"booking_id": booking_id, "reason": "预约记录不存在"})
            continue
            
        # 检查是否已经签到过
        existing_checkin = await erp_competition_center_checking.get_one(
            default_db, 
            booking_id=booking_id, 
            check_status=1,
        )
        if existing_checkin:
            failed_list.append({"booking_id": booking_id, "reason": "已经签到过"})
            continue
            
        # 创建签到记录
        try:
            await erp_competition_center_checking.create(
                default_db, 
                commit=False,
                booking_id=booking_id,
                check_status=1,
            )
            success_count += 1
        except Exception as e:
            failed_list.append({"booking_id": booking_id, "reason": f"签到失败: {str(e)}"})
    
    await default_db.commit()
    
    result = {
        "success_count": success_count,
        "failed_count": len(failed_list),
        "failed_list": failed_list
    }
    
    return await ApiSuccessResponse(result)



# 使用file上传竞赛目标文件
@router.post("/teacher/upload_competition_target")
async def upload_competition_target(
        file: UploadFile,
        uat_db: AsyncSession = Depends(get_uat_db),
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    使用file上传竞赛目标文件
    Excel文件格式：支持多个sheet，每个sheet对应一个学生
    每个sheet格式：
    - 列A: 姓名
    - 列B: 手机号  
    - 列C: 日期
    - 列D: 学习计划时长
    - 列E-N: 学习目标1-10
    """
    
    # 读取Excel文件的所有sheet
    contents = await file.read()
    
    # try:
    #     # 读取所有sheet
    #     excel_data = pd.read_excel(BytesIO(contents), sheet_name=None)  # sheet_name=None会读取所有sheet
    # except Exception as e:
    #     return await ApiFailedResponse(f'Excel文件读取失败: {str(e)}')
    
    # # 验证是否有sheet
    # if not excel_data:
    #     return await ApiFailedResponse('Excel文件中没有找到任何sheet')
    
    try:
        # 读取所有sheet，强制手机号列为字符串类型，避免科学计数法
        excel_data = pd.read_excel(
            BytesIO(contents), 
            sheet_name=None,
            dtype={1: str}  # 第二列（手机号）强制为字符串
        )
    except Exception as e:
        return await ApiFailedResponse(f'Excel文件读取失败: {str(e)}')
    
    # 初始化总体结果统计
    total_success_count = 0
    total_failed_count = 0
    processed_students = []
    all_failed_list = []
    
    rb_student = CRUD(RbStudent)
    
    # 遍历每个sheet
    for sheet_name, df in excel_data.items():
        # print(f"解析sheet: {sheet_name}, {df}")
        sheet_result = {
            "sheet_name": sheet_name,
            "student_info": None,
            "success_count": 0,
            "failed_count": 0,
            "failed_list": []
        }
        
        try:
            # 验证sheet格式
            if len(df.columns) < 5:
                sheet_result["failed_list"].append({"row": "sheet", "reason": f"Sheet '{sheet_name}' 格式错误，列数不足"})
                sheet_result["failed_count"] = 1
                processed_students.append(sheet_result)
                continue
            
            # 从第一行获取学生信息
            if len(df) == 0:
                sheet_result["failed_list"].append({"row": "sheet", "reason": f"Sheet '{sheet_name}' 为空"})
                sheet_result["failed_count"] = 1
                processed_students.append(sheet_result)
                continue
                
            first_row = df.iloc[0]
            stu_name = str(first_row.iloc[0]).strip() if pd.notna(first_row.iloc[0]) else None
            stu_mobile = str(first_row.iloc[1]).strip() if pd.notna(first_row.iloc[1]) else None
            
            if not stu_name or not stu_mobile:
                sheet_result["failed_list"].append({"row": 1, "reason": f"Sheet '{sheet_name}' 第一行的姓名或手机号为空"})
                sheet_result["failed_count"] = 1
                processed_students.append(sheet_result)
                continue
            
            # 通过手机号查询学生ID
            stu_obj = await rb_student.get_one(uat_db, StuRealMobile=stu_mobile)
            if not stu_obj:
                sheet_result["failed_list"].append({"row": 1, "reason": f"Sheet '{sheet_name}' 根据手机号{stu_mobile}未找到学生信息"})
                sheet_result["failed_count"] = 1
                processed_students.append(sheet_result)
                continue
            
            stu_id = stu_obj.StuId
            sheet_result["student_info"] = {"name": stu_name, "mobile": stu_mobile, "stu_id": stu_id}
            
            # 处理该sheet的每一行数据
            for index, row in df.iterrows():
                try:
                    # 获取日期和计划时长
                    target_date_str = str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else None
                    plan_period = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else None
                    
                    if not target_date_str:
                        sheet_result["failed_list"].append({"row": index + 2, "reason": "日期为空"})
                        continue
                    
                    # 解析日期
                    try:
                        if isinstance(row.iloc[2], pd.Timestamp):
                            target_date = row.iloc[2].to_pydatetime()
                        else:
                            target_date = pd.to_datetime(target_date_str).to_pydatetime()
                    except Exception:
                        sheet_result["failed_list"].append({"row": index + 2, "reason": f"日期格式错误: {target_date_str}"})
                        continue
                    
                    # 处理学习目标（第4列开始，最多10个目标）
                    row_has_targets = False
                    for target_index in range(1, 11):  # 目标索引1-10
                        col_index = 3 + target_index  # 第4列开始
                        if col_index < len(row):
                            target_value = str(row.iloc[col_index]).strip() if pd.notna(row.iloc[col_index]) else None
                            
                            # 如果目标值不为空，则保存到数据库
                            if target_value and target_value.lower() not in ['nan', '']:
                                row_has_targets = True
                                # 检查是否已存在相同记录
                                existing_target = await erp_competition_center_stu_target.get_one(
                                    default_db,
                                    stu_id=stu_id,
                                    target_date=target_date.date(),
                                    target_index=target_index
                                )
                                
                                if existing_target:
                                    # 更新现有记录
                                    existing_target.target_value = target_value
                                    existing_target.plan_period = plan_period
                                    existing_target.update_time = datetime.now()
                                    existing_target.update_by = jjyw_user.uid
                                else:
                                    # 创建新记录
                                    await erp_competition_center_stu_target.create(
                                        default_db,
                                        commit=False,
                                        stu_name=stu_name,
                                        stu_id=stu_id,
                                        stu_tel=stu_mobile,
                                        target_date=target_date,
                                        plan_period=plan_period,
                                        target_index=target_index,
                                        target_value=target_value,
                                        create_by=jjyw_user.uid,
                                        update_by=jjyw_user.uid
                                    )
                    
                    if row_has_targets:
                        sheet_result["success_count"] += 1
                    
                except Exception as e:
                    sheet_result["failed_list"].append({"row": index + 2, "reason": f"处理失败: {str(e)}"})
                    continue
        
        except Exception as e:
            sheet_result["failed_list"].append({"row": "sheet", "reason": f"Sheet '{sheet_name}' 处理异常: {str(e)}"})
        
        # 统计该sheet的失败数量
        sheet_result["failed_count"] = len(sheet_result["failed_list"])
        
        # 累加到总体统计
        total_success_count += sheet_result["success_count"]
        total_failed_count += sheet_result["failed_count"]
        all_failed_list.extend([{**item, "sheet": sheet_name} for item in sheet_result["failed_list"]])
        
        processed_students.append(sheet_result)
    
    # 提交所有更改
    await default_db.commit()
    
    result = {
        "total_success_count": total_success_count,
        "total_failed_count": total_failed_count,
        "processed_sheets": len(excel_data),
        "students_processed": len([s for s in processed_students if s["student_info"]]),
        "detailed_results": processed_students,
        "all_failed_list": all_failed_list[:20] if len(all_failed_list) > 20 else all_failed_list,  # 只显示前20个错误
        "total_failed": len(all_failed_list)
    }
    
    return await ApiSuccessResponse(result, f"处理完成：共处理{len(excel_data)}个sheet，成功{total_success_count}条，失败{total_failed_count}条")
        


# 按学生日期分组查询竞赛目标
@router.get("/teacher/competition_targets_grouped")
async def get_competition_targets_grouped(
        target_date_start: str = None,
        target_date_end: str = None,
        stu_id:str=None,
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    按学生日期分组查询竞赛目标数据
    """
    conditions = [ErpCompetitionCenterStuTarget.disable == 0]
    if not stu_id:
        stu_info = await app_teach_crud.get_stu_info(uat_db, jjyw_user.uid)
        stu_id = stu_info.StuId

    conditions.append(ErpCompetitionCenterStuTarget.stu_id == stu_id)
    if target_date_start and target_date_end:
        start_date = pd.to_datetime(target_date_start).date()
        end_date = pd.to_datetime(target_date_end).date()
        conditions.append(ErpCompetitionCenterStuTarget.target_date.between(start_date, end_date))
    
    # 查询所有符合条件的记录
    targets = await erp_competition_center_stu_target.get_many(default_db, raw=conditions)
    
    # 按学生和日期分组
    grouped_data = {}
    for target in targets:
        stu_key = f"{target.stu_id}_{target.stu_name}"
        date_key = target.target_date.strftime('%Y-%m-%d') if target.target_date else 'unknown'
        
        if stu_key not in grouped_data:
            grouped_data[stu_key] = {
                "stu_id": target.stu_id,
                "stu_name": target.stu_name,
                "stu_tel": target.stu_tel,
                "dates": {}
            }
        
        if date_key not in grouped_data[stu_key]["dates"]:
            grouped_data[stu_key]["dates"][date_key] = {
                "target_date": date_key,
                "plan_period": target.plan_period,
                "targets": []  # 改为列表存储多个target对象
            }
        
        # 将target对象添加到列表中
        grouped_data[stu_key]["dates"][date_key]["targets"].append(target)
    
    # 转换为列表格式
    result_list = []
    for stu_key, stu_data in grouped_data.items():
        for date_key, date_data in stu_data["dates"].items():
            result_list.append({
                "stu_id": stu_data["stu_id"],
                "stu_name": stu_data["stu_name"],
                "stu_tel": stu_data["stu_tel"],
                "target_date": date_data["target_date"],
                "plan_period": date_data["plan_period"],
                "targets": sorted(date_data["targets"], key=lambda x: x.target_index)  # 按target_index排序
            })
    
    # 按target_date排序
    result_list.sort(key=lambda x: x["target_date"])
    
    return await ApiSuccessResponse(result_list)



# 修改竞赛目标
@router.put("/teacher/update_competition_target/{target_id}")
async def update_competition_target(
        target_id: int,
        params: UpdateCompetitionTargetParams,
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 修改竞赛目标
    """
    target_obj = await erp_competition_center_stu_target.get_one(default_db, id=target_id)
    if not target_obj:
        return await ApiFailedResponse("目标不存在")
    
    target_obj.target_value = params.target_value
    target_obj.plan_period = params.plan_period
    target_obj.comments = params.comments
    target_obj.update_time = datetime.now()
    target_obj.update_by = jjyw_user.uid
    await default_db.commit()
    return await ApiSuccessResponse("修改成功")


# 删除竞赛目标
@router.delete("/teacher/competition_target/{target_id}")
async def delete_competition_target(
        target_id: int,
        default_db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    删除竞赛目标
    """
    target_obj = await erp_competition_center_stu_target.get_one(default_db, id=target_id)
    if not target_obj:
        return await ApiFailedResponse("目标不存在")
    
    target_obj.disable = 1
    target_obj.update_time = datetime.now()
    target_obj.update_by = jjyw_user.uid
    await default_db.commit()
    return await ApiSuccessResponse("删除成功")