from typing import Optional
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app_competition.serialiazer import StudentAddParams, ExamPartParams
from models.m_class import ErpClass
from models.m_competition import ErpCompetitionLog, ErpCompetitionSituation, ErpCompetitionStu

from settings import CF
from utils.db.account_handler import UserDict, role_required
from utils.db.db_handler import get_default_db
from utils.response.response_handler import ApiSuccessResponse

router = APIRouter(prefix="/competition", tags=["竞赛"])

erp_class = CF.get_crud(ErpClass)
erp_competition_log = CF.get_crud(ErpCompetitionLog)
erp_competition_situation = CF.get_crud(ErpCompetitionSituation)
erp_competition_stu = CF.get_crud(ErpCompetitionStu)


# 针对erp_competition_stu表的增删改查操作

# 新增
@router.post("/student")
async def add_student(params: StudentAddParams,
                      db: AsyncSession = Depends(get_default_db),
                      # user: UserDict = Depends(role_required([])),
                      ):
    """
    # 新增学生
    - part_type:  # 1高考 2 一试 3 二试
    """
    # 新增学生
    base_info = {
        'stu_name': params.stu_name,
        'stu_tel': params.stu_tel,
        'stu_grade': params.stu_grade,
        'stu_target': params.stu_target,
    }
    stu_obj = await erp_competition_stu.create(db, **base_info, commit=False)
    stu_id = stu_obj.id
    if params.exam_base:
        await erp_competition_situation.create(db, **{
            'competition_stu_id': stu_id,
            'part_type': 1,
            'score': params.exam_base.score,
            'comments': params.exam_base.comments,
            'suggestion': params.exam_base.suggestion,
            'book_detail': params.exam_base.book_detail,
        }, commit=False)
    if params.exam1:
        await erp_competition_situation.create(db, **{
            'competition_stu_id': stu_id,
            'part_type': 2,
            'score': params.exam1.score,
            'comments': params.exam1.comments,
            'suggestion': params.exam1.suggestion,
            'book_detail': params.exam1.book_detail,
        }, commit=False)
    if params.exam2:
        await erp_competition_situation.create(db, **{
            'competition_stu_id': stu_id,
            'part_type': 3,
            'score': params.exam2.score,
            'comments': params.exam2.comments,
            'suggestion': params.exam2.suggestion,
            'book_detail': params.exam2.book_detail,
        }, commit=False)

    # 新增竞赛日志
    await erp_competition_log.create(db, **{
        'competition_stu_id': stu_id,
        'content': f'新增了学生{params.stu_name}',
    }, commit=False)
    await db.commit()
    return await ApiSuccessResponse(stu_id)


# 查询, 可根据手机号和姓名查询
@router.get("/student")
async def query_student(
        stu_name: Optional[str] = None,
        stu_tel: Optional[str] = None,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询学生
    """
    query_params = {}
    if stu_name:
        query_params['stu_name'] = stu_name
    if stu_tel:
        query_params['stu_tel'] = stu_tel
    stu_objs = await erp_competition_stu.get_many(db, query_params)
    if not stu_objs:
        return await ApiSuccessResponse([])
    return await ApiSuccessResponse(stu_objs)


# 根据id查询学生详情
@router.get("/student/{stu_id}")
async def query_student_detail(
        stu_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询学生详情
    """
    stu_obj = await erp_competition_stu.get_by_id(db, stu_id)
    if not stu_obj:
        return await ApiSuccessResponse('学生不存在')
    # 查询学生竞赛情况
    situation_obj = await erp_competition_situation.get_one(db, competition_stu_id=stu_id, part_type=1)
    # 一试
    situation1_obj = await erp_competition_situation.get_one(db, competition_stu_id=stu_id, part_type=2)
    # 二试
    situation2_obj = await erp_competition_situation.get_one(db, competition_stu_id=stu_id, part_type=3)
    # 日志
    log_objs = await erp_competition_log.get_many(db, {'competition_stu_id': stu_id})
    return await ApiSuccessResponse({
        'base_info': stu_obj,
        'exam_base': situation_obj,
        'exam1': situation1_obj,
        'exam2': situation2_obj,
        'logs': log_objs
    })


# 修改学生基础信息
@router.put("/student/{stu_id}")
async def update_student(
        stu_id: int,
        params: StudentAddParams,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 修改学生基础信息
    - 修改勿传考试情况，传递无效
    """
    # 修改学生基础信息
    # 先查询学生
    stu_obj = await erp_competition_stu.get_by_id(db, stu_id)
    if not stu_obj:
        return await ApiSuccessResponse('学生不存在')
    if params.stu_name and params.stu_name != stu_obj.stu_name:
        stu_obj.stu_name = params.stu_name
    if params.stu_tel and params.stu_tel != stu_obj.stu_tel:
        stu_obj.stu_tel = params.stu_tel
    if params.stu_grade and params.stu_grade != stu_obj.stu_grade:
        stu_obj.stu_grade = params.stu_grade
    if params.stu_target and params.stu_target != stu_obj.stu_target:
        stu_obj.stu_target = params.stu_target
    await erp_competition_log.create(db, **{
        'competition_stu_id': stu_id,
        'content': f'修改了学生{params.stu_name}',
    }, commit=False)
    await db.commit()
    return await ApiSuccessResponse('修改成功')


# 修改学生考试情况
@router.put("/student_exam/{exam_id}")
async def update_student_exam(
        exam_id: int,
        params: ExamPartParams,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 修改学生考试情况
    """
    # 修改学生基础信息
    # 先查询学生
    exam_obj = await erp_competition_situation.get_by_id(db, exam_id)
    if not exam_obj:
        return await ApiSuccessResponse('考试情况不存在')
    if params.part_type and params.part_type != exam_obj.part_type:
        exam_obj.part_type = params.part_type
    if params.score and params.score != exam_obj.score:
        exam_obj.score = params.score
    if params.comments and params.comments != exam_obj.comments:
        exam_obj.comments = params.comments
    if params.suggestion and params.suggestion != exam_obj.suggestion:
        exam_obj.suggestion = params.suggestion
    if params.book_detail and params.book_detail != exam_obj.book_detail:
        exam_obj.book_detail = params.book_detail
    await erp_competition_log.create(db, **{
        'competition_stu_id': exam_obj.competition_stu_id,
        'content': f'修改了学生考试情况',
    }, commit=False)
    await db.commit()
    return await ApiSuccessResponse('修改成功')


# 增加考试情况
@router.post("/student_exam")
async def add_student_exam(
        params: ExamPartParams,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 增加考试情况
    """
    # 新增考试情况
    exam_obj = await erp_competition_situation.create(db, **{
        'competition_stu_id': params.competition_stu_id,
        'part_type': params.part_type,
        'score': params.score,
        'comments': params.comments,
        'suggestion': params.suggestion,
        'book_detail': params.book_detail,
    }, commit=False)
    await erp_competition_log.create(db, **{
        'competition_stu_id': params.competition_stu_id,
        'content': f'新增了学生考试情况',
    }, commit=False)
    await db.commit()
    return await ApiSuccessResponse(exam_obj.id)


# 删除考试情况
@router.delete("/student_exam/{exam_id}")
async def delete_student_exam(
        exam_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 删除考试情况
    """
    # 删除考试情况
    exam_obj = await erp_competition_situation.get_by_id(db, exam_id)
    if not exam_obj:
        return await ApiSuccessResponse('考试情况不存在')
    await erp_competition_situation.delete_one(db, exam_id, commit=False)
    await erp_competition_log.create(db, **{
        'competition_stu_id': exam_obj.competition_stu_id,
        'content': f'删除了学生考试情况',
    }, commit=False)
    await db.commit()
    return await ApiSuccessResponse('删除成功')


# 创建日志
@router.post("/student_log")
async def add_student_log(
        competition_stu_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 创建日志
    """
    # 新增日志
    await erp_competition_log.create(db, **{
        'competition_stu_id': competition_stu_id,
        'content': "点击了重置跟进按钮",
    }, commit=False)
    await db.commit()
    return await ApiSuccessResponse('新增成功')
