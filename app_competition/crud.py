from sqlalchemy import and_, or_, select
from models.old_models.old_class import RbClass
from models.old_models.old_order import Rb<PERSON>rde<PERSON>, RbOrderMain
from models.old_models.old_student import RbStudent
from models.old_models.old_teach import RbTeacher
from utils.enum.enum_order import OrderState


async def get_stu_info(db, keyword, page=None, page_size=None):
    selects = [

        RbStudent.StuId,
        RbStudent.StuName,
        RbStudent.StuRealMobile,
        RbStudent.Serial,
    ]
    conditions = [
        or_(
            RbStudent.StuRealMobile.ilike(f"%{keyword}%"),
            RbStudent.StuName.ilike(f"%{keyword}%"),
        )
    ]
    stmt = (
        select(*selects)
        .select_from(RbStudent)
        .where(and_(*conditions))
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    # compiled_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compiled_sql)
    result = await db.execute(stmt)
    return result.fetchall()



async def get_stu_class(db, stu_id, page=None, page_size=None):
    selects = [
        RbClass.ClassName,
        RbOrder.CreateTime.label('OrderCreateTime'),
        RbOrder.OrderState,
        RbStudent.StuName,
        RbStudent.StuRealMobile,
        RbStudent.Serial,
        RbTeacher.TeacherName,
        RbTeacher.TeacherTel,
        RbOrder.PreferPrice,
    ]
    conditions = [
        RbStudent.StuId == stu_id,
        RbClass.ClassId > 0
    ]
    stmt = (
        select(*selects)
        .select_from(RbStudent)
        .outerjoin(RbOrderMain, RbOrderMain.StuId == RbStudent.StuId)
        .outerjoin(RbOrder, and_(
            RbOrder.OfferId == RbOrderMain.Id,
            RbOrder.ClassId > 0,
            RbOrder.OrderState.in_([
                OrderState.PAID.value,
                OrderState.REFUND.value,
            ]),
        ))
        .outerjoin(RbClass, RbClass.ClassId == RbOrder.ClassId)
        .outerjoin(RbTeacher, RbTeacher.TId == RbClass.Teacher_Id)
        .where(and_(*conditions))
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    return result.fetchall()