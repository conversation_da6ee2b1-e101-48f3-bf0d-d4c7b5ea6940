from typing import Optional, List

from pydantic import BaseModel


class BookDetailParams(BaseModel):
    book_name: Optional[str]
    process: Optional[float]


class ExamPartParams(BaseModel):
    competition_stu_id: Optional[int]
    part_type: Optional[int]  # 1高考 2 一试 3 二试
    score: Optional[float]
    comments: Optional[str]
    suggestion: Optional[str]
    book_detail: Optional[List]


class StudentAddParams(BaseModel):
    stu_name: str
    stu_tel: str
    stu_grade: str
    stu_target: str

    exam_base: Optional[ExamPartParams]
    exam1: Optional[ExamPartParams]
    exam2: Optional[ExamPartParams]


class StudentAddCenterParams(BaseModel):
    stu_id: int

class BatchBookingItem(BaseModel):
    booking_date: str
    start_time: str
    end_time: str

class BatchBookingParams(BaseModel):
    booking_list: List[BatchBookingItem]
    stu_id: Optional[int]


class BatchDeleteBookingParams(BaseModel):
    booking_id_list: List[int]


class BatchCheckInParams(BaseModel):
    booking_id_list: List[int]


class UpdateCompetitionTargetParams(BaseModel):
    target_value: str
    plan_period: str
    comments: str