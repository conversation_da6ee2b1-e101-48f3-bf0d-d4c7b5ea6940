from sqlalchemy import select, and_, func
from sqlalchemy.orm import aliased

from models.m_desk import ErpStudentConsultant, ErpStudentPlus
from models.m_discount import ErpStudentDiscountCoupon
from models.m_mall import MallAccount
from models.m_online_exam import ErpOnlineStuPaper, ErpOnlinePaper, ErpOnlineStuScore, ErpOnlinePaperQuestion, \
    ErpOnlineQuestion
from models.m_order import <PERSON>rp<PERSON><PERSON>r, ErpOrderOffer, ErpOrderRefundDetail, ErpOrderStudent
from models.m_student import ErpStudent
from models.models import ErpAccount
from models.old_models.old_student import RbStudent, RbAccountHk, RbAccount
from settings import logger
from utils.create_model.models_erp2 import ErpStudentDiscountFixed, ErpOnlineQuestionOption
from models.m_class import ErpClass


async def student_pool(db, page=None, page_size=None, user_id=None, consultant_status=None):
    """分页获取公海池erp_student_plus
    """
    ConsultantAccount = aliased(ErpAccount)
    selects = [
        ErpStudentPlus.id.label('stu_plus_id'),
        ErpStudentPlus.stu_id,
        ErpStudentPlus.training_plan,
        ErpStudentPlus.consultant_status,
        ErpStudentPlus.consultant_id,
        ConsultantAccount.employee_name.label('consultant_name'),
        ErpStudentPlus.parental_appeal,
        ErpStudentPlus.character,
        ErpStudentPlus.is_multi_child,
        ErpStudentPlus.real_guardian,
        ErpStudentPlus.major_concern_point,
        ErpStudentPlus.reject_point,
        ErpStudentPlus.famous_teacher_effect,
        ErpStudentPlus.other_comments,
        ErpStudentPlus.create_time,
        ErpStudentPlus.update_time,
        ErpStudentPlus.create_by,
        ErpStudentPlus.update_by,
        ErpStudentPlus.disable,
        ErpStudent.id.label('stu_id'),
        ErpStudent.stu_username,
        ErpStudent.stu_name,
        ErpStudent.stu_birth,
        ErpStudent.stu_gender,
        ErpStudent.stu_avatar,
        ErpStudent.stu_area,
        ErpStudent.stu_address,
        ErpStudent.stu_grade,
        ErpStudent.stu_school_name,
        ErpStudent.stu_serial,
        ErpStudent.how_known
    ]
    conditions = []
    if user_id:
        conditions.append(ErpStudentPlus.consultant_id == user_id)
    if consultant_status:
        conditions.append(ErpStudentPlus.consultant_status == consultant_status)
    stmt = (
        select(*selects)
        .select_from(ErpStudentPlus)
        .outerjoin(ErpStudent, ErpStudent.id == ErpStudentPlus.stu_id)
        .outerjoin(ConsultantAccount, ConsultantAccount.id == ErpStudentPlus.consultant_id)
        .where(and_(*conditions))
        .order_by(
            ErpStudentPlus.create_time.desc(),
            ErpStudent.create_time.desc(),
        )
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()


async def get_max_student(db):
    """获取最大学生id
    """
    stmt = (
        select(RbStudent.StuId,
               RbStudent.Serial
               )
        .select_from(RbStudent)
        .order_by(RbStudent.StuId.desc())
        .limit(1)
    )
    result = await db.execute(stmt)
    return result.fetchone()


async def insert_mall_without_repeat(db, item):
    stmt = (
        select(MallAccount)
        .where(MallAccount.PhoneNumber == item['PhoneNumber'])
    )
    result = await db.execute(stmt)
    obj = result.scalar()
    if obj:
        logger.info(f'[Mall]注册{item["PhoneNumber"]}已存在, 或登录老师账户')
        return False
    mall_account_obj = MallAccount(**item)
    db.add(mall_account_obj)
    try:
        await db.flush()
        await db.refresh(mall_account_obj)  # Refresh the object after commit
        logger.info(f"[Mall]成功创建学生：{item['PhoneNumber']}")
        return mall_account_obj.UserId
    except Exception as e:
        logger.info(f"[Mall]插入失败：{e}")


async def insert_account_hk_without_repeat(db, item):
    stmt = (
        select(RbAccountHk)
        .where(
            RbAccountHk.Id == item['Id'],
            RbAccountHk.Account == item['Account'],
            RbAccountHk.Password == item['Password'],
            RbAccountHk.AccountType == item['AccountType'],
            RbAccountHk.AccountId == item['AccountId'],
            RbAccountHk.Status == 0,
        )
    )
    result = await db.execute(stmt)
    obj = result.scalar()
    if obj:
        logger.info(f"[Account]已存在account_hk学生：{item['Account']}")
        return False
    account_hk_obj = RbAccountHk(**item)
    db.add(account_hk_obj)
    try:
        await db.flush()
        await db.refresh(account_hk_obj)  # Refresh the object after commit
        logger.info(f"[Account]成功创建account_hk学生：{item['Account']}")
        return account_hk_obj
    except Exception as e:
        logger.info(f"[Account]插入失败：{e}")


async def insert_student_without_repeat(db, item):
    stmt = (
        select(RbStudent)
        .where(RbStudent.StuRealMobile == item['StuRealMobile'])
    )
    result = await db.execute(stmt)
    obj = result.scalar()
    if obj:
        logger.info(f'[Student]注册{item["StuRealMobile"]}已存在')
        return obj.StuId
    student_obj = RbStudent(**item)
    db.add(student_obj)
    try:
        # await db.commit()
        # await db.refresh(student_obj)
        await db.flush()
        await db.refresh(student_obj)
        # return obj
        logger.info(f"[Student]成功创建学生：{item['StuRealMobile']}")
        return student_obj.StuId
    except Exception as e:
        logger.info(f"[Student]插入失败：{e}")


async def insert_account_without_repeat(db, item):
    stmt = (
        select(RbAccount)
        .where(RbAccount.Account == item['Account'])
    )
    result = await db.execute(stmt)
    obj = result.scalar()
    if obj:
        logger.info(f'[Account]注册{item["Account"]}已存在')
        return obj.Id
    account_obj = RbAccount(**item)
    db.add(account_obj)
    try:
        # await db.commit()
        await db.flush()
        await db.refresh(account_obj)  # Refresh the object after commit
        logger.info(f"[Account]成功创建学生：{item['Account']}")
        return account_obj.Id
    except Exception as e:
        logger.info(f"[Account]插入失败：{e}")


async def get_stu_exam_history(db, plus_id, page=None, page_size=None):
    selects = [
        ErpOnlineStuPaper.id.label('stu_paper_id'),
        ErpOnlineStuPaper.paper_id,
        ErpOnlineStuPaper.erp_stu_id,
        ErpOnlineStuPaper.current_process,
        ErpOnlineStuPaper.all_process,
        ErpOnlineStuPaper.create_time,
        ErpOnlinePaper.total_score,
        ErpOnlinePaper.paper_name
    ]
    conditions = [
        ErpStudentPlus.id == plus_id
    ]
    stmt = (
        select(*selects)
        .select_from(ErpStudentPlus)
        .outerjoin(ErpStudent, ErpStudent.id == ErpStudentPlus.stu_id)
        .outerjoin(ErpOnlineStuPaper, ErpOnlineStuPaper.erp_stu_id == ErpStudentPlus.stu_id)
        .outerjoin(ErpOnlinePaper, ErpOnlinePaper.id == ErpOnlineStuPaper.paper_id)
        .where(and_(*conditions))
        .order_by(ErpOnlineStuPaper.create_time.desc())
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()


async def get_stu_score_result(db, stu_paper_id):
    selects = [
        ErpOnlineStuScore.id.label('score_id'),
        ErpOnlineStuScore.paper_stu_id,
        ErpOnlineStuScore.paper_question_id,
        ErpOnlineStuScore.choice_option_id,
        ErpOnlineStuScore.is_correct,
        ErpOnlineStuScore.stu_score,
        ErpOnlineStuScore.answer.label('stu_answer'),
        ErpOnlinePaperQuestion.question_id,
        ErpOnlinePaperQuestion.sort,
        ErpOnlineQuestion.content,
        ErpOnlineQuestion.difficulty_level,
        ErpOnlineQuestion.score,
        ErpOnlineQuestion.knowledge_point,
        ErpOnlineQuestion.analysis,
        ErpOnlineQuestion.question_type,
        ErpOnlineQuestionOption.option_content,
        ErpOnlineQuestionOption.answer.label('option_answer'),
        ErpOnlineQuestionOption.correct.label('option_correct')
    ]
    conditions = [
        ErpOnlineStuScore.paper_stu_id == stu_paper_id,
        ErpOnlineStuScore.disable == 0
    ]
    stmt = (
        select(*selects)
        .select_from(ErpOnlineStuScore)
        .outerjoin(ErpOnlinePaperQuestion, ErpOnlinePaperQuestion.id == ErpOnlineStuScore.paper_question_id)
        .outerjoin(ErpOnlineQuestion, ErpOnlineQuestion.id == ErpOnlinePaperQuestion.question_id)
        .outerjoin(ErpOnlineQuestionOption, ErpOnlineQuestionOption.id == ErpOnlineStuScore.choice_option_id)
        .where(and_(*conditions))
        .order_by(ErpOnlinePaperQuestion.sort)
    )
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()

async def student_pool_order_module(db, page=None, page_size=None, stu_name=None, order_id=None, start_time=None, end_time=None, count=False):
    """
    获取公海池学生订单数据
    
    Args:
        db: 数据库会话
        page: 页码
        page_size: 每页大小
        stu_name: 学生姓名关键词
        order_id: 订单ID
        start_time: 开始时间
        end_time: 结束时间
        count: 是否只返回总数
        
    Returns:
        count=True时返回总数，否则返回订单列表
    """
    # 构建查询条件
    conditions = [
        # ErpOrder.id != None
    ]
    if stu_name:
        conditions.append(ErpStudent.stu_name.like(f'%{stu_name}%'))
    if order_id:
        conditions.append(ErpOrder.id == order_id)
    if start_time:
        conditions.append(ErpOrder.create_time >= start_time)
    if end_time:
        conditions.append(ErpOrder.create_time <= end_time)
    
    # 构建基础查询连接
    base_join = (
        ErpStudentPlus.__table__.outerjoin(ErpStudent, ErpStudent.id == ErpStudentPlus.stu_id)
        .outerjoin(ErpOrderStudent, ErpOrderStudent.stu_id == ErpStudentPlus.stu_id)
        .outerjoin(ErpOrder, and_(ErpOrder.order_student_id == ErpOrderStudent.id, ErpOrder.order_class_type == 1))
        .outerjoin(ErpOrderOffer, ErpOrderOffer.id == ErpOrder.offer_id)
        .outerjoin(ErpClass, ErpClass.id == ErpOrderStudent.class_id)
        .outerjoin(ErpStudentConsultant, ErpStudentConsultant.id == ErpStudentPlus.consultant_id)
        .outerjoin(ErpAccount, ErpAccount.id == ErpStudentConsultant.account_id)
    )
    
    # 如果只需要计数
    if count:
        stmt = (
            select(func.count().label('total'))
            .select_from(base_join)
            .where(and_(*conditions))
        )
        result = await db.execute(stmt)
        return result.scalar()
    
    # 查询详细数据
    selects = [
        ErpStudentPlus.id.label('stu_plus_id'),
        ErpStudentPlus.stu_id,
        ErpStudentPlus.training_plan,
        ErpStudentPlus.consultant_status,
        ErpOrderStudent.id.label('order_student_id'),
        ErpStudent.stu_name,
        ErpStudent.stu_avatar,
        ErpStudent.stu_gender,
        ErpOrder.buy_num,
        ErpOrder.id.label('order_id'),
        ErpOrder.create_time.label('order_create_time'),
        ErpClass.class_name,
        ErpClass.id.label('class_id'),
        ErpAccount.employee_name.label('consultant_name'),
        ErpAccount.avatar.label('consultant_avatar'),
        ErpOrderOffer.id.label('offer_id')
    ]
    
    stmt = (
        select(*selects)
        .select_from(base_join)
        .where(and_(*conditions))
    )
    
    # 分页处理
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    
    result = await db.execute(stmt)
    return result.fetchall()
    
    
    