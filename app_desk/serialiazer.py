from typing import Optional, List

from pydantic import BaseModel


class ErpStudentConsultantBase(BaseModel):
    account_id: Optional[int] = None
    consultant_qr: Optional[str] = None
    nick_name: Optional[str] = None
    enterprise_image: Optional[str] = None

    class Config:
        orm_mode = True


class StudentInfoBase(BaseModel):
    phone_number: Optional[str] = None
    campus_id: Optional[int] = None
    stu_name: Optional[str] = None
    gender: Optional[int] = None
    birth: Optional[str] = None
    stu_address: Optional[str] = None
    stu_area: Optional[str] = None
    grade: Optional[str] = None
    how_known: Optional[str] = None
    current_school: Optional[str] = None
    stu_idcard: Optional[str] = None

    class Config:
        orm_mode = True


class StudentPlusInfoBase(BaseModel):
    training_plan: Optional[str] = None
    consultant_status: Optional[int] = None
    consultant_id: Optional[int] = None
    parental_appeal: Optional[str] = None
    character: Optional[str] = None
    is_multi_child: Optional[str] = None
    real_guardian: Optional[str] = None
    major_concern_point: Optional[str] = None
    reject_point: Optional[str] = None
    famous_teacher_effect: Optional[str] = None
    other_comments: Optional[str] = None
    course_labels: Optional[List[int]] = None

    class Config:
        orm_mode = True


class StudentParams(BaseModel):
    student_base_info: StudentInfoBase
    student_plus_base: Optional[StudentPlusInfoBase] = None

    class Config:
        orm_mode = True


class PlusFlow(BaseModel):
    plus_id: int
    content: str


class OfflineParams(BaseModel):
    appointment_time: Optional[str] = None
    appointment_center_id: Optional[int] = None
    paper_name: Optional[str] = None
    stu_score: Optional[float] = None
    paper_score: Optional[float] = None
    exam_result: Optional[str] = None
    plus_id: Optional[int] = None
    attachment: Optional[list] = None
