from datetime import datetime
import settings
from app_desk.crud import get_max_student
from app_user.crud import get_classin_config
from models.m_desk import ErpStudentConsultant, ErpStudentPlus
from models.m_mall import MallAccount
from models.m_student import ErpStudent
from models.old_models.old_student import RbStudent, RbAccountHk, RbAccount
from modules.classin.classin_helper import ClassInHelper
from utils.enum.enum_consultant import ConsultantStatus
from utils.enum.enum_log import LogType
from utils.other.DBLogHandler import LogHandler
from utils.other.PassEncrypt import encrypt
from utils.other.tools import hide_phone_number
from utils.response.response_handler import ApiFailedResponse, ApiSuccessResponse
from settings import logger

erp_student_consultant = settings.CF.get_crud(ErpStudentConsultant)

erp_student_plus = settings.CF.get_crud(ErpStudentPlus)
erp_student = settings.CF.get_crud(ErpStudent)
rb_student = settings.CF.get_crud(RbStudent)
rb_account_hk = settings.CF.get_crud(RbAccountHk)
mall_account = settings.CF.get_crud(MallAccount)
rb_account = settings.CF.get_crud(RbAccount)


async def get_next_consultant(db):
    """
    获取下一个课程顾问（不自动提交事务）
    :param db:
    :return:
    """
    consultant_objs = await erp_student_consultant.get_many(db)
    if not consultant_objs:
        return None
        
    consultant_objs = sorted(consultant_objs, key=lambda x: x.id)
    
    # 如果只有一个顾问,直接返回该顾问
    if len(consultant_objs) == 1:
        consultant_objs[0].current_point = 1
        return consultant_objs[0]
        
    current_obj = await erp_student_consultant.get_one(db, current_point=1)
    next_index = consultant_objs.index(current_obj) + 1
    if next_index >= len(consultant_objs):
        next_obj = consultant_objs[0]
    else:
        next_obj = consultant_objs[next_index]
    next_obj.current_point = 1
    current_obj.current_point = 0
    return current_obj


# 分配课程顾问
async def assign_consultant(db, stu_id, user_id=None):
    """
    分配课程顾问
    :param db:
    :param stu_id:
    :return:
    """
    # 获取学生的学生信息
    stu_plus_obj = await erp_student_plus.get_one(db, stu_id=stu_id)
    if not stu_plus_obj:
        stu_plus_obj = await erp_student_plus.create(db,
                                                     commit=False,
                                                     stu_id=stu_id,
                                                     create_by=user_id,
                                                     update_by=user_id,
                                                     )
    if stu_plus_obj.consultant_id and stu_plus_obj.consultant_id > 0:
        # return await ApiFailedResponse('请勿再次分配课程顾问')
        # 毛老师要求这里不做处理了
        return True
    else:
        # 获取课程顾问名单
        next_consultant = await get_next_consultant(db)
        # 更新学生的课程顾问
        stu_plus_obj.consultant_id = next_consultant.account_id  # 课程顾问id
        stu_plus_obj.consultant_status = ConsultantStatus.FOLLOW_UP.value  # 课程顾问状态改为跟进中
        stu_plus_obj.update_time = datetime.now()
        stu_plus_obj.complete_exam = 1
        return True


async def register_student_of_pool(db, uat_db, stu_params, user_id):
    """
    注册公海池学生 引申自 -- 可能的重复代码：user视图中的注册学生
    :param db:
    :return:
    """
    base_info = stu_params.student_base_info
    plus_info = stu_params.student_plus_base
    classin_uid = 0
    phone_number = base_info.phone_number
    stu_name = base_info.stu_name
    student_obj = await get_max_student(uat_db)
    # 生成学号
    code = student_obj.Serial
    letter = code[0]  # 获取首字母S
    number = str(int(code[1:]) + 1).zfill(6)  # 数字部分加1,并保持6位数格式
    # 组合新编码
    serial = letter + number
    # 注册classIn以及获取回调
    item = {
        'telephone': phone_number,
        'nickname': stu_name,
        'password': phone_number[-6:],
        'addToSchoolMember': 1
    }
    r_msg = await ClassInHelper.register_user(**item)
    if r_msg.get('error_info').get('errno') in (1, 135):
        classin_uid = r_msg.get('data')
        if r_msg.get('error_info').get('errno') == 135:
            t_msg = await ClassInHelper.add_school_student({
                'studentAccount': phone_number,
                'studentName': stu_name,
                'addToSchoolMember': 1
            })
            if t_msg.get('error_info').get('errno') == 133:
                logger.info(f'[ClassIn]注册{phone_number}:{stu_name}已存在')
                return await ApiFailedResponse('该手机号Classin已存在')
            else:
                logger.info(f'[ClassIn]注册{phone_number}:{stu_name},状态：{t_msg}')
        else:
            logger.info(f'[ClassIn]注册{phone_number}:{stu_name}成功')
    else:
        logger.info(f'[ClassIn]注册{phone_number}:{stu_name}失败， {r_msg}')

    # 插入rb_student表
    student_item = {
        "StuName": stu_name,
        "StuTel": hide_phone_number(phone_number),
        "StuSex": 0 if base_info.gender == '男' else 1,
        "StuBirth": base_info.birth,
        "AreaName": base_info.stu_area,
        "StuAddress": base_info.stu_address,
        "School_Id": 1,
        "Group_Id": 100001,
        "CreateBy": 4001,
        "UpdateBy": 4001,
        "CreateTime": datetime.now(),
        "UpdateTime": datetime.now(),
        "FirstEnrollDate": datetime.now(),
        "StuRealMobile": base_info.phone_number,
        "BaseCondition": base_info.current_school,
        "SchoolClass": base_info.grade,
        "SchoolSource": base_info.how_known,
        "Serial": serial,
        "ClassInAccount": phone_number,
        "ClassInUID": classin_uid,
        "StuStage": 1,
    }
    stu_obj = await rb_student.get_one(uat_db, StuRealMobile=phone_number)
    account_obj = await rb_account.get_one(uat_db, Account=phone_number)
    mall_account_obj = await mall_account.get_one(uat_db, PhoneNumber=phone_number)
    if not stu_obj:
        stu_obj = await rb_student.create(uat_db, commit=False, **student_item)
        # 既然没有重复记录，那么插入公海表erp_student_plus
        student_plus_item = {
            "stu_id": stu_obj.StuId,
            "consultant_status": ConsultantStatus.WAIT_EXAM.value,
            "consultant_id": user_id,
            "create_by": user_id,
            "update_by": user_id,
            "complete_exam": 0,
        }
        # 插入学生附加信息
        if plus_info.training_plan:
            student_plus_item.update({"training_plan": plus_info.training_plan})
        if plus_info.parental_appeal:
            student_plus_item.update({"parental_appeal": plus_info.parental_appeal})
        if plus_info.character:
            student_plus_item.update({"character": plus_info.character})
        if plus_info.is_multi_child:
            student_plus_item.update({"is_multi_child": plus_info.is_multi_child})
        if plus_info.real_guardian:
            student_plus_item.update({"real_guardian": plus_info.real_guardian})
        if plus_info.major_concern_point:
            student_plus_item.update({"major_concern_point": plus_info.major_concern_point})
        if plus_info.reject_point:
            student_plus_item.update({"reject_point": plus_info.reject_point})
        if plus_info.famous_teacher_effect:
            student_plus_item.update({"famous_teacher_effect": plus_info.famous_teacher_effect})
        if plus_info.other_comments:
            student_plus_item.update({"other_comments": plus_info.other_comments})
        if plus_info.course_labels:
            student_plus_item.update({"course_labels": plus_info.course_labels})

        plus_obj = await erp_student_plus.create(db, commit=False, **student_plus_item)
        erp_stu_item = {
            "stu_username": phone_number,
            "stu_name": stu_name,
            "stu_birth": base_info.birth,
            "stu_gender": base_info.gender,
            "stu_avatar": base_info.stu_avatar,
            "stu_area": base_info.stu_area,
            "stu_address": base_info.stu_address,
            "stu_grade": base_info.grade,
            "stu_idcard": base_info.stu_idcard,
            "stu_school_name": base_info.current_school,
            "stu_serial": serial,
            "how_known": base_info.how_known,
            "classin_uid": classin_uid,
        }
        await erp_student.create(db, commit=False, **erp_stu_item)

        log = LogHandler(db)
        await log.create_log(
            log_type=LogType.STUDENT_PLUS_ENTRY.value,
            action='新建客户',
            content=["添加了该客户"],
            account_id=user_id,
            obj_id=plus_obj.id
        )
    stu_id = stu_obj.StuId
    # 插入rb_account表
    if not account_obj:
        account_item = {
            "Account": phone_number,
            "Password": encrypt(phone_number[-6:]),
            "AccountType": 4,
            "AccountId": stu_id,
            "CreateBy": 4001,
            "UpdateBy": 4001,
            "CreateTime": datetime.now(),
            "UpdateTime": datetime.now(),
            "Group_Id": 100001,
            "School_Id": 1,
        }
        account_obj = await rb_account.create(uat_db, commit=False, **account_item)
    uid = account_obj.Id
    # 插入mall_account表
    if not mall_account_obj:
        mall_item = {
            "Uid": uid,
            "StuId": stu_id,
            "PhoneNumber": phone_number,
            "RealName": stu_name,
            "CreateTime": datetime.now(),
        }
        mall_account_obj = await mall_account.create(uat_db, commit=False, **mall_item)
    userid = mall_account_obj.UserId
    if not userid:
        await uat_db.rollback()
        return await ApiFailedResponse('该手机号已存在')
    # 这里插入classin同步学生信息到ERP的逻辑
    rb_account_hk_item = {
        "Id": 0,
        "Account": phone_number,
        "Password": encrypt(phone_number[-6:]),
        "AccountType": 2,
        "AccountId": classin_uid,
        "Status": 0,
    }
    account_hk_obj = await  rb_account_hk.get_one(uat_db, **rb_account_hk_item)
    if not account_hk_obj:
        await rb_account_hk.create(uat_db, commit=False, **rb_account_hk_item)

    await uat_db.commit()
    await db.commit()

    return await ApiSuccessResponse({
        "student": stu_id,
        "account": uid,
        "userid": userid,
        "classin_uid": classin_uid,
        "phone_number": phone_number
    })
