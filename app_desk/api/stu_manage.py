from typing import Optional

from fastapi import APIRouter, Depends
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from models.m_online_exam import ErpOnlinePaperCourse
import settings
# from app_desk.crud import student_pool, get_stu_exam_history, get_stu_score_result, get_student_list_crud
from app_desk.modules import assign_consultant, register_student_of_pool
from app_desk.serialiazer import ErpStudentConsultantBase, StudentParams, PlusFlow, OfflineParams
from app_office.api.consumable import erp_account
from app_user.api.department import account_by_department_id
from models.m_common import ErpLog
from models.m_desk import ErpStudentConsultant, ErpStudentPlus, ErpOfflineExam
from models.m_student import ErpStudent
from utils.create_model.models_erp2 import ErpOnlineStuPaper
from utils.db.account_handler import UserDict, role_required
from utils.db.db_handler import get_default_db, get_uat_db
from utils.db.default_crud import generate_crud_routes
from utils.enum.enum_log import LogType
from utils.other.DBLogHandler import LogHandler
from utils.other.MathMap import get_knowledge_ability_map

from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

router = APIRouter(prefix="/stu_manage", tags=["教务"])

erp_student = settings.CF.get_crud(ErpStudent)
# erp_student_plus = settings.CF.get_crud(ErpStudentPlus)
# erp_log = settings.CF.get_crud(ErpLog)
# erp_offline_exam = settings.CF.get_crud(ErpOfflineExam)
# erp_online_paper_course = settings.CF.get_crud(ErpOnlinePaperCourse)
# erp_online_stu_paper = settings.CF.get_crud(ErpOnlineStuPaper)

# 获取学生列表
# @router.get("/student_list")
# async def get_student_list(
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
# ):
#     await get_student_list_crud(db, page=None, page_size=None)



