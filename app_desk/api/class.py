from datetime import datetime

from fastapi import APIRouter, Depends
from sqlalchemy import or_, func
from sqlalchemy.ext.asyncio import AsyncSession

import settings
from app_teach.crud import query_class_with_page, get_classplan_by_classid, get_transfer_record, \
    get_class_change_record, query_teacher_class_plan, query_teacher_class_times
from app_teach.modules import generate_serial_number, get_course_config, add_course_log
from app_teach.serializer import CourseBase, CourseTermBase, CourseCategoryBase, ClassBase, ClassModify, OutlineBase, \
    ClassPlanBase
from models.m_class import ErpClass, ErpCourse, ErpCourseTerm, ErpCourseCategory, ErpCourseLog, ErpCourseOutline, \
    ErpClassPlan
from models.m_teacher import ErpAccountTeacher
from models.old_models.old_class import RbClassPlan, RbStudentTempinvitation
from settings import CF
from utils.db.account_handler import UserDict, role_required
from utils.db.db_handler import get_default_db, get_uat_db
from utils.enum.enum_class import CourseLogType
from utils.other.config_handler import get_config
from utils.response.response_handler import ApiSuccessResponse

router = APIRouter(prefix="/class", tags=["班级和课程"])

erp_class = CF.get_crud(ErpClass)
erp_class_plan = CF.get_crud(ErpClassPlan)
erp_course = CF.get_crud(ErpCourse)
erp_course_term = CF.get_crud(ErpCourseTerm)
erp_course_category = CF.get_crud(ErpCourseCategory)
erp_course_outline = CF.get_crud(ErpCourseOutline)
erp_course_log = CF.get_crud(ErpCourseLog)

erp_account_teacher = CF.get_crud(ErpAccountTeacher)


# 获取课程配置信息
@router.get("/course_config")
async def get_course_config_info(
        db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
):
    """
    # 获取课程配置信息
    """
    # 调取课程配置信息
    course_config = await get_course_config(db, conf)
    return await ApiSuccessResponse(course_config)


# 创建针对erp_course_term的增删改查
@router.post("/course_term")
async def create_course_term(
        term_params: CourseTermBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增学期
    """
    create_item = {
        "term_name": term_params.term_name,
        "term_type": term_params.term_type,
        "year": term_params.year,
    }

    await erp_course_term.create(db, commit=True, **create_item)
    return await ApiSuccessResponse(True)
