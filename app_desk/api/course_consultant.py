import copy
from typing import Optional

from fastapi import APIRouter, Depends
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from models.m_online_exam import ErpOnlinePaperCourse
from models.m_order import ErpOrderRefundDetail
import settings
from app_desk.crud import student_pool, get_stu_exam_history, get_stu_score_result, student_pool_order_module
from app_desk.modules import assign_consultant, register_student_of_pool
from app_desk.serialiazer import ErpStudentConsultantBase, StudentParams, PlusFlow, OfflineParams
from app_office.api.consumable import erp_account
from app_user.api.department import account_by_department_id
from models.m_common import ErpLog
from models.m_desk import ErpStudentConsultant, ErpStudentPlus, ErpOfflineExam
from models.m_student import ErpStudent
from utils.create_model.models_erp2 import ErpOnlineStuPaper
from utils.db.account_handler import UserDict, role_required
from utils.db.db_handler import get_default_db, get_uat_db
from utils.db.default_crud import generate_crud_routes
from utils.enum.enum_log import LogType
from utils.other.DBLogHandler import LogHandler
from utils.other.MathMap import get_knowledge_ability_map
from modules.classin.classinApiHandler import ClassInSDK

from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

router = APIRouter(prefix="/consultant", tags=["课程顾问"])

erp_student = settings.CF.get_crud(ErpStudent)
erp_student_plus = settings.CF.get_crud(ErpStudentPlus)
erp_log = settings.CF.get_crud(ErpLog)
erp_offline_exam = settings.CF.get_crud(ErpOfflineExam)
erp_online_paper_course = settings.CF.get_crud(ErpOnlinePaperCourse)
erp_online_stu_paper = settings.CF.get_crud(ErpOnlineStuPaper)
erp_order_refund_detail = settings.CF.get_crud(ErpOrderRefundDetail)

# 初始化ClassIn SDK
classin_sdk = ClassInSDK()

# 课程顾问的增删改查路由（这里使用通用的crud路由创建）
paper_course_router = generate_crud_routes(
    db_model=ErpStudentConsultant,
    schema=ErpStudentConsultantBase,
    AuthModel=UserDict,
    default_db=get_default_db,
    prefix="consultant",
    title="课程顾问",
)
router.include_router(paper_course_router)


# 分页获取公海池erp_student_plus
@router.get("/student_pool")
async def get_student_pool(
        page: int = None,
        page_size: int = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 分页获取公海池erp_student_plus
    """
    data = await student_pool(db, page, page_size)
    count_data = await student_pool(db)
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 我的客户
@router.get("/my_student")
async def get_my_student(
        page: int = None,
        page_size: int = None,
        consultant_status: int = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 我的客户
    - consultant_status  0 待测验 1 跟进中 2 学习中 3 无报名计划 4 已毕业
    """
    data = await student_pool(db, page, page_size, user_id=user.uid, consultant_status=consultant_status)
    count_data = await student_pool(db, user_id=user.uid, consultant_status=consultant_status)
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 添加公海池学生
@router.post("/student_pool")
async def add_student_pool(
        params: StudentParams,
        db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 添加公海池学生
    """
    flag = await register_student_of_pool(db, uat_db, params, user_id=user.uid)
    if flag:
        return await ApiSuccessResponse(True)
    return await ApiFailedResponse("添加公海池学生失败")


# 查询公海池的指定学生
@router.get("/student_pool/{stu_id}")
async def get_student_pool_by_stu_id(
        stu_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 查询公海池的指定学生
    - plus_info: 学生的附加信息
    """
    stu_base_info = await erp_student.get_by_id(db, stu_id)
    if not stu_base_info:
        return await ApiFailedResponse("学生id不存在")
    plus_info = await erp_student_plus.get_one(db, stu_id=stu_id)
    # if not plus_info:
    #     return await ApiFailedResponse("学生附加信息不存在")
    # 顾问信息
    if plus_info and plus_info.consultant_id:
        consultant_info = await erp_account.get_by_id(db, plus_info.consultant_id)
        # plus_info.consultant_name = consultant_info.employee_name
        if consultant_info:
            plus_info.consultant_name = consultant_info.employee_name
        else:
            plus_info.consultant_name = "顾问账号异常"
    stu_base_info.plus_info = plus_info
    return await ApiSuccessResponse(stu_base_info)


# 修改公海池的指定学生的状态
@router.put("/consultant_status/{stu_id}")
async def update_consultant_status(
        stu_id: int,
        consultant_status: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改公海池的指定学生的状态
    - consultant_status: 1 跟进中 2 学习中 3 无报名计划 4 已毕业
    """
    plus_info = await erp_student_plus.get_one(db, stu_id=stu_id)
    if not plus_info:
        return await ApiFailedResponse("学生附加信息不存在")
    plus_info.consultant_status = consultant_status
    await db.commit()
    return await ApiSuccessResponse(True)


# 修改公海池的指定学生
@router.put("/student_pool/{stu_id}")
async def update_student_pool_by_stu_id(
        stu_id: int,
        params: StudentParams,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改公海池的指定学生
    - plus_info.consultant_id: 修改此字段表示转移课程顾问
    """

    stu_base_info = await erp_student.get_by_id(db, stu_id)
    if not stu_base_info:
        return await ApiFailedResponse("update_student_pool_by_stu_id-学生id不存在")
    plus_info = await erp_student_plus.get_one(db, stu_id=stu_id)
    base_update_info = {}
    plus_params = params.student_plus_base
    
    # 记录是否修改了学生姓名，用于后续同步classin
    is_name_changed = False
    new_stu_name = None
    
    # 支持修改的字段 stu_name、gender、birth、stu_address、Province、City、Area、grade、how_known、current_school
    if params.student_base_info.stu_name is not None and stu_base_info.stu_name != params.student_base_info.stu_name:
        base_update_info.update({"stu_name": params.student_base_info.stu_name})
        is_name_changed = True
        new_stu_name = params.student_base_info.stu_name
    if params.student_base_info.gender is not None and stu_base_info.stu_gender != params.student_base_info.gender:
        base_update_info.update({"stu_gender": params.student_base_info.gender})
    if params.student_base_info.birth is not None and str(stu_base_info.stu_birth)[:10] != str(params.student_base_info.birth)[:10]:
        base_update_info.update({"stu_birth": params.student_base_info.birth})
    if params.student_base_info.stu_address is not None and stu_base_info.stu_address != params.student_base_info.stu_address:
        base_update_info.update({"stu_address": params.student_base_info.stu_address})
    if params.student_base_info.stu_area is not None and stu_base_info.stu_area != params.student_base_info.stu_area:
        base_update_info.update({"stu_area": params.student_base_info.stu_area})

    if params.student_base_info.grade is not None and stu_base_info.stu_grade != params.student_base_info.grade:
        base_update_info.update({"stu_grade": params.student_base_info.grade})
    if params.student_base_info.how_known is not None and stu_base_info.how_known != params.student_base_info.how_known:
        base_update_info.update({"how_known": params.student_base_info.how_known})
    if params.student_base_info.current_school is not None and stu_base_info.stu_school_name != params.student_base_info.current_school:
        base_update_info.update({"stu_school_name": params.student_base_info.current_school})
    plus_update_info = {}
    if plus_params is not None and plus_info is not None:
        # 支持修改的字段 training_plan、consultant_status、consultant_id、parental_appeal、character、is_multi_child、real_guardian、major_concern_point、reject_point、famous_teacher_effect、other_comments
        if plus_params.training_plan is not None and plus_info.training_plan != plus_params.training_plan:
            plus_update_info.update({"training_plan": plus_params.training_plan})
        if plus_params.consultant_status is not None and plus_info.consultant_status != plus_params.consultant_status:
            plus_update_info.update({"consultant_status": plus_params.consultant_status})
        if plus_params.consultant_id is not None and plus_info.consultant_id != plus_params.consultant_id:
            plus_update_info.update({"consultant_id": plus_params.consultant_id})
        if plus_params.parental_appeal is not None and plus_info.parental_appeal != plus_params.parental_appeal:
            plus_update_info.update({"parental_appeal": plus_params.parental_appeal})
        if plus_params.character is not None and plus_info.character != plus_params.character:
            plus_update_info.update({"character": plus_params.character})
        if plus_params.is_multi_child is not None and plus_info.is_multi_child != plus_params.is_multi_child:
            plus_update_info.update({"is_multi_child": plus_params.is_multi_child})
        if plus_params.real_guardian is not None and plus_info.real_guardian != plus_params.real_guardian:
            plus_update_info.update({"real_guardian": plus_params.real_guardian})
        if plus_params.major_concern_point is not None and plus_info.major_concern_point != plus_params.major_concern_point:
            plus_update_info.update({"major_concern_point": plus_params.major_concern_point})
        if plus_params.reject_point is not None and plus_info.reject_point != plus_params.reject_point:
            plus_update_info.update({"reject_point": plus_params.reject_point})
        if plus_params.famous_teacher_effect is not None and plus_info.famous_teacher_effect != plus_params.famous_teacher_effect:
            plus_update_info.update({"famous_teacher_effect": plus_params.famous_teacher_effect})
        if plus_params.other_comments is not None and plus_info.other_comments != plus_params.other_comments:
            plus_update_info.update({"other_comments": plus_params.other_comments})

        if plus_params.course_labels is not None and plus_info.course_labels != plus_params.course_labels:
            plus_update_info.update({"course_labels": plus_params.course_labels})
    elif plus_params is not None and plus_info is None:
        # 如果plus_info不存在但有plus_params，需要创建新的plus记录
        plus_create_info = {}
        if plus_params.training_plan is not None:
            plus_create_info.update({"training_plan": plus_params.training_plan})
        if plus_params.consultant_status is not None:
            plus_create_info.update({"consultant_status": plus_params.consultant_status})
        if plus_params.consultant_id is not None:
            plus_create_info.update({"consultant_id": plus_params.consultant_id})
        if plus_params.parental_appeal is not None:
            plus_create_info.update({"parental_appeal": plus_params.parental_appeal})
        if plus_params.character is not None:
            plus_create_info.update({"character": plus_params.character})
        if plus_params.is_multi_child is not None:
            plus_create_info.update({"is_multi_child": plus_params.is_multi_child})
        if plus_params.real_guardian is not None:
            plus_create_info.update({"real_guardian": plus_params.real_guardian})
        if plus_params.major_concern_point is not None:
            plus_create_info.update({"major_concern_point": plus_params.major_concern_point})
        if plus_params.reject_point is not None:
            plus_create_info.update({"reject_point": plus_params.reject_point})
        if plus_params.famous_teacher_effect is not None:
            plus_create_info.update({"famous_teacher_effect": plus_params.famous_teacher_effect})
        if plus_params.other_comments is not None:
            plus_create_info.update({"other_comments": plus_params.other_comments})
        if plus_params.course_labels is not None:
            plus_create_info.update({"course_labels": plus_params.course_labels})
        
        if plus_create_info:
            plus_create_info.update({"stu_id": stu_id})
            plus_info = await erp_student_plus.create(db, **plus_create_info, commit=False)

    log_content = []  # 逐项添加文本, 每项为[]->[]
    # 数据库操作
    if base_update_info:
        for k, v in base_update_info.items():
            log_content.append(f"{k}: {stu_base_info.__dict__.get(k)} 修改为 {v}")
        await erp_student.update_one(db, stu_id, base_update_info, commit=False)

    if plus_update_info and plus_info:
        for k, v in plus_update_info.items():
            log_content.append(f"{k}: {plus_info.__dict__.get(k)} 修改为 {v}")
        await erp_student_plus.update_many(db, {"stu_id": stu_id}, plus_update_info, commit=False)

    # 公海池学生修改日志记录
    if log_content and plus_info:
        log = LogHandler(db)
        await log.create_log(
            log_type=LogType.STUDENT_PLUS_MODIFY.value,
            action='修改学生信息',
            content=log_content,
            account_id=user.uid,
            obj_id=plus_info.id
        )
    stu_uid = copy.deepcopy(stu_base_info.classin_uid)
    await db.commit()
    
    # 如果修改了学生姓名，尝试同步到classin（不影响主进程）
    if is_name_changed and new_stu_name:
        try:
            # 检查学生是否有classin_uid
            if stu_uid and int(stu_uid) > 0:
                # 调用classin接口修改学生姓名
                response = await classin_sdk.user.edit_school_student(
                    student_uid=str(stu_uid),
                    student_name=new_stu_name
                )
                settings.logger.info(f"同步修改classin学生姓名: stu_id={stu_id}, classin_uid={stu_uid}, new_name={new_stu_name}, response={response}")
            else:
                settings.logger.info(f"学生无classin_uid，跳过同步: stu_id={stu_id}")
        except Exception as e:
            # 记录错误但不影响主进程
            settings.logger.error(f"同步修改classin学生姓名失败: stu_id={stu_id}, error={str(e)}")
    
    return await ApiSuccessResponse(True)


# 根据plus_id查询日志
@router.get("/student_pool_log/{plus_id}")
async def get_student_pool_log_by_plus_id(
        page: int = 1,
        page_size: int = 10,
        plus_id: int = None,
        action_type: int = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 根据plus_id查询日志(包含记录和跟进)
    - action_type: 1 记录 2 跟进
    ## return:
    - log_type: 1 新增记录 2 修改记录 3 跟进
    """
    raw = [
        ErpLog.obj_id == plus_id,
    ]
    if action_type == 1:
        raw.append(ErpLog.log_type.in_([LogType.STUDENT_PLUS_ENTRY.value, LogType.STUDENT_PLUS_MODIFY.value]))
    elif action_type == 2:
        raw.append(ErpLog.log_type == LogType.STUDENT_PLUS_FOLLOW.value)
    else:
        return await ApiFailedResponse('action_type只能是1或者2')
    data = await erp_log.get_many_with_pagination(db, page=page, page_size=page_size, raw=raw)
    # 构建账号映射
    account_objs = await erp_account.get_many(db)
    account_map = {obj.id: obj.employee_name for obj in account_objs}
    for i in data:
        i.operate_name = account_map.get(i.account_id, '未知')
    count_data = await erp_log.get_many(db, raw=raw)
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 通过plus_id查询测验信息
@router.get("/student_pool_exam/{plus_id}")
async def get_student_pool_exam_by_plus_id(
        plus_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 通过plus_id查询测验信息
    - 根据create_time时间线排序，但其他字段不同
    """

    online_history = await get_stu_exam_history(db, plus_id)
    offline_history = await erp_offline_exam.get_many(db, {"plus_id": plus_id})
    # 转为字典
    online_dict = [dict(i) for i in online_history]
    offline_dict = [i.as_dict() for i in offline_history]
    # 为两种记录添加类型标记
    for item in online_dict:
        item['exam_type'] = 'online'
    for item in offline_dict:
        item['exam_type'] = 'offline'

    # 合并数据
    new_data = online_dict + offline_dict
    # 按create_time排序
    new_data.sort(key=lambda x: x['create_time'], reverse=True)
    return await ApiSuccessResponse(new_data)


# 新增线下测验
@router.post("/student_pool_exam_offline")
async def add_student_pool_exam_offline(
        params: OfflineParams,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增线下测验
    """
    await erp_offline_exam.create(db, **{
        "appointment_time": params.appointment_time,
        "appointment_center_id": params.appointment_center_id,
        "paper_name": params.paper_name,
        "stu_score": params.stu_score,
        "paper_score": params.paper_score,
        "exam_result": params.exam_result,
        "plus_id": params.plus_id,
        "attachment": params.attachment,
        "update_by": user.uid,
        "create_by": user.uid

    })
    return await ApiSuccessResponse(True)


# 根据paper_id查询测验结果
@router.get("/student_pool_exam_result/{stu_paper_id}")
async def get_student_pool_exam_result_by_paper_id(
        stu_paper_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 根据paper_id查询测验结果
    """
    data = await get_stu_score_result(db, stu_paper_id)
    stu_paper_info = await erp_online_stu_paper.get_one(db, id=stu_paper_id)
    stu_score = sum([i['stu_score'] for i in data])
    match_classes = await erp_online_paper_course.get_many(db, raw=[
        ErpOnlinePaperCourse.paper_id == stu_paper_info.paper_id,
        ErpOnlinePaperCourse.max_score >= stu_score,
        ErpOnlinePaperCourse.min_score <= stu_score
    ])
    if len(match_classes) > 1:
        match_class = "教师班型录入重复"
    elif len(match_classes) == 1:
        match_class = match_classes[0].course_name
    else:
        match_class = "教师班型录入错误"
    skills = get_knowledge_ability_map()
    new_data = []
    for i in data:
        j = dict(i)
        j['skill_name'] = skills.get(i.knowledge_point)
        new_data.append(j)
    return await ApiSuccessResponse(
        {
            "match_class": match_class,
            "stu_score": stu_score,
            "data": new_data
        }
    )


# 创建跟进信息
@router.post("/student_pool_follow")
async def add_student_pool_follow(
        prams: PlusFlow,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 创建跟进信息
    """
    log = LogHandler(db)
    await log.create_log(
        log_type=LogType.STUDENT_PLUS_FOLLOW.value,
        action='新增了跟进信息',
        content=[prams.content],
        account_id=user.uid,
        obj_id=prams.plus_id
    )
    await db.commit()
    return await ApiSuccessResponse(True)


# 模拟测验结束的module接口
@router.get("/test_complete_exam_module")
async def test_complete_exam_module(
        stu_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 模拟测验结束的module接口
    - 访问此module，说明stu_id的学生已经完成了测验,需要分配课程顾问
    """
    flag = await assign_consultant(db, stu_id, user_id=user.uid)
    if flag:
        await db.commit()
        return await ApiSuccessResponse(True)
    await db.rollback()
    return await ApiFailedResponse("分配课程顾问失败")



# 查询公海池学生订单
@router.get("/student_pool_order")
async def get_student_pool_order(
        page: int = 1,
        page_size: int = 10,
        stu_name: str = None,
        order_id: int = None,
        start_time: str = None,
        end_time: str = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 查询公海池学生订单
    - 课前退费 buy_num = sum[refund_num]; 课中退费 buy_num > sum[refund_num]
    """
    data = await student_pool_order_module(db, page=page, page_size=page_size, stu_name=stu_name, order_id=order_id, start_time=start_time, end_time=end_time, count=False)
    count = await student_pool_order_module(db, page=page, page_size=page_size, stu_name=stu_name, order_id=order_id, start_time=start_time, end_time=end_time, count=True)

    # 预查询退费数据，避免循环中查询
    order_ids = [i.order_id for i in data]
    refund_data = await erp_order_refund_detail.get_many(db, raw=[ErpOrderRefundDetail.order_id.in_(order_ids)])
    
    # 按order_id组织退费数据
    refund_map = {}
    for refund in refund_data:
        if refund.order_id not in refund_map:
            refund_map[refund.order_id] = []
        refund_map[refund.order_id].append(refund)
    new_data = []
    for i in data:
        item = dict(i)
        # 组装退费数据
        item['refund_detail'] = refund_map.get(i.order_id, [])
        new_data.append(item)   
    return await ApiSuccessResponse({
        "data": new_data,
        "count": count
    })
