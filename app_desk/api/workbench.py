"""
工作台支持插件
"""
from fastapi import APIRouter, Depends
from sqlalchemy import or_
from sqlalchemy.ext.asyncio import AsyncSession
from models.m_class import ErpClass
from models.m_order import ErpOrderStudent
from models.m_student import ErpStudent
from utils.db.account_handler import UserDict, role_required
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db
from utils.enum.enum_order import StudentState
from utils.response.response_handler import ApiFailedResponse, ApiSuccessResponse

router = APIRouter(prefix="/workbench",tags=["工作台"])

erp_student = CRUD(ErpStudent)
erp_class = CRUD(ErpClass)
erp_order_student = CRUD(ErpOrderStudent)


@router.get(f"/student_info")
async def query_student_info(
        keyword: str,
        secret: str,
        page: int = None,
        page_size: int = None,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 关键词查询学生
    """
    if secret != 'zxnci!@#--0sd..':
        return await ApiFailedResponse('认证失败')
    # data = await get_stu_info(db, keyword, page, page_size)
    
    data = await erp_student.get_many(db, raw=[
        or_(
            ErpStudent.stu_name.like(f'%{keyword}%'),
            ErpStudent.stu_username.like(f'%{keyword}%'),
        )
    ])
    new_data = [{
        "stu_id": i.id,
        "stu_name": i.stu_name,
        "stu_username": i.stu_username,
    } for i in data]
    return await ApiSuccessResponse(new_data)


@router.get(f"/student_class")
async def query_student_class(
        stu_id: int,
        secret: str,
        page: int = 1,
        page_size: int = 10,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询学生的报名记录
    """
    if secret != 'zxnci!@#--0sd..':
        return await ApiFailedResponse('认证失败')
    order_student_objs = await erp_order_student.get_many_with_pagination(db,  page, page_size, raw=[
        ErpOrderStudent.stu_id == stu_id,
        ErpOrderStudent.student_state.in_([StudentState.TRANSFER_IN.value, StudentState.NORMAL.value]),
    ])
    class_ids = [order_student.class_id for order_student in order_student_objs]
    class_objs = await erp_class.get_many(db, raw=[
        ErpClass.id.in_(class_ids)
    ])
    class_map = {class_obj.id: class_obj for class_obj in class_objs}
    for order_student in order_student_objs:
        order_student.class_obj = class_map.get(order_student.class_id)
    return await ApiSuccessResponse(order_student_objs)
