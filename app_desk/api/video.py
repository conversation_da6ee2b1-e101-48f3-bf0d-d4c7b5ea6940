from datetime import datetime

from fastapi import APIRouter, Depends
from sqlalchemy import or_, func
from sqlalchemy.ext.asyncio import AsyncSession

import settings
from utils.response.response_handler import ApiSuccessResponse

router = APIRouter(prefix="/video", tags=["视频"])

erp_eeo_course_class = settings.CF.get_crud("ErpEeoCourseClass")
erp_eeo_vod_playlist = settings.CF.get_crud("ErpEeoVodPlaylist")



