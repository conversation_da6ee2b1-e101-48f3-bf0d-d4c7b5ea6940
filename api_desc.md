# JJSWERP 2.0.0 API文档

本文档提供了JJSWERP 2.0.0后端系统的所有API接口说明。

## 目录

- [用户模块 (app_user)](#用户模块-app_user)
- [人力资源模块 (app_human_resources)](#人力资源模块-app_human_resources)
- [教学模块 (app_teach)](#教学模块-app_teach)
- [公共API模块 (public_api)](#公共api模块-public_api)
- [财务模块 (app_finance)](#财务模块-app_finance)
- [订单模块 (app_order)](#订单模块-app_order)
- [办公模块 (app_office)](#办公模块-app_office)
- [前台模块 (app_desk)](#前台模块-app_desk)
- [竞赛模块 (app_competition)](#竞赛模块-app_competition)

## 基本信息

- 基础URL: `/erp`
- 认证方式: Bearer Token
- 响应格式: JSON

## 用户模块 (app_user)

### 认证相关接口

#### 扫码登录

- **URL**: `/erp/app_user/auth/scan_code_login`
- **方法**: GET
- **描述**: 通过企业微信扫码或工作台登录
- **参数**:
  - `code`: 企业微信授权码
- **响应**:
  ```json
  {
    "access_token": "token字符串",
    "token_type": "bearer",
    "header": "Authorization",
    "meta": {
      "employee_name": "员工姓名",
      "employee_number": "员工编号"
    }
  }
  ```

#### 账号密码登录

- **URL**: `/erp/app_user/auth/token`
- **方法**: POST
- **描述**: 通过用户名和密码登录
- **参数**:
  - `username`: 用户名
  - `password`: 密码
- **响应**:
  ```json
  {
    "access_token": "token字符串",
    "token_type": "bearer",
    "header": "Authorization",
    "meta": {
      "employee_name": "员工姓名",
      "employee_number": "员工编号"
    }
  }
  ```

### 账号管理接口

#### 创建ERP账户

- **URL**: `/erp/app_user/account/account`
- **方法**: POST
- **描述**: 创建ERP账户，用于员工入职
- **权限**: 需要认证
- **请求体**:
  ```json
  {
    "username": "手机号",
    "employee_name": "员工姓名",
    "employee_idcard": "身份证号",
    "employee_gender": "性别",
    "employee_education": "学历",
    "employee_type": "员工类型",
    "employee_status": "员工状态",
    "departments": [{"dept_id": "部门ID"}]
  }
  ```
- **响应**: 成功创建的账户信息

#### 更新ERP账户

- **URL**: `/erp/app_user/account/account/{account_id}`
- **方法**: PUT
- **描述**: 更新指定账户的信息
- **权限**: 需要认证
- **路径参数**:
  - `account_id`: 账户ID
- **请求体**: 账户更新信息
- **响应**: 更新后的账户信息

#### 手动同步账户到企业微信

- **URL**: `/erp/app_user/account/manual_sync_account`
- **方法**: POST
- **描述**: 手动将ERP账户同步到企业微信
- **权限**: 需要认证
- **请求体**:
  - `username`: 用户名
- **响应**: 同步结果

#### 查询ERP账户列表

- **URL**: `/erp/app_user/account/account`
- **方法**: GET
- **描述**: 查询ERP账户列表，支持分页和关键词搜索
- **权限**: 需要认证
- **查询参数**:
  - `keywords`: 搜索关键词（可选）
  - `page`: 页码（可选）
  - `page_size`: 每页数量（可选）
- **响应**: 账户列表和总数

#### 员工离职

- **URL**: `/erp/app_user/account/account_exit`
- **方法**: POST
- **描述**: 处理员工离职
- **权限**: 需要认证
- **请求体**:
  - `account_id`: 账户ID
  - `exit_date`: 离职日期
- **响应**: 处理结果

#### 查询单个账户信息

- **URL**: `/erp/app_user/account/account/{account_id}`
- **方法**: GET
- **描述**: 查询指定账户的详细信息
- **权限**: 需要认证
- **路径参数**:
  - `account_id`: 账户ID
- **响应**: 账户详细信息

### 部门管理接口

#### 查询部门列表

- **URL**: `/erp/app_user/department/department`
- **方法**: GET
- **描述**: 查询部门列表，以树形结构返回
- **权限**: 需要认证
- **响应**: 部门树形结构数据

#### 查询部门下的账户

- **URL**: `/erp/app_user/department/account_by_department_id`
- **方法**: GET
- **描述**: 查询指定部门下的所有账户
- **权限**: 需要认证
- **查询参数**:
  - `dept_id`: 部门ID
- **响应**: 部门下的账户列表

#### 创建部门

- **URL**: `/erp/app_user/department/department`
- **方法**: POST
- **描述**: 创建新部门
- **权限**: 需要认证
- **请求体**:
  ```json
  {
    "dept_name": "部门名称",
    "manager_id": "部门经理ID",
    "parent_id": "父部门ID",
    "dept_level": "部门级别"
  }
  ```
- **响应**: 创建的部门信息

#### 更新部门

- **URL**: `/erp/app_user/department/department/{dept_id}`
- **方法**: PUT
- **描述**: 更新指定部门的信息
- **权限**: 需要认证
- **路径参数**:
  - `dept_id`: 部门ID
- **请求体**: 部门更新信息
- **响应**: 更新后的部门信息

#### 删除部门

- **URL**: `/erp/app_user/department/department`
- **方法**: DELETE
- **描述**: 删除指定部门
- **权限**: 需要认证
- **查询参数**:
  - `dept_id`: 部门ID
- **响应**: 删除结果

### 角色管理接口

#### 查询角色列表

- **URL**: `/erp/app_user/role/role`
- **方法**: GET
- **描述**: 查询角色列表，支持分页
- **权限**: 需要特定角色权限
- **查询参数**:
  - `page`: 页码
  - `page_size`: 每页数量
- **响应**: 角色列表和总数

#### 获取单个角色

- **URL**: `/erp/app_user/role/role/{role_id}`
- **方法**: GET
- **描述**: 获取指定角色的详细信息
- **权限**: 需要特定角色权限
- **路径参数**:
  - `role_id`: 角色ID
- **响应**: 角色详细信息

#### 创建角色

- **URL**: `/erp/app_user/role/role`
- **方法**: POST
- **描述**: 创建新角色
- **权限**: 需要特定角色权限
- **请求体**:
  ```json
  {
    "role_name": "角色名称",
    "role_desc": "角色描述"
  }
  ```
- **响应**: 创建的角色信息

#### 更新角色

- **URL**: `/erp/app_user/role/role/{role_id}`
- **方法**: PUT
- **描述**: 更新指定角色的信息
- **权限**: 需要特定角色权限
- **路径参数**:
  - `role_id`: 角色ID
- **请求体**: 角色更新信息
- **响应**: 更新后的角色信息

#### 删除角色

- **URL**: `/erp/app_user/role/role/{role_id}`
- **方法**: DELETE
- **描述**: 删除指定角色
- **权限**: 需要特定角色权限
- **路径参数**:
  - `role_id`: 角色ID
- **响应**: 删除结果

#### 获取角色路由

- **URL**: `/erp/app_user/role/role_route/{role_id}`
- **方法**: GET
- **描述**: 获取指定角色的路由权限
- **权限**: 需要特定角色权限
- **路径参数**:
  - `role_id`: 角色ID
- **响应**: 角色的路由权限列表

#### 创建角色路由

- **URL**: `/erp/app_user/role/role_route`
- **方法**: POST
- **描述**: 为角色添加路由权限
- **权限**: 需要特定角色权限
- **请求体**:
  ```json
  {
    "role_id": "角色ID",
    "route_id": "路由ID"
  }
  ```
- **响应**: 创建的角色路由信息

#### 删除角色路由

- **URL**: `/erp/app_user/role/role_route/{role_route_id}`
- **方法**: DELETE
- **描述**: 删除指定的角色路由权限
- **权限**: 需要特定角色权限
- **路径参数**:
  - `role_route_id`: 角色路由ID
- **响应**: 删除结果

#### 查询路由列表

- **URL**: `/erp/app_user/role/route`
- **方法**: GET
- **描述**: 查询所有路由
- **权限**: 需要特定角色权限
- **响应**: 路由列表

#### 获取用户权限

- **URL**: `/erp/app_user/role/permission`
- **方法**: GET
- **描述**: 获取用户的权限信息
- **权限**: 需要特定角色权限
- **查询参数**:
  - `flatten`: 是否扁平化返回（可选）
  - `account_id`: 账户ID（可选）
- **响应**: 用户权限信息

## 人力资源模块 (app_human_resources)

### 招聘管理接口

#### 查询招聘计划

- **URL**: `/erp/app_human_resources/hr/hr`
- **方法**: GET
- **描述**: 查询所有招聘计划
- **权限**: 需要特定角色权限
- **查询参数**:
  - `page`: 页码（可选）
  - `page_size`: 每页数量（可选）
- **响应**: 招聘计划列表

#### 查询单个招聘计划

- **URL**: `/erp/app_human_resources/hr/hr/{pk_id}`
- **方法**: GET
- **描述**: 查询指定ID的招聘计划详情
- **权限**: 需要特定角色权限
- **路径参数**:
  - `pk_id`: 招聘计划ID
- **响应**: 招聘计划详情

#### 添加员工

- **URL**: `/erp/app_human_resources/hr/hr_add_employee`
- **方法**: POST
- **描述**: 添加新员工
- **权限**: 需要特定角色权限
- **请求体**:
  ```json
  {
    "hr_plan_id": "招聘计划ID",
    "employee_name": "员工姓名",
    "employee_gender": "员工性别",
    "employee_idcard": "身份证号",
    "employee_education": "学历",
    "employee_type": "员工类型"
  }
  ```
- **响应**: 添加结果

#### 查询招聘记录

- **URL**: `/erp/app_human_resources/hr/hr_records`
- **方法**: GET
- **描述**: 查询招聘记录
- **权限**: 需要特定角色权限
- **查询参数**:
  - `page`: 页码（可选）
  - `page_size`: 每页数量（可选）
- **响应**: 招聘记录列表

### 考勤管理接口

#### 下载考勤模板

- **URL**: `/erp/app_human_resources/attendance/download_check_template`
- **方法**: POST
- **描述**: 下载员工考勤模板
- **权限**: 需要特定角色权限
- **响应**: 考勤模板文件

#### 检查考勤记录是否存在

- **URL**: `/erp/app_human_resources/attendance/check_records_exist`
- **方法**: GET
- **描述**: 检查指定月份的考勤记录是否已存在
- **权限**: 需要特定角色权限
- **查询参数**:
  - `ym`: 年月（格式：YYYYMM）
- **响应**: 考勤记录存在状态

#### 上传考勤记录

- **URL**: `/erp/app_human_resources/attendance/upload_check_records`
- **方法**: POST
- **描述**: 上传员工考勤记录
- **权限**: 需要特定角色权限
- **查询参数**:
  - `ym`: 年月（格式：YYYYMM）
- **请求体**: 
  - `file`: 考勤记录文件
- **响应**: 上传结果

#### 预览考勤记录

- **URL**: `/erp/app_human_resources/attendance/check_records_preview`
- **方法**: GET
- **描述**: 预览指定月份的考勤记录
- **权限**: 需要特定角色权限
- **查询参数**:
  - `ym`: 年月（格式：YYYYMM）
- **响应**: 考勤记录预览数据

#### 更新考勤记录

- **URL**: `/erp/app_human_resources/attendance/check_records_preview`
- **方法**: POST
- **描述**: 更新考勤记录
- **权限**: 需要特定角色权限
- **请求体**:
  ```json
  {
    "account_id": "账户ID",
    "check_date": "考勤日期",
    "day_period": "日期周期",
    "ym": "年月",
    "check_value": "考勤值"
  }
  ```
- **响应**: 更新结果

#### 预览考勤报表

- **URL**: `/erp/app_human_resources/attendance/check_report_preview`
- **方法**: GET
- **描述**: 预览考勤报表
- **权限**: 需要特定角色权限
- **查询参数**:
  - `ym`: 年月（格式：YYYYMM）
  - `account_id`: 账户ID（可选）
- **响应**: 考勤报表预览数据

#### 下载考勤报表

- **URL**: `/erp/app_human_resources/attendance/download_check_report_preview`
- **方法**: GET
- **描述**: 下载考勤报表
- **权限**: 需要特定角色权限
- **查询参数**:
  - `ym`: 年月（格式：YYYYMM）
- **响应**: 考勤报表文件

#### 删除考勤记录

- **URL**: `/erp/app_human_resources/attendance/check_records`
- **方法**: DELETE
- **描述**: 删除指定月份的考勤记录
- **权限**: 需要特定角色权限
- **查询参数**:
  - `ym`: 年月（格式：YYYYMM）
- **响应**: 删除结果

#### 查询考勤记录

- **URL**: `/erp/app_human_resources/attendance/check_records`
- **方法**: GET
- **描述**: 查询考勤记录
- **权限**: 需要特定角色权限
- **响应**: 考勤记录列表

#### 查询员工考勤历史

- **URL**: `/erp/app_human_resources/attendance/account_attendance_history`
- **方法**: GET
- **描述**: 查询指定员工的考勤历史
- **权限**: 需要特定角色权限
- **查询参数**:
  - `account_id`: 账户ID
- **响应**: 员工考勤历史数据

#### 查询员工考勤报表

- **URL**: `/erp/app_human_resources/attendance/account_attendance_report`
- **方法**: GET
- **描述**: 查询指定员工的考勤报表
- **权限**: 需要特定角色权限
- **查询参数**:
  - `account_id`: 账户ID
  - `ym_start`: 开始年月（可选）
  - `ym_end`: 结束年月（可选）
  - `ym`: 指定年月（可选）
- **响应**: 员工考勤报表数据

#### 查询部门考勤历史

- **URL**: `/erp/app_human_resources/attendance/dept_attendance_history`
- **方法**: GET
- **描述**: 查询指定部门的考勤历史
- **权限**: 需要特定角色权限
- **查询参数**:
  - `dept_id`: 部门ID
- **响应**: 部门考勤历史数据

#### 查询部门考勤报表

- **URL**: `/erp/app_human_resources/attendance/dept_attendance_report`
- **方法**: GET
- **描述**: 查询指定部门的考勤报表
- **权限**: 需要特定角色权限
- **查询参数**:
  - `dept_id`: 部门ID
  - `ym_start`: 开始年月（可选）
  - `ym_end`: 结束年月（可选）
  - `ym`: 指定年月（可选）
- **响应**: 部门考勤报表数据

### 职级管理接口

#### 查询职级

- **URL**: `/erp/app_human_resources/level/job_level`
- **方法**: GET
- **描述**: 查询职级列表
- **权限**: 需要认证
- **响应**: 职级列表

#### 创建职级

- **URL**: `/erp/app_human_resources/level/job_level`
- **方法**: POST
- **描述**: 创建新职级
- **权限**: 需要认证
- **请求体**: 职级信息
- **响应**: 创建的职级信息

#### 更新职级

- **URL**: `/erp/app_human_resources/level/job_level/{job_level_id}`
- **方法**: PUT
- **描述**: 更新指定职级
- **权限**: 需要认证
- **路径参数**:
  - `job_level_id`: 职级ID
- **请求体**: 职级更新信息
- **响应**: 更新后的职级信息

#### 删除职级

- **URL**: `/erp/app_human_resources/level/job_level/{job_level_id}`
- **方法**: DELETE
- **描述**: 删除指定职级
- **权限**: 需要认证
- **路径参数**:
  - `job_level_id`: 职级ID
- **响应**: 删除结果

## 教学模块 (app_teach)

...

## 公共API模块 (public_api)

...

## 财务模块 (app_finance)

...

## 订单模块 (app_order)

...

## 办公模块 (app_office)

...

## 前台模块 (app_desk)

...

## 竞赛模块 (app_competition)

...
