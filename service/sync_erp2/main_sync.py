"""
主同步脚本
提供命令行接口和API接口，支持各种同步操作
"""

import asyncio
import argparse
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from settings import logger
from sync_erp2.core.sync_manager import sync_manager
from sync_erp2.config.table_mappings import TABLE_MAPPINGS, SYNC_TASKS

class SyncCLI:
    """同步命令行接口"""
    
    def __init__(self):
        self.sync_manager = sync_manager
    
    async def run_full_sync(self, table_name: Optional[str] = None, parallel: bool = True):
        """运行全量同步"""
        try:
            if table_name:
                # 单表同步
                logger.info(f"开始单表全量同步: {table_name}")
                result = await self.sync_manager.sync_single_table(table_name, "full")
                self._print_single_result(result)
            else:
                # 全部表同步
                logger.info("开始全量同步所有表")
                result = await self.sync_manager.sync_all_full(parallel)
                self._print_batch_result(result)
                
        except Exception as e:
            logger.error(f"全量同步失败: {str(e)}")
            print(f"❌ 全量同步失败: {str(e)}")
    
    async def run_incremental_sync(self, table_name: Optional[str] = None, since_hours: int = 24, parallel: bool = True):
        """运行增量同步"""
        try:
            since_time = datetime.now() - timedelta(hours=since_hours)
            
            if table_name:
                # 单表增量同步
                logger.info(f"开始单表增量同步: {table_name}")
                result = await self.sync_manager.sync_single_table(table_name, "incremental", since_time)
                self._print_single_result(result)
            else:
                # 全部表增量同步
                logger.info("开始增量同步所有表")
                result = await self.sync_manager.sync_all_incremental(parallel, since_time)
                self._print_batch_result(result)
                
        except Exception as e:
            logger.error(f"增量同步失败: {str(e)}")
            print(f"❌ 增量同步失败: {str(e)}")
    
    async def show_status(self):
        """显示同步状态"""
        try:
            print("📊 获取同步状态...")
            status = await self.sync_manager.get_sync_status_all()
            
            print("\n" + "="*80)
            print("📋 同步状态报告")
            print("="*80)
            
            for mapping_key, info in status.items():
                if "error" in info:
                    print(f"❌ {mapping_key}: 错误 - {info['error']}")
                else:
                    source_table = info.get("source_table", "未知")
                    target_table = info.get("target_table", "未知")
                    source_count = info.get("source_count", 0)
                    target_count = info.get("target_count", 0)
                    sync_ratio = info.get("sync_ratio", 0)
                    
                    print(f"\n📊 {mapping_key}")
                    print(f"   源表: {source_table} ({source_count:,} 条记录)")
                    print(f"   目标表: {target_table} ({target_count:,} 条记录)")
                    print(f"   同步率: {sync_ratio:.2%}")
                    
                    if sync_ratio >= 0.95:
                        print("   状态: ✅ 良好")
                    elif sync_ratio >= 0.8:
                        print("   状态: ⚠️  需要注意")
                    else:
                        print("   状态: ❌ 需要同步")
            
            print("\n" + "="*80)
            
        except Exception as e:
            logger.error(f"获取状态失败: {str(e)}")
            print(f"❌ 获取状态失败: {str(e)}")
    
    async def validate_mappings(self):
        """验证映射配置"""
        try:
            print("🔍 验证映射配置...")
            validation_results = await self.sync_manager.validate_all_mappings()
            
            print("\n" + "="*80)
            print("🔍 映射配置验证报告")
            print("="*80)
            
            valid_count = 0
            invalid_count = 0
            error_count = 0
            
            for mapping_key, result in validation_results.items():
                status = result.get("status", "unknown")
                
                if status == "valid":
                    valid_count += 1
                    print(f"✅ {mapping_key}: 配置有效")
                    print(f"   源表字段: {result.get('source_columns', 0)}")
                    print(f"   目标表字段: {result.get('target_columns', 0)}")
                    print(f"   映射字段: {result.get('mapping_fields', 0)}")
                elif status == "invalid":
                    invalid_count += 1
                    print(f"❌ {mapping_key}: 配置无效")
                    print(f"   源表存在: {'是' if result.get('source_exists') else '否'}")
                    print(f"   目标表存在: {'是' if result.get('target_exists') else '否'}")
                else:
                    error_count += 1
                    print(f"⚠️  {mapping_key}: 验证错误 - {result.get('error', '未知错误')}")
                
                print()
            
            print(f"📊 验证汇总: 有效 {valid_count}, 无效 {invalid_count}, 错误 {error_count}")
            print("="*80)
            
        except Exception as e:
            logger.error(f"验证映射失败: {str(e)}")
            print(f"❌ 验证映射失败: {str(e)}")
    
    async def list_tables(self):
        """列出所有可同步的表"""
        print("\n" + "="*80)
        print("📋 可同步表列表")
        print("="*80)
        
        for i, (mapping_key, config) in enumerate(TABLE_MAPPINGS.items(), 1):
            source_table = config.get("source_table", "未知")
            target_table = config.get("target_table", "未知")
            source_db = config.get("source_db", "未知")
            target_db = config.get("target_db", "未知")
            incremental = "✅" if config.get("enable_incremental", False) else "❌"
            
            print(f"{i:2d}. {mapping_key}")
            print(f"    源: {source_db}.{source_table}")
            print(f"    目标: {target_db}.{target_table}")
            print(f"    增量同步: {incremental}")
            print()
        
        print(f"总计: {len(TABLE_MAPPINGS)} 个表映射")
        print("="*80)
    
    def _print_single_result(self, result: dict):
        """打印单表同步结果"""
        if result.get("status") == "success":
            print(f"✅ 同步成功!")
            print(f"   处理记录: {result.get('total_processed', 0):,}")
            print(f"   成功记录: {result.get('successful_inserts', 0):,}")
            print(f"   失败记录: {result.get('failed_records', 0):,}")
            
            if result.get('start_time') and result.get('end_time'):
                duration = (result['end_time'] - result['start_time']).total_seconds()
                print(f"   耗时: {duration:.2f} 秒")
        else:
            print(f"❌ 同步失败: {result.get('error', '未知错误')}")
    
    def _print_batch_result(self, result: dict):
        """打印批量同步结果"""
        summary = result.get("summary", {})
        
        print(f"\n{'='*80}")
        print("📊 同步完成报告")
        print(f"{'='*80}")
        
        print(f"总任务数: {summary.get('total_tasks', 0)}")
        print(f"成功任务: {summary.get('successful_tasks', 0)}")
        print(f"失败任务: {summary.get('failed_tasks', 0)}")
        print(f"处理记录: {summary.get('total_processed_records', 0):,}")
        print(f"成功记录: {summary.get('total_successful_records', 0):,}")
        print(f"失败记录: {summary.get('total_failed_records', 0):,}")
        print(f"总耗时: {summary.get('duration_seconds', 0):.2f} 秒")
        print(f"平均速度: {summary.get('average_speed', 0):.2f} 记录/秒")
        
        # 显示详细结果
        details = result.get("details", {})
        if details:
            print(f"\n📋 详细结果:")
            for mapping_key, detail in details.items():
                status = "✅" if detail.get("status") == "success" else "❌"
                task_name = detail.get("task_name", mapping_key)
                print(f"   {status} {task_name}")
                
                if detail.get("status") == "success":
                    processed = detail.get("total_processed", 0)
                    successful = detail.get("successful_inserts", 0)
                    print(f"      处理: {processed:,}, 成功: {successful:,}")
                else:
                    print(f"      错误: {detail.get('error', '未知错误')}")
        
        print(f"{'='*80}")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="ERP数据同步工具")
    parser.add_argument("command", choices=["full", "incremental", "status", "validate", "list"], 
                       help="同步命令")
    parser.add_argument("--table", "-t", help="指定表名 (可选)")
    parser.add_argument("--hours", type=int, default=24, help="增量同步时间范围(小时)")
    parser.add_argument("--serial", action="store_true", help="串行执行 (默认并行)")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    cli = SyncCLI()
    
    try:
        if args.command == "full":
            await cli.run_full_sync(args.table, not args.serial)
        elif args.command == "incremental":
            await cli.run_incremental_sync(args.table, args.hours, not args.serial)
        elif args.command == "status":
            await cli.show_status()
        elif args.command == "validate":
            await cli.validate_mappings()
        elif args.command == "list":
            await cli.list_tables()
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        logger.error(f"执行失败: {str(e)}")
        print(f"❌ 执行失败: {str(e)}")
    finally:
        await cli.sync_manager.cleanup()

if __name__ == "__main__":
    asyncio.run(main()) 