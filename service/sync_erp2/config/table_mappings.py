"""
数据表同步映射配置
定义源表到目标表的字段映射关系、数据转换规则等
"""

from typing import Dict, Any, Optional, Callable
from datetime import datetime
import decimal

# 数据转换函数
def convert_gender(value: int) -> Optional[int]:
    """性别转换：0-男,1-女 -> 1-男,2-女"""
    if value is None:
        return None
    return 1 if value == 0 else 2

def convert_disable_status(value: int) -> Optional[int]:
    """禁用状态转换：1-启用,0-禁用 -> 0-禁用,1-启用"""
    if value is None:
        return None
    return 1 if value == 1 else 0

def convert_decimal(value: Any) -> Optional[decimal.Decimal]:
    """转换为Decimal类型"""
    if value is None:
        return None
    return decimal.Decimal(str(value))

def convert_datetime(value: Any) -> Optional[datetime]:
    """转换为datetime类型"""
    if value is None:
        return None
    if isinstance(value, datetime):
        return value
    return value

def convert_string(value: Any) -> Optional[str]:
    """转换为字符串类型"""
    if value is None:
        return None
    return str(value)

# 表映射配置
TABLE_MAPPINGS = {
    # 学生表映射
    "rb_student_to_erp_student": {
        "source_db": "uat_reborn_think",  # 源数据库
        "target_db": "default",           # 目标数据库
        "source_table": "rb_student",     # 源表名
        "target_table": "erp_student",    # 目标表名
        "primary_key": "StuId",           # 源表主键
        "target_primary_key": "id",       # 目标表主键
        "batch_size": 1000,               # 批量处理大小
        "enable_incremental": True,       # 是否启用增量同步
        "incremental_field": "UpdateTime", # 增量同步字段
        "field_mappings": {
            # 源字段: (目标字段, 转换函数, 是否必需)
            "StuId": ("id", None, True),
            "StuTel": ("stu_username", convert_string, False),
            "StuName": ("stu_name", convert_string, False),
            "StuBirth": ("stu_birth", convert_datetime, False),
            "StuSex": ("stu_gender", convert_gender, False),
            "StuIcon": ("stu_avatar", convert_string, False),
            "CityName": ("stu_area", convert_string, False),
            "StuAddress": ("stu_address", convert_string, False),
            "GradeType": ("stu_grade", convert_string, False),
            "StuIDCard": ("stu_idcard", convert_string, False),
            "SchoolClass": ("stu_school_name", convert_string, False),
            "E_WalletMoney": ("stu_wallet_amount", convert_decimal, False),
            "Serial": ("stu_serial", convert_string, False),
            "ClassInUID": ("classin_uid", None, False),
            "UpdateTime": ("update_time", convert_datetime, False),
            "CreateTime": ("create_time", convert_datetime, False),
            "IsDisable": ("disable", convert_disable_status, False),
            "SchoolSource": ("how_known", convert_string, False),
            "School_Id": ("campus_id", None, False),
        },
        # 默认值配置
        "default_values": {
            "classin_sync": 0,
            "mall_user_id": None,
            "wechat_open_id": None,
            "stu_passwd": None,
        },
        # 数据验证规则
        "validation_rules": {
            "stu_username": {"max_length": 125},
            "stu_name": {"max_length": 125},
            "stu_area": {"max_length": 45},
            "stu_address": {"max_length": 255},
            "stu_grade": {"max_length": 125},
            "stu_idcard": {"max_length": 125},
            "stu_school_name": {"max_length": 125},
            "stu_serial": {"max_length": 125},
            "how_known": {"max_length": 125},
            "stu_passwd": {"max_length": 255},
        }
    },
    
    # 可以继续添加其他表的映射配置
    # "source_table_to_target_table": {
    #     "source_db": "uat_reborn_think",
    #     "target_db": "default", 
    #     "source_table": "source_table",
    #     "target_table": "target_table",
    #     ...
    # }
}

# 同步任务配置
SYNC_TASKS = [
    {
        "name": "学生表同步",
        "mapping_key": "rb_student_to_erp_student",
        "enabled": True,
        "schedule": "0 2 * * *",  # 每天凌晨2点执行
        "description": "同步学生基础信息从老系统到新系统"
    },
    # 可以添加更多同步任务
]

# 全局配置
GLOBAL_CONFIG = {
    "max_retry_times": 3,           # 最大重试次数
    "retry_delay": 5,               # 重试延迟(秒)
    "log_level": "INFO",            # 日志级别
    "enable_data_validation": True,  # 是否启用数据验证
    "enable_backup": True,          # 是否启用备份
    "backup_retention_days": 7,     # 备份保留天数
    "parallel_workers": 4,          # 并行工作进程数
} 