"""
同步系统测试脚本
用于验证同步系统的基本功能
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from settings import logger
from sync_erp2.core.sync_manager import sync_manager
from sync_erp2.config.table_mappings import TABLE_MAPPINGS

async def test_database_connections():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        from sync_erp2.core.database import db_manager
        
        # 测试所有配置的数据库连接
        for db_name in ["default", "uat_reborn_think"]:
            try:
                result = await db_manager.fetch_one(db_name, "SELECT 1 as test")
                if result and result.test == 1:
                    print(f"✅ 数据库连接成功: {db_name}")
                else:
                    print(f"❌ 数据库连接失败: {db_name}")
            except Exception as e:
                print(f"❌ 数据库连接错误 ({db_name}): {str(e)}")
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {str(e)}")

async def test_table_validation():
    """测试表验证"""
    print("\n🔍 测试表验证...")
    
    try:
        validation_results = await sync_manager.validate_all_mappings()
        
        for mapping_key, result in validation_results.items():
            status = result.get("status", "unknown")
            if status == "valid":
                print(f"✅ {mapping_key}: 配置有效")
            elif status == "invalid":
                print(f"❌ {mapping_key}: 配置无效")
                print(f"   源表存在: {'是' if result.get('source_exists') else '否'}")
                print(f"   目标表存在: {'是' if result.get('target_exists') else '否'}")
            else:
                print(f"⚠️  {mapping_key}: 验证错误 - {result.get('error', '未知错误')}")
        
    except Exception as e:
        print(f"❌ 表验证测试失败: {str(e)}")

async def test_data_transformer():
    """测试数据转换器"""
    print("\n🔍 测试数据转换器...")
    
    try:
        from sync_erp2.transformers.data_transformer import DataTransformer
        from sync_erp2.config.table_mappings import convert_gender, convert_disable_status
        
        # 测试转换函数
        test_cases = [
            ("性别转换", convert_gender, [(0, 1), (1, 2), (None, None)]),
            ("状态转换", convert_disable_status, [(1, 1), (0, 0), (None, None)])
        ]
        
        for func_name, func, cases in test_cases:
            print(f"  测试 {func_name}:")
            for input_val, expected in cases:
                result = func(input_val)
                if result == expected:
                    print(f"    ✅ {input_val} -> {result}")
                else:
                    print(f"    ❌ {input_val} -> {result} (期望: {expected})")
        
        # 测试数据转换器
        if "rb_student_to_erp_student" in TABLE_MAPPINGS:
            mapping_config = TABLE_MAPPINGS["rb_student_to_erp_student"]
            transformer = DataTransformer(mapping_config)
            
            # 测试记录转换
            test_record = {
                "StuId": 1,
                "StuName": "测试学生",
                "StuTel": "13800138000",
                "StuSex": 0,
                "IsDisable": 1
            }
            
            transformed_record, errors = transformer.transform_record(test_record)
            
            if not errors:
                print(f"  ✅ 记录转换成功: {len(transformed_record)} 个字段")
            else:
                print(f"  ❌ 记录转换失败: {errors}")
        
    except Exception as e:
        print(f"❌ 数据转换器测试失败: {str(e)}")

async def test_sync_status():
    """测试同步状态获取"""
    print("\n🔍 测试同步状态获取...")
    
    try:
        status = await sync_manager.get_sync_status_all()
        
        for mapping_key, info in status.items():
            if "error" in info:
                print(f"❌ {mapping_key}: {info['error']}")
            else:
                source_count = info.get("source_count", 0)
                target_count = info.get("target_count", 0)
                print(f"📊 {mapping_key}: 源表 {source_count:,} 条, 目标表 {target_count:,} 条")
        
    except Exception as e:
        print(f"❌ 同步状态测试失败: {str(e)}")

async def test_small_sync():
    """测试小量数据同步"""
    print("\n🔍 测试小量数据同步...")
    
    try:
        # 只测试第一个配置的表
        if TABLE_MAPPINGS:
            mapping_key = list(TABLE_MAPPINGS.keys())[0]
            print(f"测试表: {mapping_key}")
            
            # 获取源表记录数
            mapping_config = TABLE_MAPPINGS[mapping_key]
            source_db = mapping_config["source_db"]
            source_table = mapping_config["source_table"]
            
            from sync_erp2.core.database import db_manager
            source_count = await db_manager.get_table_count(source_db, source_table)
            
            if source_count > 0:
                print(f"源表记录数: {source_count:,}")
                
                # 如果记录数不多，可以尝试小量同步
                if source_count <= 10:
                    print("记录数较少，尝试全量同步...")
                    result = await sync_manager.sync_single_table(mapping_key, "full")
                    
                    if result.get("status") == "success":
                        print(f"✅ 同步成功: 处理 {result.get('total_processed', 0)} 条记录")
                    else:
                        print(f"❌ 同步失败: {result.get('error', '未知错误')}")
                else:
                    print("记录数较多，跳过实际同步测试")
            else:
                print("源表无数据")
        
    except Exception as e:
        print(f"❌ 小量数据同步测试失败: {str(e)}")

async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行同步系统测试")
    print("=" * 60)
    
    start_time = datetime.now()
    
    # 运行各项测试
    await test_database_connections()
    await test_table_validation()
    await test_data_transformer()
    await test_sync_status()
    await test_small_sync()
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成，总耗时: {duration:.2f} 秒")
    
    # 清理资源
    await sync_manager.cleanup()

if __name__ == "__main__":
    asyncio.run(run_all_tests()) 