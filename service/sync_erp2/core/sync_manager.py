"""
同步管理器
统一管理多个表的同步任务，支持并行同步和任务调度
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from settings import logger
from sync_erp2.config.table_mappings import TABLE_MAPPINGS, SYNC_TASKS, GLOBAL_CONFIG
from sync_erp2.core.sync_engine import SyncEngine
from sync_erp2.core.database import db_manager

class SyncManager:
    """同步管理器"""
    
    def __init__(self):
        self.sync_engines: Dict[str, SyncEngine] = {}
        self.sync_results: Dict[str, Dict[str, Any]] = {}
        self._initialize_engines()
    
    def _initialize_engines(self):
        """初始化同步引擎"""
        for mapping_key, mapping_config in TABLE_MAPPINGS.items():
            try:
                engine = SyncEngine(mapping_config)
                self.sync_engines[mapping_key] = engine
                logger.info(f"同步引擎初始化完成: {mapping_key}")
            except Exception as e:
                logger.error(f"同步引擎初始化失败 ({mapping_key}): {str(e)}")
    
    async def sync_all_full(self, parallel: bool = True) -> Dict[str, Any]:
        """全量同步所有表"""
        logger.info("开始全量同步所有表")
        start_time = datetime.now()
        
        enabled_tasks = [task for task in SYNC_TASKS if task.get("enabled", True)]
        
        if parallel:
            # 并行同步
            tasks = []
            for task in enabled_tasks:
                mapping_key = task["mapping_key"]
                if mapping_key in self.sync_engines:
                    tasks.append(self._sync_single_full(mapping_key, task["name"]))
            
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理结果
                for i, result in enumerate(results):
                    task = enabled_tasks[i]
                    mapping_key = task["mapping_key"]
                    
                    if isinstance(result, Exception):
                        self.sync_results[mapping_key] = {
                            "status": "failed",
                            "error": str(result),
                            "task_name": task["name"]
                        }
                    else:
                        self.sync_results[mapping_key] = result
        else:
            # 串行同步
            for task in enabled_tasks:
                mapping_key = task["mapping_key"]
                if mapping_key in self.sync_engines:
                    try:
                        result = await self._sync_single_full(mapping_key, task["name"])
                        self.sync_results[mapping_key] = result
                    except Exception as e:
                        self.sync_results[mapping_key] = {
                            "status": "failed",
                            "error": str(e),
                            "task_name": task["name"]
                        }
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 汇总结果
        summary = self._generate_summary(duration)
        logger.info(f"全量同步完成，总耗时: {duration:.2f}秒")
        logger.info(f"同步汇总: {summary}")
        
        return {
            "summary": summary,
            "details": self.sync_results,
            "duration": duration,
            "start_time": start_time,
            "end_time": end_time
        }
    
    async def sync_all_incremental(self, parallel: bool = True, since_time: Optional[datetime] = None) -> Dict[str, Any]:
        """增量同步所有表"""
        logger.info("开始增量同步所有表")
        start_time = datetime.now()
        
        enabled_tasks = [task for task in SYNC_TASKS if task.get("enabled", True)]
        incremental_tasks = []
        
        # 筛选支持增量同步的表
        for task in enabled_tasks:
            mapping_key = task["mapping_key"]
            if mapping_key in TABLE_MAPPINGS:
                mapping_config = TABLE_MAPPINGS[mapping_key]
                if mapping_config.get("enable_incremental", False):
                    incremental_tasks.append(task)
        
        if not incremental_tasks:
            logger.warning("没有启用增量同步的表")
            return {"summary": {"total": 0, "successful": 0, "failed": 0}, "details": {}}
        
        if parallel:
            # 并行增量同步
            tasks = []
            for task in incremental_tasks:
                mapping_key = task["mapping_key"]
                if mapping_key in self.sync_engines:
                    tasks.append(self._sync_single_incremental(mapping_key, task["name"], since_time))
            
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理结果
                for i, result in enumerate(results):
                    task = incremental_tasks[i]
                    mapping_key = task["mapping_key"]
                    
                    if isinstance(result, Exception):
                        self.sync_results[mapping_key] = {
                            "status": "failed",
                            "error": str(result),
                            "task_name": task["name"]
                        }
                    else:
                        self.sync_results[mapping_key] = result
        else:
            # 串行增量同步
            for task in incremental_tasks:
                mapping_key = task["mapping_key"]
                if mapping_key in self.sync_engines:
                    try:
                        result = await self._sync_single_incremental(mapping_key, task["name"], since_time)
                        self.sync_results[mapping_key] = result
                    except Exception as e:
                        self.sync_results[mapping_key] = {
                            "status": "failed",
                            "error": str(e),
                            "task_name": task["name"]
                        }
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 汇总结果
        summary = self._generate_summary(duration)
        logger.info(f"增量同步完成，总耗时: {duration:.2f}秒")
        logger.info(f"同步汇总: {summary}")
        
        return {
            "summary": summary,
            "details": self.sync_results,
            "duration": duration,
            "start_time": start_time,
            "end_time": end_time
        }
    
    async def sync_single_table(self, mapping_key: str, sync_type: str = "full", since_time: Optional[datetime] = None) -> Dict[str, Any]:
        """同步单个表"""
        if mapping_key not in self.sync_engines:
            raise ValueError(f"未找到同步引擎: {mapping_key}")
        
        engine = self.sync_engines[mapping_key]
        
        try:
            if sync_type == "full":
                result = await engine.sync_full()
            elif sync_type == "incremental":
                result = await engine.sync_incremental(since_time)
            else:
                raise ValueError(f"不支持的同步类型: {sync_type}")
            
            result["status"] = "success"
            result["mapping_key"] = mapping_key
            
            return result
            
        except Exception as e:
            logger.error(f"单表同步失败 ({mapping_key}): {str(e)}")
            return {
                "status": "failed",
                "error": str(e),
                "mapping_key": mapping_key
            }
    
    async def _sync_single_full(self, mapping_key: str, task_name: str) -> Dict[str, Any]:
        """单个表全量同步"""
        logger.info(f"开始全量同步: {task_name} ({mapping_key})")
        
        engine = self.sync_engines[mapping_key]
        result = await engine.sync_full()
        result["status"] = "success"
        result["task_name"] = task_name
        result["mapping_key"] = mapping_key
        
        return result
    
    async def _sync_single_incremental(self, mapping_key: str, task_name: str, since_time: Optional[datetime] = None) -> Dict[str, Any]:
        """单个表增量同步"""
        logger.info(f"开始增量同步: {task_name} ({mapping_key})")
        
        engine = self.sync_engines[mapping_key]
        result = await engine.sync_incremental(since_time)
        result["status"] = "success"
        result["task_name"] = task_name
        result["mapping_key"] = mapping_key
        
        return result
    
    def _generate_summary(self, duration: float) -> Dict[str, Any]:
        """生成同步汇总信息"""
        total_tasks = len(self.sync_results)
        successful_tasks = len([r for r in self.sync_results.values() if r.get("status") == "success"])
        failed_tasks = total_tasks - successful_tasks
        
        total_processed = sum([r.get("total_processed", 0) for r in self.sync_results.values() if r.get("status") == "success"])
        total_successful = sum([r.get("successful_inserts", 0) for r in self.sync_results.values() if r.get("status") == "success"])
        total_failed = sum([r.get("failed_records", 0) for r in self.sync_results.values() if r.get("status") == "success"])
        
        return {
            "total_tasks": total_tasks,
            "successful_tasks": successful_tasks,
            "failed_tasks": failed_tasks,
            "total_processed_records": total_processed,
            "total_successful_records": total_successful,
            "total_failed_records": total_failed,
            "duration_seconds": duration,
            "average_speed": total_processed / duration if duration > 0 else 0
        }
    
    async def get_sync_status_all(self) -> Dict[str, Any]:
        """获取所有表的同步状态"""
        status_info = {}
        
        for mapping_key, engine in self.sync_engines.items():
            try:
                status = await engine.get_sync_status()
                status_info[mapping_key] = status
            except Exception as e:
                status_info[mapping_key] = {
                    "error": str(e)
                }
        
        return status_info
    
    async def validate_all_mappings(self) -> Dict[str, Any]:
        """验证所有映射配置"""
        validation_results = {}
        
        for mapping_key, mapping_config in TABLE_MAPPINGS.items():
            try:
                # 检查源表和目标表是否存在
                source_db = mapping_config["source_db"]
                target_db = mapping_config["target_db"]
                source_table = mapping_config["source_table"]
                target_table = mapping_config["target_table"]
                
                source_exists = await db_manager.check_table_exists(source_db, source_table)
                target_exists = await db_manager.check_table_exists(target_db, target_table)
                
                # 获取表结构信息
                source_columns = []
                target_columns = []
                
                if source_exists:
                    source_columns = await db_manager.get_table_columns(source_db, source_table)
                
                if target_exists:
                    target_columns = await db_manager.get_table_columns(target_db, target_table)
                
                validation_results[mapping_key] = {
                    "source_exists": source_exists,
                    "target_exists": target_exists,
                    "source_columns": len(source_columns),
                    "target_columns": len(target_columns),
                    "mapping_fields": len(mapping_config.get("field_mappings", {})),
                    "status": "valid" if source_exists and target_exists else "invalid"
                }
                
            except Exception as e:
                validation_results[mapping_key] = {
                    "status": "error",
                    "error": str(e)
                }
        
        return validation_results
    
    async def cleanup(self):
        """清理资源"""
        await db_manager.close_all()
        logger.info("同步管理器资源清理完成")

# 全局同步管理器实例
sync_manager = SyncManager() 