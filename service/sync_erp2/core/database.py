"""
数据库连接管理器
支持多数据库连接、事务管理、连接池等功能
"""

import asyncio
from typing import Dict, Any, Optional, AsyncGenerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, AsyncEngine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from contextlib import asynccontextmanager
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from settings import DB_CONFIGS, logger

class DatabaseManager:
    """数据库连接管理器"""
    
    def __init__(self):
        self.engines: Dict[str, AsyncEngine] = {}
        self.session_factories: Dict[str, sessionmaker] = {}
        self._initialize_engines()
    
    def _initialize_engines(self):
        """初始化数据库引擎"""
        for db_name, config in DB_CONFIGS.items():
            database_url = f"mysql+aiomysql://{config['USER']}:{config['PASSWD']}@{config['HOST']}:{config['PORT']}/{config['DB']}"
            
            engine = create_async_engine(
                database_url,
                pool_size=20,
                max_overflow=10,
                pool_recycle=3600,
                pool_pre_ping=True,
                pool_timeout=30,
                echo=False  # 设置为True可以看到SQL语句
            )
            
            session_factory = sessionmaker(
                bind=engine,
                class_=AsyncSession,
                autocommit=False,
                autoflush=False,
            )
            
            self.engines[db_name] = engine
            self.session_factories[db_name] = session_factory
            logger.info(f"数据库引擎初始化完成: {db_name}")
    
    @asynccontextmanager
    async def get_session(self, db_name: str) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话"""
        if db_name not in self.session_factories:
            raise ValueError(f"未找到数据库配置: {db_name}")
        
        session_factory = self.session_factories[db_name]
        async with session_factory() as session:
            try:
                yield session
            except Exception as e:
                await session.rollback()
                logger.error(f"数据库会话错误 ({db_name}): {e}")
                raise
            finally:
                await session.close()
    
    @asynccontextmanager
    async def get_transaction(self, db_name: str) -> AsyncGenerator[AsyncSession, None]:
        """获取事务会话"""
        async with self.get_session(db_name) as session:
            async with session.begin():
                try:
                    yield session
                except Exception as e:
                    logger.error(f"事务执行错误 ({db_name}): {e}")
                    raise
    
    async def execute_query(self, db_name: str, query: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """执行查询语句"""
        async with self.get_session(db_name) as session:
            try:
                result = await session.execute(text(query), params or {})
                return result
            except Exception as e:
                logger.error(f"查询执行错误 ({db_name}): {query}, 错误: {e}")
                raise
    
    async def fetch_all(self, db_name: str, query: str, params: Optional[Dict[str, Any]] = None) -> list:
        """获取所有查询结果"""
        result = await self.execute_query(db_name, query, params)
        return result.fetchall()
    
    async def fetch_one(self, db_name: str, query: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """获取单个查询结果"""
        result = await self.execute_query(db_name, query, params)
        return result.fetchone()
    
    async def get_table_columns(self, db_name: str, table_name: str) -> list:
        """获取表的列信息"""
        query = """
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = :table_name
        ORDER BY ORDINAL_POSITION
        """
        return await self.fetch_all(db_name, query, {"table_name": table_name})
    
    async def get_table_count(self, db_name: str, table_name: str, where_clause: str = "") -> int:
        """获取表记录数"""
        query = f"SELECT COUNT(*) as count FROM {table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        
        result = await self.fetch_one(db_name, query)
        return result.count if result else 0
    
    async def check_table_exists(self, db_name: str, table_name: str) -> bool:
        """检查表是否存在"""
        query = """
        SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = :table_name
        """
        result = await self.fetch_one(db_name, query, {"table_name": table_name})
        return result.count > 0 if result else False
    
    async def get_max_value(self, db_name: str, table_name: str, column_name: str) -> Any:
        """获取字段的最大值"""
        query = f"SELECT MAX({column_name}) as max_value FROM {table_name}"
        result = await self.fetch_one(db_name, query)
        return result.max_value if result else None
    
    async def close_all(self):
        """关闭所有数据库连接"""
        for db_name, engine in self.engines.items():
            await engine.dispose()
            logger.info(f"数据库连接已关闭: {db_name}")

# 全局数据库管理器实例
db_manager = DatabaseManager() 