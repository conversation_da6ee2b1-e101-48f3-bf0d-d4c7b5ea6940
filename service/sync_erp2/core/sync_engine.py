"""
同步引擎
实现数据同步的核心逻辑，支持全量同步和增量同步
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from settings import logger
from sync_erp2.core.database import db_manager
from sync_erp2.transformers.data_transformer import DataTransformer

class SyncEngine:
    """数据同步引擎"""
    
    def __init__(self, mapping_config: Dict[str, Any]):
        self.mapping_config = mapping_config
        self.transformer = DataTransformer(mapping_config)
        self.source_db = mapping_config["source_db"]
        self.target_db = mapping_config["target_db"]
        self.source_table = mapping_config["source_table"]
        self.target_table = mapping_config["target_table"]
        self.primary_key = mapping_config["primary_key"]
        self.target_primary_key = mapping_config["target_primary_key"]
        self.batch_size = mapping_config.get("batch_size", 1000)
        self.enable_incremental = mapping_config.get("enable_incremental", False)
        self.incremental_field = mapping_config.get("incremental_field", "UpdateTime")
        
        # 统计信息
        self.stats = {
            "total_processed": 0,
            "successful_inserts": 0,
            "successful_updates": 0,
            "failed_records": 0,
            "start_time": None,
            "end_time": None,
            "errors": []
        }
    
    async def sync_full(self) -> Dict[str, Any]:
        """全量同步"""
        logger.info(f"开始全量同步: {self.source_table} -> {self.target_table}")
        self.stats["start_time"] = datetime.now()
        
        try:
            # 检查源表和目标表是否存在
            await self._validate_tables()
            
            # 获取源表总记录数
            total_count = await db_manager.get_table_count(self.source_db, self.source_table)
            logger.info(f"源表 {self.source_table} 总记录数: {total_count}")
            
            # 分批处理数据
            offset = 0
            while offset < total_count:
                batch_result = await self._sync_batch(offset, self.batch_size)
                offset += self.batch_size
                
                logger.info(f"已处理 {min(offset, total_count)}/{total_count} 条记录")
                
                # 避免过度占用资源
                await asyncio.sleep(0.1)
            
            self.stats["end_time"] = datetime.now()
            duration = (self.stats["end_time"] - self.stats["start_time"]).total_seconds()
            
            logger.info(f"全量同步完成，耗时: {duration:.2f}秒")
            logger.info(f"同步统计: {self._format_stats()}")
            
            return self.stats
            
        except Exception as e:
            self.stats["end_time"] = datetime.now()
            self.stats["errors"].append(f"全量同步失败: {str(e)}")
            logger.error(f"全量同步失败: {str(e)}")
            raise
    
    async def sync_incremental(self, since_time: Optional[datetime] = None) -> Dict[str, Any]:
        """增量同步"""
        if not self.enable_incremental:
            raise ValueError("该表未启用增量同步")
        
        logger.info(f"开始增量同步: {self.source_table} -> {self.target_table}")
        self.stats["start_time"] = datetime.now()
        
        try:
            # 检查源表和目标表是否存在
            await self._validate_tables()
            
            # 确定增量同步的起始时间
            if since_time is None:
                since_time = await self._get_last_sync_time()
            
            logger.info(f"增量同步起始时间: {since_time}")
            
            # 构建增量查询条件
            where_clause = f"{self.incremental_field} > '{since_time}'"
            
            # 获取增量数据总数
            total_count = await db_manager.get_table_count(
                self.source_db, self.source_table, where_clause
            )
            logger.info(f"增量数据记录数: {total_count}")
            
            if total_count == 0:
                logger.info("没有需要同步的增量数据")
                return self.stats
            
            # 分批处理增量数据
            offset = 0
            while offset < total_count:
                batch_result = await self._sync_batch(offset, self.batch_size, where_clause)
                offset += self.batch_size
                
                logger.info(f"已处理 {min(offset, total_count)}/{total_count} 条增量记录")
                
                await asyncio.sleep(0.1)
            
            # 更新最后同步时间
            await self._update_last_sync_time()
            
            self.stats["end_time"] = datetime.now()
            duration = (self.stats["end_time"] - self.stats["start_time"]).total_seconds()
            
            logger.info(f"增量同步完成，耗时: {duration:.2f}秒")
            logger.info(f"同步统计: {self._format_stats()}")
            
            return self.stats
            
        except Exception as e:
            self.stats["end_time"] = datetime.now()
            self.stats["errors"].append(f"增量同步失败: {str(e)}")
            logger.error(f"增量同步失败: {str(e)}")
            raise
    
    async def _sync_batch(self, offset: int, limit: int, where_clause: str = "") -> Dict[str, Any]:
        """同步一批数据"""
        try:
            # 构建查询SQL
            source_fields = list(self.mapping_config["field_mappings"].keys())
            fields_str = ", ".join(source_fields)
            
            query = f"SELECT {fields_str} FROM {self.source_table}"
            if where_clause:
                query += f" WHERE {where_clause}"
            query += f" ORDER BY {self.primary_key} LIMIT {limit} OFFSET {offset}"
            
            # 获取源数据
            source_records = await db_manager.fetch_all(self.source_db, query)
            
            if not source_records:
                return {"processed": 0, "successful": 0, "failed": 0}
            
            # 转换为字典格式
            source_data = []
            for record in source_records:
                record_dict = {}
                for i, field in enumerate(source_fields):
                    record_dict[field] = record[i] if i < len(record) else None
                source_data.append(record_dict)
            
            # 数据转换
            transformed_records, failed_records = self.transformer.transform_batch(source_data)
            
            # 批量插入/更新目标表
            successful_count = 0
            if transformed_records:
                successful_count = await self._batch_upsert(transformed_records)
            
            # 更新统计信息
            self.stats["total_processed"] += len(source_data)
            self.stats["successful_inserts"] += successful_count
            self.stats["failed_records"] += len(failed_records)
            
            # 记录失败的记录
            for failed_record in failed_records:
                self.stats["errors"].append({
                    "type": "transform_error",
                    "record": failed_record
                })
            
            return {
                "processed": len(source_data),
                "successful": successful_count,
                "failed": len(failed_records)
            }
            
        except Exception as e:
            logger.error(f"批量同步失败 (offset: {offset}): {str(e)}")
            self.stats["errors"].append(f"批量同步失败 (offset: {offset}): {str(e)}")
            raise
    
    async def _batch_upsert(self, records: List[Dict[str, Any]]) -> int:
        """批量插入或更新记录"""
        if not records:
            return 0
        
        try:
            sql, params = self.transformer.build_insert_sql(self.target_table, records)
            
            async with db_manager.get_transaction(self.target_db) as session:
                for param in params:
                    await session.execute(sql, param)
                await session.commit()
            
            return len(records)
            
        except Exception as e:
            logger.error(f"批量插入失败: {str(e)}")
            raise
    
    async def _validate_tables(self):
        """验证源表和目标表是否存在"""
        # 检查源表
        source_exists = await db_manager.check_table_exists(self.source_db, self.source_table)
        if not source_exists:
            raise ValueError(f"源表不存在: {self.source_db}.{self.source_table}")
        
        # 检查目标表
        target_exists = await db_manager.check_table_exists(self.target_db, self.target_table)
        if not target_exists:
            raise ValueError(f"目标表不存在: {self.target_db}.{self.target_table}")
        
        logger.info("表验证通过")
    
    async def _get_last_sync_time(self) -> datetime:
        """获取最后同步时间"""
        try:
            # 从目标表获取最大更新时间
            max_time = await db_manager.get_max_value(
                self.target_db, self.target_table, "update_time"
            )
            
            if max_time:
                return max_time
            else:
                # 如果没有记录，返回一个较早的时间
                return datetime(2020, 1, 1)
                
        except Exception as e:
            logger.warning(f"获取最后同步时间失败: {str(e)}")
            return datetime(2020, 1, 1)
    
    async def _update_last_sync_time(self):
        """更新最后同步时间"""
        # 这里可以实现同步时间的记录逻辑
        # 比如写入到专门的同步状态表中
        pass
    
    def _format_stats(self) -> str:
        """格式化统计信息"""
        duration = 0
        if self.stats["start_time"] and self.stats["end_time"]:
            duration = (self.stats["end_time"] - self.stats["start_time"]).total_seconds()
        
        return (
            f"总处理: {self.stats['total_processed']}, "
            f"成功: {self.stats['successful_inserts']}, "
            f"失败: {self.stats['failed_records']}, "
            f"耗时: {duration:.2f}秒"
        )
    
    async def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        source_count = await db_manager.get_table_count(self.source_db, self.source_table)
        target_count = await db_manager.get_table_count(self.target_db, self.target_table)
        
        return {
            "source_table": f"{self.source_db}.{self.source_table}",
            "target_table": f"{self.target_db}.{self.target_table}",
            "source_count": source_count,
            "target_count": target_count,
            "sync_ratio": target_count / source_count if source_count > 0 else 0,
            "last_stats": self.stats
        } 