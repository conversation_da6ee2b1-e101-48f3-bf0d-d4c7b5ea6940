"""
同步API接口
可以集成到FastAPI应用中，提供Web接口调用同步功能
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from settings import logger
from sync_erp2.core.sync_manager import sync_manager
from sync_erp2.config.table_mappings import TABLE_MAPPINGS

# 创建路由器
router = APIRouter(prefix="/sync", tags=["数据同步"])

# 存储后台任务状态
background_tasks_status = {}

@router.get("/status")
async def get_sync_status():
    """获取所有表的同步状态"""
    try:
        status = await sync_manager.get_sync_status_all()
        return {
            "success": True,
            "data": status,
            "message": "获取同步状态成功"
        }
    except Exception as e:
        logger.error(f"获取同步状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取同步状态失败: {str(e)}")

@router.get("/tables")
async def list_sync_tables():
    """列出所有可同步的表"""
    try:
        tables = []
        for mapping_key, config in TABLE_MAPPINGS.items():
            tables.append({
                "mapping_key": mapping_key,
                "source_table": config.get("source_table"),
                "target_table": config.get("target_table"),
                "source_db": config.get("source_db"),
                "target_db": config.get("target_db"),
                "enable_incremental": config.get("enable_incremental", False),
                "batch_size": config.get("batch_size", 1000)
            })
        
        return {
            "success": True,
            "data": tables,
            "total": len(tables),
            "message": "获取表列表成功"
        }
    except Exception as e:
        logger.error(f"获取表列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取表列表失败: {str(e)}")

@router.post("/validate")
async def validate_mappings():
    """验证所有映射配置"""
    try:
        validation_results = await sync_manager.validate_all_mappings()
        
        # 统计验证结果
        valid_count = sum(1 for r in validation_results.values() if r.get("status") == "valid")
        invalid_count = sum(1 for r in validation_results.values() if r.get("status") == "invalid")
        error_count = sum(1 for r in validation_results.values() if r.get("status") == "error")
        
        return {
            "success": True,
            "data": validation_results,
            "summary": {
                "total": len(validation_results),
                "valid": valid_count,
                "invalid": invalid_count,
                "error": error_count
            },
            "message": "验证映射配置完成"
        }
    except Exception as e:
        logger.error(f"验证映射配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"验证映射配置失败: {str(e)}")

@router.post("/full")
async def run_full_sync(
    background_tasks: BackgroundTasks,
    table_name: Optional[str] = None,
    parallel: bool = True
):
    """运行全量同步"""
    try:
        task_id = f"full_sync_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 添加后台任务
        background_tasks.add_task(
            _run_full_sync_background,
            task_id,
            table_name,
            parallel
        )
        
        # 初始化任务状态
        background_tasks_status[task_id] = {
            "status": "running",
            "start_time": datetime.now(),
            "type": "full_sync",
            "table_name": table_name,
            "parallel": parallel
        }
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "全量同步任务已启动"
        }
    except Exception as e:
        logger.error(f"启动全量同步失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动全量同步失败: {str(e)}")

@router.post("/incremental")
async def run_incremental_sync(
    background_tasks: BackgroundTasks,
    table_name: Optional[str] = None,
    since_hours: int = 24,
    parallel: bool = True
):
    """运行增量同步"""
    try:
        task_id = f"incremental_sync_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 添加后台任务
        background_tasks.add_task(
            _run_incremental_sync_background,
            task_id,
            table_name,
            since_hours,
            parallel
        )
        
        # 初始化任务状态
        background_tasks_status[task_id] = {
            "status": "running",
            "start_time": datetime.now(),
            "type": "incremental_sync",
            "table_name": table_name,
            "since_hours": since_hours,
            "parallel": parallel
        }
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "增量同步任务已启动"
        }
    except Exception as e:
        logger.error(f"启动增量同步失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动增量同步失败: {str(e)}")

@router.get("/task/{task_id}")
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in background_tasks_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return {
        "success": True,
        "data": background_tasks_status[task_id],
        "message": "获取任务状态成功"
    }

@router.get("/tasks")
async def list_tasks():
    """列出所有任务"""
    return {
        "success": True,
        "data": background_tasks_status,
        "total": len(background_tasks_status),
        "message": "获取任务列表成功"
    }

@router.post("/single/{mapping_key}")
async def sync_single_table(
    mapping_key: str,
    sync_type: str = "full",
    since_hours: int = 24
):
    """同步单个表（同步执行）"""
    try:
        if mapping_key not in TABLE_MAPPINGS:
            raise HTTPException(status_code=404, detail=f"表映射不存在: {mapping_key}")
        
        since_time = None
        if sync_type == "incremental":
            since_time = datetime.now() - timedelta(hours=since_hours)
        
        result = await sync_manager.sync_single_table(mapping_key, sync_type, since_time)
        
        return {
            "success": True,
            "data": result,
            "message": f"单表{sync_type}同步完成"
        }
    except Exception as e:
        logger.error(f"单表同步失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"单表同步失败: {str(e)}")

async def _run_full_sync_background(task_id: str, table_name: Optional[str], parallel: bool):
    """后台执行全量同步"""
    try:
        if table_name:
            result = await sync_manager.sync_single_table(table_name, "full")
        else:
            result = await sync_manager.sync_all_full(parallel)
        
        # 更新任务状态
        background_tasks_status[task_id].update({
            "status": "completed",
            "end_time": datetime.now(),
            "result": result
        })
        
        logger.info(f"全量同步任务完成: {task_id}")
        
    except Exception as e:
        # 更新任务状态为失败
        background_tasks_status[task_id].update({
            "status": "failed",
            "end_time": datetime.now(),
            "error": str(e)
        })
        
        logger.error(f"全量同步任务失败: {task_id}, 错误: {str(e)}")

async def _run_incremental_sync_background(task_id: str, table_name: Optional[str], since_hours: int, parallel: bool):
    """后台执行增量同步"""
    try:
        since_time = datetime.now() - timedelta(hours=since_hours)
        
        if table_name:
            result = await sync_manager.sync_single_table(table_name, "incremental", since_time)
        else:
            result = await sync_manager.sync_all_incremental(parallel, since_time)
        
        # 更新任务状态
        background_tasks_status[task_id].update({
            "status": "completed",
            "end_time": datetime.now(),
            "result": result
        })
        
        logger.info(f"增量同步任务完成: {task_id}")
        
    except Exception as e:
        # 更新任务状态为失败
        background_tasks_status[task_id].update({
            "status": "failed",
            "end_time": datetime.now(),
            "error": str(e)
        })
        
        logger.error(f"增量同步任务失败: {task_id}, 错误: {str(e)}")

# 清理过期任务的函数
async def cleanup_old_tasks():
    """清理超过24小时的任务记录"""
    cutoff_time = datetime.now() - timedelta(hours=24)
    
    tasks_to_remove = []
    for task_id, task_info in background_tasks_status.items():
        if task_info.get("start_time", datetime.now()) < cutoff_time:
            tasks_to_remove.append(task_id)
    
    for task_id in tasks_to_remove:
        del background_tasks_status[task_id]
    
    if tasks_to_remove:
        logger.info(f"清理了 {len(tasks_to_remove)} 个过期任务")