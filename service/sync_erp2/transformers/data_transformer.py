"""
数据转换器
处理字段映射、数据类型转换、数据验证等功能
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from settings import logger

class DataTransformer:
    """数据转换器"""
    
    def __init__(self, mapping_config: Dict[str, Any]):
        self.mapping_config = mapping_config
        self.field_mappings = mapping_config.get("field_mappings", {})
        self.default_values = mapping_config.get("default_values", {})
        self.validation_rules = mapping_config.get("validation_rules", {})
    
    def transform_record(self, source_record: Dict[str, Any]) -> Tuple[Dict[str, Any], List[str]]:
        """
        转换单条记录
        返回: (转换后的记录, 错误列表)
        """
        transformed_record = {}
        errors = []
        
        # 处理字段映射
        for source_field, (target_field, transform_func, is_required) in self.field_mappings.items():
            try:
                source_value = source_record.get(source_field)
                
                # 检查必需字段
                if is_required and source_value is None:
                    errors.append(f"必需字段 {source_field} 为空")
                    continue
                
                # 应用转换函数
                if transform_func and source_value is not None:
                    transformed_value = transform_func(source_value)
                else:
                    transformed_value = source_value
                
                # 数据验证
                validation_error = self._validate_field(target_field, transformed_value)
                if validation_error:
                    errors.append(validation_error)
                    continue
                
                transformed_record[target_field] = transformed_value
                
            except Exception as e:
                errors.append(f"字段 {source_field} 转换错误: {str(e)}")
        
        # 添加默认值
        for field, default_value in self.default_values.items():
            if field not in transformed_record:
                transformed_record[field] = default_value
        
        return transformed_record, errors
    
    def transform_batch(self, source_records: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        批量转换记录
        返回: (成功转换的记录列表, 失败记录列表)
        """
        successful_records = []
        failed_records = []
        
        for i, record in enumerate(source_records):
            try:
                transformed_record, errors = self.transform_record(record)
                
                if errors:
                    failed_records.append({
                        "index": i,
                        "source_record": record,
                        "errors": errors
                    })
                    logger.warning(f"记录转换失败 (索引 {i}): {errors}")
                else:
                    successful_records.append(transformed_record)
                    
            except Exception as e:
                failed_records.append({
                    "index": i,
                    "source_record": record,
                    "errors": [f"转换异常: {str(e)}"]
                })
                logger.error(f"记录转换异常 (索引 {i}): {str(e)}")
        
        return successful_records, failed_records
    
    def _validate_field(self, field_name: str, value: Any) -> Optional[str]:
        """验证字段值"""
        if field_name not in self.validation_rules:
            return None
        
        rules = self.validation_rules[field_name]
        
        # 检查最大长度
        if "max_length" in rules and value is not None:
            if isinstance(value, str) and len(value) > rules["max_length"]:
                return f"字段 {field_name} 长度超过限制 ({len(value)} > {rules['max_length']})"
        
        # 检查最小长度
        if "min_length" in rules and value is not None:
            if isinstance(value, str) and len(value) < rules["min_length"]:
                return f"字段 {field_name} 长度不足 ({len(value)} < {rules['min_length']})"
        
        # 检查数值范围
        if "min_value" in rules and value is not None:
            if isinstance(value, (int, float)) and value < rules["min_value"]:
                return f"字段 {field_name} 值过小 ({value} < {rules['min_value']})"
        
        if "max_value" in rules and value is not None:
            if isinstance(value, (int, float)) and value > rules["max_value"]:
                return f"字段 {field_name} 值过大 ({value} > {rules['max_value']})"
        
        # 检查正则表达式
        if "pattern" in rules and value is not None:
            import re
            if isinstance(value, str) and not re.match(rules["pattern"], value):
                return f"字段 {field_name} 格式不正确"
        
        # 检查枚举值
        if "enum" in rules and value is not None:
            if value not in rules["enum"]:
                return f"字段 {field_name} 值不在允许范围内: {value}"
        
        return None
    
    def get_target_fields(self) -> List[str]:
        """获取目标字段列表"""
        target_fields = []
        
        # 从字段映射中获取目标字段
        for source_field, (target_field, _, _) in self.field_mappings.items():
            target_fields.append(target_field)
        
        # 添加默认值字段
        for field in self.default_values.keys():
            if field not in target_fields:
                target_fields.append(field)
        
        return target_fields
    
    def build_insert_sql(self, table_name: str, records: List[Dict[str, Any]]) -> Tuple[str, List[tuple]]:
        """构建批量插入SQL"""
        if not records:
            return "", []
        
        # 获取所有字段
        all_fields = set()
        for record in records:
            all_fields.update(record.keys())
        
        fields = list(all_fields)
        placeholders = ", ".join([f":{field}" for field in fields])
        
        sql = f"""
        INSERT INTO {table_name} ({', '.join(fields)})
        VALUES ({placeholders})
        ON DUPLICATE KEY UPDATE
        {', '.join([f"{field} = VALUES({field})" for field in fields if field != self.mapping_config.get('target_primary_key', 'id')])}
        """
        
        # 准备参数
        params = []
        for record in records:
            param_dict = {}
            for field in fields:
                param_dict[field] = record.get(field)
            params.append(param_dict)
        
        return sql, params
    
    def build_update_sql(self, table_name: str, record: Dict[str, Any], where_condition: str) -> Tuple[str, Dict[str, Any]]:
        """构建更新SQL"""
        if not record:
            return "", {}
        
        set_clauses = []
        params = {}
        
        for field, value in record.items():
            set_clauses.append(f"{field} = :{field}")
            params[field] = value
        
        sql = f"UPDATE {table_name} SET {', '.join(set_clauses)} WHERE {where_condition}"
        
        return sql, params 