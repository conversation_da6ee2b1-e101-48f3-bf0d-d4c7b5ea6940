# ERP数据同步系统

一个灵活、高效的MySQL数据表同步系统，支持字段映射、数据转换、增量同步等功能。

## 功能特性

- ✅ **字段映射**: 支持复杂的字段映射和重命名
- ✅ **数据转换**: 内置多种数据转换函数，支持自定义转换
- ✅ **增量同步**: 基于时间戳的增量同步机制
- ✅ **批量处理**: 分批处理大量数据，避免内存溢出
- ✅ **并行同步**: 支持多表并行同步，提高效率
- ✅ **数据验证**: 完整的数据验证机制
- ✅ **错误处理**: 详细的错误日志和异常处理
- ✅ **API接口**: 提供REST API和命令行接口
- ✅ **监控统计**: 实时同步状态和统计信息

## 目录结构

```
sync_erp2/
├── config/
│   └── table_mappings.py      # 表映射配置
├── core/
│   ├── database.py            # 数据库连接管理
│   ├── sync_engine.py         # 同步引擎
│   └── sync_manager.py        # 同步管理器
├── transformers/
│   └── data_transformer.py    # 数据转换器
├── logs/                      # 日志目录
├── main_sync.py              # 命令行接口
├── api_sync.py               # API接口
└── README.md                 # 说明文档
```

## 快速开始

### 1. 配置表映射

编辑 `config/table_mappings.py` 文件，添加您的表映射配置：

```python
TABLE_MAPPINGS = {
    "your_table_mapping": {
        "source_db": "source_database",
        "target_db": "target_database", 
        "source_table": "source_table_name",
        "target_table": "target_table_name",
        "primary_key": "source_primary_key",
        "target_primary_key": "target_primary_key",
        "field_mappings": {
            "source_field": ("target_field", transform_function, is_required),
            # 更多字段映射...
        },
        "default_values": {
            "target_field": "default_value",
            # 更多默认值...
        }
    }
}
```

### 2. 命令行使用

```bash
# 查看所有可同步的表
python sync_erp2/main_sync.py list

# 验证映射配置
python sync_erp2/main_sync.py validate

# 查看同步状态
python sync_erp2/main_sync.py status

# 全量同步所有表
python sync_erp2/main_sync.py full

# 全量同步单个表
python sync_erp2/main_sync.py full --table rb_student_to_erp_student

# 增量同步（最近24小时）
python sync_erp2/main_sync.py incremental

# 增量同步（最近6小时）
python sync_erp2/main_sync.py incremental --hours 6

# 串行执行（默认并行）
python sync_erp2/main_sync.py full --serial
```

### 3. API接口使用

将API路由集成到您的FastAPI应用中：

```python
from fastapi import FastAPI
from sync_erp2.api_sync import router

app = FastAPI()
app.include_router(router)
```

API端点：

- `GET /sync/status` - 获取同步状态
- `GET /sync/tables` - 列出所有表
- `POST /sync/validate` - 验证配置
- `POST /sync/full` - 全量同步
- `POST /sync/incremental` - 增量同步
- `GET /sync/task/{task_id}` - 获取任务状态

## 配置说明

### 表映射配置

每个表映射包含以下配置项：

| 配置项 | 说明 | 必需 |
|--------|------|------|
| source_db | 源数据库名称 | ✅ |
| target_db | 目标数据库名称 | ✅ |
| source_table | 源表名称 | ✅ |
| target_table | 目标表名称 | ✅ |
| primary_key | 源表主键 | ✅ |
| target_primary_key | 目标表主键 | ✅ |
| field_mappings | 字段映射关系 | ✅ |
| batch_size | 批处理大小 | ❌ |
| enable_incremental | 是否启用增量同步 | ❌ |
| incremental_field | 增量同步字段 | ❌ |
| default_values | 默认值 | ❌ |
| validation_rules | 验证规则 | ❌ |

### 字段映射格式

```python
"source_field": ("target_field", transform_function, is_required)
```

- `source_field`: 源字段名
- `target_field`: 目标字段名
- `transform_function`: 转换函数（可选）
- `is_required`: 是否必需字段

### 内置转换函数

- `convert_gender(value)` - 性别转换
- `convert_disable_status(value)` - 状态转换
- `convert_decimal(value)` - 转换为Decimal
- `convert_datetime(value)` - 转换为datetime
- `convert_string(value)` - 转换为字符串

### 自定义转换函数

```python
def custom_transform(value):
    """自定义转换函数"""
    if value is None:
        return None
    # 自定义转换逻辑
    return transformed_value
```

## 数据验证

支持以下验证规则：

```python
"validation_rules": {
    "field_name": {
        "max_length": 255,      # 最大长度
        "min_length": 1,        # 最小长度
        "max_value": 100,       # 最大值
        "min_value": 0,         # 最小值
        "pattern": r"^[a-zA-Z]+$",  # 正则表达式
        "enum": ["value1", "value2"]  # 枚举值
    }
}
```

## 监控和日志

### 日志文件

- 信息日志: `log/info/{date}.log`
- 错误日志: `log/error/{date}.log`

### 同步统计

每次同步都会生成详细的统计信息：

- 总处理记录数
- 成功记录数
- 失败记录数
- 执行时间
- 平均速度

## 最佳实践

### 1. 配置优化

- 根据数据量调整 `batch_size`
- 合理设置数据库连接池大小
- 启用增量同步减少数据传输

### 2. 性能优化

- 使用并行同步提高效率
- 在低峰期执行大量同步
- 定期清理日志文件

### 3. 错误处理

- 监控同步状态和错误日志
- 设置重试机制
- 建立数据备份策略

### 4. 安全考虑

- 使用专用数据库用户
- 限制数据库访问权限
- 加密敏感配置信息

## 故障排除

### 常见问题

1. **连接失败**
   - 检查数据库配置
   - 确认网络连通性
   - 验证用户权限

2. **字段映射错误**
   - 检查字段名拼写
   - 确认字段类型兼容
   - 验证转换函数

3. **性能问题**
   - 调整批处理大小
   - 优化数据库索引
   - 使用增量同步

4. **数据不一致**
   - 检查转换逻辑
   - 验证数据完整性
   - 重新执行全量同步

### 调试模式

启用详细日志：

```python
# 在database.py中设置
engine = create_async_engine(
    database_url,
    echo=True  # 显示SQL语句
)
```

## 扩展开发

### 添加新的转换函数

```python
def your_custom_transform(value):
    """自定义转换函数"""
    # 实现转换逻辑
    return transformed_value

# 在field_mappings中使用
"source_field": ("target_field", your_custom_transform, False)
```

### 添加新的验证规则

在 `data_transformer.py` 的 `_validate_field` 方法中添加新的验证逻辑。

### 集成定时任务

使用APScheduler或Celery集成定时同步：

```python
from apscheduler.schedulers.asyncio import AsyncIOScheduler

scheduler = AsyncIOScheduler()
scheduler.add_job(
    sync_manager.sync_all_incremental,
    'cron',
    hour=2,  # 每天凌晨2点执行
    minute=0
)
scheduler.start()
```

## 版本历史

- v1.0.0 - 初始版本，支持基本同步功能
- 计划中的功能：
  - 数据压缩传输
  - 分布式同步
  - 可视化监控界面
  - 更多数据源支持

## 技术支持

如有问题或建议，请联系开发团队或查看项目文档。 