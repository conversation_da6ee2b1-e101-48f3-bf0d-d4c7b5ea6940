"""
ERP数据同步服务
集成新的同步系统，提供统一的同步接口
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from settings import logger
from sync_erp2.core.sync_manager import sync_manager

class ERPSyncService:
    """ERP同步服务"""
    
    def __init__(self):
        self.sync_manager = sync_manager
    
    async def sync_student_data(self, sync_type: str = "incremental", since_hours: int = 24):
        """同步学生数据"""
        try:
            logger.info(f"开始同步学生数据 - 类型: {sync_type}")
            
            if sync_type == "full":
                result = await self.sync_manager.sync_single_table(
                    "rb_student_to_erp_student", "full"
                )
            else:
                since_time = datetime.now() - timedelta(hours=since_hours)
                result = await self.sync_manager.sync_single_table(
                    "rb_student_to_erp_student", "incremental", since_time
                )
            
            if result.get("status") == "success":
                logger.info(f"学生数据同步成功: 处理 {result.get('total_processed', 0)} 条记录")
                return True
            else:
                logger.error(f"学生数据同步失败: {result.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            logger.error(f"学生数据同步异常: {str(e)}")
            return False
    
    async def sync_all_tables(self, sync_type: str = "incremental", parallel: bool = True):
        """同步所有表"""
        try:
            logger.info(f"开始同步所有表 - 类型: {sync_type}, 并行: {parallel}")
            
            if sync_type == "full":
                result = await self.sync_manager.sync_all_full(parallel)
            else:
                result = await self.sync_manager.sync_all_incremental(parallel)
            
            summary = result.get("summary", {})
            successful_tasks = summary.get("successful_tasks", 0)
            total_tasks = summary.get("total_tasks", 0)
            
            logger.info(f"所有表同步完成: {successful_tasks}/{total_tasks} 个任务成功")
            
            return successful_tasks == total_tasks
            
        except Exception as e:
            logger.error(f"所有表同步异常: {str(e)}")
            return False
    
    async def get_sync_status(self):
        """获取同步状态"""
        try:
            return await self.sync_manager.get_sync_status_all()
        except Exception as e:
            logger.error(f"获取同步状态失败: {str(e)}")
            return {}
    
    async def validate_sync_config(self):
        """验证同步配置"""
        try:
            return await self.sync_manager.validate_all_mappings()
        except Exception as e:
            logger.error(f"验证同步配置失败: {str(e)}")
            return {}

# 全局同步服务实例
erp_sync_service = ERPSyncService()

# 兼容性函数，保持原有接口
async def sync_student_table():
    """同步学生表（兼容性函数）"""
    return await erp_sync_service.sync_student_data("incremental")

async def sync_all_erp_tables():
    """同步所有ERP表（兼容性函数）"""
    return await erp_sync_service.sync_all_tables("incremental")

# 主函数，用于直接运行
async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="ERP数据同步服务")
    parser.add_argument("--type", choices=["full", "incremental"], default="incremental", help="同步类型")
    parser.add_argument("--table", choices=["student", "all"], default="all", help="同步表")
    parser.add_argument("--hours", type=int, default=24, help="增量同步时间范围(小时)")
    parser.add_argument("--serial", action="store_true", help="串行执行")
    
    args = parser.parse_args()
    
    try:
        if args.table == "student":
            success = await erp_sync_service.sync_student_data(args.type, args.hours)
        else:
            success = await erp_sync_service.sync_all_tables(args.type, not args.serial)
        
        if success:
            print("✅ 同步完成")
        else:
            print("❌ 同步失败")
            
    except Exception as e:
        logger.error(f"同步执行失败: {str(e)}")
        print(f"❌ 同步执行失败: {str(e)}")
    finally:
        await erp_sync_service.sync_manager.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
