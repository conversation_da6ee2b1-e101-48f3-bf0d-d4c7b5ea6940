from collections import defaultdict
from datetime import datetime, timedelta
from io import BytesIO
import pandas as pd
import openpyxl
import settings
# 移除循环导入，改为在函数内部导入
# from app_finance.modules import calculate_deductions_and_salary
from app_human_resources.crud import get_salary_check_records
from app_human_resources.private_dict import checking_in_fields, check_report_default_data
from models.models import ErpAccountDepartment, ErpDepartment
from settings import CF
from utils.db.data_handler import query_child_obj
from utils.enum.enum_account import EmployeeType, EmployeeStatus


def jud_employee_type(v):
    if v == '全职':
        return EmployeeType.FullTimeJob.value
    elif v == '兼职':
        return EmployeeType.PartTimeJob.value
    else:
        return EmployeeType.NOT_CHOICE.value


def jud_employee_status(v):
    if v == '在职':
        return EmployeeStatus.EMPLOYED.value
    elif v == '离职':
        return EmployeeStatus.RESIGNED.value
    elif v == '交接中':
        return EmployeeStatus.IN_HANOVER.value
    elif v == '试用':
        return EmployeeStatus.PROBATION.value
    else:
        return EmployeeStatus.INACTIVE.value


def template_replace(employee_data):
    template_path = settings.ATTENDANCE_PATH
    # save_path = 'app_common/template'
    wb = openpyxl.load_workbook(template_path)
    ws = wb.worksheets[0]
    row_start = 2  # 数据开始行
    for employee in employee_data:
        ws[f"A{row_start}"].value = employee['id']
        ws[f"B{row_start}"].value = employee['username']
        ws[f"C{row_start}"].value = employee['employee_number']
        ws[f"D{row_start}"].value = employee['employee_name']
        row_start += 2
    filename = f"考勤报告_{str(datetime.now().date()).replace('-', '')}.xlsx"
    virtual_workbook = BytesIO()
    wb.save(virtual_workbook)
    return filename, virtual_workbook.getvalue()


def number_to_date(days_since_epoch):
    # Excel的日期从1900年1月1日开始，减去2天（Excel认为1900年是闰年，但实际不是）
    start_date = datetime(1899, 12, 30)
    delta = timedelta(days=days_since_epoch)
    return (start_date + delta).strftime('%Y-%m-%d')


def custom_ffill(df, columns):
    """
    只填充中间合并部分单元格
    :param df:
    :param columns:
    :return:
    """
    for col in columns:
        first_valid_index = df[col].first_valid_index()
        last_valid_index = df[col].last_valid_index()
        df.loc[first_valid_index:last_valid_index + 1, col] = (
            df.loc[first_valid_index:last_valid_index + 1, col].ffill())


async def handle_upload_dataframe(df):
    # 处理NaT值，将其转换为None或其他合适的值
    for col in df.select_dtypes(include=['datetime']):
        df[col] = df[col].apply(lambda x: x if pd.notnull(x) else "")
    columns_to_ffill = ['账号ID', '用户名', '员工编号', '姓名']
    custom_ffill(df, columns_to_ffill)
    df = df.where(pd.notnull(df), "")
    transposed_df = df.melt(id_vars=['账号ID', '用户名', '员工编号', '姓名', '时段'],
                            var_name='check_date',
                            value_name='check_value')
    transposed_df.rename(columns={
        '账号ID': 'account_id',
        '姓名': 'employee_name',
        '时段': 'day_period',
        '用户名': 'username',
        '员工编号': 'employee_number',
    }, inplace=True)
    transposed_df['check_date'] = transposed_df['check_date'].map(number_to_date)
    data = transposed_df.to_dict(orient='records')
    data = [i for i in data if i['account_id']]
    return data


async def get_attendance_report(db, ym, account_id=None):
    # 在函数内部导入，避免循环导入
    from app_finance.modules import calculate_deductions_and_salary
    
    data = await get_salary_check_records(db, ym, account_id)
    data = [dict(i) for i in data]
    checking_data = defaultdict(lambda: check_report_default_data.copy())
    for row in data:
        account_id = row['account_id']
        v = row['check_value']
        if not checking_data[account_id]['account_id']:
            checking_data[account_id]['account_id'] = row['account_id']
            checking_data[account_id]['employee_name'] = row['employee_name']
            checking_data[account_id]['employee_number'] = row['employee_number']
            checking_data[account_id]['salary_base'] = row['salary_base']
            checking_data[account_id]['salary_performance'] = row['salary_performance']
            checking_data[account_id]['enterprise_name'] = row['enterprise_name']
            checking_data[account_id]['ym'] = row['ym']
        if v == '正常':
            continue
        checking_data[account_id][checking_in_fields[v]] += 1
    checking_data_list = [v for k, v in checking_data.items()]
    checking_data_list.sort(key=lambda x: x['account_id'])
    # 处理其他情况
    statistic_data = []
    for checking_row in checking_data_list:
        new_row = calculate_deductions_and_salary(checking_row)
        # 半天合并整天的
        if new_row['absent_without_notice']:
            new_row['absent_without_notice'] = new_row['absent_without_notice'] / 2
        if new_row['sick_leave']:
            new_row['sick_leave'] = new_row['sick_leave'] / 2
        if new_row['overtime']:
            new_row['overtime'] = new_row['overtime'] / 2
        if new_row['personal_leave']:
            new_row['personal_leave'] = new_row['personal_leave'] / 2
        if new_row['other_paid_leave']:
            new_row['other_paid_leave'] = new_row['other_paid_leave'] / 2
        statistic_data.append(new_row)
    return statistic_data


async def all_children_node(db, model, tag_id, nodes=None):
    """
    获取所有子节点
    :param db:
    :param model:
    :param tag_id:
    :param nodes:
    :return:
    """
    if nodes is None:
        nodes = []
    children_objs = await query_child_obj(db, model, tag_id)
    for children_obj in children_objs:
        nodes.append({
            "dept_name": children_obj.dept_name,
            "id": children_obj.id,
        })
        await all_children_node(db, model, children_obj.id, nodes=nodes)  # 修正：去除return，确保递归完全执行
    return nodes


async def get_account_ids_by_dept_id(db, dept_id):
    # erp_account = CF.get_crud(ErpAccount)
    ERP_DEPARTMENT = CF.get_crud(ErpDepartment)
    ERP_ACCOUNT_DEPARTMENT = CF.get_crud(ErpAccountDepartment)

    depts = await ERP_DEPARTMENT.get_by_id(db, dept_id)
    all_dept_ids = [depts.id]  # 查询的部门和所有子部门
    child_depts = await all_children_node(db, ErpDepartment, dept_id)
    for i in child_depts:
        all_dept_ids.append(i.get('id'))

    account_dept_objs = await ERP_ACCOUNT_DEPARTMENT.get_many(db, raw=[
        ErpAccountDepartment.dept_id.in_(all_dept_ids)
    ])
    account_ids = [i.account_id for i in account_dept_objs]
    return depts, account_ids
