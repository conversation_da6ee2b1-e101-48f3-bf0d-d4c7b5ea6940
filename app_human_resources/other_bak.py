# async def data2mysql(db, data):
#     for employee_detail in data:
#         exist = await erp_account.get_one(db, username=employee_detail["联系电话"])
#         if exist:
#             # logger.info(f'已略过：{exist.username}')
#             continue
#         item = {
#             "employee_number": employee_detail["工号"],
#             "employee_name": employee_detail["姓名"],
#             "username": employee_detail["联系电话"],
#             "employee_type": jud_employee_type(employee_detail["类型"]),
#             "employee_status": jud_employee_status(employee_detail["状态"]),
#             "employee_hire_date": employee_detail["入职时间"] if employee_detail["入职时间"] else None,
#             "employee_leave_date": employee_detail["离职时间"] if employee_detail["离职时间"] else None,
#
#             "password": AccountHandler.get_password_hash(INIT_PASSWORD),
#             "sync_status": SyncStatus.WAITING.value,
#             "create_by": 20000,
#             "update_by": 20000,
#         }
#         print('正在上传：', item)
#         await erp_account.create(db, commit=True, **item)
#     return True
#
#
# @router.post("/upload_employee")
# async def upload_employee_list(
#         file: UploadFile,
#         user: UserDict = Depends(role_required(allow_role)),
#         db: AsyncSession = Depends(get_default_db)
# ):
#     """
#     # 上传员工表
#     :param file:
#     :return:
#     """
#     if not file.filename.endswith(('.xlsx', '.xls')):
#         return await ApiFailedResponse("Invalid file type. Only Excel files are allowed.")
#     contents = await file.read()
#     df = pd.read_excel(BytesIO(contents))
#     # 处理NaT值，将其转换为None或其他合适的值
#     for col in df.select_dtypes(include=['datetime']):
#         df[col] = df[col].apply(lambda x: x if pd.notnull(x) else "")
#     data = df.where(pd.notnull(df), "").to_dict(orient='records')
#     resp = await data2mysql(db, data)
#     if not resp:
#         return await ApiFailedResponse('同步出错')
#     return await ApiSuccessResponse(True)
