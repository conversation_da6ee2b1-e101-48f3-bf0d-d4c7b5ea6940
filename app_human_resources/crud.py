from sqlalchemy import select, and_, func, case, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased
from models.m_hr import ErpHrAccount, ErpHrPlan
from models.m_teacher import ErpAccountTeacher
from models.models import ErpAccount, ErpSalaryBase, ErpEnterprise, ErpJobLevel, ErpCheckingRecords, ErpChecking


async def get_salary_base(db: AsyncSession, keyword: str = None, page: int = None, page_size: int = None, count=False):
    selects = [
        ErpAccount.id,
        ErpAccount.employee_number,
        ErpAccount.employee_name,
        ErpAccount.employee_status,
        ErpAccount.employee_type,
        ErpSalaryBase.id.label('salary_base_id'),
        ErpSalaryBase.enterprise_id,
        ErpSalaryBase.salary_base,
        ErpSalaryBase.salary_performance,
        ErpSalaryBase.bank_city,
        ErpSalaryBase.bank_sub_name,
        ErpSalaryBase.bank_card_number,

        ErpEnterprise.enterprise_name,
        ErpJobLevel.level_name
    ] if not count else [func.count(1)]
    conditions = [
        ErpAccount.disable == 0,
    ]
    if keyword:
        conditions.append(
            or_(
                ErpAccount.employee_name.ilike(f"%{keyword}%"),
                ErpAccount.employee_number.ilike(f"%{keyword}%"),
                ErpAccount.username.ilike(f"%{keyword}%"),
            )
        )
    # Define the case expression for sorting
    case_expression = case(
        [(ErpAccount.employee_status == 1, 0),
         (ErpAccount.employee_status == 3, 2)],
        else_=1
    )
    stmt = (
        select(*selects)
        .select_from(ErpAccount)
        .outerjoin(ErpSalaryBase, ErpAccount.id == ErpSalaryBase.account_id)
        .outerjoin(ErpEnterprise, ErpEnterprise.id == ErpSalaryBase.enterprise_id)
        .outerjoin(ErpJobLevel, ErpAccount.level_id == ErpJobLevel.id)
        .where(and_(*conditions))
        .order_by(case_expression, ErpAccount.id)
    )
    if not count and page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    if count:
        return result.scalar()
    return result.fetchall()


async def get_account_with_page(db: AsyncSession, page: int = None, page_size: int = None, count=False, keywords=None):
    selects = [
        ErpAccount
    ] if not count else [func.count(1)]
    conditions = [
        ErpAccount.disable == 0
    ]
    if keywords:
        conditions.append(
            or_(
                ErpAccount.employee_name.ilike(f"%{keywords}%"),
                ErpAccount.employee_number.ilike(f"%{keywords}%"),
                ErpAccount.username.ilike(f"%{keywords}%"),
            )
        )
    # Define the case expression for sorting
    case_expression = case(
        [(ErpAccount.employee_status == 1, 0),
         (ErpAccount.employee_status == 3, 2)],
        else_=1
    )
    stmt = (
        select(*selects)
        .select_from(ErpAccount)
        # .outerjoin(ErpSalaryBase, ErpAccount.id == ErpSalaryBase.account_id)
        # .outerjoin(ErpEnterprise, ErpEnterprise.id == ErpSalaryBase.enterprise_id)
        # .outerjoin(ErpJobLevel, ErpAccount.level_id == ErpJobLevel.id)
        .where(and_(*conditions))
        .order_by(case_expression, ErpAccount.id)
    )
    if not count and page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    if count:
        return result.scalar()
    # return result.fetchall()
    return result.scalars().all()


async def get_salary_check_records(db: AsyncSession, ym: int, account_id: int = None):
    selects = [
        ErpCheckingRecords.account_id,
        ErpCheckingRecords.employee_number,
        ErpCheckingRecords.employee_name,
        ErpCheckingRecords.check_date,
        ErpCheckingRecords.day_period,
        ErpCheckingRecords.check_value,
        ErpCheckingRecords.ym,
        ErpSalaryBase.salary_base,
        ErpSalaryBase.salary_performance,
        ErpEnterprise.enterprise_name,
        ErpEnterprise.enterprise_short_name,
    ]
    conditions = [
        ErpCheckingRecords.ym == ym,
        ErpCheckingRecords.disable == 0,
    ]
    if account_id and account_id > 0:
        conditions.append(ErpCheckingRecords.account_id == account_id)
    stmt = (
        select(*selects)
        .select_from(ErpCheckingRecords)
        .outerjoin(ErpSalaryBase, ErpSalaryBase.account_id == ErpCheckingRecords.account_id)
        .outerjoin(ErpEnterprise, ErpSalaryBase.enterprise_id == ErpEnterprise.id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()


async def get_records_history(db, ):
    createAccount = aliased(ErpAccount)
    updateAccount = aliased(ErpAccount)
    selects = [
        ErpChecking.title,
        ErpChecking.ym,
        ErpChecking.update_time,
        ErpChecking.create_time,
        createAccount.employee_name.label('CreateBy'),
        updateAccount.employee_name.label('UpdateBy'),
    ]
    conditions = []
    stmt = (
        select(*selects)
        .select_from()
        .outerjoin(createAccount, createAccount.id == ErpChecking.create_by)
        .outerjoin(updateAccount, updateAccount.id == ErpChecking.update_by)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()


async def account_attendance_detail(db, account_id):
    selects = [
        ErpAccount.id,
        ErpAccount.employee_name,
        ErpAccount.employee_status,
        ErpAccount.employee_type,
        ErpAccount.username,
        ErpAccount.qy_wechat_position,
        ErpAccount.qy_wechat_department,
        ErpAccount.is_teacher,
        ErpAccountTeacher.teacher_certification,
        ErpJobLevel.level_name,
    ]
    conditions = [
        ErpAccount.id == account_id
    ]
    stmt = (
        select(*selects)
        .select_from(ErpAccount)
        .outerjoin(ErpAccountTeacher, ErpAccountTeacher.account_id == ErpAccount.id)
        .outerjoin(ErpJobLevel, ErpJobLevel.id == ErpAccount.level_id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()


async def get_hr_records(db, page=None, page_size=None, keyword=None, count=False):
    IntroducerAccount = aliased(ErpAccount)
    
    # 如果是查询总数，只查询总数
    if count:
        selects = [func.count(1)]
    else:
        selects = [
            ErpHrPlan.title,
            ErpHrAccount.create_time.label('entry_time'),
            ErpAccount.employee_name,
            IntroducerAccount.employee_name.label('introducer'),
            ErpAccount.employee_status,
        ]
    
    conditions = [
        ErpHrAccount.disable == 0
    ]
    if keyword:
        conditions.append(
            or_(
                ErpHrPlan.title.ilike(f"%{keyword}%"),
                ErpAccount.employee_name.ilike(f"%{keyword}%"),
                IntroducerAccount.employee_name.ilike(f"%{keyword}%")
            )
        )
    
    stmt = (
        select(*selects)
        .select_from(ErpHrAccount)
        .outerjoin(ErpAccount, ErpHrAccount.account_id == ErpAccount.id)
        .outerjoin(ErpHrPlan, ErpHrPlan.id == ErpHrAccount.hr_plan_id)
        .outerjoin(IntroducerAccount, ErpHrAccount.introducer == IntroducerAccount.id)
        .where(and_(*conditions))
    )
    
    if not count and page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    
    result = await db.execute(stmt)
    
    if count:
        return result.scalar()
    
    return result.fetchall()


async def get_talent_pool(db, page=None, page_size=None, keyword=None, count=False):
    from models.m_hr import ErpHrTalentPool
    CreateAccount = aliased(ErpAccount)
    UpdateAccount = aliased(ErpAccount)
    selects = [
        ErpHrTalentPool,
        ErpHrPlan,
        CreateAccount.employee_name.label('create_name'),
        UpdateAccount.employee_name.label('update_name')
    ] if not count else [func.count(1)]
    
    conditions = [
        ErpHrTalentPool.disable == 0
    ]
    
    if keyword:
        conditions.append(
            or_(
                ErpHrTalentPool.name.ilike(f"%{keyword}%"),
                ErpHrPlan.title.ilike(f"%{keyword}%")
            )
        )
    
    stmt = (
        select(*selects)
        .select_from(ErpHrTalentPool)
        .outerjoin(ErpHrPlan, ErpHrPlan.id == ErpHrTalentPool.hr_plan_id)
        .outerjoin(CreateAccount, CreateAccount.id == ErpHrTalentPool.create_by)
        .outerjoin(UpdateAccount, UpdateAccount.id == ErpHrTalentPool.update_by)
        .where(and_(*conditions))
        .order_by(ErpHrTalentPool.create_time.desc())
    )
    
    if not count and page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    
    if count:
        return result.scalar()
    
    return result.fetchall()
