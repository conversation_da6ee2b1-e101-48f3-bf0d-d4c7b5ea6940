from datetime import datetime, date
from typing import Union, List, Optional
from pydantic import BaseModel


class AccountEntity(BaseModel):
    username: str
    employee_name: Optional[str]
    employee_idcard: Optional[str]
    employee_gender: Optional[int]
    employee_education: Optional[int]
    employee_hire_date: Optional[date]
    employee_birth: Optional[date]
    employee_city: Optional[str]
    employee_address: Optional[str]
    employee_emergency: Optional[str]
    employee_emergency: Optional[str]
    direct_leader: List[str]
    department: List[int]
    position: Optional[str]
    employee_type: Optional[str]
    is_teacher: Optional[bool]


class HrPlanBase(BaseModel):
    title: str
    create_by: Optional[int]
    desired_num: Optional[int]
    employee_type: Optional[int]
    push_date: Optional[date]
    desc: Optional[str]
    end_date: Optional[date]


class HrAccountCreate(BaseModel):
    account_ids: List[int]
    hr_plan_id: int
    introducer: int
    source: str
    comments: Optional[str]


class PromotionPersonBase(BaseModel):
    level_id: int
    account_id: int


class BatchPromotionPersonBase(BaseModel):
    level_id: int
    account_ids: List[int]


class HrTalentPoolBase(BaseModel):
    name: str
    interview_time: Optional[datetime]
    hr_plan_id: Optional[int]
    is_accept: Optional[int]
    comments: Optional[str]
    attachment: Optional[List]
    update_by: Optional[int]
    create_by: Optional[int]