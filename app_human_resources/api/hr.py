from datetime import datetime

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

import settings
from app_human_resources.crud import get_hr_records, get_talent_pool
from app_human_resources.serializer import Hr<PERSON>lanBase, HrAccountCreate, HrTalentPoolBase
from models.m_hr import ErpHrPlan, ErpHrAccount, ErpHrTalentPool
from models.models import ErpAccount
from utils.db.account_handler import UserDict, role_required
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db
from utils.db.default_crud import generate_crud_routes
from utils.response.response_handler import ApiSuccessResponse

router = APIRouter(prefix="/hr", tags=["招聘"])

paper_course_router = generate_crud_routes(
    db_model=ErpHrPlan,
    schema=HrPlanBase,
    AuthModel=UserDict,
    default_db=get_default_db,
    prefix="hr",
    title="招聘计划",
    action=['post', 'put', 'delete']
)

router.include_router(paper_course_router)

erp_hr_plan = CRUD(ErpHrPlan)
erp_hr_account = CRUD(ErpHrAccount)
erp_account = CRUD(ErpAccount)


@router.get(f"/hr")
async def query_all_plan(
        page: int = None,
        page_size: int = None,
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询招聘计划
    """
    accounts = await erp_account.get_many(db, )
    account_dict = {i.id: i.employee_name for i in accounts}
    data = await erp_hr_plan.get_many(db, raw=[
        ErpHrPlan.push_date <= datetime.now(settings.TIME_ZONE).date(),
        ErpHrPlan.desired_num > ErpHrPlan.entry_num
    ])
    for i in data:
        i.create_name = account_dict.get(i.create_by)
    return await ApiSuccessResponse(data)


@router.get("/hr/{pk_id}")
async def query_all_plan(
        pk_id: int,
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询单个招聘计划
    """
    obj = await erp_hr_plan.get_by_id(db, pk_id)
    create_obj = await erp_account.get_by_id(db, obj.create_by)
    obj.create_name = create_obj.employee_name
    return await ApiSuccessResponse(obj)


@router.post(f"/hr_add_employee")
async def add_employee(
        hr_account: HrAccountCreate,
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 到岗
    """
    num = len(hr_account.account_ids)
    hr_plan_obj = await erp_hr_plan.get_by_id(db, hr_account.hr_plan_id)
    hr_plan_obj.entry_num += num
    for account_id in hr_account.account_ids:
        await erp_hr_account.create(db, commit=False, **{
            "account_id": account_id,
            "hr_plan_id": hr_account.hr_plan_id,
            "introducer": hr_account.introducer,
            "source": hr_account.source,
        })
    await db.commit()
    return await ApiSuccessResponse(True)


@router.get(f"/hr_records")
async def query_hr_records(
        page: int = None,
        page_size: int = None,
        keyword: str = None,
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询招聘记录
    """
    data = await get_hr_records(db, page, page_size, keyword)
    count = await get_hr_records(db, keyword=keyword, count=True)
    return await ApiSuccessResponse({
        "count": count,
        "data": data,
    })


# 人才库的增删改查 ErpHrTalentPool
talent_pool_router = generate_crud_routes(
    db_model=ErpHrTalentPool,
    schema=HrTalentPoolBase,
    AuthModel=UserDict,
    default_db=get_default_db,
    prefix="talent",
    title="人才库",
    action=['post', 'put', 'delete']
)

router.include_router(talent_pool_router)

ERP_HR_TALENT_POOL = CRUD(ErpHrTalentPool)


@router.get("/talent")
async def query_talent_pool(
        page: int = None,
        page_size: int = None,
        keyword: str = None,
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询人才库
    """
    data = await get_talent_pool(db, page, page_size, keyword)
    count_data = await get_talent_pool(db, keyword=keyword, count=True)
    return await ApiSuccessResponse({
        "count": count_data,
        "data": data,
    })


@router.get("/talent/{pk_id}")
async def query_talent_detail(
        pk_id: int,
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询单个人才信息
    """
    talent = await ERP_HR_TALENT_POOL.get_by_id(db, pk_id)
    if talent:
        if talent.create_by:
            create_account = await erp_account.get_by_id(db, talent.create_by)
            if create_account:
                talent.create_name = create_account.employee_name
        
        if talent.update_by:
            update_account = await erp_account.get_by_id(db, talent.update_by)
            if update_account:
                talent.update_name = update_account.employee_name
                
        if talent.hr_plan_id:
            hr_plan = await erp_hr_plan.get_by_id(db, talent.hr_plan_id)
            if hr_plan:
                talent.hr_plan_title = hr_plan.title
    
    return await ApiSuccessResponse(talent)