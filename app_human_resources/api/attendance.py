import asyncio
from datetime import datetime
from io import BytesIO
from urllib.parse import quote

from fastapi import APIRouter, Depends, UploadFile
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
import pandas as pd
from starlette.responses import StreamingResponse

from app_finance.modules import add_excel_style
from app_human_resources.crud import get_records_history
from app_human_resources.modules import template_replace, handle_upload_dataframe, \
    get_attendance_report, get_account_ids_by_dept_id
from app_human_resources.private_dict import merge_report_key, merge_attendance_times_key
from models.m_attendance import ErpCheckingReport
from models.models import ErpAccount, ErpCheckingRecords, ErpSalary, ErpChecking, ErpAccountDepartment, ErpDepartment
from settings import CF, FILE_SERVER, TIME_ZONE
from utils.db.account_handler import UserDict, role_required
from utils.db.db_handler import get_default_db
from utils.db.model_handler import ModelDataHelper
from utils.enum.enum_account import EmployeeType, EmployeeStatus, SalaryStatus
from utils.file.minio_handler import S3Client
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

erp_account = CF.get_crud(ErpAccount)
ERP_ACCOUNT_DEPARTMENT = CF.get_crud(ErpAccountDepartment)
ERP_DEPARTMENT = CF.get_crud(ErpDepartment)
ERP_CHECKING_RECORDS = CF.get_crud(ErpCheckingRecords)
ERP_CHECKING = CF.get_crud(ErpChecking)
ERP_SALARY = CF.get_crud(ErpSalary)
ERP_CHECKING_REPORT = CF.get_crud(ErpCheckingReport)

router = APIRouter(prefix="/attendance", tags=["考勤"])

allow_role = []


@router.post("/download_check_template")
async def download_check_template(
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db)
):
    """
    # 下载员工考勤模版
    """
    employee_list = await erp_account.get_many(db, raw=[
        ErpAccount.employee_status.in_([
            EmployeeStatus.EMPLOYED.value,
            EmployeeStatus.IN_HANOVER.value,
            EmployeeStatus.PROBATION.value
        ]),
        ErpAccount.employee_type.in_([
            EmployeeType.FullTimeJob.value,
            EmployeeType.Internship.value,
        ])
    ])
    # student = await rb_course_student.get_objs_by_field(db, {"courseId": course_id})
    employee_data = ModelDataHelper.filter_field_module(employee_list,
                                                        ["id",
                                                         "username",
                                                         "employee_number",
                                                         "employee_name"])
    employee_data.sort(key=lambda x: x['employee_number'], reverse=False)
    loop = asyncio.get_event_loop()
    filename, content = await loop.run_in_executor(None, template_replace, employee_data)
    s3 = S3Client()
    url = await s3.upload_file(FILE_SERVER['bucket'], filename, content)
    return await ApiSuccessResponse(url, '员工考勤模版')


@router.get(f"/check_records_exist")
async def check_records_exist(
        ym: int,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询指定期段考勤记录是否已存在
    :param ym:
    :param db:
    :return:
    """
    exist = await ERP_CHECKING_RECORDS.get_one(db, ym=ym)
    if exist:
        return await ApiSuccessResponse('exist')
    else:
        return await ApiSuccessResponse('notExist')


@router.post(f"/upload_check_records")
async def upload_employee_check(
        ym: int,
        file: UploadFile,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
):
    """
    上传员工考勤表
    """
    content = await file.read()
    df = pd.read_excel(BytesIO(content), engine='openpyxl', dtype={
        '账号ID': str, '用户名': str, '员工编号': str
    })
    data = await handle_upload_dataframe(df)
    exist = await ERP_CHECKING_RECORDS.get_one(db, ym=ym)
    if exist:
        return await ApiFailedResponse('该周期的考勤记录已存在')
    records = []
    for row in data:
        item = {
            "ym": ym,
            "account_id": row["account_id"],
            "employee_name": row["employee_name"],
            "employee_number": row["employee_number"],
            "check_date": row["check_date"],
            "day_period": 1 if row["day_period"] == '上午' else 2,
            "check_value": '正常' if row["check_value"] == '' else row["check_value"],
        }
        records.append(item)
    await ERP_CHECKING_RECORDS.create_many(db, records, commit=False)

    await ERP_CHECKING.create(db, commit=False, **{
        "title": f"{str(ym)[:4]}年{str(ym)[4:]}月考勤记录",
        "ym": ym,
        "create_by": user.uid,
        "update_by": user.uid,
    })

    await ERP_SALARY.create(db, commit=False, **{
        "title": f"{str(ym)[:4]}年{str(ym)[4:]}月工资条",
        "ym": ym,
        "salary_status": SalaryStatus.WAITING_UPLOAD.value,
        "comments": '程序自动生成'
    })
    await db.commit()
    return await ApiSuccessResponse(True, '上传考勤详情')


@router.get(f"/check_records_preview")
async def check_records_preview(
        ym: int,
        # account_id: int = None,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 预览考勤表
    :param ym:
    """
    conditions = {
        "ym": ym,
    }
    # if account_id and account_id > 0:
    #     conditions.update({"account_id": account_id})
    data = await ERP_CHECKING_RECORDS.get_many(db, condition=conditions)
    if not data:
        return await ApiSuccessResponse([])
    df = pd.DataFrame([ModelDataHelper.model_to_dict(i) for i in data])
    # 数据转置， 列值转字段值
    pivoted_df = df.pivot(
        index=['account_id', 'employee_number', 'employee_name', 'day_period', 'ym'],
        columns='check_date',
        values='check_value'
    ).reset_index()
    data = pivoted_df.to_dict('records')
    return await ApiSuccessResponse(data, '预览考勤详情')


class CheckRecord(BaseModel):
    account_id: int
    check_date: str
    day_period: int
    ym: int
    check_value: str


@router.post(f"/check_records_preview")
async def update_check_records_preview(
        record: CheckRecord,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 修改考勤记录
    """
    await ERP_CHECKING_RECORDS.update_many(db,
                                           {
                                               "account_id": record.account_id,
                                               "check_date": record.check_date,
                                               "day_period": record.day_period,
                                               "ym": record.ym,
                                           },
                                           {
                                               "check_value": record.check_value,
                                               "update_time": datetime.now(TIME_ZONE),
                                           })
    return await ApiSuccessResponse(True, )


@router.get(f"/check_report_preview")
async def check_report_preview(
        ym: int,
        account_id: int = None,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 预览考勤报告
    """
    statistic_data = await get_attendance_report(db, ym, account_id)
    return await ApiSuccessResponse(statistic_data, )


@router.get(f"/download_check_report_preview")
async def download_check_report_preview(
        ym: int,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
):
    # check_report_data = await check_report_preview(ym, inner_callback=True, db=db)
    check_report_data = await get_attendance_report(db, ym)
    rename_field = {
        "account_id": '账号ID',
        "employee_number": "工号",
        "employee_name": '姓名',
        "ym": '年月',
        "actual_attendance_days": "核算计薪天数",
        "late_within_10_min": "迟到小于10min次数",
        "late_10_to_30_min": "迟到10-30mins次数",
        "absent_without_notice": "旷工天数",
        "early_leave": "早退次数",
        "forgot_to_check_in": "忘打卡次数",
        "sick_leave": "病假天数",
        "personal_leave": "事假天数",
        "overtime": "加班天数",
        "other_paid_leave": "全薪假天数",
        "forgot_to_check_in_deduction": "忘打卡扣款",
        "late_deduction": "迟到扣款",
        "early_leave_deduction": "早退扣款",
        "absent_without_notice_deduction": "旷工扣款",
        "personal_leave_deduction": "事假扣款",
        "sick_leave_deduction": "病假扣款",
        "sick_leave_addition": "病假补助",
    }
    df = pd.DataFrame(check_report_data)
    df = df[rename_field.keys()]
    columns_to_color = {i: 'f5f6f9' for i in rename_field.values()}
    df.rename(columns=rename_field, inplace=True)
    output = BytesIO()
    output = add_excel_style(df, output, columns_to_color)
    output.seek(0)
    filename = f'员工考勤统计_{ym}'
    filename = quote(filename)
    headers = {
        'Content-Disposition': f'attachment; filename="{filename}"'
    }
    # 返回StreamingResponse
    return StreamingResponse(output, media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                             headers=headers)


@router.delete(f"/check_records")
async def delete_records_preview(
        ym: int,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除考勤记录
    :param ym:
    """
    await ERP_CHECKING_RECORDS.delete_many(db, {"ym": ym})
    await ERP_CHECKING.delete_many(db, {"ym": ym})
    return await ApiSuccessResponse(True)


@router.get(f"/check_records")
async def query_records_preview(
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询已提交过的考勤记录
    """
    data = await get_records_history(db)
    return await ApiSuccessResponse(data)


async def merge_dept_attendance_report(data):
    df = pd.DataFrame(data)
    df = df.drop(columns='ym')

    # 累加字段
    fields_to_sum = [i for i in merge_report_key.keys()]
    # 计次字段
    fields_to_count = [i for i in merge_attendance_times_key.keys()]

    # 累加操作
    sum_df = df[fields_to_sum].sum()
    sum_df.index = ['sum_' + col for col in sum_df.index]

    # 计次操作，只计不为0的值
    count_df = df[fields_to_count].apply(lambda x: (x != 0).sum())
    count_df.index = ['count_' + col for col in count_df.index]

    # 合并累加和计次结果
    merged_df = pd.concat([sum_df, count_df], axis=0)

    final_data = merged_df.to_dict()
    return final_data


async def merge_attendance_report(data, dept=False):
    df = pd.DataFrame(data)
    df = df.drop(columns='ym')
    fields_to_sum = [i for i in merge_report_key.keys()]
    if dept:
        fields_to_sum = [i for i in merge_attendance_times_key.keys()]
    merged_df = df.groupby("account_id")[fields_to_sum].sum().reset_index()
    first_df = df.groupby("account_id").first().reset_index()
    final_df = first_df.drop(columns=fields_to_sum).merge(merged_df, on="account_id")
    final_data = final_df.to_dict('records')
    return final_data


@router.get(f"/account_attendance_history")
async def query_account_attendance_history(
        account_id: int,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询员工考勤记录
    """
    data = await ERP_CHECKING_REPORT.get_many(db, {"account_id": account_id})
    yms = [i.ym for i in data]
    return await ApiSuccessResponse(yms)


@router.get(f"/account_attendance_report")
async def query_account_attendance_report(
        account_id: int,
        ym_start: str = None,
        ym_end: str = None,
        ym: str = None,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询账号的考勤报告
    - ym 和ym_start等为互斥关系
    - ym_start <= xxx < ym_end
    """
    if ym:
        data = await ERP_CHECKING_REPORT.get_many(db, {
            "account_id": account_id,
            "ym": ym
        })
    elif ym_start and ym_end:
        data = await ERP_CHECKING_REPORT.get_many(db, raw=[
            ErpCheckingReport.account_id == account_id,
            ErpCheckingReport.ym >= ym_start,
            ErpCheckingReport.ym <= ym_end,
        ])
    else:
        return await ApiFailedResponse('传参错误1')
    data = [ModelDataHelper.model_to_dict(i) for i in data]
    if not data:
        return await ApiSuccessResponse([])
    final_data = await merge_attendance_report(data)
    # 返回成功响应
    return await ApiSuccessResponse(final_data)


@router.get(f"/dept_attendance_history")
async def query_dept_attendance_history(
        dept_id: int,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询部门考勤记录
    """
    depts, account_ids = await get_account_ids_by_dept_id(db, dept_id)
    data = await ERP_CHECKING_REPORT.get_many(db, raw=[
        ErpCheckingReport.account_id.in_(account_ids)
    ])
    yms = [i.ym for i in data]
    yms = list(set(yms))
    return await ApiSuccessResponse(yms)


@router.get(f"/dept_attendance_report")
async def query_dept_attendance_report(
        dept_id: int,
        ym_start: str = None,
        ym_end: str = None,
        ym: str = None,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询部门考勤报告
    """
    depts, account_ids = await get_account_ids_by_dept_id(db, dept_id)
    if ym:
        data = await ERP_CHECKING_REPORT.get_many(db, raw=[
            ErpCheckingReport.account_id.in_(account_ids),
            ErpCheckingReport.ym == ym
        ])
    elif ym_start and ym_end:
        data = await ERP_CHECKING_REPORT.get_many(db, raw=[
            ErpCheckingReport.account_id.in_(account_ids),
            ErpCheckingReport.ym >= ym_start,
            ErpCheckingReport.ym <= ym_end,
        ])
    else:
        return await ApiFailedResponse('传参错误1')
    data = [ModelDataHelper.model_to_dict(i) for i in data]
    if not data:
        return await ApiSuccessResponse([])
    if ym:
        final_data = await merge_dept_attendance_report(data)
    else:
        final_data = await merge_dept_attendance_report(data)
        data = await merge_attendance_report(data)
    detail_data = []
    for i in data:
        detail_data.append(
            {'id': i.get('id'),
             'account_id': i.get('account_id'),
             'ym': i.get('ym'),
             'employee_name': i.get('employee_name'),
             'employee_number': i.get('employee_number'),
             'late_within_10_min': i.get('late_within_10_min'),
             'late_10_to_30_min': i.get('late_10_to_30_min'),
             'absent_without_notice': i.get('absent_without_notice'),
             'early_leave': i.get('early_leave'),
             'forgot_to_check_in': i.get('forgot_to_check_in'),
             'sick_leave': i.get('sick_leave'),
             'personal_leave': i.get('personal_leave'),
             'overtime': i.get('overtime'),
             'other_paid_leave': i.get('other_paid_leave'),
             'actual_attendance_days': i.get('actual_attendance_days'),
             })

    return await ApiSuccessResponse({
        "dept_id": dept_id,
        "dept_name": depts.dept_name,
        "statistic": final_data,
        "detail": detail_data
    })
