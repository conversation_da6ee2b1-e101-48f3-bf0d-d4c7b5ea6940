from fastapi import APIRouter, Depends, Query
from app_human_resources.serializer import PromotionPersonBase, BatchPromotionPersonBase
from app_teach.serializer import JobLevelBase
from models.models import ErpJobLevel, ErpAccountLevel, ErpAccount
from settings import CF
from utils.db.account_handler import UserDict, get_current_active_user
from utils.db.db_handler import get_default_db
from utils.db.default_crud import generate_crud_routes
from sqlalchemy.ext.asyncio import AsyncSession
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse
from typing import List, Optional
from datetime import datetime

erp_job_level = CF.get_crud(ErpJobLevel)
erp_account_level = CF.get_crud(ErpAccountLevel)
erp_account = CF.get_crud(ErpAccount)

router = APIRouter(prefix="/level", tags=["职级"])

# 删除通用路由生成代码
# paper_course_router = generate_crud_routes(
#     db_model=ErpJobLevel,
#     schema=JobLevelBase,
#     AuthModel=UserDict,
#     default_db=get_default_db,
#     prefix="job_level",
#     title="职级"
# )


# 创建职级
@router.post("/job_level", summary="创建职级")
async def create_job_level(
    data: JobLevelBase,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 创建新职级
    """
    # 检查是否已存在相同名称的职级
    exist = await erp_job_level.get_one(db, condition={"level_name": data.level_name, "disable": 0})
    if exist:
        return await ApiFailedResponse("已存在同名职级")
    
    # 创建新职级
    new_level = await erp_job_level.create(db, **{
        "level_name": data.level_name,
        "level_order": data.level_order,
        "level_desc": data.level_desc,
        "create_by": user.uid,
        "update_by": user.uid,
        "create_time": datetime.now(),
        "update_time": datetime.now(),
        "disable": 0,
        "level_item": data.level_item
    })
    
    return await ApiSuccessResponse(new_level)

# 更新职级
@router.put("/job_level/{level_id}", summary="更新职级")
async def update_job_level(
    level_id: int,
    data: JobLevelBase,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 更新职级信息
    """
    # 检查职级是否存在
    exist = await erp_job_level.get_by_id(db, level_id)
    if not exist:
        return await ApiFailedResponse("职级不存在")
    
    # 检查是否存在同名但不同ID的职级
    same_name = await erp_job_level.get_many(db, raw=[
        ErpJobLevel.level_name == data.level_name,
        ErpJobLevel.id != level_id,
        ErpJobLevel.disable == 0
    ])
    if same_name:
        return await ApiFailedResponse("已存在同名职级")
    
    # 更新职级信息
    exist.level_name = data.level_name
    exist.level_desc = data.level_desc
    exist.update_by = user.uid
    exist.update_time = datetime.now()
    exist.level_item = data.level_item
    
    await db.commit()
    
    return await ApiSuccessResponse(exist)

# 删除职级
@router.delete("/job_level/{level_id}", summary="删除职级")
async def delete_job_level(
    level_id: int,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除职级
    """
    # 检查职级是否存在
    exist = await erp_job_level.get_by_id(db, level_id)
    if not exist:
        return await ApiFailedResponse("职级不存在")
    
    # 检查是否有关联的晋升记录
    related_records = await erp_account_level.get_many(db, condition={
        "level_id": level_id,
        "disable": 0
    })
    
    if related_records:
        return await ApiFailedResponse("该职级下有关联的晋升人员，无法删除")
    
    # 逻辑删除职级
    exist.disable = 1
    exist.update_by = user.uid
    exist.update_time = datetime.now()
    
    await db.commit()
    
    return await ApiSuccessResponse(True)

# 查询职级列表，包含满足晋升条件的人员信息
@router.get("/job_level", summary="查询职级列表")
async def get_job_levels(
    level_name: Optional[str] = None,
    page: int = Query(1, description="页码"),
    page_size: int = Query(20, description="每页数量"),
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询职级列表，包含满足晋升条件的人员信息
    """
    condition = {"disable": 0}
    if level_name:
        condition["level_name"] = level_name
    
    # 获取带分页的职级列表
    levels = await erp_job_level.get_many_with_pagination(
        db, page, page_size, condition=condition
    )
    
    # 获取总数
    count_data = await erp_job_level.get_many(db, condition=condition)
    total_count = len(count_data)
    
    # 如果有职级数据，获取每个职级下满足晋升条件的人员信息
    if levels:
        level_ids = [level.id for level in levels]
        
        # 获取所有相关的晋升记录
        promotion_records = await erp_account_level.get_many(db, raw=[
            ErpAccountLevel.level_id.in_(level_ids),
            ErpAccountLevel.disable == 0
        ])
        
        # 按职级ID分组
        promotion_by_level = {}
        for record in promotion_records:
            if record.level_id not in promotion_by_level:
                promotion_by_level[record.level_id] = []
            promotion_by_level[record.level_id].append(record)
        
        # 获取所有相关账号信息
        account_ids = [record.account_id for record in promotion_records]
        if account_ids:
            accounts = await erp_account.get_many(db, raw=[
                ErpAccount.id.in_(account_ids)
            ])
            account_dict = {account.id: account for account in accounts}
            
            # 为每个晋升记录添加账号信息
            for records in promotion_by_level.values():
                for record in records:
                    account = account_dict.get(record.account_id)
                    if account:
                        record.employee_name = account.employee_name
                        record.avatar = account.avatar
        
        # 为每个职级添加满足晋升条件的人员信息
        for level in levels:
            level.promotion_persons = promotion_by_level.get(level.id, [])
    
    return await ApiSuccessResponse({
        "count": total_count,
        "data": levels
    })

# 获取单个职级详情，包含满足晋升条件的人员信息
@router.get("/job_level/{level_id}", summary="获取职级详情")
async def get_job_level(
    level_id: int,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 获取单个职级详情，包含满足晋升条件的人员信息
    """
    # 检查职级是否存在
    level = await erp_job_level.get_by_id(db, level_id)
    if not level:
        return await ApiFailedResponse("职级不存在")
    
    # 获取该职级对应的晋升人员
    promotion_records = await erp_account_level.get_many(db, condition={
        "level_id": level_id,
        "disable": 0
    })
    
    # 获取所有相关账号信息
    account_ids = [record.account_id for record in promotion_records]
    promotion_persons = []
    
    if account_ids:
        accounts = await erp_account.get_many(db, raw=[
            ErpAccount.id.in_(account_ids)
        ])
        account_dict = {account.id: account for account in accounts}
        
        # 为每个晋升记录添加账号信息
        for record in promotion_records:
            account = account_dict.get(record.account_id)
            if account:
                record.employee_name = account.employee_name
                record.avatar = account.avatar
                promotion_persons.append(record)
    
    # 添加满足晋升条件的人员信息到职级详情
    level.promotion_persons = promotion_persons
    
    return await ApiSuccessResponse(level)

# 达到职级对应的晋升人员的增删改查

# 添加职级对应的晋升人员
@router.post("/add_promotion_person")
async def add_promotion_person(
    promotion_person: PromotionPersonBase,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 添加职级对应的晋升人员
    """
    # 检查职级是否存在
    level = await erp_job_level.get_by_id(db, promotion_person.level_id)
    if not level:
        return await ApiFailedResponse("职级不存在")
    
    # 检查账号是否存在
    account = await erp_account.get_by_id(db, promotion_person.account_id)
    if not account:
        return await ApiFailedResponse("账号不存在")
    
    # 检查是否已经添加过该职级晋升记录
    exist = await erp_account_level.get_one(db, {
        "level_id": promotion_person.level_id,
        "account_id": promotion_person.account_id,
        "disable": 0
    })
    
    if exist:
        return await ApiFailedResponse("该人员已经添加到此职级")
    
    # 创建职级晋升记录
    new_record = await erp_account_level.create(db, **{
        "level_id": promotion_person.level_id,
        "account_id": promotion_person.account_id,
        "create_by": user.uid,
        "update_by": user.uid,
        "create_time": datetime.now(),
        "update_time": datetime.now(),
        "disable": 0
    })
    
    # 同时更新用户的职级信息
    account.level_id = promotion_person.level_id
    await db.commit()
    
    return await ApiSuccessResponse(new_record)

# 批量添加职级对应的晋升人员
@router.post("/batch_add_promotion_person")
async def batch_add_promotion_person(
    batch_promotion: BatchPromotionPersonBase,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 批量添加职级对应的晋升人员
    """
    # 检查职级是否存在
    level = await erp_job_level.get_by_id(db, batch_promotion.level_id)
    if not level:
        return await ApiFailedResponse("职级不存在")
    
    # 检查账号是否存在
    accounts = await erp_account.get_many(db, raw=[
        ErpAccount.id.in_(batch_promotion.account_ids)
        ])
    account_map = {account.id: account for account in accounts}
    
    # 验证所有账号是否存在
    if len(accounts) != len(batch_promotion.account_ids):
        missing_ids = set(batch_promotion.account_ids) - set(account_map.keys())
        return await ApiFailedResponse(f"以下账号不存在: {missing_ids}")
    
    # 获取已经存在的职级晋升记录
    existing_records = await erp_account_level.get_many(db, raw=[
        ErpAccountLevel.level_id == batch_promotion.level_id,
        ErpAccountLevel.account_id.in_(batch_promotion.account_ids),
        ErpAccountLevel.disable == 0
    ])
    existing_account_ids = {record.account_id for record in existing_records}
    
    # 过滤出需要添加的账号ID
    account_ids_to_add = [id for id in batch_promotion.account_ids if id not in existing_account_ids]
    
    # 批量创建记录
    new_records = []
    now = datetime.now()
    for account_id in account_ids_to_add:
        record_data = {
            "level_id": batch_promotion.level_id,
            "account_id": account_id,
            "create_by": user.uid,
            "update_by": user.uid,
            "create_time": now,
            "update_time": now,
            "disable": 0
        }
        new_records.append(record_data)
    
    if new_records:
        await erp_account_level.create_many(db, new_records, commit=False)
    
    # 批量更新用户的职级信息
    for account_id in account_ids_to_add:
        account = account_map.get(account_id)
        if account:
            account.level_id = batch_promotion.level_id
    
    await db.commit()
    
    return await ApiSuccessResponse({
        "total": len(batch_promotion.account_ids),
        "added": len(account_ids_to_add),
        "skipped": len(existing_account_ids)
    })

# 查询职级对应的晋升人员列表
@router.get("/promotion_person")
async def get_promotion_persons(
    level_id: Optional[int] = None,
    account_id: Optional[int] = None,
    page: int = Query(1, description="页码"),
    page_size: int = Query(20, description="每页数量"),
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询职级对应的晋升人员列表
    """
    condition = {"disable": 0}
    if level_id:
        condition["level_id"] = level_id
    if account_id:
        condition["account_id"] = account_id
    
    # 获取带分页的晋升人员列表
    promotion_persons = await erp_account_level.get_many_with_pagination(
        db, page, page_size, condition=condition
    )
    
    # 获取总数
    count_data = await erp_account_level.get_many(db, condition=condition)
    total_count = len(count_data)
    
    # 获取关联的职级和账号信息
    if promotion_persons:
        # 提取所有职级ID和账号ID
        level_ids = [item.level_id for item in promotion_persons]
        account_ids = [item.account_id for item in promotion_persons]
        
        # 获取职级和账号信息
        levels = await erp_job_level.get_many(db, raw=[
            ErpJobLevel.id.in_(level_ids)
        ])
        accounts = await erp_account.get_many(db, raw=[
            ErpAccount.id.in_(account_ids)
        ])
        
        # 创建映射字典
        level_dict = {level.id: level.level_name for level in levels}
        account_dict = {account.id: account for account in accounts}
        
        # 为每个晋升记录添加职级名称和员工名称
        for item in promotion_persons:
            item.level_name = level_dict.get(item.level_id)
            item.employee_name = account_dict.get(item.account_id).employee_name
            item.avatar = account_dict.get(item.account_id).avatar
    
    return await ApiSuccessResponse({
        "count": total_count,
        "data": promotion_persons
    })

# 更新职级对应的晋升人员
@router.put("/promotion_person/{record_id}")
async def update_promotion_person(
    record_id: int,
    promotion_person: PromotionPersonBase,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 更新职级对应的晋升人员
    """
    # 检查职级是否存在
    level = await erp_job_level.get_by_id(db, promotion_person.level_id)
    if not level:
        return await ApiFailedResponse("职级不存在")
    
    # 检查账号是否存在
    account = await erp_account.get_by_id(db, promotion_person.account_id)
    if not account:
        return await ApiFailedResponse("账号不存在")
    
    # 检查记录是否存在
    exist = await erp_account_level.get_by_id(db, record_id)
    if not exist:
        return await ApiFailedResponse("记录不存在")
    
    # 更新职级晋升记录
    exist.level_id = promotion_person.level_id
    exist.account_id = promotion_person.account_id
    exist.update_by = user.uid
    exist.update_time = datetime.now()
    
    # 同时更新用户的职级信息
    account.level_id = promotion_person.level_id
    
    await db.commit()
    
    return await ApiSuccessResponse(exist)

# 批量更新职级对应的晋升人员
@router.put("/batch_update_promotion_person/{level_id}")
async def batch_update_promotion_person(
    level_id: int,
    batch_promotion: BatchPromotionPersonBase,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 批量更新职级对应的晋升人员
    """
    # 检查职级是否存在
    level = await erp_job_level.get_by_id(db, level_id)
    if not level:
        return await ApiFailedResponse("职级不存在")
    
    # 验证请求中的level_id与路径参数一致
    if batch_promotion.level_id != level_id:
        return await ApiFailedResponse("请求中的level_id与路径参数不一致")
    
    # 检查账号是否存在
    accounts = await erp_account.get_many(db, raw=[
        ErpAccount.id.in_(batch_promotion.account_ids)
    ])
    account_map = {account.id: account for account in accounts}
    
    # 验证所有账号是否存在
    if len(accounts) != len(batch_promotion.account_ids):
        missing_ids = set(batch_promotion.account_ids) - set(account_map.keys())
        return await ApiFailedResponse(f"以下账号不存在: {missing_ids}")
    
    # 获取该职级下所有现有的晋升记录
    existing_records = await erp_account_level.get_many(db, condition={
        "level_id": level_id,
        "disable": 0
    })
    
    # 获取需要保留的记录和需要新增的记录
    existing_account_ids = {record.account_id for record in existing_records}
    account_ids_to_keep = set(batch_promotion.account_ids) & existing_account_ids
    account_ids_to_add = set(batch_promotion.account_ids) - existing_account_ids
    account_ids_to_remove = existing_account_ids - set(batch_promotion.account_ids)
    
    # 删除需要移除的记录（逻辑删除）
    now = datetime.now()
    for record in existing_records:
        if record.account_id in account_ids_to_remove:
            record.disable = 1
            record.update_by = user.uid
            record.update_time = now
            
            # 如果用户的当前职级是此职级，则清除用户职级
            account = account_map.get(record.account_id)
            if account and account.level_id == level_id:
                account.level_id = None
    
    # 批量创建新记录
    new_records = []
    for account_id in account_ids_to_add:
        record_data = {
            "level_id": level_id,
            "account_id": account_id,
            "create_by": user.uid,
            "update_by": user.uid,
            "create_time": now,
            "update_time": now,
            "disable": 0
        }
        new_records.append(record_data)
    
    if new_records:
        await erp_account_level.create_many(db, new_records, commit=False)
    
    # 批量更新用户的职级信息（对于新添加的账号）
    for account_id in account_ids_to_add:
        account = account_map.get(account_id)
        if account:
            account.level_id = level_id
    
    await db.commit()
    
    return await ApiSuccessResponse({
        "total": len(batch_promotion.account_ids),
        "kept": len(account_ids_to_keep),
        "added": len(account_ids_to_add),
        "removed": len(account_ids_to_remove)
    })

# 删除职级对应的晋升人员
@router.delete("/promotion_person/{record_id}")
async def delete_promotion_person(
    record_id: int,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除职级对应的晋升人员
    """
    # 检查记录是否存在
    exist = await erp_account_level.get_by_id(db, record_id)
    if not exist:
        return await ApiFailedResponse("记录不存在")
    
    # 获取关联的账号
    account = await erp_account.get_by_id(db, exist.account_id)
    if account and account.level_id == exist.level_id:
        # 清除用户职级信息
        account.level_id = None
    
    # 逻辑删除记录
    exist.disable = 1
    exist.update_by = user.uid
    exist.update_time = datetime.now()
    
    await db.commit()
    
    return await ApiSuccessResponse(True)