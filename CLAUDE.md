# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**进阶ERP服务 2.0.0** - A comprehensive Enterprise Resource Planning system built with FastAPI, providing educational management solutions for "进阶思维" (Advanced Thinking) company.

## Architecture & Structure

### Core Technology Stack
- **Backend**: FastAPI 0.97.0 with Python 3.10
- **Database**: MySQL 5.7+ with SQLAlchemy 1.4.49 ORM
- **Cache**: Redis 5.2.0+ (aioredis 2.0.1)
- **Async Framework**: asyncio with uvicorn 0.32.0
- **Task Queue**: Celery with <PERSON>is backend
- **Authentication**: JWT tokens with python-jose
- **File Storage**: MinIO S3-compatible storage

### Project Layout
```
├── app_*/                    # Business modules (8 main domains)
│   ├── api/                  # REST API endpoints
│   ├── crud.py               # Database operations
│   ├── modules.py            # Business logic
│   ├── serializer.py         # Pydantic schemas
│   └── views.py              # FastAPI routers
├── models/                   # SQLAlchemy models
├── modules/                  # Shared utilities
├── tasks/                    # Background async tasks
├── public_api/               # Public API endpoints
├── utils/                    # Utility modules
├── service.py                # Background service manager
└── main.py                   # FastAPI application entry
```

### Business Domains
1. **app_user** - User management & authentication
2. **app_desk** - Front desk operations & course consultants
3. **app_teach** - Teaching management (classes, exams, materials)
4. **app_competition** - Student competitions
5. **app_finance** - Financial management & reports
6. **app_human_resources** - HR, attendance, salary
7. **app_office** - Office supplies & consumables
8. **app_order** - Order processing & discounts

## Development Commands

### Environment Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Activate virtual environment
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows
```

### Running Services
```bash
# Development - single API server
python main.py
# or
uvicorn main:app --host 0.0.0.0 --port 20000 --reload

# Production - all services
python start_all.py          # API + background services
python service.py            # Background services only

# Docker deployment
docker-compose up --build
```

### Background Services
The system uses a dual-process architecture:
- **FastAPI Server**: Handles HTTP requests (main.py)
- **Background Service**: Async tasks (service.py)

Key background tasks include:
- Enterprise WeChat sync
- Financial report generation
- Attendance backup
- Class data synchronization
- Salary notifications

### Database Operations
```bash
# Model generation (from database)
cd utils/create_model
./async_db.sh                # Generate models from DB

# Database configuration
# Check settings.py: DB_CONFIGS for connection details
```

## API Documentation
- **Swagger UI**: `http://localhost:20000/e5c54f204f583e52700aba0359701713`
- **Base API Path**: `/erp/*`
- **Authentication**: JWT tokens via `/erp/app_user/auth/token`

## Key Configuration Files

### Database (settings.py)
- **Production**: `ERP2.0` on `*************:6033`
- **Test**: `uat_reborn_think` on same host
- **Redis**: `*************:16379` (password: `jjsw@2023`)

### External Services
- **WeChat**: Enterprise WeChat integration (CorpID: `ww3cfc327d9b25a572`)
- **SMS**: Alibaba Cloud SMS service
- **File Storage**: MinIO at `https://minio.jjsw.vip`
- **ClassIn**: Online classroom integration

## Common Development Tasks

### Adding New API Endpoints
1. Create endpoint in `app_[domain]/api/`
2. Add business logic in `app_[domain]/modules.py`
3. Update serializer schemas in `app_[domain]/serializer.py`
4. Register routes in `app_[domain]/views.py`
5. Import in `main.py` and add router

### Database Schema Changes
1. Update database schema in MySQL
2. Run model generation: `python utils/create_model/models_erp2.py`
3. Update affected models in `models/` directory
4. Update related CRUD operations

### Background Tasks
1. Create task in `tasks/` directory
2. Register in `service.py` TaskManager.task_configs
3. Restart background service: `python service.py`

### Environment Configuration
- **Development**: Set `DEBUG = True` in settings.py
- **Production**: Set `DEBUG = False` and configure production DB
- **Redis**: Update REDIS_CONFIG for different environments
- **Secrets**: All API keys and secrets in settings.py

## Important Notes

### Redis Lock Issue
The system uses redis-py >=4.2.0. When implementing distributed locks:
- Use `await lock.acquire()` and `await lock.release()` explicitly
- Avoid `with` context manager for Redis locks
- See README.md for detailed fix documentation

### Error Handling
- All async tasks have automatic restart with exponential backoff
- Logs are separated into `log/info/` and `log/error/` directories
- Service logs go to `log/service.log`

### Testing
- Public API tests: `public_api/tests/`
- Model validation through Pydantic schemas
- Background task testing via individual task files

### Deployment Checklist
1. Verify all external service configurations
2. Check database connectivity
3. Ensure Redis is accessible
4. Validate file storage (MinIO) credentials
5. Test WeChat/SMS integrations
6. Run background service health check