#!/bin/bash

# ERP 系统停止脚本
echo "🛑 停止 ERP 系统..."

# 从 PID 文件读取进程 ID
if [ -f "service.pid" ]; then
    SERVICE_PID=$(cat service.pid)
    echo "停止后台服务进程 (PID: $SERVICE_PID)..."
    kill $SERVICE_PID 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ 后台服务已停止"
    else
        echo "⚠️  后台服务进程可能已经停止"
    fi
    rm -f service.pid
else
    echo "⚠️  未找到后台服务 PID 文件"
fi

if [ -f "api.pid" ]; then
    API_PID=$(cat api.pid)
    echo "停止 FastAPI 应用进程 (PID: $API_PID)..."
    kill $API_PID 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ FastAPI 应用已停止"
    else
        echo "⚠️  FastAPI 应用进程可能已经停止"
    fi
    rm -f api.pid
else
    echo "⚠️  未找到 FastAPI 应用 PID 文件"
fi

# 强制杀死可能残留的进程
echo "🔍 检查残留进程..."
pkill -f "python service.py" 2>/dev/null
pkill -f "python main.py" 2>/dev/null

echo "🎉 所有服务已停止！" 