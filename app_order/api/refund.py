import asyncio
from collections import defaultdict
import copy
from datetime import datetime
import random
from decimal import Decimal, ROUND_HALF_UP
from aioredis import Redis
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app_order.crud import get_orders_by_offer_ids, get_refund_by_page
from app_order.modules import start_refund_workflow
from app_order.serializer import RefundApplyParams, RefundListParams
from models.m_finance import ErpFinanceCostType, ErpFinanceTradePayment
from models.m_order import  ErpOrder, ErpOrderOffer, ErpOrderRefund, ErpOrderRefundDetail, ErpOrderStudent
from models.m_student import ErpStudent
from models.m_workflow import  ErpReceipt, ErpReceiptFinance
from public_api.crud import get_user_dept, get_workflow_cost_type_related
from settings import CF
import settings
from utils.db.account_handler import UserDict, role_required
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db, get_redis
from utils.enum.enum_approval import  AuditState, ClassAuditStatus, CostTypeBind,  RelatedObjType
from utils.enum.enum_class import ClassStatus
from utils.enum.enum_order import  OfferState, OrderState, OrderType, RefundDetailState, RefundWay, StudentState,  TradeStatus
from utils.other.money_handler import MoneyHandler
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

router = APIRouter(prefix="/refund", tags=["退费"])

erp_order = CF.get_crud(ErpOrder)
erp_order_offer = CF.get_crud(ErpOrderOffer)
erp_finance_cost_type = CF.get_crud(ErpFinanceCostType)
erp_student = CF.get_crud(ErpStudent)
erp_order_refund = CRUD(ErpOrderRefund)
erp_order_student = CRUD(ErpOrderStudent)
erp_order_refund_detail = CRUD(ErpOrderRefundDetail)
erp_finance_trade_payment = CRUD(ErpFinanceTradePayment)
erp_receipt = CRUD(ErpReceipt)
erp_receipt_finance = CRUD(ErpReceiptFinance)


async def find_original_order_student(db: AsyncSession, order_student_id: int) -> ErpOrderStudent:
    """
    递归查找原学生订单
    如果student_state = TRANSFER_IN (转入单), 通过p_id向上递归查找直到找到TRANSFER_OUT (转出单/原学生订单)
    
    Args:
        db: 数据库会话
        order_student_id: 学生订单ID
        
    Returns:
        ErpOrderStudent: 原学生订单对象
    """
    order_student_obj = await erp_order_student.get_by_id(db, order_student_id)
    if not order_student_obj:
        return None
    
    # 如果是转入单(TRANSFER_IN)，需要递归向上查找原学生订单
    if order_student_obj.student_state in ([StudentState.TRANSFER_IN.value, StudentState.TRANSFER_OUT.value, StudentState.NORMAL.value]) and order_student_obj.p_id:
        # 递归查找父订单
        return await find_original_order_student(db, order_student_obj.p_id)
    else:
        # 如果不是转入单，或者没有父订单，返回当前订单
        return order_student_obj


# 退费申请接口
@router.post("/apply")
async def refund_apply(
        params: RefundApplyParams,
        db: AsyncSession = Depends(get_default_db),
        redis_client: Redis = Depends(get_redis),
        user: UserDict = Depends(role_required([])),
):
    """
    # 退费/结转申请
    - refund_type: 退费类型 1 退费 2 结转
    - refund_money: 退费/结转金额
    - refund_way: 退费到账方式 1 原路  3 转账
    - 转账退费请携带receipt_id
    """
    # 参数验证
    if not params.refund_detail or len(params.refund_detail) == 0:
        return await ApiFailedResponse("退费申请明细不能为空")
    
    if params.refund_type not in [1, 2]:
        return await ApiFailedResponse("退费类型错误，1-退费，2-结转")
    
    if params.refund_way == RefundWay.TRANSFER.value:
        if not params.receipt_id:
            return await ApiFailedResponse("转账退费请制单后进行，必传receipt_id")
    
    if params.apply_money <= 0:
        return await ApiFailedResponse("申请金额必须大于0")
    
    # 验证退费方式
    valid_refund_ways = [RefundWay.ORIGINAL.value, RefundWay.CASH.value, RefundWay.TRANSFER.value]
    for detail in params.refund_detail:
        if detail.refund_way not in valid_refund_ways:
            return await ApiFailedResponse(f"退费方式错误，支持的方式：{RefundWay.ORIGINAL.value}-原路，{RefundWay.CASH.value}-现金，{RefundWay.TRANSFER.value}-转账，当前值：{detail.refund_way}")
        if MoneyHandler.is_less_than(detail.apply_money, 0):
            return await ApiFailedResponse("退费明细申请金额不能小于0")
    
    # 查询用户的部门
    dept_obj = await get_user_dept(db, user.uid)
    if not dept_obj:
        return await ApiFailedResponse("用户部门信息不存在，请联系管理员")

    # 创建退费单
    # 结转类型直接设置为已通过状态，不需要审批
    audit_state = 1 if params.refund_type == 2 else 0  # 结转直接通过，退费需要审批
    refund_obj = await erp_order_refund.create(db, commit=False, **{
        "refund_reason": params.refund_reason,
        "stu_id": params.stu_id,
        "refund_remark": params.refund_remark,
        "audit_state": audit_state,  # 审核状态 0 待审核 1 审核通过 2 审核不通过
        "create_by": user.uid,
        "update_by": user.uid,
        "apply_date": params.apply_date,
        "apply_money": params.apply_money,
        "refund_type": params.refund_type,
        "attachment": params.attachment,
    })
    refund_id = refund_obj.id
    
    # 如果是结转类型，直接处理结转逻辑，不需要工作流审批
    if params.refund_type == 2:  # 结转
        # 导入电子钱包变动函数
        from app_finance.modules import stu_ewallet_change
        
        # 更新退费单状态为已完成
        refund_obj.refund_money = params.apply_money  # 设置实际退费金额
        
        # 处理退费明细和订单状态
        for index, detail in enumerate(params.refund_detail):
            # 退费锁
            refund_lock = await redis_client.get(f'refund_lock_{detail.order_id}')
            if refund_lock:
                return await ApiFailedResponse(f'退费申请中，请勿频繁操作，10s后稍后再试吧')
            await redis_client.set(f'refund_lock_{detail.order_id}', '1', ex=10)
            
            # 验证付款单信息 - 转账退费无需验证付款单
            can_refund_money = Decimal('0.00')
            if MoneyHandler.is_greater_than(params.apply_money, 0) and detail.refund_way != RefundWay.TRANSFER.value:
                # 先查询付款单
                payment_obj = await erp_finance_trade_payment.get_one(db, payment_order_no=detail.order_no)
                if not payment_obj:
                    return await ApiFailedResponse(f'付款单号有误{detail.order_no}')
                if payment_obj.trade_status != TradeStatus.SUCCESS.value:
                    return await ApiFailedResponse(f'订单未付款，不能退款：{detail.order_no}')
                # 验证最大可退费金额
                can_refund_money = MoneyHandler.to_decimal(payment_obj.money_pay)
                # 查询是否有已退款订单
                refund_detail_objs = await erp_order_refund_detail.get_many(db, raw=[
                    ErpOrderRefundDetail.order_no == detail.order_no,
                    ErpOrderRefundDetail.refund_state.in_([RefundDetailState.PENDING.value, RefundDetailState.SUCCESS.value]),
                ])
                if refund_detail_objs:
                    refund_amounts = [obj.refund_money for obj in refund_detail_objs if obj.refund_money]
                    refund_total_money = MoneyHandler.sum_list(refund_amounts)
                    if MoneyHandler.is_greater_than(refund_total_money, can_refund_money) or MoneyHandler.is_equal(refund_total_money, can_refund_money):
                        return await ApiFailedResponse(f'订单已全额退款，不能再次退款：{detail.order_no}')
                    can_refund_money = MoneyHandler.subtract(can_refund_money, refund_total_money)

                # 验证可退费金额
                apply_money_decimal = MoneyHandler.to_decimal(params.apply_money)
                if MoneyHandler.is_greater_than(apply_money_decimal, can_refund_money):
                    msg = f'可退费金额不足：{detail.order_no}, 可退费金额：{can_refund_money}, 申请退费金额：{apply_money_decimal}'
                    settings.logger.error(msg)
                    return await ApiFailedResponse(msg)

            # 更新订单状态
            current_order_obj = await erp_order.get_by_id(db, detail.order_id)
            if not current_order_obj:
                return await ApiFailedResponse(f'明细中有订单不存在')
            current_order_obj.order_state = OrderState.REFUND.value
            
            # 查找原学生订单（处理转入单的情况）
            original_order_student_obj = await find_original_order_student(db, detail.order_student_id)
            if not original_order_student_obj:
                return await ApiFailedResponse(f'无法找到原学生订单：{detail.order_student_id}')
            print(original_order_student_obj)
            # 修改学生订单状态
            order_objs = await erp_order.get_many(db, {"order_student_id": original_order_student_obj.id})
            if not order_objs:
                return await ApiFailedResponse(f'原订单未找到，无法结转：{original_order_student_obj.id}')
            
            order_student_obj = await erp_order_student.get_by_id(db, detail.order_student_id)
            if len(order_objs) == 1:  # 一笔学生订单对应一笔订单
                order_student_obj.student_state = StudentState.REFUND.value
            else:  # 一笔学生订单对应多笔订单， 则学生状态要所有订单全部退费才变为退费
                # 检查所有关联订单是否都是退费状态
                all_refunded = all(order.order_state == OrderState.REFUND.value or order.id == detail.order_id for order in order_objs)
                if all_refunded:
                    order_student_obj.student_state = StudentState.REFUND.value
            
            # 扣减退费的课时数
            if detail.refund_num and detail.refund_num > 0:
                current_hours = float(order_student_obj.total_hours or 0)
                refund_hours = float(detail.refund_num)
                order_student_obj.total_hours = max(0, current_hours - refund_hours)  # 确保不会变成负数
                settings.logger.info(f"结转退费扣减课时: 学生订单ID{detail.order_student_id}, 扣减课时{refund_hours}, 当前总课时{order_student_obj.total_hours}")
            
            # 生成退费订单号
            refund_order_no = f"R{datetime.now().strftime('%Y%m%d%H%M%S%f')[:16]}{detail.order_id}{random.randint(1000, 9999)}"
            
            # 创建退费详情记录
            await erp_order_refund_detail.create(db, commit=False, **{
                "refund_id": refund_id,
                "order_no": detail.order_no,
                "refund_order_no": refund_order_no,
                "order_student_id": detail.order_student_id,
                "order_id": detail.order_id,
                "unit_price": detail.unit_price,
                "refund_num": detail.refund_num,
                "total_money": detail.total_money,
                "unit": detail.unit,
                "create_by": user.uid,
                "update_by": user.uid,
                "apply_money": detail.apply_money,
                "refund_money": detail.apply_money,  # 结转时直接设置退费金额
                "refund_type": params.refund_type,
                "refund_state": 1,  # 结转成功
                "refund_way": detail.refund_way,
                "refund_ewallet": detail.refund_ewallet,
                "sort_no": index + 1,
                "pay_time": datetime.now(),  # 结转完成时间
            })
        
        # 将申请金额转入学生电子钱包
        await stu_ewallet_change(
            db, 
            stu_id=params.stu_id,
            change_type=1,  # 增加
            amount=params.apply_money,
            uid=user.uid,
            desc=f"退费结转-退费单号：{refund_id}",
            from_order_id=params.refund_detail[0].order_id if params.refund_detail else None,
            commit=False
        )
        
        settings.logger.info(f'结转申请完成：退费单{refund_id}，金额：{params.apply_money}，学生ID：{params.stu_id}')
        
        await db.commit()
        return await ApiSuccessResponse(message="结转申请成功，金额已转入电子钱包")
    
    # 以下是退费类型的处理逻辑，需要工作流审批
    # 根据退费详情中的退费方式确定工作流类型
    workflow_cost_type_bind_obj = None
    if params.refund_detail and len(params.refund_detail) > 0:
        # 验证所有退费详情的退费方式是否一致
        refund_ways = set([detail.refund_way for detail in params.refund_detail])
        if len(refund_ways) > 1:
            return await ApiFailedResponse("同一退费申请中的所有明细必须使用相同的退费方式")
        
        first_detail = params.refund_detail[0]
        refund_way = first_detail.refund_way
        
        # 根据退费方式确定工作流类型
        if refund_way == RefundWay.ORIGINAL.value:  # 原路退款（系统自动处理）
            workflow_cost_type_bind_obj = await get_workflow_cost_type_related(db, CostTypeBind.CourseBankRefund.value)
        elif refund_way in (RefundWay.CASH.value, RefundWay.TRANSFER.value):  # 现金退款和转账退款（人工处理）
            workflow_cost_type_bind_obj = await get_workflow_cost_type_related(db, CostTypeBind.CourseCashRefund.value)
        else:
            return await ApiFailedResponse(f"不支持的退费方式: {refund_way}")
            
        settings.logger.info(f"退费申请 - 退费方式: {refund_way}, 工作流类型: {workflow_cost_type_bind_obj.id if workflow_cost_type_bind_obj else None}")
    else:
        return await ApiFailedResponse("退费申请明细不能为空")
    
    if not workflow_cost_type_bind_obj:
        return await ApiFailedResponse("未找到退费工作流配置，请先配置工作流")

    workflow_id = workflow_cost_type_bind_obj.workflow_id
    # 查询课程退费的默认费用类型
    cost_type_obj = await erp_finance_cost_type.get_by_id(db, workflow_cost_type_bind_obj.default_cost_type_id)
    
    # 获取学生信息 - 使用第一个退费明细的学生信息
    if params.refund_detail and len(params.refund_detail) > 0:
        first_detail = params.refund_detail[0]
        order_student_obj = await erp_order_student.get_by_id(db, first_detail.order_student_id)
        stu_obj = await erp_student.get_by_id(db, order_student_obj.stu_id)
        
        # 检查是否传入了receipt_id，如果传入则使用现有单据，否则创建新单据
        if params.receipt_id and params.receipt_id > 0:
            # 使用传入的单据ID
            receipt_obj = await erp_receipt.get_by_id(db, params.receipt_id)
            if not receipt_obj:
                return await ApiFailedResponse(f"指定的单据ID不存在：{params.receipt_id}")
            
            # 验证单据状态是否允许关联退费
            if receipt_obj.audit_state not in [AuditState.DRAFT.value]:
                return await ApiFailedResponse(f"单据状态不允许关联退费，当前状态：{receipt_obj.audit_state}")
            
            # 更新单据关联退费单ID
            receipt_obj.refund_id = refund_id
            receipt_obj.update_by = user.uid
            
            # 检查是否已有财务信息，如果没有则创建
            finance_obj = await erp_receipt_finance.get_one(db, receipt_id=receipt_obj.id)
            if not finance_obj:
                # 创建财务信息
                finance_data = {
                    "receipt_id": receipt_obj.id,
                    "order_no": first_detail.order_no,
                    "apply_money": params.apply_money,
                    "ie_type": 0,  # 支出
                    "desc": f"退费申请，退费单号：{refund_id}",
                    "create_by": user.uid,
                    "update_by": user.uid,
                }
                finance_obj = await erp_receipt_finance.create(db, commit=False, **finance_data)
                
                # 更新单据关联财务ID
                receipt_obj.relate_receipt_id = finance_obj.id
            else:
                # 更新现有财务信息
                finance_obj.apply_money = params.apply_money
                finance_obj.desc = f"退费申请，退费单号：{refund_id}"
                finance_obj.update_by = user.uid
            
            settings.logger.info(f"使用传入的单据ID: {params.receipt_id} 关联退费单: {refund_id}")
        else:
            # 创建新的单据基本信息
            receipt_data = {
                "workflow_id": workflow_id,
                "apply_reason": f"学生[{stu_obj.stu_name}]退费申请",
                "related_obj_id": params.stu_id,
                "related_obj_type": RelatedObjType.INTERNAL_STUDENT.value, 
                "apply_remark": params.refund_reason,
                "audit_state": AuditState.DRAFT.value,  # 暂存状态
                "dept_id": dept_obj.id,
                "dept_name": dept_obj.dept_name,
                "attachment": params.attachment,
                "create_by": user.uid,
                "update_by": user.uid,
                "refund_id": refund_id,  # 关联退费单ID
            }
            
            receipt_obj = await erp_receipt.create(db, commit=False, **receipt_data)
            
            # 创建财务信息
            finance_data = {
                "receipt_id": receipt_obj.id,
                "order_no": first_detail.order_no,
                "apply_money": params.apply_money,
                "ie_type": 0,  # 支出
                "desc": f"退费申请，退费单号：{refund_id}",
                "create_by": user.uid,
                "update_by": user.uid,
            }
            finance_obj = await erp_receipt_finance.create(db, commit=False, **finance_data)
            
            # 更新单据关联财务ID
            receipt_obj.relate_receipt_id = finance_obj.id
            
            settings.logger.info(f"创建新单据关联退费单: {refund_id}")
        
        # 更新退费单关联单据ID
        await erp_order_refund.update_one(db, refund_id, {
            "receipt_id": receipt_obj.id,
            "update_by": user.uid
        }, commit=False)
    else:
        return await ApiFailedResponse("退费申请明细不能为空")

    # 处理退费明细 - 申请时立即更新学生状态（出班）
    for index, detail in enumerate(params.refund_detail):
        # 退费锁
        refund_lock = await redis_client.get(f'refund_lock_{detail.order_id}')
        if refund_lock:
            return await ApiFailedResponse(f'退费申请中，请勿频繁操作，10s后稍后再试吧')
        await redis_client.set(f'refund_lock_{detail.order_id}', '1', ex=10)
        
        # 验证付款单信息 - 转账退费无需验证付款单
        can_refund_money = Decimal('0.00')
        if MoneyHandler.is_greater_than(params.apply_money, 0) and detail.refund_way != RefundWay.TRANSFER.value:
            # 先查询付款单
            payment_obj = await erp_finance_trade_payment.get_one(db, payment_order_no=detail.order_no)
            if not payment_obj:
                return await ApiFailedResponse(f'付款单号有误{detail.order_no}')
            if payment_obj.trade_status != TradeStatus.SUCCESS.value:
                return await ApiFailedResponse(f'订单未付款，不能退款：{detail.order_no}')
            # 验证最大可退费金额
            can_refund_money = MoneyHandler.to_decimal(payment_obj.money_pay)
            # 查询是否有已退款订单
            refund_detail_objs = await erp_order_refund_detail.get_many(db, raw=[
                ErpOrderRefundDetail.order_no == detail.order_no,
                ErpOrderRefundDetail.refund_type == params.refund_type,
            ])
            if refund_detail_objs:
                refund_amounts = [obj.refund_money for obj in refund_detail_objs if obj.refund_money]
                refund_total_money = MoneyHandler.sum_list(refund_amounts)
                if MoneyHandler.is_greater_than(refund_total_money, can_refund_money) or MoneyHandler.is_equal(refund_total_money, can_refund_money):
                    return await ApiFailedResponse(f'订单已全额退款，不能再次退款：{detail.order_no}')
                can_refund_money = MoneyHandler.subtract(can_refund_money, refund_total_money)

            # 验证可退费金额
            apply_money_decimal = MoneyHandler.to_decimal(params.apply_money)
            if MoneyHandler.is_greater_than(apply_money_decimal, can_refund_money):
                msg = f'可退费金额不足：{detail.order_no}, 可退费金额：{can_refund_money}, 申请退费金额：{apply_money_decimal}'
                settings.logger.error(msg)
                return await ApiFailedResponse(msg)

        # 立即更新订单状态为退费状态（申请时生效）
        current_order_obj = await erp_order.get_by_id(db, detail.order_id)
        if not current_order_obj:
            return await ApiFailedResponse(f'明细中有订单不存在')
        current_order_obj.order_state = OrderState.REFUND.value
        
        # 查找原学生订单（处理转入单的情况）
        original_order_student_obj = await find_original_order_student(db, detail.order_student_id)
        if not original_order_student_obj:
            return await ApiFailedResponse(f'无法找到原学生订单：{detail.order_student_id}')
        
        # 立即修改学生订单状态为退费状态（申请时生效，学生出班）
        order_objs = await erp_order.get_many(db, {"order_student_id": original_order_student_obj.id})
        if not order_objs:
            return await ApiFailedResponse(f'原订单未找到，无法退费：{original_order_student_obj.id}')
        
        order_student_obj = await erp_order_student.get_by_id(db, detail.order_student_id)
        if len(order_objs) == 1:  # 一笔学生订单对应一笔订单
            order_student_obj.student_state = StudentState.REFUND.value
        else:  # 一笔学生订单对应多笔订单， 则学生状态要所有订单全部退费才变为退费
            # 检查所有关联订单是否都是退费状态
            all_refunded = all(order.order_state == OrderState.REFUND.value or order.id == detail.order_id for order in order_objs)
            if all_refunded:
                order_student_obj.student_state = StudentState.REFUND.value
        
        # 扣减退费的课时数
        if detail.refund_num and detail.refund_num > 0:
            current_hours = float(order_student_obj.total_hours or 0)
            refund_hours = float(detail.refund_num)
            order_student_obj.total_hours = max(0, current_hours - refund_hours)  # 确保不会变成负数
            settings.logger.info(f"转账退费扣减课时: 学生订单ID{detail.order_student_id}, 扣减课时{refund_hours}, 当前总课时{order_student_obj.total_hours}")
        
        # 立即更新报价单状态
        if current_order_obj.offer_id:
            offer_obj = await erp_order_offer.get_by_id(db, current_order_obj.offer_id)
            if offer_obj:
                # 检查该报价单下的所有订单是否都是退费状态
                offer_orders = await erp_order.get_many(db, {"offer_id": current_order_obj.offer_id})
                all_offer_orders_refunded = all(order.order_state == OrderState.REFUND.value or order.id == detail.order_id for order in offer_orders)
                if all_offer_orders_refunded:
                    offer_obj.offer_state = OfferState.FULL_REFUND.value
                else:
                    offer_obj.offer_state = OfferState.PARTIAL_REFUND.value
        
        # 生成退费订单号
        refund_order_no = f"R{datetime.now().strftime('%Y%m%d%H%M%S%f')[:16]}{detail.order_id}{random.randint(1000, 9999)}"
        
        # 查询费用类型 - 使用与工作流一致的费用类型配置
        if current_order_obj.order_class_type == OrderType.COURSE.value:   # 课程
            if detail.refund_way == RefundWay.ORIGINAL.value:  # 原路退款（系统自动处理）
                cost_type_obj = await get_workflow_cost_type_related(db, CostTypeBind.CourseBankRefund.value)
            elif detail.refund_way in (RefundWay.CASH.value, RefundWay.TRANSFER.value):  # 现金退款和转账退款（人工处理）
                cost_type_obj = await get_workflow_cost_type_related(db, CostTypeBind.CourseCashRefund.value)
            else:
                return await ApiFailedResponse(f'不支持的退费方式：{detail.refund_way}')
        else:  # 教材等其他类型
            cost_type_obj = await get_workflow_cost_type_related(db, CostTypeBind.TextbookRefund.value)
        
        if not cost_type_obj:
            return await ApiFailedResponse(f'未找到对应的费用类型配置，退费方式：{detail.refund_way}')
            
        finance_cost_type_obj = await erp_finance_cost_type.get_by_id(db, cost_type_obj.default_cost_type_id)
        if not finance_cost_type_obj:
            return await ApiFailedResponse(f'费用类型配置错误，费用类型ID：{cost_type_obj.default_cost_type_id}')
        
        await erp_order_refund_detail.create(db, commit=False, **{
            "refund_id": refund_id,
            "order_no": detail.order_no,
            "refund_order_no": refund_order_no,
            "order_student_id": detail.order_student_id,
            "order_id": detail.order_id,
            "unit_price": detail.unit_price,
            "refund_num": detail.refund_num,
            "total_money": detail.total_money,
            "unit": detail.unit,
            "create_by": user.uid,
            "update_by": user.uid,
            "apply_money": detail.apply_money,
            "refund_type": params.refund_type,
            "refund_state": 0,  # 待退费状态
            "refund_way": detail.refund_way,   # 1 原路退款 2 现金退款 3 转账退款
            "refund_ewallet": detail.refund_ewallet,
            "sort_no": index + 1,
            "cost_type_id": finance_cost_type_obj.id,
            "cost_type_name": finance_cost_type_obj.name,
        })

    workflow_id = copy.deepcopy(workflow_id)
    receipt_id = copy.deepcopy(receipt_obj.id)
    
    await db.commit()
    
    # 记录日志
    settings.logger.info(f'退费申请成功：退费单{refund_id}，申请金额：{params.apply_money}，学生ID：{params.stu_id}，退费方式：{first_detail.refund_way}，学生状态已更新为退费状态')
    asyncio.create_task(start_refund_workflow(workflow_id, receipt_id, user.uid))
    return await ApiSuccessResponse(message="退费申请成功，已自动发起审批流程，学生已出班")



# 查询退费单据
@router.get("/list")
async def refund_list(
    params: RefundListParams = Depends(),
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 退费单据查询
    - page: 页码
    - page_size: 每页数量
    - order_no: 订单号
    - stu_id: 学生ID
    - refund_type: 退费类型 1 退费 2 结转
    - audit_state: 审核状态 0 待审核 1 审核通过 2 审核不通过
    - apply_date_start: 申请日期开始
    - apply_date_end: 申请日期结束
    - refund_id: 退费单ID
    - order_student_id: 学生订单ID
    - order_id: 订单ID
    """
    # 创建查询条件
    erp_order_refund = CRUD(ErpOrderRefund)
    erp_order_refund_detail = CRUD(ErpOrderRefundDetail)
    
    # 构建基本查询条件
    condition = {}
    raw_conditions = []
    
    # 添加查询条件
    if params.order_no:
        condition["order_no"] = params.order_no
    if params.stu_id:
        condition["stu_id"] = params.stu_id
    if params.refund_type:
        condition["refund_type"] = params.refund_type
    if params.audit_state is not None:
        condition["audit_state"] = params.audit_state
    if params.refund_id:
        condition["id"] = params.refund_id
        
    # 日期范围查询
    if params.apply_date_start:
        raw_conditions.append(ErpOrderRefund.apply_date >= params.apply_date_start)
    if params.apply_date_end:
        raw_conditions.append(ErpOrderRefund.apply_date <= params.apply_date_end)
    
    # 处理详情表中的查询条件
    detail_condition = {}
    if params.order_student_id or params.order_id:
        # 如果有详情表的查询条件，先查询详情表获取退费单ID列表
        detail_raw_conditions = []
        if params.order_student_id:
            detail_raw_conditions.append(ErpOrderRefundDetail.order_student_id == params.order_student_id)
        if params.order_id:
            detail_raw_conditions.append(ErpOrderRefundDetail.order_id == params.order_id)
        
        # 查询符合条件的退费详情
        detail_stmt = select(ErpOrderRefundDetail.refund_id).where(
            and_(*detail_raw_conditions, ErpOrderRefundDetail.disable == 0)
        ).distinct()
        detail_result = await db.execute(detail_stmt)
        refund_ids = detail_result.scalars().all()
        
        # 如果没有找到符合条件的退费单ID，直接返回空结果
        if not refund_ids:
            return await ApiSuccessResponse(data={
                "count": 0,
                "data": []
            })
        
        # 将退费单ID添加到主表查询条件中
        raw_conditions.append(ErpOrderRefund.id.in_(refund_ids))
    
    # 查询总记录数
    total = await erp_order_refund.count(db, condition=condition, raw=raw_conditions)
    
    # 查询退费单列表
    refund_list = await erp_order_refund.get_many_with_pagination(
        db, 
        page=params.page, 
        page_size=params.page_size, 
        condition=condition, 
        raw=raw_conditions,
        reverse=True  # 按创建时间倒序
    )
    
    # 如果没有数据，直接返回空结果
    if not refund_list:
        return await ApiSuccessResponse(data={
            "count": 0,
            "data": []
        })
    
    # 获取所有退费单ID
    refund_ids = [refund.id for refund in refund_list]
    
    # 查询所有退费单对应的退费详情
    detail_stmt = select(ErpOrderRefundDetail).where(
        and_(
            ErpOrderRefundDetail.refund_id.in_(refund_ids),
            ErpOrderRefundDetail.disable == 0
        )
    )
    detail_result = await db.execute(detail_stmt)
    detail_list = detail_result.scalars().all()
    
    # 将退费详情按退费单ID分组
    detail_dict = {}
    for detail in detail_list:
        if detail.refund_id not in detail_dict:
            detail_dict[detail.refund_id] = []
        detail_dict[detail.refund_id].append(detail)
    
    # 组装返回结果
    result_items = []
    for refund in refund_list:
        refund_dict = {
            "id": refund.id,
            "refund_reason": refund.refund_reason,
            "order_no": refund.order_no,
            "stu_id": refund.stu_id,
            "refund_remark": refund.refund_remark,
            "audit_state": refund.audit_state,
            "create_time": refund.create_time.strftime("%Y-%m-%d %H:%M:%S") if refund.create_time else None,
            "update_time": refund.update_time.strftime("%Y-%m-%d %H:%M:%S") if refund.update_time else None,
            "apply_date": refund.apply_date.strftime("%Y-%m-%d") if refund.apply_date else None,
            "apply_money": MoneyHandler.to_float(refund.apply_money),
            "refund_money": MoneyHandler.to_float(refund.refund_money),
            "create_by": refund.create_by,
            "update_by": refund.update_by,
            "refund_type": refund.refund_type,
            # "carryover_money": MoneyHandler.to_float(refund.carryover_money),
            "details": []
        }
        
        # 添加退费详情
        if refund.id in detail_dict:
            for detail in detail_dict[refund.id]:
                detail_dict_item = {
                    "id": detail.id,
                    "refund_id": detail.refund_id,
                    "order_no": detail.order_no,
                    "refund_order_no": detail.refund_order_no,
                    "order_student_id": detail.order_student_id,
                    "order_id": detail.order_id,
                    "unit_price": MoneyHandler.to_float(detail.unit_price),
                    "refund_num": detail.refund_num,
                    "total_money": MoneyHandler.to_float(detail.total_money),
                    "unit": detail.unit,
                    "create_time": detail.create_time.strftime("%Y-%m-%d %H:%M:%S") if detail.create_time else None,
                    "update_time": detail.update_time.strftime("%Y-%m-%d %H:%M:%S") if detail.update_time else None,
                    "apply_money": MoneyHandler.to_float(detail.apply_money),
                    "refund_money": MoneyHandler.to_float(detail.refund_money)
                }
                refund_dict["details"].append(detail_dict_item)
        
        result_items.append(refund_dict)
    
    # 返回结果
    return await ApiSuccessResponse(data={
        "count": total,
        "data": result_items,
    })




# 获取账户对应的订单
@router.get("/can_refund_orders/")
async def query_orders(
        stu_id: int,
        page: int=1,
        page_size: int=10,
        offer_state: int = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),):
    """
    # 获取账户可退费的订单
    ## 参数
    - page
    - page_size
    - offer_state 付款状态：1-未付款，2-已付款，3-已关闭

    ## 返回

    """
    erp_order_offer = CF.get_crud(ErpOrderOffer)
    conditions =[
        ErpOrderOffer.stu_id == stu_id,
        ErpOrderOffer.disable == 0,
        ErpOrderOffer.offer_state.in_([OfferState.PAID.value, OfferState.PARTIAL_REFUND.value])
    ]
    # if offer_state is not None:
    #     conditions.append()
    # 获取报价单
    offers = await erp_order_offer.get_many_with_pagination(db, page, page_size, raw=conditions)
    count = await erp_order_offer.count(db, raw=conditions)

    order_nos = [offer.order_no for offer in offers]
    refund_detail_objs = await erp_order_refund_detail.get_many(db, raw=[
        ErpOrderRefundDetail.order_no.in_(order_nos),
    ])
    # 构建退费详情映射，按order_no分组
    refund_detail_map = defaultdict(list)
    for obj in refund_detail_objs:
        refund_detail_map[obj.order_no].append(obj)
    
    # 查询退费单主表信息
    refund_ids = [obj.refund_id for obj in refund_detail_objs]
    refund_objs = []
    if refund_ids:
        refund_objs = await erp_order_refund.get_many(db, raw=[
            ErpOrderRefund.id.in_(refund_ids),
        ])
    refund_map = {obj.id: obj for obj in refund_objs}

    # 生成订单字典
    offer_ids = [offer.id for offer in offers]
    orders = await get_orders_by_offer_ids(db, offer_ids)

    # orders = [order for order in orders if order.order_class_type==3 or (order.order_class_type==1 and order.class_status != ClassStatus.Closed.value)]
    offer_order_dict = defaultdict(list)
    for order in orders:
        # 获取该订单的历史退费记录
        order_refund_history = []
        if hasattr(order, 'order_no') and order.order_no in refund_detail_map:
            for refund_detail in refund_detail_map[order.order_no]:
                refund_main = refund_map.get(refund_detail.refund_id)
                if refund_main:
                    order_refund_history.append({
                        'refund_id': refund_detail.refund_id,
                        'refund_order_no': refund_detail.refund_order_no,
                        'apply_money': MoneyHandler.to_float(refund_detail.apply_money),
                        'refund_money': MoneyHandler.to_float(refund_detail.refund_money),
                        'refund_state': refund_detail.refund_state,  # 退款状态：0-待退款，1-已退款，2-失败，3-驳回
                        'refund_type': refund_detail.refund_type,    # 退费类型：1-退费，2-结转
                        'refund_way': refund_detail.refund_way,      # 退费方式：1-原路，2-现金，3-转账
                        'audit_state': refund_main.audit_state,     # 审核状态：0-待审核，1-审核通过，2-审核不通过
                        'apply_date': refund_main.apply_date.strftime('%Y-%m-%d') if refund_main.apply_date else None,
                        'create_time': refund_detail.create_time.strftime('%Y-%m-%d %H:%M:%S') if refund_detail.create_time else None,
                        'pay_time': refund_detail.pay_time.strftime('%Y-%m-%d %H:%M:%S') if refund_detail.pay_time else None,
                    })
        
        # 确保订单信息包含student_state字段
        order_dict = {
            'order_id': order.order_id,
            'offer_id': order.offer_id,
            'order_state': order.order_state,
            'class_price': order.class_price,
            'refund_orders': order_refund_history,  # 替换为历史退费记录
            'buy_num': order.buy_num,
            'unit': order.unit,
            'total_receivable': order.total_receivable,
            'total_income': order.total_income,
            'discount_money': order.discount_money,
            'ewallet_money': order.ewallet_money,
            'order_student_id': order.order_student_id,
            'class_id': order.class_id,
            'order_class_type': order.order_class_type,
            'student_state': order.student_state,  # 包含student_state字段
            'p_id': order.p_id,  # 包含父订单ID字段
            'total_hours': order.total_hours,
            'complete_hours': order.complete_hours,
            'class_name': order.class_name,
            'class_status': getattr(order, 'class_status', None),
            'audit_status': getattr(order, 'audit_status', None),
            'course_id': getattr(order, 'course_id', None),
            'course_name': getattr(order, 'course_name', None),
            'original_price': getattr(order, 'original_price', None),
            'sale_price': getattr(order, 'sale_price', None),
            'teacher_name': getattr(order, 'teacher_name', None),
            'textbook_id': getattr(order, 'textbook_id', None),
            'textbook_name': getattr(order, 'textbook_name', None),
            'textbook_origin_price': getattr(order, 'textbook_origin_price', None),
            'textbook_sale_price': getattr(order, 'textbook_sale_price', None),
        }
        offer_order_dict[order.offer_id].append(order_dict)
    # 开始附带订单信息
    for offer in offers:
        offer.order_list = offer_order_dict[offer.id]
    return await ApiSuccessResponse({
        "data": offers,
        "count": count
    }, '查询成功')



# 退款查询
@router.get("/refund_query")
async def refund_query(
    page: int=1,
    page_size: int=10,
    keyword: str=None,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 退款查询
    """
    data = await get_refund_by_page(db, page=page, page_size=page_size, keyword=keyword)
    count = await get_refund_by_page(db, page=page, page_size=page_size, count=True, keyword=keyword)
    return await ApiSuccessResponse({
        "data": data,
        "count": count,
    })

