from collections import defaultdict
import copy
from datetime import datetime, timedelta
from aioredis import Redis
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal, ROUND_HALF_UP
from models.m_finance import ErpFinanceTradePayment, ErpFinanceTradeRefund
import settings
from app_order.crud import  get_orders_by_offer_ids, order_class_by_offerids
from app_order.modules import push_order_modules, offer_data, create_offer_modules, query_offer_data, create_renew_offer
from app_order.serializer import BatchRenewOrderParams, CashReceiveWayParams, OfferParams, PushOfferParams, RenewOrderParams
from models.m_class import ErpClass, ErpCourse, ErpCourseTextbook
from models.m_discount import ErpStudentDiscountCoupon, ErpStudentCouponCourse, ErpStudentDiscountFixed
from models.m_order import ErpOrder, ErpOrderOffer, ErpOrderStudent
from models.m_student import ErpStudent
from settings import CF
from utils.db.account_handler import UserDict, role_required
from utils.db.db_handler import get_default_db, get_redis
from utils.enum.enum_order import DiscountStatus, OfferState, OrderState, StudentState, OrderType
from utils.other.money_handler import MoneyHandler
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

router = APIRouter(prefix="/order", tags=["订单"])

erp_order = CF.get_crud(ErpOrder)
erp_order_offer = CF.get_crud(ErpOrderOffer)
erp_order_student = CF.get_crud(ErpOrderStudent)
erp_student = CF.get_crud(ErpStudent)
erp_class = CF.get_crud(ErpClass)
erp_course_textbook = CF.get_crud(ErpCourseTextbook)
erp_student_discount_coupon = CF.get_crud(ErpStudentDiscountCoupon)
erp_student_coupon_course = CF.get_crud(ErpStudentCouponCourse)
erp_student_discount_fixed = CF.get_crud(ErpStudentDiscountFixed)
erp_finance_trade_payment = CF.get_crud(ErpFinanceTradePayment)
erp_finance_trade_refund = CF.get_crud(ErpFinanceTradeRefund)

"""
预制订单包含多个订单， 每个订单一个班级
"""


async def check_student_restricted_course_enrollment(db: AsyncSession, stu_id: int, target_class_id: int) -> tuple[bool, str]:
    """
    检测学生是否已经报名过限制类型的课程
    
    限制条件：
    1. type_id 为 33 或 103 的课程
    2. 相同的 term_id, grade_id, subject_id
    
    Args:
        db: 数据库会话
        stu_id: 学生ID
        target_class_id: 目标班级ID
        
    Returns:
        tuple[bool, str]: (是否被限制, 限制原因)
            - (True, "限制原因"): 表示被限制，不能报名
            - (False, ""): 表示不被限制，可以报名
    """
    try:
        # 获取目标班级的课程信息
        target_class = await erp_class.get_one(db, id=target_class_id)
        if not target_class:
            return True, "目标班级不存在"
            
        target_course = await CF.get_crud(ErpCourse).get_one(db, id=target_class.course_id)
        if not target_course:
            return True, "目标课程不存在"
            
        # 检查目标课程是否为限制类型 (type_id = 33 或 103)
        if target_course.type_id not in [33, 103]:
            return False, ""  # 不是限制类型，无需检查
            
        # 查找学生已报名的所有订单（包括未付款、已付款、转入状态的订单）
        student_orders = await erp_order_student.get_many(db, raw=[
            ErpOrderStudent.stu_id == stu_id,
            ErpOrderStudent.order_class_type == OrderType.COURSE.value,  # 只检查课程订单
            ErpOrderStudent.student_state.in_([
                StudentState.WAIT_PAY.value,    # 未付款
                StudentState.NORMAL.value,      # 已付款正常状态
                StudentState.TRANSFER_IN.value  # 转入状态
            ]),
            ErpOrderStudent.disable == 0
        ])
        
        if not student_orders:
            return False, ""  # 学生没有任何订单，可以报名
            
        # 获取学生已报名的所有班级ID
        enrolled_class_ids = [order.class_id for order in student_orders]
        
        # 获取这些班级的课程信息
        enrolled_classes = await erp_class.get_many(db, raw=[
            ErpClass.id.in_(enrolled_class_ids)
        ])
        
        enrolled_course_ids = [cls.course_id for cls in enrolled_classes]
        
        # 获取已报名的课程详细信息
        enrolled_courses = await CF.get_crud(ErpCourse).get_many(db, raw=[
            ErpCourse.id.in_(enrolled_course_ids),
            ErpCourse.type_id.in_([33, 103])  # 只检查限制类型的课程
        ])
        
        # 检查是否有相同条件的课程
        for enrolled_course in enrolled_courses:
            if (enrolled_course.term_id == target_course.term_id and 
                enrolled_course.grade_id == target_course.grade_id and 
                enrolled_course.subject_id == target_course.subject_id):
                return True, f"学生已报名过相同条件的限制类型课程（课程ID: {enrolled_course.id}, 课程名称: {enrolled_course.course_name}）"
                
        return False, ""  # 没有冲突，可以报名
        
    except Exception as e:
        settings.logger.error(f"检查学生限制课程报名失败: {str(e)}")
        return True, f"检查报名限制时发生错误: {str(e)}"


def round_money(amount: float, precision: int = 2) -> Decimal:
    """
    金额精度控制，默认保留2位小数，四舍五入
    """
    return Decimal(str(amount)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)


# 创建报价单
@router.post("/create_offer")
async def create_order(
        params: OfferParams,
        db: AsyncSession = Depends(get_default_db),
        redis_client: Redis = Depends(get_redis),
        user: UserDict = Depends(role_required([])),
):
    """
    # 创建报价单（待付款订单）
    - buy_num: 实际购买次数，需要结合unit 1 按期收费 2 按课节收费
    - insert_plan_index: 购买课程从第几节开始
    - trade_way: 交易方式 1 线上 2 线下
    - cash_receive_way: 现金收款方式 1 现金 默认0线上收款 存储到erp_order_offer.cash_receive_way 
    - order_class: 购课信息
        - buy_type: 购买类型 1 课程单 2 讲义单（注意：续费订单已移除，请使用独立的续费接口 /renew_order）
        - obj_id: 班级id 或讲义id
        - enter_date: 入学日期
        - discount_id: 优惠券id 讲义不传
        - discount_price: 优惠金额 讲义不传
        - discount_remark: 优惠备注 讲义不传
        - sale_price: 总售价
        - buy_num: 购买数量
    """
    flag, offer_id = await create_offer_modules(params, db, redis_client, user, is_erp=1)
    if flag:
        print(f"创建订单成功，订单id: {offer_id}")
        await db.commit()
        return await ApiSuccessResponse(True)
    return await ApiFailedResponse(offer_id)


# 修改报价单为现金收款
@router.post("/offer2cash")
async def update_offer_cash_receive_way(
        params: CashReceiveWayParams,
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):  
    """
    # 修改报价单为现金收款
    > 修改已经制单为线上收款的报价单为现金收款
    > 修改后会自动将报价单状态设置为已付款，学生入班，并创建现金收入单据
    """
    # 获取报价单信息
    offer_obj = await erp_order_offer.get_one(db, id=params.offer_id)
    if not offer_obj:
        return await ApiFailedResponse("报价单不存在")
        
    # 检查报价单状态，只有未付款的报价单才能修改
    if offer_obj.offer_state not in [OfferState.NOT_PAY.value]:
        return await ApiFailedResponse("该报价单状态不允许修改为现金收款")
        
    # 如果已经是现金收款，直接返回成功
    if offer_obj.cash_receive_way == 1:
        return await ApiSuccessResponse("报价单收款方式无需修改")
        
    if not params.cash_voucher or params.cash_voucher.strip() == "":
        return await ApiFailedResponse("现金收款必须提供付款凭证")
            
    # 获取学生信息
    stu_obj = await erp_student.get_one(db, id=offer_obj.stu_id)
    if not stu_obj:
        return await ApiFailedResponse("学生不存在")
        
    # 更新报价单现金收款信息
    offer_obj.cash_receive_way = 1
    offer_obj.cash_voucher = params.cash_voucher
    offer_obj.cash_pay_remark = params.cash_pay_remark
    offer_obj.update_by = user.uid
    offer_obj.update_time = datetime.now()
    
    # 更新报价单状态为已付款
    offer_obj.offer_state = OfferState.PAID.value
    
    # 获取并更新相关订单状态
    orders = await erp_order.get_many(db, {"offer_id": offer_obj.id})
    for order in orders:
        order.order_state = OrderState.PAID.value
        order.update_by = user.uid
        order.update_time = datetime.now()
        
    # 获取并更新相关学生订单状态（入班）
    if orders:
        order_student_ids = [order.order_student_id for order in orders]
        order_students = await erp_order_student.get_many(db, raw=[
            ErpOrderStudent.id.in_(order_student_ids)
        ])
        
        for order_student in order_students:
            order_student.student_state = StudentState.NORMAL.value  # 设置为正常上课状态（入班）
            order_student.update_by = user.uid
            order_student.update_time = datetime.now()
            settings.logger.info(f"学生入班成功: 学生ID={order_student.stu_id}, 班级ID={order_student.class_id}, 订单学生ID={order_student.id}")
            
            # 异步推送老师名片给家长
            try:
                from app_teach.modules import auto_push_teacher_card_after_enroll
                import asyncio
                asyncio.create_task(auto_push_teacher_card_after_enroll(
                    order_student_id=order_student.id,
                    stu_id=order_student.stu_id,
                    class_id=order_student.class_id
                ))
                settings.logger.info(f"已启动老师名片推送任务: 学生ID={order_student.stu_id}, 班级ID={order_student.class_id}")
            except Exception as push_error:
                settings.logger.error(f"启动老师名片推送任务失败: {str(push_error)}")
            
    # 创建现金收入单据并发起工作流（如果报价单金额大于0）
    if offer_obj.total_sale_price > 0:
        try:
            from app_order.modules import create_cash_income_receipt_and_workflow
            receipt_id = await create_cash_income_receipt_and_workflow(db, offer_obj, orders, user, stu_obj)
            settings.logger.info(f"现金收款单据创建成功，单据ID: {receipt_id}, 报价单ID: {offer_obj.id}")
        except Exception as e:
            settings.logger.error(f"创建现金收款单据失败: {str(e)}")
            # 现金收款单据创建失败不影响订单状态更新，只记录错误日志
            
    # 推送支付成功通知
    from app_order.modules import push_order_modules
    import asyncio
    asyncio.create_task(push_order_modules(offer_obj.id, is_paid=True))
    
    settings.logger.info(f"报价单{offer_obj.id}修改为现金收款成功，自动设置为已支付状态")
        
    # 提交事务
    await db.commit()
    
    return await ApiSuccessResponse("修改报价单收款方式成功")
        

# 整班续费
@router.post("/batch_renew_order")
async def batch_renew_order(
        params: BatchRenewOrderParams,
        db: AsyncSession = Depends(get_default_db),
        redis_client: Redis = Depends(get_redis),
        user: UserDict = Depends(role_required([])),
):
    """
    # 整班续费
    > 整班续费会对每个学生创建一个报价单，参考@router.post("/create_offer")，最后将报价单推送到微信公众号进行支付
    > 讲义自动添加：erp_course.bound_textbook_id 
    > 批量制单优惠券默认使用，参阅参考@router.post("/create_offer")中的固定折扣和优惠券的优先级
    > 其中，需要检测学生的该班级是否已经制过单，如果已经制过单，则不进行制单， 添加到fail列表中，不阻塞循环
    ## 参数
    - stu_ids: 学生id列表
    - class_id: 班级id
    ## 返回
    > 未成功制单的学生名单+成功制单的学生名单
    - {
        "success": [
            {
                "stu_id": 1,
                "stu_name": "张三",
                "status": "成功"
            }
        ],
        "fail": [
            {
                "stu_id": 1,
                "stu_name": "张三",
                "status": "该学生已制过单"
            }
        ]
    }
    """
    success_list = []
    fail_list = []
    # 获取班级信息
    class_obj = await erp_class.get_one(db, id=params.class_id)
    if not class_obj:
        return await ApiFailedResponse("班级不存在")
    
    # 获取课程信息以获取讲义ID
    erp_course = CF.get_crud(ErpCourse)
    course_obj = await erp_course.get_one(db, id=class_obj.course_id)
    if not course_obj:
        return await ApiFailedResponse("课程不存在")
        
    # 获取学生信息
    student_objs = await erp_student.get_many(db, raw=[
        ErpStudent.id.in_(params.stu_ids)
    ])
    student_map = {stu.id: stu for stu in student_objs}
    
    # 批量检测是否已经制过单
    existing_orders = await erp_order_student.get_many(db, raw=[
        ErpOrderStudent.stu_id.in_(params.stu_ids),
        ErpOrderStudent.class_id == params.class_id,
        ErpOrderStudent.order_class_type == OrderType.COURSE.value,
        ErpOrderStudent.student_state.in_([
            StudentState.WAIT_PAY.value,    # 未付款
            StudentState.NORMAL.value,      # 已付款正常状态
            StudentState.TRANSFER_IN.value  # 转入状态（也算已付款）
        ]),
        ErpOrderStudent.disable == 0
    ])
    
    # 创建已制单学生ID集合
    existing_student_ids = set([order.stu_id for order in existing_orders])
    
    # 为每个学生创建报价单
    for stu_id in params.stu_ids:
        stu_obj = student_map.get(stu_id)
        if not stu_obj:
            fail_list.append({
                "stu_id": stu_id,
                "stu_name": "未知",
                "status": "学生不存在"
            })
            continue
            
        # 检测是否已经制过单
        if stu_id in existing_student_ids:
            fail_list.append({
                "stu_id": stu_id,
                "stu_name": stu_obj.stu_name,
                "status": "该学生已制过单"
            })
            continue
            
        # 检测学生是否已报名限制类型课程（type_id=33,103且相同term_id,grade_id,subject_id）
        is_restricted, restriction_reason = await check_student_restricted_course_enrollment(db, stu_id, params.class_id)
        if is_restricted:
            fail_list.append({
                "stu_id": stu_id,
                "stu_name": stu_obj.stu_name,
                "status": restriction_reason
            })
            continue
        # 获取学生的固定折扣信息
        discount_fixed = await erp_student_discount_fixed.get_one(db, stu_id=stu_id)
        discount_fixed_rate = discount_fixed.discount_rate if discount_fixed else 0
        
        # 获取优惠券信息（只有没有固定折扣的学生才处理优惠券）
        universal_coupons = {}
        coupon_course_ids = set()
        coupon_course_amount = {}
        coupon_limit = {}
        
        if not discount_fixed:
            discount_coupons = await erp_student_discount_coupon.get_many(
                db,
                {"stu_id": stu_id, "status": DiscountStatus.AVAILABLE.value}
            )
            if discount_coupons:
                coupon_limit = {i.id: MoneyHandler.to_decimal(i.limit_money) for i in discount_coupons}
                expired_coupons_ids = []
                for discount_coupon in discount_coupons:
                    if discount_coupon.expired_time < datetime.now():
                        discount_coupon.status = DiscountStatus.EXPIRED.value
                        expired_coupons_ids.append(discount_coupon.id)
                discount_coupons = [i for i in discount_coupons if i.id not in expired_coupons_ids]
                universal_coupons = {i.id: i for i in discount_coupons if i.is_universal > 0}
                course_coupons = await erp_student_coupon_course.get_many(db, raw=[
                    ErpStudentCouponCourse.coupon_id.in_([i.id for i in discount_coupons if i.is_universal == 0])
                ])
                coupon_course_ids = {i.course_id for i in course_coupons}
                coupon_course_amount = {i.id: i.amount for i in discount_coupons if i.is_universal == 0}
        
        # 构建订单课程信息
        order_class_list = []
        
        # 1. 添加课程单
        order_class_list.append({
            "buy_type": 1,  # 课程单
            "obj_id": params.class_id,
            "enter_date": datetime.now().strftime("%Y-%m-%d"),
            "discount_id": None,
            "discount_price": 0,
            "discount_remark": None,
            "sale_price": Decimal(course_obj.sale_price),
            "buy_num": class_obj.planning_class_times,
            "unit": 2  # 按次收费
        })
        
        # 2. 自动添加讲义（如果有绑定讲义）
        textbook_obj = None
        if course_obj.bound_textbook_id and course_obj.bound_textbook_id > 0:
            textbook_obj = await erp_course_textbook.get_one(db, id=course_obj.bound_textbook_id)
            if textbook_obj and textbook_obj.sale_price > 0:
                order_class_list.append({
                    "buy_type": 2,  # 讲义单
                    "obj_id": course_obj.bound_textbook_id,
                    "enter_date": datetime.now().strftime("%Y-%m-%d"),
                    "discount_id": None,
                    "discount_price": 0,
                    "discount_remark": None,
                    "sale_price": Decimal(textbook_obj.sale_price),
                    "buy_num": 1,
                    "unit": 1  # 按本
                })
        
        # 计算总原价
        # 按次收费：课节原价 = 课程原价 / 总课节数，总原价 = 课节原价 × 购买课节数
        course_lesson_original_price = float(course_obj.original_price) / float(course_obj.number_of_lessons) if course_obj.number_of_lessons > 0 else 0
        total_original_price = course_lesson_original_price * float(class_obj.planning_class_times)
        
        # 处理讲义原价
        if textbook_obj and textbook_obj.sale_price > 0:
            total_original_price += float(textbook_obj.sale_price)
        
        # 计算总售价，考虑固定折扣和优惠券
        # 按次收费：课节单价 = 课程售价 / 总课节数，总售价 = 课节单价 × 购买课节数
        course_lesson_price = float(course_obj.sale_price) / float(course_obj.number_of_lessons) if course_obj.number_of_lessons > 0 else 0
        course_sale_price = course_lesson_price * float(class_obj.planning_class_times)
        course_final_price = course_sale_price
        
        # 应用固定折扣或查找最优优惠券
        if discount_fixed_rate > 0:
            # 有固定折扣，直接应用
            course_discount = course_sale_price * discount_fixed_rate
            course_final_price = course_sale_price - course_discount
            settings.logger.info(f"学生{stu_id}应用固定折扣{discount_fixed_rate*100}%，课程原价{course_sale_price}，折扣后{course_final_price}")
        elif universal_coupons or coupon_course_ids:
            # 没有固定折扣，查找最优优惠券
            best_discount = 0
            best_coupon_id = None
            
            # 检查通用优惠券
            for coupon_id, coupon in universal_coupons.items():
                if MoneyHandler.is_greater_than(MoneyHandler.to_decimal(course_sale_price), coupon_limit[coupon_id]):
                    coupon_discount = float(coupon.amount)
                    if coupon_discount > best_discount:
                        best_discount = coupon_discount
                        best_coupon_id = coupon_id
            
            # 检查课程专用优惠券
            if course_obj.id in coupon_course_ids:
                # 找到适用于该课程的优惠券
                for coupon in discount_coupons:
                    if coupon.is_universal == 0:
                        # 检查该优惠券是否适用于当前课程
                        course_coupon_exists = any(
                            cc.course_id == course_obj.id and cc.coupon_id == coupon.id 
                            for cc in course_coupons
                        )
                        if course_coupon_exists:
                            if MoneyHandler.is_greater_than(MoneyHandler.to_decimal(course_sale_price), coupon_limit[coupon.id]):
                                coupon_discount = float(coupon.amount)
                                if coupon_discount > best_discount:
                                    best_discount = coupon_discount
                                    best_coupon_id = coupon.id
            
            # 应用最优优惠券
            if best_discount > 0 and best_coupon_id:
                course_final_price = course_sale_price - best_discount
                # 更新订单课程信息中的优惠券ID和优惠金额
                order_class_list[0]["discount_id"] = best_coupon_id
                order_class_list[0]["discount_price"] = best_discount
                settings.logger.info(f"学生{stu_id}应用优惠券{best_coupon_id}，优惠金额{best_discount}，课程原价{course_sale_price}，优惠后{course_final_price}")
        
        total_sale_price = course_final_price
        
        # 处理讲义价格（讲义不享受固定折扣和优惠券）
        if textbook_obj and textbook_obj.sale_price > 0:
            total_sale_price += float(textbook_obj.sale_price)
        
        # 构建报价单参数
        from app_order.serializer import OfferParams, OrderClass

        # 处理生效时间，如果传入的是字符串则直接使用，否则使用当前时间
        if params.effective_start:
            effective_start = params.effective_start
        else:
            effective_start = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 计算结束时间，需要将字符串转换为datetime对象进行计算
        try:
            start_dt = datetime.strptime(effective_start, "%Y-%m-%d %H:%M:%S")
            effective_end = (start_dt + timedelta(days=2)).strftime("%Y-%m-%d %H:%M:%S")
        except ValueError:
            # 如果时间格式不正确，使用当前时间
            start_dt = datetime.now()
            effective_start = start_dt.strftime("%Y-%m-%d %H:%M:%S")
            effective_end = (start_dt + timedelta(days=2)).strftime("%Y-%m-%d %H:%M:%S")
        offer_params = OfferParams(
            stu_id=stu_id,
            effective_start=effective_start,
            effective_end=effective_end,
            total_original_price=total_original_price,
            total_sale_price=total_sale_price,
            total_discount_price=0,
            integral_money=0,
            ewallet_money=0,
            inner_remark=f"整班续费-班级ID:{params.class_id}",
            out_remark="整班续费",
            cash_voucher=None,
            cash_pay_remark=None,
            order_class=[OrderClass(**oc) for oc in order_class_list],
            is_push=params.is_push,  # 根据参数决定是否推送
            trade_way=2,  # 线上交易
            insert_plan_index=None,
            cash_receive_way=0  # 线上收款
        )
        stu_name = copy.deepcopy(stu_obj.stu_name)
        # 创建报价单
        flag, offer_id = await create_offer_modules(offer_params, db, redis_client, user, is_erp=1)
        
        if flag:
            success_list.append({
                "stu_id": stu_id,
                "stu_name": stu_name,
                "status": "成功",
                "offer_id": offer_id
            })
            settings.logger.info(f"整班续费创建成功 - 学生ID:{stu_id}, 报价单ID:{offer_id}")
        else:
            fail_list.append({
                "stu_id": stu_id,
                "stu_name": stu_name,
                "status": f"创建报价单失败: {offer_id if isinstance(offer_id, str) else '未知错误'}"

            })
            settings.logger.error(f"整班续费创建失败 - 学生ID:{stu_id}, 错误:{offer_id}")
    
    await db.commit()
    # 返回结果
    return await ApiSuccessResponse({
        "success": success_list,
        "fail": fail_list,
        "total_count": len(params.stu_ids)
    })



# 手动推送订单
@router.post("/push_order")
async def push_order(
        params: PushOfferParams,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 手动推送订单
      - 已存在的未支付订单的推送到微信公众号进行支付
      - offer_ids: 逗号分隔的报价单ID字符串，支持批量推送
    """
    import asyncio
    
    # 解析报价单ID列表
    try:
        offer_id_list = [int(x.strip()) for x in params.offer_ids.split(',') if x.strip()]
    except ValueError:
        return await ApiFailedResponse("报价单ID格式错误")
    
    if not offer_id_list:
        return await ApiFailedResponse("报价单ID不能为空")
    
    # 验证报价单是否存在
    offer_objs = await erp_order_offer.get_many(db, raw=[
        ErpOrderOffer.id.in_(offer_id_list)
    ])
    
    existing_offer_ids = {offer.id for offer in offer_objs}
    invalid_offer_ids = [offer_id for offer_id in offer_id_list if offer_id not in existing_offer_ids]
    
    if invalid_offer_ids:
        return await ApiFailedResponse(f"以下报价单不存在: {','.join(map(str, invalid_offer_ids))}")
    
    # 创建异步任务进行推送
    for offer_id in offer_id_list:
        asyncio.create_task(push_order_modules(offer_id))
    
    return await ApiSuccessResponse(True)


# 分页查询报价单
@router.get("/query_offer")
async def query_offer_view(
        page: int = None,
        page_size: int = None,
        order_no: str = None,  # 订单编号/支付单号
        create_time_start: str = None,  # 下单时间开始
        create_time_end: str = None,  # 下单时间结束
        pay_time_start: str = None,  # 付款时间开始
        pay_time_end: str = None,  # 付款时间结束
        stu_name: str = None,  # 学员姓名
        is_push: int = None,  # 是否推送
        rule_id: int = None,  # 续报规则id
        offer_state: int = None,  # 1-未付款，2-已付款，3-已关闭 4 -已失效 5 - 部分退款 6 - 已全部退款
        bind_official: int = None,  # 绑定公众号状态(0-未绑定,1-已绑定)
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 分页查询报价单
    - order_no: 订单编号/支付单号
    - create_time_start: 制单时间开始
    - create_time_end: 制单时间结束  
    - pay_time_start: 付款时间开始
    - pay_time_end: 付款时间结束
    - stu_name: 学员姓名
    - is_push: 是否推送
    - offer_state: 订单状态( # 1-未付款，2-已付款，3-已关闭 4 -已失效 5 - 部分退款 6 - 已全部退款)
    # - bind_official: 绑定公众号状态(0-未绑定,1-已绑定)
    """
    try:
        offers_data, count = await query_offer_data(
            db=db,
            page=page,
            page_size=page_size,
            order_no=order_no,
            create_time_start=create_time_start,
            create_time_end=create_time_end,
            pay_time_start=pay_time_start,
            pay_time_end=pay_time_end,
            stu_name=stu_name,
            is_push=is_push,
            offer_state=offer_state,
            rule_id=rule_id,
            # bind_official=bind_official
        )

        return await ApiSuccessResponse({
            "data": offers_data,
            "count": count
        })

    except ValueError as e:
        return await ApiFailedResponse(str(e))
    except Exception as e:
        settings.logger.error(f"查询报价单失败: {str(e)}")
        return await ApiFailedResponse(f"查询报价单失败: {str(e)}")


# 报价单详情
@router.get("/offer_detail")
async def offer_detail(
        offer_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 报价单详情
    """
    # 获取报价单基本信息
    offers = await offer_data(db, conditions={"id": offer_id})
    if not offers:
        return await ApiFailedResponse("报价单不存在")
    offer = offers[0]

    # 获取订单课程信息
    order_classes = await order_class_by_offerids(db, [offer_id])

    # 获取退费信息
    refund_data = await erp_finance_trade_refund.get_many(db, raw=[
        ErpFinanceTradeRefund.offer_id == offer_id
    ])

    # 构建返回数据
    offer_dict = dict(offer)
    offer_dict["order_classes"] = order_classes
    offer_dict["refunds"] = refund_data

    return await ApiSuccessResponse(offer_dict)

# 关闭订单
@router.post("/close_order")
async def close_order_view(
        offer_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 关闭订单
    - 关闭订单会执行以下操作:
      1. 更新报价单状态为已关闭
      2. 更新订单状态为已取消
      3. 更新学生订单状态为已取消
      4. 返还电子钱包余额(如果使用了)
      5. 返还优惠券(如果使用了)
    """
    try:
        # 获取报价单信息
        offer_obj = await erp_order_offer.get_one(db, id=offer_id)
        if not offer_obj:
            return await ApiFailedResponse("报价单不存在")

        # 检查订单状态，只有未付款的订单才能关闭
        if offer_obj.offer_state in [OfferState.PAID.value, OfferState.CLOSED.value]:
            return await ApiFailedResponse("该订单状态不允许关闭")

        # 获取关联的订单信息
        orders = await erp_order.get_many(db, {"offer_id": offer_id})
        if not orders:
            return await ApiFailedResponse("未找到关联订单")

        # 获取订单关联的学生订单
        order_student_ids = [order.order_student_id for order in orders]
        order_students = await erp_order_student.get_many(db, raw=[
            ErpOrderStudent.id.in_(order_student_ids)
        ])

        # 1. 更新报价单状态为已关闭
        offer_obj.offer_state = OfferState.CLOSED.value
        offer_obj.update_by = user.uid
        offer_obj.update_time = datetime.now()

        # 2. 更新订单状态为已取消
        for order in orders:
            order.order_state = OrderState.CANCEL.value
            order.update_by = user.uid
            order.update_time = datetime.now()

        # 3. 更新学生订单状态为等待付款
        for order_student in order_students:
            order_student.student_state = StudentState.CANCEL.value
            order_student.update_by = user.uid
            order_student.update_time = datetime.now()

        # 4. 返还电子钱包余额
        if offer_obj.ewallet_money > 0:
            # 获取学生信息
            stu_obj = await erp_student.get_one(db, id=offer_obj.stu_id)
            if stu_obj:
                # 返还电子钱包余额
                current_wallet = MoneyHandler.to_decimal(stu_obj.stu_wallet_amount)
                ewallet_return = MoneyHandler.to_decimal(offer_obj.ewallet_money)
                new_wallet_amount = MoneyHandler.add(current_wallet, ewallet_return)
                stu_obj.stu_wallet_amount = new_wallet_amount
                stu_obj.update_by = user.uid
                stu_obj.update_time = datetime.now()
                # 记录日志
                settings.logger.info(
                    f"学生{stu_obj.id}电子钱包余额变动，变动金额: +{offer_obj.ewallet_money}, 余: {stu_obj.stu_wallet_amount}")

        # 5. 返还优惠券
        for order in orders:
            if order.discount_id:
                await erp_student_discount_coupon.update_one(
                    db,
                    order.discount_id,
                    {
                        "status": DiscountStatus.NOT_USED.value,
                        "update_by": user.uid,
                        "update_time": datetime.now()
                    },
                    commit=False
                )

        # 提交事务
        await db.commit()
        return await ApiSuccessResponse(True)

    except Exception as e:
        await db.rollback()
        return await ApiFailedResponse(f"关闭订单失败: {str(e)}")


# 学生续费
@router.post("/renew_order")
async def renew_order(
        params: RenewOrderParams,
        db: AsyncSession = Depends(get_default_db),
        redis_client: Redis = Depends(get_redis),
        user: UserDict = Depends(role_required([])),
):
    """
    # 学生续费接口
    > 根据学生订单ID和续费课节数创建续费报价单
    > 自动计算续费价格，优惠默认使用最大优惠（固定折扣优先，否则使用最优优惠券）
    > 支持现金收款和线上支付
    ## 参数
    - order_student_id: 学生订单ID（erp_order_student.id）
    - renew_lessons: 续费课节数
    - is_push: 是否立即推送到微信 1 是 0 否
    - ewallet_money: 使用电子钱包金额（可选）
    - cash_receive_way: 现金收款方式 1 现金 默认0线上收款
    - cash_voucher: 现金收款凭证（现金收款时必填）
    - cash_pay_remark: 现金收款备注
    - inner_remark: 内部备注
    - out_remark: 外部备注
    ## 返回
    - 续费报价单ID
    """
    try:
        # 验证现金收款参数
        if params.cash_receive_way == 1:  # 现金收款
            if not params.cash_voucher or params.cash_voucher.strip() == "":
                return await ApiFailedResponse("现金收款必须提供付款凭证")
        
        # 创建续费报价单
        flag, result = await create_renew_offer(
            order_student_id=params.order_student_id,
            renew_lessons=params.renew_lessons,
            db=db,
            redis_client=redis_client,
            user=user,
            ewallet_money=params.ewallet_money,
            cash_receive_way=params.cash_receive_way,
            cash_voucher=params.cash_voucher,
            cash_pay_remark=params.cash_pay_remark,
            inner_remark=params.inner_remark,
            out_remark=params.out_remark,
            is_push=params.is_push
        )
        
        if flag:
            await db.commit()
            return await ApiSuccessResponse({
                "offer_id": result,
                "message": "续费订单创建成功"
            })
        else:
            return await ApiFailedResponse(result)
            
    except Exception as e:
        await db.rollback()
        settings.logger.error(f"续费订单创建失败: {str(e)}", exc_info=True)
        return await ApiFailedResponse(f"续费订单创建失败: {str(e)}")


# # 分页查询订单erp_order


