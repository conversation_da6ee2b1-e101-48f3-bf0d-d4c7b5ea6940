
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from models.models import ErpEnterprise, ErpPublicSettings, ErpAccount, ErpSalaryBase
from models.m_finance import ErpFinanceTradeRefund, ErpFinanceTradePayment, ErpFinanceCostType
from settings import logger, CF
from utils.db.account_handler import get_current_active_user, UserDict
from utils.db.db_handler import get_default_db
from utils.response.response_handler import ApiSuccessResponse

ERP_ENTERPRISE = CF.get_crud(ErpEnterprise)
ERP_PUBLIC_SETTINGS = CF.get_crud(ErpPublicSettings)
erp_account = CF.get_crud(ErpAccount)
ERP_FINANCE_TRADE_REFUND = CF.get_crud(ErpFinanceTradeRefund)
ERP_FINANCE_TRADE_PAYMENT = CF.get_crud(ErpFinanceTradePayment)
ERP_FINANCE_COST_TYPE = CF.get_crud(ErpFinanceCostType)

ERP_SALARY_BASE = CF.get_crud(ErpSalaryBase)

router = APIRouter(prefix="/order", tags=["订单管理"])


@router.get(f"/statistic_order")
async def query_statistic_order(
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 统计订单信息
    """
    # data = await get_order_statistic(db)
    return await ApiSuccessResponse([])


