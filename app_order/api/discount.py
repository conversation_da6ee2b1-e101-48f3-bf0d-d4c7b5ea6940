from datetime import datetime
from optparse import Option
from typing import Optional, List

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app_order.crud import all_coupon, all_discount_fixed
from app_order.serializer import StuCouponParams, DiscountFixedParams
from models.m_class import ErpCourse
from models.m_discount import ErpStudentCouponCourse, ErpStudentDiscountCoupon, \
    ErpStudentDiscountFixed
from models.models import ErpEnterprise, ErpPublicSettings, ErpAccount, ErpSalaryBase
from models.m_finance import ErpFinanceTradeRefund, ErpFinanceTradePayment, ErpFinanceCostType
from settings import logger, CF
from utils.db.account_handler import get_current_active_user, UserDict
from utils.db.db_handler import get_default_db
from utils.enum.enum_order import DiscountStatus
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

# 课程优惠券
erp_student_coupon_course = CF.get_crud(ErpStudentCouponCourse)
# 学生优惠券
erp_student_discount_coupon = CF.get_crud(ErpStudentDiscountCoupon)
# 学生固定折扣
erp_student_discount_fixed = CF.get_crud(ErpStudentDiscountFixed)

erp_course = CF.get_crud(ErpCourse)

router = APIRouter(prefix="/discount", tags=["优惠券"])


# 增加学生优惠券
@router.post("/stu_coupon")
async def create_student_coupon(
        params: StuCouponParams,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(get_current_active_user),
):
    """
    # 新增学生优惠券
    - is_universal: 1 通用优惠券 0 非通用优惠券
    - course_ids: 课程id列表 用于非通用优惠券
    """
    # 0.先检查stu_id是否在固定折扣表
    exist_fixed = await erp_student_discount_fixed.get_one(db, stu_id=params.stu_id)
    if exist_fixed:
        return await ApiFailedResponse("该学生已存在固定折扣，无法创建优惠券")
    # 1.创建学生优惠券
    coupon_obj = await erp_student_discount_coupon.create(db, commit=False, **{
        "stu_id": params.stu_id,
        "amount": params.amount,
        "expired_time": params.expired_time,
        "limit_money": params.limit_money,
        "is_universal": params.is_universal,
        "create_by": user.uid,
        "update_by": user.uid,
        "status": 1
    })
    # 2.然后检测是否为通用优惠券，如果是通用优惠券，则不需要关联课程
    if params.is_universal > 0:
        await db.commit()
        return await ApiSuccessResponse(True)
    # 3.如果不是通用优惠券，则需要关联课程
    for course_id in params.course_ids:
        await erp_student_coupon_course.create(db, commit=False, **{
            "coupon_id": coupon_obj.id,
            "course_id": course_id
        })
    await db.commit()
    return await ApiSuccessResponse(True)


# 按照倒序分页查询优惠券
@router.get("/stu_coupon")
async def get_student_coupon(
        page: int = 1,
        page_size: int = 10,
        stu_id: Optional[int] = None,
        stu_name: Optional[str] = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(get_current_active_user),
):
    """
    # 查询学生优惠券
    - stu_id: 学生id, 如果不传则查询所有学生的优惠券
    - stu_name: 学生姓名, 模糊查询
    """
    data = await all_coupon(db, page, page_size, stu_id=stu_id, stu_name=stu_name)
    new_data = []
    for i in data:
        item = dict(i)
        if i.is_universal == 1:
            item["course"] = []
            new_data.append(item)
            continue
        course_ids = await erp_student_coupon_course.get_many(db,
                                                              raw=[ErpStudentCouponCourse.coupon_id == i["coupon_id"]])
        course_ids = [j.course_id for j in course_ids]
        course_objs = await erp_course.get_many(db, raw=[ErpCourse.id.in_(course_ids)])
        course_list = [{"course_id": i.id, "course_name": i.course_name, } for i in course_objs if i.disable == 0]
        item["course"] = course_list
        new_data.append(item)
    count_data = await all_coupon(db, stu_id=stu_id, stu_name=stu_name)
    return await ApiSuccessResponse({
        "data": new_data,
        "count": len(count_data)
    })


# 修改学生优惠券
@router.put("/stu_coupon/{coupon_id}")
async def update_student_coupon(
    coupon_id: int,
    params: StuCouponParams,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(get_current_active_user),
):
    """
    # 修改学生优惠券
    - is_universal: 1 通用优惠券 0 非通用优惠券
    - course_ids: 课程id列表 用于非通用优惠券
    """
    # 1.检查优惠券是否存在
    coupon_obj = await erp_student_discount_coupon.get_one(db, id=coupon_id)
    if not coupon_obj:
        return await ApiFailedResponse("优惠券不存在")
    
    # 2.检查优惠券状态
    if coupon_obj.status != DiscountStatus.AVAILABLE.value:
        return await ApiFailedResponse("只能修改未使用的优惠券")
        
    # 3.更新优惠券基本信息
    await erp_student_discount_coupon.update_one(db, coupon_id, {
        "stu_id": params.stu_id,
        "amount": params.amount,
        "expired_time": params.expired_time,
        "limit_money": params.limit_money,
        "is_universal": params.is_universal,
        "update_by": user.uid
    }, commit=False)
    
    # 4.如果是非通用优惠券,需要更新关联课程
    if params.is_universal == 0:
        # 删除旧的关联关系
        await erp_student_coupon_course.delete_many(db, {"coupon_id": coupon_id}, commit=False)
        # 创建新的关联关系
        for course_id in params.course_ids:
            await erp_student_coupon_course.create(db, commit=False, **{
                "coupon_id": coupon_id,
                "course_id": course_id
            })
            
    await db.commit()
    return await ApiSuccessResponse(True)


# 删除学生优惠券
@router.delete("/stu_coupon/{coupon_id}")
async def delete_student_coupon(
    coupon_id: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(get_current_active_user),
):
    """
    # 删除学生优惠券
    - 只能删除未使用的优惠券
    """
    # 1.检查优惠券是否存在
    coupon_obj = await erp_student_discount_coupon.get_one(db, id=coupon_id)
    if not coupon_obj:
        return await ApiFailedResponse("优惠券不存在")
        
    # 2.检查优惠券状态
    if coupon_obj.status == DiscountStatus.USED.value:
        return await ApiFailedResponse("优惠券已使用，不可删除，只能删除未使用的优惠券")
        
    # 3.删除优惠券
    await erp_student_discount_coupon.delete_one(db, coupon_id, commit=False)
    
    # 4.删除关联的课程
    await erp_student_coupon_course.delete_many(db, {"coupon_id": coupon_id}, commit=False)
    
    await db.commit()
    return await ApiSuccessResponse(True)


# 创建固定折扣
@router.post("/stu_discount_fixed")
async def create_student_discount_fixed(
        params: DiscountFixedParams,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(get_current_active_user),
):
    """
    # 新增学生固定折扣
    - 当某用户已经发布优惠券后，给他设定固定折扣，此前优惠券自动失效
    """

    await erp_student_discount_fixed.create(db, commit=False, **{
        "stu_id": params.stu_id,
        "discount_rate": params.discount_rate,
        "create_by": user.uid,
        "update_by": user.uid,
    })
    # 优惠券失效
    await erp_student_discount_coupon.update_many(db, {"stu_id": params.stu_id}, {
        "status": DiscountStatus.EXPIRED.value,
    }, commit=True)
    return await ApiSuccessResponse(True)


# 仿照优惠券，查询固定折扣
@router.get("/stu_discount_fixed")
async def get_student_discount_fixed(
        page: int = 1,
        page_size: int = 10,
        stu_id: Optional[int] = None,
        stu_name: Optional[str] = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(get_current_active_user),
):
    """
    # 查询学生固定折扣
    - stu_id: 学生id, 如果不传则查询所有学生的固定折扣
    """
    data = await all_discount_fixed(db, page, page_size, stu_id=stu_id, stu_name=stu_name)
    count_data = await all_discount_fixed(db, stu_id=stu_id, stu_name=stu_name)
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 修改固定折扣
@router.put("/stu_discount_fixed/{discount_id}")
async def update_student_discount_fixed(
    discount_id: int,
    params: DiscountFixedParams,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(get_current_active_user),
):
    """
    # 修改学生固定折扣
    """
    # 1.检查固定折扣是否存在
    discount_obj = await erp_student_discount_fixed.get_one(db, id=discount_id)
    if not discount_obj:
        return await ApiFailedResponse("固定折扣不存在")
        
    # 2.更新固定折扣,原对象上更新
    # discount_obj.stu_id = params.stu_id
    discount_obj.discount_rate = params.discount_rate
    discount_obj.update_by = user.uid
    discount_obj.update_time = datetime.now()
    await db.commit()
    return await ApiSuccessResponse(True)


# 删除固定折扣
@router.delete("/stu_discount_fixed/{discount_id}")
async def delete_student_discount_fixed(
    discount_id: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(get_current_active_user),
):
    """
    # 删除学生固定折扣
    """
    # 1.检查固定折扣是否存在
    discount_obj = await erp_student_discount_fixed.get_one(db, id=discount_id)
    if not discount_obj:
        return await ApiFailedResponse("固定折扣不存在")
        
    # 2.删除固定折扣(软删除)
    await erp_student_discount_fixed.delete_one(db, discount_id)
    
    return await ApiSuccessResponse(True)

