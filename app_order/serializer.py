from typing import Optional, List

from pydantic import BaseModel


class StuCouponParams(BaseModel):
    stu_id: int
    amount: float
    expired_time: str
    limit_money: str
    is_universal: int
    course_ids: Optional[List[int]] = None


class DiscountFixedParams(BaseModel):
    stu_id: Optional[int] = None
    discount_rate: float


class OrderOfferBase(BaseModel):
    """
	报价单
    """
    id: Optional[int] = None
    name: str
    effective_start: str
    effective_end: str
    total_original_price: float
    total_price: float
    total_discount_price: float
    campus_id: int
    order_no: str
    offer_type: int
    stu_id: int
    offer_state: Optional[str] = None
    integral_money: Optional[float] = None
    ewallet_money: Optional[float] = None
    is_push: Optional[int] = None
    cash_voucher: Optional[str] = None
    cash_pay_remark: Optional[str] = None
    inner_remark: Optional[str] = None
    out_remark: Optional[str] = None
    order_from: Optional[int] = None



class OrderClass(BaseModel):
    buy_type: int   # 购买类型 1 课程单 2 讲义单
    obj_id: int   # 班级id 或讲义的id
    enter_date: str    # 入学日期
    discount_id: Optional[int] = None   # 优惠券id
    discount_price: Optional[float] = None  # 优惠金额
    discount_remark: Optional[str] = None   # 优惠备注
    sale_price: float   # 总售价
    # 数量
    buy_num: int
    # 单位
    unit: int   # buy_type 1：1 按期收费  2 按课节收费 | buy_type 2：讲义单 1 本 2 套



class OfferParams(BaseModel):
    stu_id: int
    effective_start: str
    effective_end: str
    total_original_price: float
    total_sale_price: float
    total_discount_price: float
    integral_money: float
    ewallet_money: float
    inner_remark:  Optional[str] = None
    out_remark: Optional[str] = None
    cash_voucher: Optional[str] = None
    cash_pay_remark: Optional[str] = None
    # discount_id: Optional[int] = None
    order_class: List[OrderClass]
    is_push: int
    trade_way: int
    insert_plan_index: Optional[int] = None
    cash_receive_way: Optional[int] = 0


class RefundDetailParams(BaseModel):
    """
    退费详情参数
    """
    order_student_id: int
    order_id: int
    unit_price: float
    refund_num: int
    total_money: float
    unit: int
    # carryover_money: Optional[float] = None
    apply_money: float
    refund_ewallet: Optional[float] = None
    order_no: Optional[str] = None
    refund_way: int


# 退费申请参数
class RefundApplyParams(BaseModel):
    """
    退费申请参数
    """
    stu_id: int
    refund_reason: Optional[str] = None
    refund_remark: Optional[str] = None
    apply_date: str
    apply_money: float
    refund_type: int
    attachment: Optional[List[str]] = None
    refund_detail: List[RefundDetailParams]
    receipt_id: Optional[int] = None    # 转账退费需传入单据id
    refund_way: Optional[int] = None    # 退费方式 1 原路 3 转账


# 退费单据查询参数
class RefundListParams(BaseModel):
    """
    退费单据查询参数
    """
    page: int = 1
    page_size: int = 10
    order_no: Optional[str] = None
    stu_id: Optional[int] = None
    refund_type: Optional[int] = None
    audit_state: Optional[int] = None
    apply_date_start: Optional[str] = None
    apply_date_end: Optional[str] = None
    refund_id: Optional[int] = None
    order_student_id: Optional[int] = None
    order_id: Optional[int] = None



class BatchRenewOrderParams(BaseModel):
    """
    整班续费参数
    """
    stu_ids: List[int]   # 学生id列表
    class_id: int   # 订单id
    is_push: int   # 是否立即推送 1 是 0 否
    effective_start: Optional[str] = None   # 报价单生效时间



class CashReceiveWayParams(BaseModel):
    """
    现金收款参数
    """
    offer_id: int
    # 现金收款凭证
    cash_voucher: Optional[str] = None
    # 现金收款备注
    cash_pay_remark: Optional[str] = None



class PushOfferParams(BaseModel):
    """
    推送报价单参数
    """
    offer_ids: str


class RenewOrderParams(BaseModel):
    """
    学生续费参数
    """
    order_student_id: int  # 学生订单ID
    renew_lessons: int     # 续费课节数
    is_push: int = 1       # 是否立即推送 1 是 0 否
    ewallet_money: float = 0  # 使用电子钱包金额
    cash_receive_way: int = 0  # 现金收款方式 1 现金 默认0线上收款
    cash_voucher: Optional[str] = None     # 现金收款凭证
    cash_pay_remark: Optional[str] = None  # 现金收款备注
    inner_remark: Optional[str] = None     # 内部备注
    out_remark: Optional[str] = None       # 外部备注