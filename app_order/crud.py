from pydantic.main import create_model
from sqlalchemy import select, and_, case, func, or_
from sqlalchemy.orm import aliased

from models.m_class import ErpClass, ErpCourse, ErpCourseTextbook
from models.m_discount import ErpStudentDiscountCoupon, ErpStudentDiscountFixed
from models.m_finance import ErpFinanceTradePayment, ErpFinanceTradeRefund
from models.m_mall import MallMerchantConfig
from models.m_order import ErpOrder, ErpOrderOffer, ErpOrderRefund, ErpOrderRefundDetail, ErpOrderStudent
from models.m_student import ErpStudent
from models.m_teacher import ErpAccountTeacher
from models.m_workflow import ErpReceipt, ErpWorkflowInstance, ErpWorkflowInstanceReviewer
from models.models import ErpAccount
from utils.enum.enum_approval import ClassAuditStatus
from utils.enum.enum_class import ClassStatus
from utils.enum.enum_order import StudentState

CreateBy = aliased(ErpAccount)
UpdateBy = aliased(ErpAccount)

async def all_coupon(db, page=None, page_size=None, stu_id=None, stu_name=None):
    """
    查询所有优惠券
    :param db:
    :param page:
    :param page_size:
    :param stu_id:
    """
    selects = [
        ErpStudent.id.label('stu_id'),
        ErpStudent.stu_name,
        ErpStudent.stu_username,
        ErpStudentDiscountCoupon.id.label('coupon_id'),
        ErpStudentDiscountCoupon.stu_id,
        ErpStudentDiscountCoupon.amount,
        ErpStudentDiscountCoupon.expired_time,
        ErpStudentDiscountCoupon.limit_money,
        ErpStudentDiscountCoupon.status,
        ErpStudentDiscountCoupon.create_time,
        ErpStudentDiscountCoupon.update_time,
        ErpStudentDiscountCoupon.create_by,
        ErpStudentDiscountCoupon.is_universal,
        CreateBy.employee_name.label('create_by_name'),
        ErpStudentDiscountCoupon.update_by,
        UpdateBy.employee_name.label('update_by_name'),
    ]
    conditions = [
        ErpStudentDiscountCoupon.disable == 0,
    ]
    if stu_id:
        conditions.append(ErpStudentDiscountCoupon.stu_id == stu_id)
    if stu_name:
        conditions.append(ErpStudent.stu_name.like(f'%{stu_name}%'))
    stmt = (
        select(*selects)
        .select_from(ErpStudentDiscountCoupon)
        .outerjoin(ErpStudent, ErpStudentDiscountCoupon.stu_id == ErpStudent.id)
        .outerjoin(CreateBy, CreateBy.id == ErpStudentDiscountCoupon.update_by)
        .outerjoin(UpdateBy, UpdateBy.id == ErpStudentDiscountCoupon.update_by)
        .where(and_(*conditions))
        .order_by(ErpStudentDiscountCoupon.create_time.desc())
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()


# 查询学生固定折扣
async def all_discount_fixed(db, page=None, page_size=None, stu_id=None, stu_name=None):
    selects = [
        ErpStudent.id.label('stu_id'),
        ErpStudent.stu_name,
        ErpStudent.stu_username,
        ErpStudentDiscountFixed.id.label('discount_fixed_id'),
        ErpStudentDiscountFixed.stu_id,
        ErpStudentDiscountFixed.discount_rate,
        ErpStudentDiscountFixed.create_time,
        ErpStudentDiscountFixed.update_time,
        ErpStudentDiscountFixed.create_by,
        ErpStudentDiscountFixed.update_by,
        CreateBy.employee_name.label('create_by_name'),
        UpdateBy.employee_name.label('update_by_name'),
    ]
    conditions = [
        ErpStudentDiscountFixed.disable == 0,
    ]
    if stu_name:
        conditions.append(ErpStudent.stu_name.like(f'%{stu_name}%'))
    if stu_id:
        conditions.append(ErpStudentDiscountFixed.stu_id == stu_id)
    stmt = (
        select(*selects)
        .select_from(ErpStudentDiscountFixed)
        .outerjoin(ErpStudent, ErpStudentDiscountFixed.stu_id == ErpStudent.id)
        .outerjoin(CreateBy, CreateBy.id == ErpStudentDiscountFixed.update_by)
        .outerjoin(UpdateBy, UpdateBy.id == ErpStudentDiscountFixed.update_by)
        .where(and_(*conditions))
        .order_by(ErpStudentDiscountFixed.create_time.desc())
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()


async def class_info_by_classids(db, class_ids):
    """
    根据班级id查询班级信息
    :param db:
    :param class_id:
    """
    selects = [
        ErpClass.id.label('class_id'),
        ErpClass.class_name,
        ErpClass.teacher_id,
        ErpClass.use_standard_full_rate,
        ErpClass.planning_class_times,
        ErpClass.miniprogram_start_enrollment_time,
        ErpClass.miniprogram_end_enrollment_time,
        ErpClass.classin_id,
        ErpClass.class_status,
        ErpCourse.id.label('course_id'),   # 课程id
        ErpCourse.original_price,   # 课程原价
        ErpCourse.sale_price,   # 课程售价
        ErpCourse.course_name,  # 课程名称
        ErpCourse.number_of_lessons,
        ErpCourse.bound_textbook_id,
        ErpCourse.cost_calculation_plan,
        ErpCourse.allow_repeated_purchase,
        ErpCourse.course_coefficient,
        ErpCourse.allow_refund,
        ErpCourse.year,


    ]
    conditions = [
        ErpClass.disable == 0,
        ErpClass.id.in_(class_ids)
    ]
    stmt = (
        select(*selects)
        .select_from(ErpClass)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(and_(*conditions))
        .order_by()
    )
    # if page and page_size:
    #     stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()


async def offers_by_page(db, page=None, page_size=None, stu_id=None):
    CreateBy = aliased(ErpAccount)
    UpdateBy = aliased(ErpAccount)

    selects = [
        ErpOrderOffer.id.label('offer_id'),
        ErpOrderOffer.name,
        ErpOrderOffer.effective_start,
        ErpOrderOffer.effective_end,
        ErpOrderOffer.total_original_price,
        ErpOrderOffer.total_sale_price,
        ErpOrderOffer.total_discount_price,
        ErpOrderOffer.campus_id,
        ErpOrderOffer.order_no,
        ErpOrderOffer.offer_type,
        ErpOrderOffer.stu_id,
        ErpOrderOffer.offer_state,
        ErpOrderOffer.integral_money,
        ErpOrderOffer.ewallet_money,
        ErpOrderOffer.is_push,
        ErpOrderOffer.cash_voucher,
        ErpOrderOffer.cash_pay_remark,
        ErpOrderOffer.inner_remark,
        ErpOrderOffer.out_remark,
        ErpOrderOffer.order_from,
        ErpOrderOffer.create_time,
        ErpOrderOffer.update_time,
        ErpOrderOffer.create_by,
        ErpOrderOffer.update_by,
        CreateBy.employee_name.label('create_by_name'),
        UpdateBy.employee_name.label('update_by_name'),
        ErpStudent.stu_name,
        ErpFinanceTradePayment.id.label('payment_id'),
        ErpFinanceTradePayment.cmb_pay_time.label('payment_time'),
        ErpFinanceTradePayment.money_income,
        ErpFinanceTradePayment.merchant_id,

        ErpFinanceTradeRefund.id.label('refund_id'),
        ErpFinanceTradeRefund.cmb_pay_time.label('refund_time'),
        ErpFinanceTradeRefund.money_refund,

        MallMerchantConfig.MerchantName.label('merchant_name'),
        MallMerchantConfig.Type.label('merchant_type'),
        MallMerchantConfig.Enable.label('merchant_enable'),

    ]
    conditions = []
    if stu_id:
        conditions.append(ErpOrderOffer.stu_id == stu_id)
    stmt = (
        select(*selects)
        .select_from(ErpOrderOffer)
        .outerjoin(ErpStudent, ErpOrderOffer.stu_id == ErpStudent.id)
        .outerjoin(ErpFinanceTradePayment, ErpOrderOffer.id == ErpFinanceTradePayment.offer_id)
        .outerjoin(ErpFinanceTradeRefund, ErpOrderOffer.id == ErpFinanceTradeRefund.offer_id)
        .outerjoin(MallMerchantConfig, ErpFinanceTradePayment.merchant_id == MallMerchantConfig.Id)
        .outerjoin(CreateBy, CreateBy.id == ErpOrderOffer.update_by)
        .outerjoin(UpdateBy, UpdateBy.id == ErpOrderOffer.update_by)
        .where(and_(*conditions))
        .order_by()
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()


async def order_class_by_offerids(db, offer_ids):
    selects = [
        ErpOrder.id.label('order_id'),
        ErpOrder.offer_id,
        ErpOrder.order_state,
        ErpOrder.trade_way,
        ErpOrder.class_price,
        ErpOrder.lesson_price,
        ErpOrder.total_receivable,
        ErpOrder.total_income,
        ErpOrder.discount,
        ErpOrder.buy_num,
        ErpOrder.unit,
        ErpOrderStudent.id.label('order_student_id'),
        ErpOrderStudent.student_state,
        ErpOrderStudent.class_id,
        ErpOrderStudent.total_hours,
        ErpOrderStudent.complete_hours,
        ErpOrder.order_class_type,
        # 根据order_class_type选择不同的名称字段
        case(
            (ErpOrder.order_class_type == 1, ErpClass.class_name),
            (ErpOrder.order_class_type == 3, ErpCourseTextbook.name),
            else_=None
        ).label('class_name'),
        # 根据order_class_type选择不同的教师ID和名称
        case(
            (ErpOrder.order_class_type == 1, ErpClass.teacher_id),
            else_=None
        ).label('teacher_id'),
        case(
            (ErpOrder.order_class_type == 1, ErpAccount.employee_name),
            else_=None
        ).label('employee_name'),
    ]
    conditions = [
        ErpOrder.disable == 0,
        ErpOrder.offer_id.in_(offer_ids),
        ErpOrderStudent.class_id > 0
    ]
    stmt = (
        select(*selects)
        .select_from(ErpOrder)
        .outerjoin(ErpOrderStudent, ErpOrder.order_student_id == ErpOrderStudent.id)
        # 根据order_class_type动态关联不同的表
        .outerjoin(
            ErpClass,
            and_(
                ErpOrderStudent.class_id == ErpClass.id,
                ErpOrder.order_class_type == 1
            )
        )
        .outerjoin(
            ErpCourseTextbook,
            and_(
                ErpOrderStudent.class_id == ErpCourseTextbook.id,
                ErpOrder.order_class_type == 3
            )
        )
        # 只在order_class_type = 1时关联教师相关表
        .outerjoin(
            ErpAccountTeacher,
            and_(
                ErpClass.teacher_id == ErpAccountTeacher.id,
                ErpOrder.order_class_type == 1
            )
        )
        .outerjoin(
            ErpAccount,
            and_(
                ErpAccountTeacher.account_id == ErpAccount.id,
                ErpOrder.order_class_type == 1
            )
        )
        .where(and_(*conditions))
        .order_by()
    )
    result = await db.execute(stmt)
    return result.fetchall()


async def get_order_student_by_page(db, page=None, page_size=None, conditions=None):
    """
    查询学生已付款订单（报名）
    """
    if conditions is None:
        conditions = []
    selects = []
    conditions = [
        ErpOrderStudent.student_state.in_([
            StudentState.NORMAL.value,
            StudentState.TRANSFERRED_IN.value,
            StudentState.GRADUATED.value
        ])
    ]
    stmt = (
        select(*selects)
        .select_from(ErpOrderStudent)
        .outerjoin()
        .where(and_(*conditions))
        .order_by()
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()



async def get_orders_by_offer_ids(db, offer_ids):
    selects = [
        ErpOrder.id.label('order_id'),
        ErpOrder.offer_id,
        ErpOrder.order_state,
        func.coalesce(ErpOrder.class_price, 0).label('class_price'),
        func.coalesce(ErpOrder.buy_num, 0).label('buy_num'),
        ErpOrder.unit,
        func.coalesce(ErpOrder.total_receivable, 0).label('total_receivable'),
        func.coalesce(ErpOrder.total_income, 0).label('total_income'),
        func.coalesce(ErpOrder.discount, 0).label('discount_money'),
        func.coalesce(ErpOrder.ewallet_money, 0).label('ewallet_money'),

        ErpOrderStudent.id.label('order_student_id'),
        ErpOrderStudent.class_id,
        ErpOrderStudent.order_class_type,
        ErpOrderStudent.student_state,
        ErpOrderStudent.p_id,  # 添加父订单ID字段
        func.coalesce(ErpOrderStudent.total_hours, 0).label('total_hours'),
        func.coalesce(ErpOrderStudent.complete_hours, 0).label('complete_hours'),
        ErpClass.class_name,
        ErpClass.class_status,
        ErpClass.audit_status,
        ErpCourse.id.label('course_id'),
        ErpCourse.course_name,
        func.coalesce(ErpCourse.original_price, 0).label('original_price'),
        func.coalesce(ErpCourse.sale_price, 0).label('sale_price'),

        ErpAccount.employee_name.label('teacher_name'),

        ErpCourseTextbook.id.label('textbook_id'),
        ErpCourseTextbook.name.label('textbook_name'),
        func.coalesce(ErpCourseTextbook.origin_price, 0).label('textbook_origin_price'),
        func.coalesce(ErpCourseTextbook.sale_price, 0).label('textbook_sale_price'),
    ]
    conditions = [
        ErpOrder.offer_id.in_(offer_ids),
        ErpOrder.disable == 0,
    ]
    stmt = (select(*selects)
            .select_from(ErpOrder)
            .outerjoin(ErpOrderStudent, ErpOrder.order_student_id == ErpOrderStudent.id)
            .outerjoin(ErpClass, and_(ErpOrderStudent.class_id == ErpClass.id, ErpOrderStudent.order_class_type == 1))
            .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
            .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
            .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
            .outerjoin(ErpCourseTextbook, and_(ErpOrderStudent.class_id == ErpCourseTextbook.id, ErpOrderStudent.order_class_type == 3))

            .where(and_(*conditions)))
    result = await db.execute(stmt)
    orders = result.fetchall()
    
    # 处理转出单的递归查询
    expanded_orders = []
    for order in orders:
        expanded_orders.append(order)
        
        # 如果是转出单（student_state = 5），递归查询转入链
        if order.student_state == StudentState.TRANSFER_OUT.value:
            transfer_chain = await _get_transfer_chain(db, order.order_student_id)
            expanded_orders.extend(transfer_chain)
    
    return expanded_orders


async def _get_transfer_chain(db, start_order_student_id):
    """
    递归查询转出单的完整转入链条
    从转出单开始，查找所有相关的转入单，直到找到最终的转入单（student_state=4）
    子订单只存在于erp_order_student表中，它们共享同一个父订单的erp_order记录
    """
    # 首先获取原始订单的erp_order信息
    original_order_stmt = (select(ErpOrder)
                          .where(ErpOrder.order_student_id == start_order_student_id)
                          .where(ErpOrder.disable == 0))
    original_order_result = await db.execute(original_order_stmt)
    original_order = original_order_result.scalar_one_or_none()
    
    if not original_order:
        return []
    
    transfer_orders = []
    current_id = start_order_student_id
    visited_ids = set()  # 防止循环引用
    
    while current_id and current_id not in visited_ids:
        visited_ids.add(current_id)
        
        # 查询以当前订单为父订单的所有子订单（只查询erp_order_student表）
        child_selects = [
            # 子订单的学生信息
            ErpOrderStudent.id.label('order_student_id'),
            ErpOrderStudent.class_id,
            ErpOrderStudent.order_class_type,
            ErpOrderStudent.student_state,
            ErpOrderStudent.p_id,
            func.coalesce(ErpOrderStudent.total_hours, 0).label('total_hours'),
            func.coalesce(ErpOrderStudent.complete_hours, 0).label('complete_hours'),
            ErpClass.class_name,
            ErpClass.class_status,
            ErpClass.audit_status,
            ErpCourse.id.label('course_id'),
            ErpCourse.course_name,
            func.coalesce(ErpCourse.original_price, 0).label('original_price'),
            func.coalesce(ErpCourse.sale_price, 0).label('sale_price'),

            ErpAccount.employee_name.label('teacher_name'),

            ErpCourseTextbook.id.label('textbook_id'),
            ErpCourseTextbook.name.label('textbook_name'),
            func.coalesce(ErpCourseTextbook.origin_price, 0).label('textbook_origin_price'),
            func.coalesce(ErpCourseTextbook.sale_price, 0).label('textbook_sale_price'),
        ]
        
        child_conditions = [
            ErpOrderStudent.p_id == current_id,
            ErpOrderStudent.disable == 0,
        ]
        
        child_stmt = (select(*child_selects)
                     .select_from(ErpOrderStudent)
                     .outerjoin(ErpClass, and_(ErpOrderStudent.class_id == ErpClass.id, ErpOrderStudent.order_class_type == 1))
                     .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
                     .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
                     .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
                     .outerjoin(ErpCourseTextbook, and_(ErpOrderStudent.class_id == ErpCourseTextbook.id, ErpOrderStudent.order_class_type == 3))
                     .where(and_(*child_conditions)))
        
        child_result = await db.execute(child_stmt)
        child_orders = child_result.fetchall()
        
        if not child_orders:
            break
            
        for child_order in child_orders:
            # 手动构造包含原始订单信息的结果对象
            transfer_order = type('TransferOrder', (), {
                'order_id': original_order.id,
                'offer_id': original_order.offer_id,
                'order_state': original_order.order_state,
                'class_price': float(original_order.class_price or 0),
                'buy_num': original_order.buy_num or child_order.total_hours or 0,
                'unit': original_order.unit,
                'total_receivable': float(original_order.total_receivable or 0),
                'total_income': float(original_order.total_income or 0),
                'discount_money': float(original_order.discount or 0),
                'ewallet_money': float(original_order.ewallet_money or 0),
                'order_student_id': child_order.order_student_id,
                'class_id': child_order.class_id,
                'order_class_type': child_order.order_class_type,
                'student_state': child_order.student_state,
                'p_id': child_order.p_id,
                'total_hours': child_order.total_hours,
                'complete_hours': child_order.complete_hours,
                'class_name': child_order.class_name,
                'class_status': getattr(child_order, 'class_status', None),
                'audit_status': getattr(child_order, 'audit_status', None),
                'course_id': getattr(child_order, 'course_id', None),
                'course_name': getattr(child_order, 'course_name', None),
                'original_price': getattr(child_order, 'original_price', None),
                'sale_price': getattr(child_order, 'sale_price', None),
                'teacher_name': getattr(child_order, 'teacher_name', None),
                'textbook_id': getattr(child_order, 'textbook_id', None),
                'textbook_name': getattr(child_order, 'textbook_name', None),
                'textbook_origin_price': getattr(child_order, 'textbook_origin_price', None),
                'textbook_sale_price': getattr(child_order, 'textbook_sale_price', None),
            })()
            
            transfer_orders.append(transfer_order)
            
            # 如果找到转入单（student_state=4），结束递归
            if child_order.student_state == StudentState.TRANSFER_IN.value:
                return transfer_orders
            
            # 如果还是转出单，继续递归
            if child_order.student_state == StudentState.TRANSFER_OUT.value:
                current_id = child_order.order_student_id
                break
        else:
            # 如果没有找到转出单，结束循环
            break
    
    return transfer_orders


async def get_refund_by_page(db, page=None, page_size=None, conditions=None, count=False, keyword=None):
    """
    查询退费
    """
    CreateBy = aliased(ErpAccount, name='create_by')
    # UpdateBy = aliased(ErpAccount, name='update_by')

    # 退费
    selects = [
        ErpOrderRefund.id.label('refund_id'),
        ErpOrderRefund.refund_reason,
        ErpOrderRefund.stu_id,
        ErpOrderRefund.refund_remark,
        ErpOrderRefund.audit_state,
        ErpOrderRefund.create_time,
        ErpOrderRefund.apply_date,
        ErpOrderRefund.apply_money,
        ErpOrderRefund.refund_money,
        ErpOrderRefund.create_by,
        ErpOrderRefund.refund_type,
        ErpOrderRefund.attachment,
        ErpOrderRefund.receipt_id,
        ErpStudent.stu_name,
        ErpStudent.stu_username,
        CreateBy.employee_name.label('create_by_name'),
        # 单据相关审核状态字段
        ErpReceipt.audit_state.label('receipt_audit_state'),
        ErpReceipt.workflow_instance_id,
        ErpReceipt.workflow_id,
        ErpReceipt.apply_reason.label('receipt_apply_reason'),
        ErpReceipt.apply_remark.label('receipt_apply_remark'),
        # 工作流实例相关字段
        ErpWorkflowInstance.current_node_id,
        ErpWorkflowInstance.current_node_name,
        ErpWorkflowInstance.status.label('workflow_status'),
    ]
    if count:
        selects = [
            func.count(ErpOrderRefund.id).label('count')
        ]
    conditions = [
        ErpOrderRefund.disable == 0,
        ErpReceipt.disable == 0,
    ]
    if keyword:
        conditions.append(or_(ErpStudent.stu_name.like(f'%{keyword}%'), ErpStudent.stu_username.like(f'%{keyword}%')))
    stmt = (select(*selects)
            .select_from(ErpOrderRefund)
            .outerjoin(ErpStudent, ErpOrderRefund.stu_id == ErpStudent.id)
            .outerjoin(CreateBy, CreateBy.id == ErpOrderRefund.create_by)
            .outerjoin(ErpReceipt, ErpOrderRefund.receipt_id == ErpReceipt.id)
            .outerjoin(ErpWorkflowInstance, ErpReceipt.workflow_instance_id == ErpWorkflowInstance.id)
            .where(and_(*conditions))
            .order_by(ErpOrderRefund.create_time.desc())
            )
    if count:
        result = await db.execute(stmt)
        return result.scalar()
    else:
        # 添加分页
        if page and page_size:
            stmt = stmt.limit(page_size).offset((page - 1) * page_size)
        result = await db.execute(stmt)
        refund_list = result.fetchall()
        
        # 如果没有数据，直接返回空列表
        if not refund_list:
            return []
            
        # 获取所有退费单ID，查询对应的退费明细
        refund_ids = [refund.refund_id for refund in refund_list]
        
        # 查询退费明细，包含班级信息
        detail_stmt = select(
            ErpOrderRefundDetail.id.label('detail_id'),
            ErpOrderRefundDetail.refund_id,
            ErpOrderRefundDetail.order_no,
            ErpOrderRefundDetail.refund_order_no,
            ErpOrderRefundDetail.order_student_id,
            ErpOrderRefundDetail.order_id,
            ErpOrderRefundDetail.unit_price,
            ErpOrderRefundDetail.refund_num,
            ErpOrderRefundDetail.total_money,
            ErpOrderRefundDetail.unit,
            ErpOrderRefundDetail.apply_money,
            ErpOrderRefundDetail.refund_money.label('detail_refund_money'),
            ErpOrderRefundDetail.refund_state,
            ErpOrderRefundDetail.refund_type.label('detail_refund_type'),
            ErpOrderRefundDetail.refund_way,
            ErpOrderRefundDetail.refund_ewallet,
            ErpOrderRefundDetail.cost_type_id,
            ErpOrderRefundDetail.cost_type_name,
            ErpOrderRefundDetail.pay_time,
            ErpOrderRefundDetail.create_time.label('detail_create_time'),
            ErpOrderRefundDetail.sort_no,
            ErpOrderRefundDetail.act_money,
            ErpOrderRefundDetail.refund_times,
            # 班级相关信息
            ErpClass.id.label('class_id'),
            ErpClass.class_name,
            ErpClass.class_status,
            ErpClass.audit_status,
            ErpCourse.id.label('course_id'),
            ErpCourse.course_name,
            ErpCourse.original_price,
            ErpCourse.sale_price,
            ErpAccountTeacher.id.label('teacher_id'),
            ErpAccount.employee_name.label('teacher_name'),
            # 订单类型信息
            ErpOrder.order_class_type,
            # 教材信息
            ErpCourseTextbook.id.label('textbook_id'),
            ErpCourseTextbook.name.label('textbook_name'),
            ErpCourseTextbook.origin_price.label('textbook_origin_price'),
            ErpCourseTextbook.sale_price.label('textbook_sale_price')
        ).select_from(ErpOrderRefundDetail
        ).outerjoin(ErpOrderStudent, ErpOrderRefundDetail.order_student_id == ErpOrderStudent.id
        ).outerjoin(ErpOrder, ErpOrderRefundDetail.order_id == ErpOrder.id
        ).outerjoin(ErpClass, and_(ErpOrderStudent.class_id == ErpClass.id, ErpOrderStudent.order_class_type == 1)
        ).outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id
        ).outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id
        ).outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id
        ).outerjoin(ErpCourseTextbook, and_(ErpOrderStudent.class_id == ErpCourseTextbook.id, ErpOrderStudent.order_class_type == 3)
        ).where(
            and_(
                ErpOrderRefundDetail.refund_id.in_(refund_ids),
                ErpOrderRefundDetail.disable == 0
            )
        ).order_by(ErpOrderRefundDetail.refund_id, ErpOrderRefundDetail.sort_no)
        
        detail_result = await db.execute(detail_stmt)
        detail_list = detail_result.fetchall()
        
        # 将退费明细按退费单ID分组
        detail_dict = {}
        for detail in detail_list:
            if detail.refund_id not in detail_dict:
                detail_dict[detail.refund_id] = []
            detail_dict[detail.refund_id].append({
                'detail_id': detail.detail_id,
                'order_no': detail.order_no,
                'refund_order_no': detail.refund_order_no,
                'order_student_id': detail.order_student_id,
                'order_id': detail.order_id,
                'unit_price': float(detail.unit_price) if detail.unit_price else 0.0,
                'refund_num': detail.refund_num,
                'total_money': float(detail.total_money) if detail.total_money else 0.0,
                'unit': detail.unit,
                'apply_money': float(detail.apply_money) if detail.apply_money else 0.0,
                'refund_money': float(detail.detail_refund_money) if detail.detail_refund_money else 0.0,
                'refund_state': detail.refund_state,
                'refund_type': detail.detail_refund_type,
                'refund_way': detail.refund_way,
                'refund_ewallet': float(detail.refund_ewallet) if detail.refund_ewallet else 0.0,
                'cost_type_id': detail.cost_type_id,
                'cost_type_name': detail.cost_type_name,
                'pay_time': detail.pay_time.strftime('%Y-%m-%d %H:%M:%S') if detail.pay_time else None,
                'create_time': detail.detail_create_time.strftime('%Y-%m-%d %H:%M:%S') if detail.detail_create_time else None,
                'sort_no': detail.sort_no,
                'act_money': float(detail.act_money) if detail.act_money else 0.0,
                'refund_times': detail.refund_times,
                # 班级信息
                'class_id': detail.class_id,
                'class_name': detail.class_name,
                'class_status': detail.class_status,
                'audit_status': detail.audit_status,
                'course_id': detail.course_id,
                'course_name': detail.course_name,
                'original_price': float(detail.original_price) if detail.original_price else 0.0,
                'sale_price': float(detail.sale_price) if detail.sale_price else 0.0,
                'teacher_id': detail.teacher_id,
                'teacher_name': detail.teacher_name,
                'order_class_type': detail.order_class_type,
                # 教材信息
                'textbook_id': detail.textbook_id,
                'textbook_name': detail.textbook_name,
                'textbook_origin_price': float(detail.textbook_origin_price) if detail.textbook_origin_price else 0.0,
                'textbook_sale_price': float(detail.textbook_sale_price) if detail.textbook_sale_price else 0.0
            })
        
        # 查询当前审核人信息
        instance_ids = [refund.workflow_instance_id for refund in refund_list if refund.workflow_instance_id]
        current_reviewers_dict = {}
        if instance_ids:
            # 查询所有待审批的审核人
            reviewer_stmt = select(
                ErpWorkflowInstanceReviewer.instance_id,
                ErpWorkflowInstanceReviewer.reviewer_id,
                ErpWorkflowInstanceReviewer.reviewer_name,
                ErpWorkflowInstanceReviewer.status,
                ErpWorkflowInstanceReviewer.node_id
            ).where(
                and_(
                    ErpWorkflowInstanceReviewer.instance_id.in_(instance_ids),
                    ErpWorkflowInstanceReviewer.status == 0,  # 待审批状态
                    ErpWorkflowInstanceReviewer.disable == 0
                )
            )
            reviewer_result = await db.execute(reviewer_stmt)
            reviewers = reviewer_result.fetchall()
            
            # 按实例ID分组当前审核人
            for reviewer in reviewers:
                if reviewer.instance_id not in current_reviewers_dict:
                    current_reviewers_dict[reviewer.instance_id] = []
                current_reviewers_dict[reviewer.instance_id].append({
                    'reviewer_id': reviewer.reviewer_id,
                    'reviewer_name': reviewer.reviewer_name,
                    'status': reviewer.status,
                    'node_id': reviewer.node_id
                })

        # 将退费明细添加到退费单数据中
        enhanced_refund_list = []
        for refund in refund_list:
            # 将原始数据转换为字典格式
            refund_dict = {
                'refund_id': refund.refund_id,
                'refund_reason': refund.refund_reason,
                'stu_id': refund.stu_id,
                'refund_remark': refund.refund_remark,
                'audit_state': refund.audit_state,
                'create_time': refund.create_time,
                'apply_date': refund.apply_date,
                'apply_money': refund.apply_money,
                'refund_money': refund.refund_money,
                'create_by': refund.create_by,
                'refund_type': refund.refund_type,
                'attachment': refund.attachment,
                'receipt_id': refund.receipt_id,
                'stu_name': refund.stu_name,
                'stu_username': refund.stu_username,
                'create_by_name': refund.create_by_name,
                # 单据相关审核状态字段
                'receipt_audit_state': refund.receipt_audit_state,
                'workflow_instance_id': refund.workflow_instance_id,
                'workflow_id': refund.workflow_id,
                'receipt_apply_reason': refund.receipt_apply_reason,
                'receipt_apply_remark': refund.receipt_apply_remark,
                # 工作流实例相关字段
                'current_node_id': refund.current_node_id,
                'current_node_name': refund.current_node_name,
                'workflow_status': refund.workflow_status,
                # 当前审核人信息
                'current_reviewers': current_reviewers_dict.get(refund.workflow_instance_id, []) if refund.workflow_instance_id else [],
                # 新增退费明细字段
                'refund_details': detail_dict.get(refund.refund_id, [])
            }
            enhanced_refund_list.append(refund_dict)
        
        return enhanced_refund_list