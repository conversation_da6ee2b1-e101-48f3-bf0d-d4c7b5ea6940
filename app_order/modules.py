from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased
import copy
import random
import asyncio
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP
from sqlalchemy import select, and_, func

from models.m_finance import ErpFinanceTradePayment, ErpFinanceTradeRefund
import settings
from models.m_class import ErpClass
from models.m_mall import MallMerchantConfig
from models.models import ErpAccount
from settings import CF
from app_order.crud import class_info_by_classids, order_class_by_offerids, offers_by_page
from models.m_order import ErpOrderOffer, ErpOrderStudent, ErpOrder
from models.m_student import ErpStudent, ErpStudentWechat
from models.m_class import ErpClass, ErpCourseTextbook
from models.m_discount import ErpStudentDiscountCoupon, ErpStudentCouponCourse, ErpStudentDiscountFixed
from models.m_workflow import Erp<PERSON>eceipt, ErpReceiptFinance, ErpReceiptDetail, ErpCostTypeBind
from utils.db.db_handler import get_default_db
from utils.tencent.wechatTools import WeChatTemplateMessage
from sqlalchemy import select, and_
from utils.enum.enum_order import DiscountStatus, OfferState, OfferType, OrderState, StudentState, OrderType
from utils.enum.enum_approval import CostTypeBind as CostTypeBindEnum, AuditState, RelatedObjType
from utils.enum.enum_order import ItemType
from utils.other.money_handler import MoneyHandler
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

erp_order_offer = CF.get_crud(ErpOrderOffer)
erp_student = CF.get_crud(ErpStudent)
erp_student_wechat = CF.get_crud(ErpStudentWechat)
erp_order = CF.get_crud(ErpOrder)
erp_order_student = CF.get_crud(ErpOrderStudent)
erp_class = CF.get_crud(ErpClass)
erp_course_textbook = CF.get_crud(ErpCourseTextbook)
erp_student_discount_coupon = CF.get_crud(ErpStudentDiscountCoupon)
erp_student_coupon_course = CF.get_crud(ErpStudentCouponCourse)
erp_student_discount_fixed = CF.get_crud(ErpStudentDiscountFixed)
erp_finance_trade_payment = CF.get_crud(ErpFinanceTradePayment)
erp_finance_trade_refund = CF.get_crud(ErpFinanceTradeRefund)
erp_receipt = CF.get_crud(ErpReceipt)
erp_receipt_finance = CF.get_crud(ErpReceiptFinance)
erp_receipt_detail = CF.get_crud(ErpReceiptDetail)
erp_cost_type_bind = CF.get_crud(ErpCostTypeBind)


async def send_sign_notify(openid, template_id, data, access_token=None, miniprogram=None):
    wechat = WeChatTemplateMessage(app_id=settings.GZH_CONFIG['app_id'],
                                   app_secret=settings.GZH_CONFIG['app_secret'])
    try:
        result = await wechat.send_template_message(openid, template_id, data, access_token=access_token, miniprogram=miniprogram)
        return result
    except Exception as e:
        settings.logger.error(e)
        return False


async def push_order_notify(db, offer_id: int, is_paid: bool = False):
    """
    推送订单通知到微信
    Args:
        db: AsyncSession - 数据库会话
        offer_id: int - 报价单ID
        is_paid: bool - 是否是支付成功的通知
    """
    try:
        offer_obj = await erp_order_offer.get_one(db, id=offer_id)
        if not offer_obj:
            print(f"报价单不存在: {offer_id}")
            return False

        # 已支付的订单不能重复推送
        if offer_obj.offer_state == OfferState.PAID.value and not is_paid:
            print(f"订单{offer_id}已支付，不能重复推送待支付通知")
            return False
            
        stu_obj = await erp_student.get_one(db, id=offer_obj.stu_id)
        if not stu_obj:
            print(f"学生不存在: {offer_obj.stu_id}")
            return False
        
        order_objs = await erp_order.get_many(db, {"offer_id":offer_obj.id})
        if not order_objs:
            print(f"订单不存在: {offer_obj.id}")
            return False

        order_student_ids = [order_obj.order_student_id for order_obj in order_objs if order_obj.order_student_id >0]
        order_student_objs = await erp_order_student.get_many(db, raw=[
            ErpOrderStudent.id.in_(order_student_ids)
        ])
        if not order_student_objs:
            print(f"学生订单不存在: {order_student_ids}")
            return False

        for order_student_obj in order_student_objs:
            order_student_obj.student_state = StudentState.WAIT_PAY.value
        
        order_class_data = await order_class_by_offerids(db, [offer_id])
        class_names = [item.class_name for item in order_class_data]
        class_name_str = f"{class_names[0][:10]}等{len(class_names)}件" if class_names else "未知课程"
        
        wechat_open_ids = await erp_student_wechat.get_many(db, {"stu_id": offer_obj.stu_id})
        if not wechat_open_ids:
            print(f'{stu_obj.stu_name}没有微信open_id, 无法推送')
            return False

        template_id = "ktkaFwTt_GP0qHWCfmqMdU35uZLDvpIBkKnEje7iA6g"
        miniprogram = {
            "appid": settings.MINI_PROGRAM_CONFIG['app_id'],
            "pagepath": "pagesPersonStudent/pages/Order/Order?index=1"
        }
        if is_paid:   # 已支付
            miniprogram['pagepath'] = "pagesPersonStudent/pages/Order/Order?index=2"
        
        status = "已支付" if is_paid else "未支付"
        remark = "支付成功" if is_paid else "点击去支付"
        
        data = {
            "keyword1": {
                "value": class_name_str,
                "color": "#177817"
            },
            "keyword2": {
                "value": round(float(offer_obj.total_sale_price), 2),
                "color": "#783417"
            },
            "keyword3": {
                "value": status,
                "color": "#173177"
            },
            "keyword4": {
                "value": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "color": "#173177"
            },
            "keyword5": {
                "value": f"{offer_obj.order_no}",
                "color": "#173177"
            },
            "remark": {
                "value": remark,
                "color": "#173177"
            },
        }
        for wechat_bind_obj in wechat_open_ids:
            wechat_open_id = wechat_bind_obj.wechat_open_id
            print(f"推送订单通知至微信: {wechat_open_id} -- {stu_obj.stu_name}")
            await send_sign_notify(wechat_open_id, template_id, data, miniprogram=miniprogram)
            
        if not is_paid:
            offer_obj.is_push = 1
            offer_obj.offer_state = OfferState.NOT_PAY.value
            for order_student_obj in order_student_objs:
                order_student_obj.student_state = StudentState.WAIT_PAY.value

            for order_obj in order_objs:
                order_obj.order_state = OrderState.NOT_PAY.value
            await db.commit()
            
        return True
    except Exception as e:
        print(f"push_order_notify error: {e}")
        return False


async def push_order_modules(offer_id, is_paid=False):
    """
    推送订单到小程序
    """
    try:
        # 使用正确的异步生成器语法
        async for db in get_default_db():
            await push_order_notify(db, offer_id, is_paid)
            break  # 只需要执行一次
    except Exception as e:
        settings.logger.error(f"push_order_modules error: {e}", exc_info=True)


async def create_cash_income_receipt_and_workflow(db, offer_obj, orders, user, stu_obj):
    """
    创建现金收入单据并发起bind_id=8的工作流
    Args:
        db: 数据库会话
        offer_obj: 报价单对象
        orders: 订单列表
        user: 用户信息
        stu_obj: 学生对象
    Returns:
        receipt_id: 创建的单据ID
    """
    try:
        # 获取bind_id=8的工作流配置（课程费用现金收入）
        cost_type_bind_obj = await erp_cost_type_bind.get_one(db, id=CostTypeBindEnum.CourseCashIncome.value)
        if not cost_type_bind_obj:
            settings.logger.error("未找到bind_id=8的工作流配置（课程费用现金收入）")
            raise Exception("未找到课程费用现金收入工作流配置，请联系管理员")
        
        workflow_id = cost_type_bind_obj.workflow_id
        if not workflow_id:
            settings.logger.error("bind_id=8的工作流ID为空")
            raise Exception("课程费用现金收入工作流ID未配置，请联系管理员")
        
        # 创建单据基本信息
        receipt_data = {
            "apply_reason": f"学生[{stu_obj.stu_name}]现金收费-订单{offer_obj.order_no}",
            "related_obj_id": stu_obj.id,
            "related_obj_type": RelatedObjType.INTERNAL_STUDENT.value,
            "related_obj_name": stu_obj.stu_name,
            "apply_remark": f"备注：{offer_obj.cash_pay_remark or '无'}",
            "audit_state": AuditState.DRAFT.value,  # 暂存状态
            "workflow_id": workflow_id,
            "attachment": [offer_obj.cash_voucher] if offer_obj.cash_voucher else [],
            "create_by": user.uid,
            "update_by": user.uid,
        }
        
        receipt_obj = await erp_receipt.create(db, commit=False, **receipt_data)
        
        # 创建财务信息
        finance_data = {
            "receipt_id": receipt_obj.id,
            "order_no": offer_obj.order_no,
            "apply_money": float(offer_obj.total_sale_price),
            "trade_money": float(offer_obj.total_sale_price),
            "ie_type": 0,  # 收入
            "desc": f"现金收费-{stu_obj.stu_name}",
            "create_by": user.uid,
            "update_by": user.uid,
        }
        
        receipt_finance_obj = await erp_receipt_finance.create(db, commit=False, **finance_data)
        
        # 更新单据关联财务ID
        receipt_obj.relate_finance_id = receipt_finance_obj.id
        
        # 创建明细信息，对每个订单创建一条明细记录
        for index, order in enumerate(orders):
            # 确定费用类型
            if order.order_class_type == OrderType.COURSE.value:
                cost_type_obj = cost_type_bind_obj
            else:
                # 讲义单使用讲义收入的费用类型
                textbook_cost_type_bind_obj = await erp_cost_type_bind.get_one(db, id=CostTypeBindEnum.TextbookIncome.value)
                cost_type_obj = textbook_cost_type_bind_obj if textbook_cost_type_bind_obj else cost_type_bind_obj
            
            # 计算单价
            buy_num = Decimal(str(order.buy_num or 0))
            total_income = Decimal(str(order.total_income or 0))
            item_unit_price = total_income / buy_num if buy_num > 0 else Decimal('0.00')
            
            # 获取订单关联的班级名称
            order_student = await erp_order_student.get_one(db, id=order.order_student_id)
            class_obj = await erp_class.get_one(db, id=order_student.class_id) if order_student else None
            item_name = class_obj.class_name if class_obj else f"订单{order.id}"
            
            detail_data = {
                "receipt_id": receipt_obj.id,
                "item_type": ItemType.FINANCE.value,  # 财务明细
                "item_name": item_name,
                "item_num": order.buy_num,
                "item_unit_price": float(item_unit_price),
                "item_total_price": float(total_income),
                "cost_type_id": cost_type_obj.default_cost_type_id,
                "cost_type_name": "课程费用现金收入",
                "amount": float(order.total_income),
                "remark": f"现金收费-{item_name}",
                "sort_no": index + 1,
                "create_by": user.uid,
                "update_by": user.uid,
                "order_id": order.id,
            }
            
            await erp_receipt_detail.create(db, commit=False, **detail_data)
        
        settings.logger.info(f"现金收入单据创建成功，单据ID: {receipt_obj.id}, 报价单ID: {offer_obj.id}")
        
        # 发起工作流
        from public_api.modules import start_workflow
        instance_id = await start_workflow(
            db=db,
            workflow_id=workflow_id,
            business_id=receipt_obj.id,
            user_id=user.uid,
            business_type="receipt"
        )
        
        settings.logger.info(f"现金收入工作流发起成功，工作流实例ID: {instance_id}, 单据ID: {receipt_obj.id}")
        
        return receipt_obj.id
        
    except Exception as e:
        settings.logger.error(f"创建现金收入单据和发起工作流失败: {str(e)}", exc_info=True)
        raise e


async def offer_data(db, page=None, page_size=None, conditions=None, raw_conditions=None):
    """
    查询订单数据
    - conditions: 精确匹配条件
    - raw_conditions: 原生查询条件
    """
    CreateBy = aliased(ErpAccount)
    UpdateBy = aliased(ErpAccount)

    base_conditions = []
    if conditions:
        for key, value in conditions.items():
            base_conditions.append(getattr(ErpOrderOffer, key) == value)
            
    if raw_conditions:
        base_conditions.extend(raw_conditions)
        
    stmt = (
        select(
            ErpOrderOffer.id,
            ErpOrderOffer.order_no,
            ErpOrderOffer.offer_state,
            ErpOrderOffer.create_time,
            ErpOrderOffer.effective_start,
            ErpOrderOffer.effective_end,
            ErpOrderOffer.total_original_price,
            ErpOrderOffer.total_sale_price,
            ErpOrderOffer.total_discount_price,
            ErpOrderOffer.offer_type,
            ErpOrderOffer.offer_state,
            ErpOrderOffer.integral_money,
            ErpOrderOffer.ewallet_money,
            ErpOrderOffer.is_push,
            ErpOrderOffer.cash_voucher,
            ErpOrderOffer.cash_pay_remark,
            ErpOrderOffer.inner_remark,
            ErpOrderOffer.out_remark,
            ErpOrderOffer.order_from,
            ErpOrderOffer.create_by,
            ErpOrderOffer.update_by,
            ErpOrderOffer.create_time,
            ErpOrderOffer.update_time,
            ErpOrderOffer.cash_receive_way,
            ErpStudent.stu_name,
            ErpStudent.id.label('stu_id'),
            ErpStudent.stu_gender,
            ErpStudent.stu_username,
            CreateBy.employee_name.label('create_by_name'),
            UpdateBy.employee_name.label('update_by_name'),

            ErpFinanceTradePayment.create_time.label('payment_create_time'),
            ErpFinanceTradePayment.payment_order_no,
            ErpFinanceTradePayment.trade_type,
            ErpFinanceTradePayment.money_pay,
            ErpFinanceTradePayment.money_income,
            ErpFinanceTradePayment.openid,
            ErpFinanceTradePayment.trade_status,
            ErpFinanceTradePayment.merchant_id,
            ErpFinanceTradePayment.cmb_order_id,
            ErpFinanceTradePayment.cmb_pay_time,
            ErpFinanceTradePayment.cmb_trade_type,
            ErpFinanceTradePayment.third_order_id,
            MallMerchantConfig.MerchantName.label('merchant_name'),
        )
        .outerjoin(ErpStudent, ErpOrderOffer.stu_id == ErpStudent.id)
        .outerjoin(ErpFinanceTradePayment, ErpOrderOffer.id == ErpFinanceTradePayment.offer_id)
        .outerjoin(MallMerchantConfig, ErpFinanceTradePayment.merchant_id == MallMerchantConfig.Id)
        .outerjoin(CreateBy, CreateBy.id == ErpOrderOffer.update_by)
        .outerjoin(UpdateBy, UpdateBy.id == ErpOrderOffer.update_by)

        .where(and_(*base_conditions))
        .order_by(ErpOrderOffer.create_time.desc())
    )
    
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
        
    result = await db.execute(stmt)
    return result.fetchall()


def round_money(amount: float, precision: int = 2) -> Decimal:
    """
    金额精度控制，默认保留2位小数，四舍五入
    """
    return Decimal(str(amount)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)


async def validate_order_params(params, stu_obj):
    """
    验证订单参数
    """
    if not params.order_class:
        return False, "订单课程信息不能为空"
        
    if params.trade_way not in [1, 2]:
        return False, "无效的交易方式"
        
    # 现金收款校验
    if params.cash_receive_way == 1:  # 现金收款
        if not params.cash_voucher or params.cash_voucher.strip() == "":
            return False, "现金收款必须提供付款凭证"
        
    for class_params in params.order_class:
        if class_params.unit not in [1, 2]:
            return False, "无效的购买单位"
        if class_params.buy_num <= 0:
            return False, "购买数量必须大于0"
            
    if not stu_obj:
        return False, "学生id不存在"
        
    return True, None


async def validate_duplicate_class_orders(db, params):
    """
    验证学生是否已有相同班级的未付款或已付款订单
    """
    try:
        # 提取要购买的班级ID列表（只检查课程单，不检查讲义单和续费订单）
        class_ids_to_check = []
        for class_params in params.order_class:
            if class_params.buy_type == 1:  # 只检查课程单
                class_ids_to_check.append(class_params.obj_id)
        
        if not class_ids_to_check:
            return True, None  # 没有课程单需要检查
        
        # 查询学生在这些班级中是否已有订单
        # 查询条件：学生ID匹配 + 班级ID在列表中 + 订单状态为未付款或已付款
        existing_orders = await erp_order_student.get_many(db, raw=[
            ErpOrderStudent.stu_id == params.stu_id,
            ErpOrderStudent.class_id.in_(class_ids_to_check),
            ErpOrderStudent.order_class_type == OrderType.COURSE.value,  # 课程订单
            ErpOrderStudent.student_state.in_([
                StudentState.WAIT_PAY.value,    # 未付款
                StudentState.NORMAL.value,      # 已付款正常状态
                StudentState.TRANSFER_IN.value  # 转入状态（也算已付款）
            ]),
            ErpOrderStudent.disable == 0
        ])
        
        if existing_orders:
            # 找出重复的班级ID
            duplicate_class_ids = list(set([order.class_id for order in existing_orders]))
            
            # 查询班级名称以便在错误信息中显示
            class_objs = await erp_class.get_many(db, raw=[
                ErpClass.id.in_(duplicate_class_ids)
            ])
            class_names = [class_obj.class_name for class_obj in class_objs]
            
            error_msg = f"学生已有以下班级的未付款或已付款订单，不能重复购买：{', '.join(class_names)}"
            # settings.logger.warning(f"重复班级订单验证失败: 学生ID={params.stu_id}, 重复班级ID={duplicate_class_ids}")
            return False, error_msg
        
        return True, None
        
    except Exception as e:
        settings.logger.error(f"验证重复班级订单失败: {str(e)}", exc_info=True)
        return False, f"验证重复班级订单失败: {str(e)}"


async def validate_ewallet(params, stu_obj, total_price=None):
    """
    验证电子钱包
    Args:
        params: 订单参数
        stu_obj: 学生对象
        total_price: 订单总价(可选)，用于验证电子钱包使用金额不超过订单金额
    """
    use_ewallet_residue = Decimal('0.00')
    new_ewallet_residue = Decimal('0.00')
    actual_ewallet_usage = Decimal('0.00')
    
    ewallet_money_decimal = MoneyHandler.to_decimal(params.ewallet_money)
    if MoneyHandler.is_greater_than(ewallet_money_decimal, 0):
        ewallet_residue = MoneyHandler.to_decimal(stu_obj.stu_wallet_amount)
        if MoneyHandler.is_less_than(ewallet_residue, 0) or MoneyHandler.is_equal(ewallet_residue, 0):
            return None, None, None, "电子钱包余额不足"
        
        # 验证电子钱包余额是否足够
        if MoneyHandler.is_less_than(ewallet_residue, ewallet_money_decimal):
            return None, None, None, f"电子钱包余额不足，当前余额: {ewallet_residue}, 需要使用: {ewallet_money_decimal}"
        
        # 如果提供了订单总价，验证使用金额是否超过订单金额
        if total_price is not None:
            total_price_decimal = MoneyHandler.to_decimal(total_price)
            if MoneyHandler.is_greater_than(ewallet_money_decimal, total_price_decimal):
                # 如果电子钱包使用金额超过了订单总价，则实际使用金额为订单总价
                actual_ewallet_usage = total_price_decimal
                settings.logger.info(
                    f"调整电子钱包使用金额，原计划使用: {ewallet_money_decimal}, 实际使用: {actual_ewallet_usage}, "
                    f"订单金额: {total_price_decimal}"
                )
            else:
                actual_ewallet_usage = ewallet_money_decimal
        else:
            actual_ewallet_usage = ewallet_money_decimal
            
        use_ewallet_residue = actual_ewallet_usage
        new_ewallet_residue = MoneyHandler.subtract(ewallet_residue, actual_ewallet_usage)
        
    return use_ewallet_residue, new_ewallet_residue, actual_ewallet_usage, None


async def validate_class_capacity(db, params):
    """
    验证班级人数是否超限
    Args:
        db: 数据库会话
        params: 订单参数
    Returns:
        tuple: (is_valid: bool, error_msg: str)
    """
    try:
        # 提取要购买的班级ID列表（只检查课程单，不检查讲义单和续费订单）
        class_ids_to_check = []
        for class_params in params.order_class:
            if class_params.buy_type == 1:  # 只检查课程单
                class_ids_to_check.append(class_params.obj_id)
        
        if not class_ids_to_check:
            return True, None  # 没有课程单需要检查
        
        # 查询班级信息获取班级容量
        class_objs = await erp_class.get_many(db, raw=[
            ErpClass.id.in_(class_ids_to_check)
        ])
        
        if not class_objs:
            return False, "班级不存在"
        
        # 创建班级容量映射
        class_capacity_map = {class_obj.id: class_obj.class_capacity for class_obj in class_objs}
        class_name_map = {class_obj.id: class_obj.class_name for class_obj in class_objs}
        
        # 查询各班级当前学生人数（包括已付款和未付款学生）
        current_students = await erp_order_student.get_many(db, raw=[
            ErpOrderStudent.class_id.in_(class_ids_to_check),
            ErpOrderStudent.order_class_type == OrderType.COURSE.value,  # 课程订单
            ErpOrderStudent.student_state.in_([
                StudentState.WAIT_PAY.value,    # 未付款（名额锁定）
                StudentState.NORMAL.value,      # 已付款正常状态
                StudentState.TRANSFER_IN.value  # 转入状态
            ]),
            ErpOrderStudent.disable == 0
        ])
        
        # 统计各班级当前人数
        class_current_count = {}
        for student in current_students:
            class_id = student.class_id
            if class_id not in class_current_count:
                class_current_count[class_id] = 0
            class_current_count[class_id] += 1
        
        # 检查每个班级是否超限
        over_capacity_classes = []
        for class_id in class_ids_to_check:
            capacity = class_capacity_map.get(class_id, 0)
            current_count = class_current_count.get(class_id, 0)
            class_name = class_name_map.get(class_id, f"班级ID:{class_id}")
            
            # 如果班级容量为0，跳过检查（可能是不限制人数）
            if capacity <= 0:
                settings.logger.info(f"班级{class_name}(ID:{class_id})容量为{capacity}，跳过人数限制检查")
                continue
            
            # 检查是否超限（当前人数 >= 班级容量）
            if current_count >= capacity:
                over_capacity_classes.append({
                    "class_id": class_id,
                    "class_name": class_name,
                    "capacity": capacity,
                    "current_count": current_count
                })
                settings.logger.warning(
                    f"班级{class_name}(ID:{class_id})人数超限 - 容量:{capacity}, 当前人数:{current_count}"
                )
        
        # 如果有班级超限，返回错误信息
        if over_capacity_classes:
            error_details = []
            for class_info in over_capacity_classes:
                error_details.append(
                    f"{class_info['class_name']}(容量:{class_info['capacity']}, 当前:{class_info['current_count']})"
                )
            error_msg = f"以下班级人数已满，无法继续报名：{', '.join(error_details)}"
            return False, error_msg
        
        return True, None
        
    except Exception as e:
        settings.logger.error(f"验证班级人数限制失败: {str(e)}", exc_info=True)
        return False, f"验证班级人数限制失败: {str(e)}"


async def create_material_order(db, class_params, offer_obj, params, user, class_objs_map, textbook_objs_map, remaining_ewallet):
    """
    创建讲义订单
    """
    # print(f"创建讲义订单, 讲义ID: {class_params.obj_id}")
    # print(f"textbook_objs_map:{textbook_objs_map}")
    relation_textbook_obj = textbook_objs_map.get(class_params.obj_id)
    if not relation_textbook_obj:
        return None, "讲义不存在"
    class_obj = class_objs_map.get(class_params.obj_id)
    if not class_obj:
        return None, "班级不存在"
        
    if relation_textbook_obj.sale_price >= 0:
        text_order_stu = await erp_order_student.create(db, commit=False, **{
            "class_id": class_params.obj_id,
            "order_class_type": OrderType.MATERIAL.value,
            "stu_id": params.stu_id,
            "student_state": StudentState.WAIT_PAY.value,
            "total_hours": 0,
            "complete_hours": 0,
            "is_renew": 0,
            "create_by": user.uid,
            "update_by": user.uid,
        })
        
        # 计算该订单使用的电子钱包金额
        sale_price_decimal = MoneyHandler.to_decimal(relation_textbook_obj.sale_price)
        buy_num_decimal = MoneyHandler.to_decimal(class_params.buy_num)
        order_amount = MoneyHandler.multiply(sale_price_decimal, buy_num_decimal)
        remaining_ewallet_decimal = MoneyHandler.to_decimal(remaining_ewallet)
        
        # 计算平台税
        platform_tax_rate = MoneyHandler.to_decimal(settings.CMB_PLATFORM_TAX)
        platform_tax = MoneyHandler.multiply(sale_price_decimal, platform_tax_rate)
        
        # 电子钱包使用金额不超过订单金额
        ewallet_amount = order_amount if MoneyHandler.is_less_than(order_amount, remaining_ewallet_decimal) else remaining_ewallet_decimal
        
        await erp_order.create(db, commit=False, **{
            "order_student_id": text_order_stu.id,
            "offer_id": offer_obj.id,
            "trade_way": params.trade_way,
            "class_price": relation_textbook_obj.sale_price,
            "lesson_price": relation_textbook_obj.sale_price,
            "total_receivable": order_amount,
            "total_income": order_amount,
            "platform_tax": platform_tax,
            "order_class_type": OrderType.MATERIAL.value,
            "order_state": 0,
            "campus_id": 1,
            'unit': class_params.unit,
            'buy_num': class_params.buy_num,
            "create_by": user.uid,
            "update_by": user.uid,
            "ewallet_money": ewallet_amount,  # 添加电子钱包金额
        })
        
        return order_amount, None
    return 0, None


async def create_course_order(db, class_params, offer_obj, params, user, class_objs_map,
                            discount_fixed_rate, universal_coupons, coupon_limit,
                            coupon_course_ids, coupon_course_amount, remaining_ewallet):
    """
    创建课程订单
    """
    class_id = class_params.obj_id
    class_obj = class_objs_map.get(class_id)
    if not class_obj:
        return None, "班级不存在"
        
    course_id = class_obj.course_id
    sale_price = class_obj.sale_price
    
    # 计算优惠
    discount, use_discount_id, discount_error = await calculate_discount(
        discount_fixed_rate, class_params, sale_price, universal_coupons,
        coupon_limit, course_id, coupon_course_ids, coupon_course_amount
    )
    if discount_error:
        return None, discount_error
        
    # 计算价格
    lesson_price, total_receivable, total_income, platform_tax = await calculate_order_price(
        class_obj, class_params, discount
    )
    
    # 计算总课节数
    if class_params.unit == 1:  # 按期收费
        total_hours = class_obj.planning_class_times
    else:  # 按次收费
        total_hours = class_params.buy_num
    
    # 创建学生订单
    order_student = await erp_order_student.create(db, commit=False, **{
        "class_id": class_id,
        "stu_id": params.stu_id,
        "student_state": StudentState.WAIT_PAY.value,
        "order_class_type": OrderType.COURSE.value,
        "total_hours": total_hours,
        "complete_hours": 0,
        "is_renew": 0,
        "create_by": user.uid,
        "update_by": user.uid,
        "is_online": params.trade_way,
    })
    
    # 计算该订单使用的电子钱包金额
    remaining_ewallet_decimal = MoneyHandler.to_decimal(remaining_ewallet)
    total_income_decimal = MoneyHandler.to_decimal(total_income)
    ewallet_amount = total_income_decimal if MoneyHandler.is_less_than(total_income_decimal, remaining_ewallet_decimal) else remaining_ewallet_decimal
    
    # 创建订单
    await erp_order.create(db, commit=False, **{
        "order_student_id": order_student.id,
        "offer_id": offer_obj.id,
        "trade_way": params.trade_way,
        "class_price": class_obj.sale_price,
        "lesson_price": lesson_price,
        "total_receivable": total_receivable,
        "total_income": total_income,
        "discount": discount,
        "discount_id": use_discount_id,
        "platform_tax": platform_tax,
        "order_state": 0,
        "campus_id": 1,
        "order_class_type": OrderType.COURSE.value,
        "insert_plan_index": params.insert_plan_index,
        "join_type": 1,
        "is_first_buy": 1,
        'unit': class_params.unit,
        'buy_num': class_params.buy_num,
        "create_by": user.uid,
        "update_by": user.uid,
        "ewallet_money": ewallet_amount,  # 添加电子钱包金额
    })
    
    return total_income, use_discount_id


# create_renew_order函数已移除，请使用独立的续费接口 /renew_order


async def create_offer_modules(params, db, redis_client, user, is_erp=1):
    """
    创建报价单模块
    Args:
        params: OfferParams - 订单参数
        db: AsyncSession - 数据库会话
        redis_client: Redis - Redis客户端
        user: UserDict - 用户信息
        is_erp: int - 是否是ERP系统创建订单 1:是 2:否
    Returns:
        tuple: (bool, int) - (是否成功, 报价单ID)
    """
    try:
        settings.logger.info(f"开始创建报价单, 学生ID: {params.stu_id}, 用户ID: {user.uid}")
        
        # 生成订单唯一标识并加锁
        order_unique_key = f"order_lock_{params.stu_id}_{datetime.now().strftime('%Y%m%d')}"
        if await redis_client.get(order_unique_key):
            settings.logger.warning(f"订单正在处理中，请勿重复提交, key: {order_unique_key}")
            return False, "订单正在处理中，请勿重复提交"
        await redis_client.set(order_unique_key, "1", ex=5)
        
        # 获取学生信息
        stu_obj = await erp_student.get_one(db, id=params.stu_id)
        
        # 验证基本参数
        is_valid, error_msg = await validate_order_params(params, stu_obj)
        if not is_valid:
            error_msg = f"订单参数验证失败: {error_msg}"
            settings.logger.error(error_msg)
            return False, error_msg
            
        # 验证重复班级订单
        is_valid, error_msg = await validate_duplicate_class_orders(db, params)
        if not is_valid:
            settings.logger.error(f"重复班级订单验证失败: {error_msg}")
            return False, error_msg
            
        # 验证电子钱包
        use_ewallet_residue, new_ewallet_residue, actual_ewallet_usage, ewallet_error = await validate_ewallet(
            params, stu_obj
        )
        if ewallet_error:
            error_msg = f"电子钱包验证失败: {ewallet_error}"
            settings.logger.error(error_msg)
            return False, error_msg
            
        # 验证班级人数是否超限
        is_valid, error_msg = await validate_class_capacity(db, params)
        if not is_valid:
            settings.logger.error(f"班级人数超限验证失败: {error_msg}")
            return False, error_msg
            
        # 获取班级和教材信息
        class_ids = [class_params.obj_id for class_params in params.order_class]
        class_objs = await class_info_by_classids(db, class_ids)
        class_objs_map = {class_obj.class_id: class_obj for class_obj in class_objs}
        
        textbook_ids = [class_obj.bound_textbook_id for class_obj in class_objs if
                       class_obj.bound_textbook_id and class_obj.bound_textbook_id > 0]
        textbook_objs = await erp_course_textbook.get_many(db)
        textbook_objs_map = {textbook_obj.id: textbook_obj for textbook_obj in textbook_objs}
        
        # 获取折扣信息
        discount_fixed = await erp_student_discount_fixed.get_one(db, stu_id=params.stu_id)
        discount_fixed_rate = discount_fixed.discount_rate if discount_fixed else 0
        
        # 获取优惠券信息
        universal_coupons = {}
        coupon_course_ids = set()
        coupon_course_amount = {}
        coupon_limit = {}
        
        if not discount_fixed:
            discount_coupons = await erp_student_discount_coupon.get_many(
                db,
                {"stu_id": params.stu_id, "status": DiscountStatus.AVAILABLE.value}
            )
            if discount_coupons:
                coupon_limit = {i.id: MoneyHandler.to_decimal(i.limit_money) for i in discount_coupons}
                expired_coupons_ids = []
                for discount_coupon in discount_coupons:
                    if discount_coupon.expired_time < datetime.now():
                        discount_coupon.status = DiscountStatus.EXPIRED.value
                        expired_coupons_ids.append(discount_coupon.id)
                discount_coupons = [i for i in discount_coupons if i.id not in expired_coupons_ids]
                universal_coupons = {i.id: i for i in discount_coupons if i.is_universal > 0}
                course_coupons = await erp_student_coupon_course.get_many(db, raw=[
                    ErpStudentCouponCourse.coupon_id.in_([i.id for i in discount_coupons if i.is_universal == 0])
                ])
                coupon_course_ids = {i.course_id for i in course_coupons}
                coupon_course_amount = {i.id: i.amount for i in discount_coupons if i.is_universal == 0}
                
        # 创建报价单
        order_no = datetime.now().strftime('%Y%m%d%H%M%S%f')[:17]+ (str(params.stu_id) or '0000') +  str(random.randint(1000, 9999))
        offer_obj = await erp_order_offer.create(db, commit=False, **{
            "name": f"{order_no}报价单",
            "effective_start": params.effective_start,
            "effective_end": params.effective_end,
            "total_original_price": params.total_original_price,
            "total_sale_price": 0,
            "total_discount_price": params.total_discount_price,
            "integral_money": params.integral_money,
            "ewallet_money": params.ewallet_money,
            "inner_remark": params.inner_remark,
            "out_remark": params.out_remark,
            "cash_voucher": params.cash_voucher,
            "cash_pay_remark": params.cash_pay_remark,
            "create_by": user.uid,
            "update_by": user.uid,
            "campus_id": 1,
            "order_no": order_no,
            "offer_type": OfferType.NEW_ORDER.value,
            "offer_state": OfferState.NOT_PAY.value,
            "stu_id": params.stu_id,
            "order_from": 1 if is_erp else 2,
            "cash_receive_way": params.cash_receive_way,  # 添加现金收款方式
        })
        
        # 处理订单
        real_sale_price = Decimal('0.00')
        real_original_price = Decimal('0.00')  # 添加实际原价计算
        use_discount_id = 0
        remaining_ewallet = params.ewallet_money  # 剩余可用的电子钱包金额
        
        for class_params in params.order_class:
            if class_params.buy_type == 2:  # 讲义单
                income, error = await create_material_order(
                    db, class_params, offer_obj, params, user,
                    class_objs_map, textbook_objs_map, remaining_ewallet
                )
                if error:
                    return False, error
                real_sale_price = MoneyHandler.add(real_sale_price, income)
                
                # 计算讲义原价（讲义原价 = 售价 * 购买数量）
                relation_textbook_obj = textbook_objs_map.get(class_params.obj_id)
                if relation_textbook_obj and relation_textbook_obj.sale_price >= 0:
                    material_original_price = MoneyHandler.multiply(
                        MoneyHandler.to_decimal(relation_textbook_obj.sale_price),
                        MoneyHandler.to_decimal(class_params.buy_num)
                    )
                    real_original_price = MoneyHandler.add(real_original_price, material_original_price)
                    settings.logger.info(
                        f"讲义{class_params.obj_id}原价计算: "
                        f"单价={relation_textbook_obj.sale_price}, "
                        f"数量={class_params.buy_num}, "
                        f"小计={material_original_price}"
                    )
                
                # 更新剩余电子钱包金额
                if remaining_ewallet > 0:
                    remaining_ewallet = max(0, remaining_ewallet - MoneyHandler.to_float(income))
                
            elif class_params.buy_type == 1:  # 课程单
                income, discount_id = await create_course_order(
                    db, class_params, offer_obj, params, user,
                    class_objs_map, discount_fixed_rate,
                    universal_coupons, coupon_limit,
                    coupon_course_ids, coupon_course_amount,
                    remaining_ewallet
                )
                if income is None:
                    return False, discount_id
                real_sale_price = MoneyHandler.add(real_sale_price, income)
                use_discount_id = discount_id
                
                # 计算课程原价
                class_obj = class_objs_map.get(class_params.obj_id)
                if class_obj:
                    if class_params.unit == 1:  # 按期收费
                        course_original_price = MoneyHandler.to_decimal(class_obj.original_price)
                        settings.logger.info(
                            f"课程{class_params.obj_id}原价计算(按期收费): "
                            f"原价={class_obj.original_price}, "
                            f"小计={course_original_price}"
                        )
                    else:  # 按次收费
                        # 原价 = 课程原价 ÷ 总课节数 × 购买课节数
                        original_price_decimal = MoneyHandler.to_decimal(class_obj.original_price)
                        planning_class_times_decimal = MoneyHandler.to_decimal(class_obj.planning_class_times)
                        buy_num_decimal = MoneyHandler.to_decimal(class_params.buy_num)
                        
                        # 检查总课节数是否为0，避免除零错误
                        if MoneyHandler.is_equal(planning_class_times_decimal, 0):
                            error_msg = f"课程{class_params.obj_id}的总课节数为0，无法计算按次收费价格"
                            settings.logger.error(error_msg)
                            return False, error_msg
                        
                        lesson_original_price = MoneyHandler.divide(original_price_decimal, planning_class_times_decimal)
                        course_original_price = MoneyHandler.multiply(lesson_original_price, buy_num_decimal)
                        settings.logger.info(
                            f"课程{class_params.obj_id}原价计算(按次收费): "
                            f"总原价={class_obj.original_price}, "
                            f"总课节数={class_obj.planning_class_times}, "
                            f"课节原价={lesson_original_price}, "
                            f"购买数量={class_params.buy_num}, "
                            f"小计={course_original_price}"
                        )
                    print('---',course_original_price)
                    real_original_price = MoneyHandler.add(real_original_price, course_original_price)
                
                # 更新剩余电子钱包金额
                if remaining_ewallet > 0:
                    remaining_ewallet = max(0, remaining_ewallet - MoneyHandler.to_float(income))
                
            elif class_params.buy_type == 3:  # 续费订单（已移除，请使用独立的续费接口 /renew_order）
                return False, "续费订单请使用独立的续费接口 /renew_order"
            else:
                return False, "无效的购买类型"
        
        # 校验总原价
        real_original_price_rounded = MoneyHandler.to_decimal(real_original_price)
        params_total_original_price_rounded = MoneyHandler.to_decimal(params.total_original_price)
        
        # 记录原价计算日志
        settings.logger.info(
            f"报价单{offer_obj.id}原价计算完成 - "
            f"前端传入原价: {params.total_original_price}, "
            f"后端计算原价: {real_original_price}, "
            f"订单数量: {len(params.order_class)}"
        )
        
        if not MoneyHandler.is_equal(real_original_price_rounded, params_total_original_price_rounded):
            error_msg = f"前端传入总原价{params.total_original_price}与实际计算原价{real_original_price}不一致，请检查"
            settings.logger.error(f"订单原价验证失败: {error_msg}")
            return False, error_msg
        
        # 更新报价单的实际总原价
        offer_obj.total_original_price = real_original_price_rounded
        settings.logger.info(f"报价单{offer_obj.id}原价校验通过")
        
        # 计算电子钱包实际使用金额
        actual_ewallet_usage = Decimal('0.00')
        if MoneyHandler.is_greater_than(params.ewallet_money, 0):
            # 重新验证电子钱包（考虑实际订单金额）
            use_ewallet_residue, new_ewallet_residue, actual_ewallet_usage, ewallet_error = await validate_ewallet(
                params, stu_obj, real_sale_price
            )
            if ewallet_error:
                settings.logger.error(f"电子钱包验证失败: {ewallet_error}")
                return False, ewallet_error
                
            # 计算实际订单金额
            real_sale_price = MoneyHandler.subtract(real_sale_price, actual_ewallet_usage)
            
        # 验证总金额
        real_sale_price_rounded = MoneyHandler.to_decimal(real_sale_price)
        params_total_sale_price_rounded = MoneyHandler.to_decimal(params.total_sale_price)
        if not MoneyHandler.is_equal(real_sale_price_rounded, params_total_sale_price_rounded) and not MoneyHandler.is_equal(params_total_sale_price_rounded, 0):
            error_msg = f"前端传入总售价{params.total_sale_price}与实际计算售价{real_sale_price}不一致，请检查"
            settings.logger.error(f"订单金额验证失败: {error_msg}")
            return False, error_msg
                
        # 更新电子钱包
        if MoneyHandler.is_greater_than(params.ewallet_money, 0) and MoneyHandler.is_greater_than(actual_ewallet_usage, 0):
            original_wallet_amount = MoneyHandler.to_decimal(stu_obj.stu_wallet_amount)
            stu_obj.stu_wallet_amount = new_ewallet_residue
            settings.logger.info(
                f"学生{stu_obj.id}电子钱包支付订单，使用金额: {actual_ewallet_usage}, "
                f"原余额: {original_wallet_amount}, 剩余金额: {stu_obj.stu_wallet_amount}"
            )
            
        # 更新优惠券状态
        if use_discount_id > 0:
            await erp_student_discount_coupon.update_one(
                db, use_discount_id,
                {"status": DiscountStatus.USED.value},
                commit=False
            )
            settings.logger.info(f"使用优惠券: {use_discount_id}, 订单ID: {offer_obj.id}")
            
        # 更新报价单金额
        offer_obj.total_sale_price = real_sale_price
        offer_obj.ewallet_money = actual_ewallet_usage

        # 判断是否为现金收款
        is_cash_payment = params.cash_receive_way == 1
        
        if MoneyHandler.is_less_than(real_sale_price, 0) or MoneyHandler.is_equal(real_sale_price, 0) or is_cash_payment:
            # 订单金额为0或者是现金收款，直接设置为已支付状态
            if is_cash_payment:
                settings.logger.info(f"现金收款订单，自动设置为已支付状态, 报价单ID: {offer_obj.id}")
            else:
                settings.logger.info(f"订单金额为0，自动设置为已支付状态, 报价单ID: {offer_obj.id}")
            
            # 更新报价单状态
            offer_obj.offer_state = OfferState.PAID.value
            
            # 获取并更新相关订单状态
            orders = await erp_order.get_many(db, {"offer_id": offer_obj.id})
            for order in orders:
                order.order_state = OrderState.PAID.value
                
            # 获取并更新相关学生订单状态（入班）
            if orders:
                order_student_ids = [order.order_student_id for order in orders]
                order_students = await erp_order_student.get_many(db, raw=[
                    ErpOrderStudent.id.in_(order_student_ids)
                ])
                
                for order_student in order_students:
                    order_student.student_state = StudentState.NORMAL.value  # 设置为正常上课状态（入班）
                    settings.logger.info(f"学生入班成功: 学生ID={order_student.stu_id}, 班级ID={order_student.class_id}, 订单学生ID={order_student.id}")
                    
                    # 异步推送老师名片给家长
                    try:
                        from app_teach.modules import auto_push_teacher_card_after_enroll
                        asyncio.create_task(auto_push_teacher_card_after_enroll(
                            order_student_id=order_student.id,
                            stu_id=order_student.stu_id,
                            class_id=order_student.class_id
                        ))
                        settings.logger.info(f"已启动老师名片推送任务: 学生ID={order_student.stu_id}, 班级ID={order_student.class_id}")
                    except Exception as push_error:
                        settings.logger.error(f"启动老师名片推送任务失败: {str(push_error)}")
            
            # 如果是现金收款，需要创建收入单据并发起工作流
            if is_cash_payment and MoneyHandler.is_greater_than(real_sale_price, 0):
                try:
                    receipt_id = await create_cash_income_receipt_and_workflow(db, offer_obj, orders, user, stu_obj)
                    settings.logger.info(f"现金收款单据创建成功，单据ID: {receipt_id}, 报价单ID: {offer_obj.id}")
                except Exception as e:
                    settings.logger.error(f"创建现金收款单据失败: {str(e)}")
                    # 现金收款单据创建失败不影响订单创建，只记录错误日志
            
            # 推送支付成功通知
            asyncio.create_task(push_order_modules(offer_obj.id, is_paid=True))
            
        # 提交事务
        offer_id = copy.deepcopy(offer_obj.id)
        settings.logger.info(f"报价单创建成功, ID: {offer_id}, 总金额: {real_sale_price}, 现金收款: {is_cash_payment}")
        
        # 推送订单
        if params.is_push > 0 and not is_cash_payment:  # 现金收款不需要推送订单通知，已经直接入班
            settings.logger.info(f"开始异步推送订单通知, 报价单ID: {offer_id}")
            asyncio.create_task(push_order_modules(offer_id))
            
        return True, offer_id
        
    except Exception as e:
        settings.logger.error(f"创建报价单失败: {str(e)}", exc_info=True)
        return False, str(e)

async def update_order_success(db, offer_id):
    """
    订单支付成功后的处理
    """
    try:
        settings.logger.info(f"开始处理订单支付成功, 报价单ID: {offer_id}")
        
        offer_obj = await erp_order_offer.get_one(db, id=offer_id)
        if not offer_obj:
            settings.logger.error(f"报价单不存在: {offer_id}")
            return False
            
        orders = await erp_order.get_many(db, {"offer_id": offer_id})
        if not orders:
            settings.logger.error(f"未找到关联订单: {offer_id}")
            return False
            
        order_student_ids = [order.order_student_id for order in orders]
        order_students = await erp_order_student.get_many(db, raw=[
            ErpOrderStudent.id.in_(order_student_ids)
        ])
        
        # 更新状态
        offer_obj.offer_state = OfferState.PAID.value
        for order in orders:
            order.order_state = OrderState.PAID.value
            
        # 处理续费订单的课节数更新
        for order in orders:
            if order.join_type == 3:  # 续费订单
                # 找到对应的学生订单
                order_student = next((os for os in order_students if os.id == order.order_student_id), None)
                if order_student:
                    # 将续费课节数加到原有课节数上
                    current_total_hours = MoneyHandler.to_decimal(order_student.total_hours or 0)
                    new_total_hours = MoneyHandler.add(current_total_hours, MoneyHandler.to_decimal(order.buy_num))
                    order_student.total_hours = new_total_hours
                    settings.logger.info(f"学生续费成功: 学生ID={order_student.stu_id}, 班级ID={order_student.class_id}, 原课节数={current_total_hours}, 续费课节数={order.buy_num}, 新课节数={new_total_hours}")
        
        # 更新学生订单状态
        for order_student in order_students:
            order_student.student_state = StudentState.NORMAL.value
            
            # 异步推送老师名片给家长（只对新入班学生推送，续费学生不需要）
            try:
                # 检查是否有非续费订单（新入班学生）
                has_new_order = any(order.join_type != 3 for order in orders if order.order_student_id == order_student.id)
                if has_new_order:
                    from app_teach.modules import auto_push_teacher_card_after_enroll
                    asyncio.create_task(auto_push_teacher_card_after_enroll(
                        order_student_id=order_student.id,
                        stu_id=order_student.stu_id,
                        class_id=order_student.class_id
                    ))
                    settings.logger.info(f"已启动老师名片推送任务: 学生ID={order_student.stu_id}, 班级ID={order_student.class_id}")
            except Exception as push_error:
                settings.logger.error(f"启动老师名片推送任务失败: {str(push_error)}")
            
        await db.commit()
        settings.logger.info(f"订单状态更新成功, 报价单ID: {offer_id}")
        
        # 异步推送支付成功通知
        settings.logger.info(f"开始异步推送支付成功通知, 报价单ID: {offer_id}")
        asyncio.create_task(push_order_modules(offer_id, is_paid=True))
        
        return True
    except Exception as e:
        settings.logger.error(f"处理订单支付失败: {str(e)}", exc_info=True)
        await db.rollback()
        return False


async def validate_offer_query_params(page: int = None, page_size: int = None,
                               create_time_start: datetime = None, create_time_end: datetime = None,
                               pay_time_start: datetime = None, pay_time_end: datetime = None,
                               is_push: int = None, offer_state: int = None) -> tuple:
    """
    验证报价单查询参数
    返回: (is_valid: bool, error_msg: str)
    """
    if page is not None and page < 1:
        return False, "页码必须大于0"
    if page_size is not None and page_size < 1:
        return False, "每页数量必须大于0"
        
    if create_time_start and create_time_end and create_time_start > create_time_end:
        return False, "创建开始时间不能大于结束时间"
    if pay_time_start and pay_time_end and pay_time_start > pay_time_end:
        return False, "支付开始时间不能大于结束时间"
        
    if is_push is not None and is_push not in [0, 1]:
        return False, "推送状态参数无效"
    if offer_state is not None and offer_state not in [1, 2, 3, 4, 5, 6]:
        return False, "订单状态参数无效"
    # if bind_official is not None and bind_official not in [0, 1]:
    #     return False, "公众号绑定状态参数无效"
        
    return True, ""


async def build_offer_query_conditions(order_no: str = None, offer_state: int = None,
                                     is_push: int = None,
                                     create_time_start: datetime = None, create_time_end: datetime = None,
                                     pay_time_start: datetime = None, pay_time_end: datetime = None,
                                     stu_name: str = None,
                                     rule_id: int = None) -> tuple:
    """
    构建报价单查询条件
    返回: (conditions: dict, raw_conditions: list)
    """
    conditions = {}
    if order_no:
        conditions["order_no"] = order_no
    if offer_state is not None:
        conditions["offer_state"] = offer_state
    # if bind_official is not None:
    #     conditions["bind_official"] = bind_official
    if is_push is not None:
        conditions["is_push"] = is_push

    raw_conditions = []
    if create_time_start:
        raw_conditions.append(ErpOrderOffer.create_time >= create_time_start)
    if create_time_end:
        raw_conditions.append(ErpOrderOffer.create_time <= create_time_end)
    if pay_time_start:
        raw_conditions.append(ErpFinanceTradePayment.create_time >= pay_time_start)
    if pay_time_end:
        raw_conditions.append(ErpFinanceTradePayment.create_time <= pay_time_end)
    if stu_name:
        raw_conditions.append(ErpStudent.stu_name.ilike(f"%{stu_name}%"))
    if rule_id:
        raw_conditions.append(ErpOrderOffer.rule_id == rule_id)
    return conditions, raw_conditions


async def get_offer_count(db: AsyncSession, conditions: dict, raw_conditions: list,
                         stu_name: str = None, pay_time_start: datetime = None,
                         pay_time_end: datetime = None) -> int:
    """
    获取报价单总数
    """
    base_conditions = []
    if conditions:
        for key, value in conditions.items():
            base_conditions.append(getattr(ErpOrderOffer, key) == value)
    if raw_conditions:
        base_conditions.extend(raw_conditions)
        
    count_stmt = select(func.count(ErpOrderOffer.id)).select_from(ErpOrderOffer)
    if stu_name:
        count_stmt = count_stmt.join(ErpStudent, ErpOrderOffer.stu_id == ErpStudent.id)
    if pay_time_start or pay_time_end:
        count_stmt = count_stmt.join(ErpFinanceTradePayment, ErpOrderOffer.id == ErpFinanceTradePayment.offer_id)
    count_stmt = count_stmt.where(and_(*base_conditions))
    
    result = await db.execute(count_stmt)
    return result.scalar() or 0


async def process_offer_data(offers: list, db: AsyncSession) -> list:
    """
    处理报价单数据，添加课程信息、退费信息和学生微信绑定数据
    """
    if not offers:
        return []
        
    offer_ids = [offer.id for offer in offers]
    
    order_classes = await order_class_by_offerids(db, offer_ids)
    order_classes_map = defaultdict(list)
    for order_class in order_classes:
        order_classes_map[order_class.offer_id].append(order_class)

    refund_data = await erp_finance_trade_refund.get_many(db, raw=[
        ErpFinanceTradeRefund.offer_id.in_(offer_ids)
    ])
    refund_map = defaultdict(list)
    for refund in refund_data:
        refund_map[refund.offer_id].append(refund)

    # 查询每个学生的微信绑定数据
    student_ids = [offer.stu_id for offer in offers if hasattr(offer, 'stu_id') and offer.stu_id]
    student_wechat = {}
    if student_ids:
        wechat_stmt = (
            select(
                ErpStudentWechat.stu_id,
                ErpStudentWechat.id,
                ErpStudentWechat.wechat_open_id
            )
            .where(
                ErpStudentWechat.stu_id.in_(student_ids),
                ErpStudentWechat.disable == 0
            )
        )
        wechat_result = await db.execute(wechat_stmt)
        wechat_data = wechat_result.fetchall()
        
        # 将微信数据组织成以stu_id为键的字典
        for wechat in wechat_data:
            if wechat.stu_id not in student_wechat:
                student_wechat[wechat.stu_id] = []
            student_wechat[wechat.stu_id].append({
                'id': wechat.id,
                'wechat_open_id': wechat.wechat_open_id
            })

    offers_data = []
    for offer in offers:
        offer_dict = dict(offer)
        offer_dict["order_classes"] = order_classes_map.get(offer.id, [])
        offer_dict["refunds"] = refund_map.get(offer.id, [])
        # 添加学生微信绑定数据
        if hasattr(offer, 'stu_id') and offer.stu_id:
            offer_dict["wechat_list"] = student_wechat.get(offer.stu_id, [])
        else:
            offer_dict["wechat_list"] = []
        offers_data.append(offer_dict)
        
    return offers_data


async def query_offer_data(db: AsyncSession, page: int = None, page_size: int = None,
                          order_no: str = None, create_time_start: datetime = None,
                          create_time_end: datetime = None, pay_time_start: datetime = None,
                          pay_time_end: datetime = None, stu_name: str = None,
                          is_push: int = None, offer_state: int = None,
                          rule_id: int = None,
                          # bind_official: int = None
                           ) -> tuple:
    """
    查询报价单数据
    返回: (offers_data: list, count: int)
    """
    try:
        is_valid, error_msg = await validate_offer_query_params(
            page, page_size, create_time_start, create_time_end,
            pay_time_start, pay_time_end, is_push, offer_state,
                  # bind_official
        )
        if not is_valid:
            raise ValueError(error_msg)

        conditions, raw_conditions = await build_offer_query_conditions(
            order_no, offer_state,
            is_push,
            create_time_start, create_time_end,
            pay_time_start, pay_time_end, stu_name,
            rule_id
        )
        # print(f"conditions: {conditions}")
        # print(f"raw_conditions: {raw_conditions}")
        offers = await offer_data(db, page, page_size, conditions=conditions, raw_conditions=raw_conditions)
        if not offers:
            return [], 0

        offers_data = await process_offer_data(offers, db)

        count = await get_offer_count(db, conditions, raw_conditions, stu_name, pay_time_start, pay_time_end)

        return offers_data, count
        
    except Exception as e:
        settings.logger.error(f"查询报价单失败: {str(e)}")
        raise e

async def validate_and_lock_coupon(db, redis_client, discount_id, stu_id, sale_price):
    """
    验证优惠券状态并加锁
    """
    if not discount_id:
        return True, None
        
    coupon_lock_key = f"coupon_lock_{discount_id}"
    if await redis_client.get(coupon_lock_key):
        return False, f"优惠券{discount_id}正在使用中，请稍后再试"
    await redis_client.set(coupon_lock_key, "1", ex=30)
    
    try:
        coupon = await erp_student_discount_coupon.get_one(db, id=discount_id)
        if not coupon:
            return False, f"优惠券不存在: {discount_id}"
        if coupon.stu_id != stu_id:
            return False, f"优惠券不属于该学生: {discount_id}"
        if coupon.status != DiscountStatus.AVAILABLE.value:
            return False, f"优惠券状态无效: {discount_id}"
        if coupon.expired_time < datetime.now():
            await erp_student_discount_coupon.update_one(db, coupon.id, {"status": DiscountStatus.EXPIRED.value}, commit=False)
            return False, f"优惠券已过期: {discount_id}"
        if coupon.limit_money > sale_price:
            return False, f"优惠券使用门槛不满足，需要订单金额大于{coupon.limit_money}"
        return True, None
    finally:
        await redis_client.delete(coupon_lock_key)

async def calculate_discount(discount_fixed_rate, class_params, sale_price, universal_coupons, 
                           coupon_limit, course_id, coupon_course_ids, coupon_course_amount):
    """
    计算优惠金额
    """
    discount = Decimal('0.00')
    use_discount_id = 0
    
    if discount_fixed_rate:
        sale_price_decimal = MoneyHandler.to_decimal(sale_price)
        discount_rate_decimal = MoneyHandler.to_decimal(discount_fixed_rate)
        discount = MoneyHandler.multiply(sale_price_decimal, discount_rate_decimal)
    elif class_params.discount_id and class_params.discount_id > 0:
        if class_params.discount_id in universal_coupons:
            sale_price_decimal = MoneyHandler.to_decimal(sale_price)
            if MoneyHandler.is_greater_than(coupon_limit[class_params.discount_id], sale_price_decimal):
                return None, None, f"通用优惠券金额不足, limit_money:{coupon_limit[class_params.discount_id]}, sale_price:{sale_price_decimal}"
            discount = MoneyHandler.to_decimal(universal_coupons[class_params.discount_id].amount)
            use_discount_id = class_params.discount_id
        elif course_id in coupon_course_ids:
            if class_params.discount_id not in coupon_course_amount:
                return None, None, f"无效的优惠券ID: {class_params.discount_id}"
            sale_price_decimal = MoneyHandler.to_decimal(sale_price)
            if MoneyHandler.is_greater_than(coupon_limit[class_params.discount_id], sale_price_decimal):
                return None, None, f"课程优惠券金额不足, course_id:{course_id}, limit_money:{coupon_course_amount[course_id]}, sale_price:{sale_price_decimal}"
            discount = MoneyHandler.to_decimal(coupon_course_amount[course_id])
            use_discount_id = class_params.discount_id
            
        if MoneyHandler.is_greater_than(discount, 0) and not MoneyHandler.is_equal(discount, class_params.discount_price):
            return None, None, f"课程优惠券金额不一致, course_id:{course_id}, discount:{discount}, discount_price:{class_params.discount_price}"
            
    return discount, use_discount_id, None

async def calculate_order_price(class_obj, class_params, discount):
    """
    计算订单价格
    """
    sale_price_decimal = MoneyHandler.to_decimal(class_obj.sale_price)
    
    if class_params.unit == 1:  # 按期收费
        lesson_price = sale_price_decimal
    else:
        planning_class_times_decimal = MoneyHandler.to_decimal(class_obj.planning_class_times)
        
        # 检查总课节数是否为0，避免除零错误
        if MoneyHandler.is_equal(planning_class_times_decimal, 0):
            raise ValueError(f"班级{class_obj.id}的总课节数为0，无法计算按次收费价格")
        
        lesson_price = MoneyHandler.divide(sale_price_decimal, planning_class_times_decimal)
        
    buy_num_decimal = MoneyHandler.to_decimal(class_params.buy_num)
    total_receivable = MoneyHandler.multiply(lesson_price, buy_num_decimal)
    discount_decimal = MoneyHandler.to_decimal(discount)
    total_income = MoneyHandler.subtract(total_receivable, discount_decimal)
    
    platform_tax_rate = MoneyHandler.to_decimal(settings.CMB_PLATFORM_TAX)
    platform_tax = MoneyHandler.multiply(total_income, platform_tax_rate)
    
    # 记录价格计算日志
    settings.logger.info(
        f"课程{class_obj.class_id}价格计算 - "
        f"总售价:{sale_price_decimal}, "
        f"优惠金额:{discount_decimal}, "
        f"总应收:{total_receivable}, "
        f"总实收:{total_income}, "
        f"课节单价:{lesson_price}, "
        f"购买数量:{class_params.buy_num}"
    )
    
    return lesson_price, total_receivable, total_income, platform_tax



async def start_refund_workflow(workflow_id, receipt_id, user_id):
    """
    自动发起退费工作流
    """
    try:
        async for db in get_default_db():
            # 导入必要的模块
            from public_api.modules import start_workflow

            # 发起工作流
            instance_id = await start_workflow(db, workflow_id, receipt_id, user_id)
            
            # 提交事务
            await db.commit()
            
            return instance_id
    except Exception as e:
        settings.logger.error(f"自动发起退费工作流失败: {str(e)}")
        raise e


async def create_renew_course_order(db, class_obj, order_student_obj, renew_lessons, 
                                  best_discount, best_coupon_id, actual_ewallet_usage, 
                                  offer_obj, user):
    """
    创建续费课程订单（不创建新的order_student，只创建order记录）
    """
    # 按次收费计算：课节单价 = 班级售价 / 总课节数
    lesson_sale_price = MoneyHandler.divide(
        MoneyHandler.to_decimal(class_obj.sale_price), 
        MoneyHandler.to_decimal(class_obj.planning_class_times)
    )
    
    # 续费总售价 = 课节单价 × 续费课节数
    total_sale_price = MoneyHandler.multiply(lesson_sale_price, MoneyHandler.to_decimal(renew_lessons))
    
    # 计算价格
    total_receivable = total_sale_price
    total_income = MoneyHandler.subtract(total_sale_price, best_discount)
    
    # 计算平台税
    platform_tax_rate = MoneyHandler.to_decimal(settings.CMB_PLATFORM_TAX or 0)
    platform_tax = MoneyHandler.multiply(total_income, platform_tax_rate)
    
    # 计算该订单使用的电子钱包金额
    ewallet_amount = actual_ewallet_usage if MoneyHandler.is_less_than(actual_ewallet_usage, total_income) else total_income
    
    # 创建续费订单（关联到现有的order_student）
    await erp_order.create(db, commit=False, **{
        "order_student_id": order_student_obj.id,  # 使用现有的order_student
        "offer_id": offer_obj.id,
        "trade_way": 2,  # 线上交易
        "class_price": class_obj.sale_price,
        "lesson_price": lesson_sale_price,
        "total_receivable": total_receivable,
        "total_income": total_income,
        "discount": best_discount,
        "discount_id": best_coupon_id if best_coupon_id else 0,
        "platform_tax": platform_tax,
        "order_state": 0,
        "campus_id": 1,
        "order_class_type": OrderType.COURSE.value,
        "insert_plan_index": 0,
        "join_type": 3,  # 续费订单
        "is_first_buy": 0,
        'unit': 2,  # 按次收费
        'buy_num': renew_lessons,
        "create_by": user.uid,
        "update_by": user.uid,
        "ewallet_money": ewallet_amount,  # 添加电子钱包金额
    })
    
    return total_income


async def create_renew_offer(order_student_id: int, renew_lessons: int, db: AsyncSession, 
                           redis_client, user, ewallet_money: float = 0,
                           cash_receive_way: int = 0, cash_voucher: str = None,
                           cash_pay_remark: str = None, inner_remark: str = None,
                           out_remark: str = None, is_push: int = 1):
    """
    创建续费报价单
    Args:
        order_student_id: 学生订单ID
        renew_lessons: 续费课节数
        db: 数据库会话
        redis_client: Redis客户端
        user: 用户信息
        ewallet_money: 电子钱包使用金额
        cash_receive_way: 现金收款方式
        cash_voucher: 现金收款凭证
        cash_pay_remark: 现金收款备注
        inner_remark: 内部备注
        out_remark: 外部备注
        is_push: 是否推送
    Returns:
        tuple: (是否成功, 报价单ID或错误信息)
    """
    try:
        settings.logger.info(f"开始创建续费报价单, 学生订单ID: {order_student_id}, 续费课节数: {renew_lessons}")
        
        # 获取学生订单信息
        order_student_obj = await erp_order_student.get_one(db, id=order_student_id)
        if not order_student_obj:
            return False, f"学生订单不存在: {order_student_id}"
        
        # 检查学生订单状态
        if order_student_obj.student_state != StudentState.NORMAL.value:
            return False, f"学生订单状态不允许续费，当前状态: {order_student_obj.student_state}"
        
        # 获取班级信息（包含课程价格信息）
        class_objs = await class_info_by_classids(db, [order_student_obj.class_id])
        if not class_objs:
            return False, f"班级不存在: {order_student_obj.class_id}"
        class_obj = class_objs[0]
            
        # 获取学生信息
        stu_obj = await erp_student.get_one(db, id=order_student_obj.stu_id)
        if not stu_obj:
            return False, f"学生不存在: {order_student_obj.stu_id}"
        
        # 生成订单唯一标识并加锁
        order_unique_key = f"renew_order_lock_{order_student_obj.stu_id}_{datetime.now().strftime('%Y%m%d')}"
        if await redis_client.get(order_unique_key):
            settings.logger.warning(f"续费订单正在处理中，请勿重复提交, key: {order_unique_key}")
            return False, "续费订单正在处理中，请勿重复提交"
        await redis_client.set(order_unique_key, "1", ex=10)
        
        try:
            # 验证续费课节数
            if renew_lessons <= 0:
                return False, "续费课节数必须大于0"
            
            # 获取学生的固定折扣信息
            discount_fixed = await erp_student_discount_fixed.get_one(db, stu_id=order_student_obj.stu_id)
            discount_fixed_rate = discount_fixed.discount_rate if discount_fixed else 0
            
            # 获取优惠券信息（只有没有固定折扣的学生才处理优惠券）
            universal_coupons = {}
            coupon_course_ids = set()
            coupon_course_amount = {}
            coupon_limit = {}
            best_discount = Decimal('0.00')
            best_coupon_id = None
            
            if not discount_fixed:
                discount_coupons = await erp_student_discount_coupon.get_many(
                    db,
                    {"stu_id": order_student_obj.stu_id, "status": DiscountStatus.AVAILABLE.value}
                )
                if discount_coupons:
                    coupon_limit = {i.id: MoneyHandler.to_decimal(i.limit_money) for i in discount_coupons}
                    # 过滤过期优惠券
                    expired_coupons_ids = []
                    for discount_coupon in discount_coupons:
                        if discount_coupon.expired_time < datetime.now():
                            discount_coupon.status = DiscountStatus.EXPIRED.value
                            expired_coupons_ids.append(discount_coupon.id)
                    
                    discount_coupons = [i for i in discount_coupons if i.id not in expired_coupons_ids]
                    universal_coupons = {i.id: i for i in discount_coupons if i.is_universal > 0}
                    
                    course_coupons = await erp_student_coupon_course.get_many(db, raw=[
                        ErpStudentCouponCourse.coupon_id.in_([i.id for i in discount_coupons if i.is_universal == 0])
                    ])
                    coupon_course_ids = {i.course_id for i in course_coupons}
                    coupon_course_amount = {i.id: i.amount for i in discount_coupons if i.is_universal == 0}
            
            # 计算续费价格
            course_id = class_obj.course_id
            
            # 按次收费计算：课节单价 = 班级售价 / 总课节数
            if class_obj.planning_class_times <= 0:
                return False, f"班级总课节数不能为0，班级ID: {class_obj.class_id}"
            
            lesson_sale_price = MoneyHandler.divide(
                MoneyHandler.to_decimal(class_obj.sale_price), 
                MoneyHandler.to_decimal(class_obj.planning_class_times)
            )
            
            # 续费总售价 = 课节单价 × 续费课节数
            total_sale_price = MoneyHandler.multiply(lesson_sale_price, MoneyHandler.to_decimal(renew_lessons))
            
            # 计算优惠金额
            if discount_fixed_rate > 0:
                # 有固定折扣，直接应用
                best_discount = MoneyHandler.multiply(total_sale_price, MoneyHandler.to_decimal(discount_fixed_rate))
                settings.logger.info(f"学生{order_student_obj.stu_id}续费应用固定折扣{discount_fixed_rate*100}%，原价{total_sale_price}，优惠{best_discount}")
            elif universal_coupons or coupon_course_ids:
                # 没有固定折扣，查找最优优惠券
                
                # 检查通用优惠券
                for coupon_id, coupon in universal_coupons.items():
                    if MoneyHandler.is_greater_than(total_sale_price, coupon_limit[coupon_id]) or MoneyHandler.is_equal(total_sale_price, coupon_limit[coupon_id]):
                        coupon_discount = MoneyHandler.to_decimal(coupon.amount)
                        # 优惠券金额不能超过订单金额
                        if MoneyHandler.is_greater_than(coupon_discount, total_sale_price):
                            coupon_discount = total_sale_price
                        if MoneyHandler.is_greater_than(coupon_discount, best_discount):
                            best_discount = coupon_discount
                            best_coupon_id = coupon_id
                
                # 检查课程专用优惠券
                if course_id in coupon_course_ids:
                    for coupon in discount_coupons:
                        if coupon.is_universal == 0:
                            # 检查该优惠券是否适用于当前课程
                            course_coupon_exists = any(
                                cc.course_id == course_id and cc.coupon_id == coupon.id 
                                for cc in course_coupons
                            )
                            if course_coupon_exists:
                                if MoneyHandler.is_greater_than(total_sale_price, coupon_limit[coupon.id]) or MoneyHandler.is_equal(total_sale_price, coupon_limit[coupon.id]):
                                    coupon_discount = MoneyHandler.to_decimal(coupon.amount)
                                    # 优惠券金额不能超过订单金额
                                    if MoneyHandler.is_greater_than(coupon_discount, total_sale_price):
                                        coupon_discount = total_sale_price
                                    if MoneyHandler.is_greater_than(coupon_discount, best_discount):
                                        best_discount = coupon_discount
                                        best_coupon_id = coupon.id
                
                if MoneyHandler.is_greater_than(best_discount, 0) and best_coupon_id:
                    settings.logger.info(f"学生{order_student_obj.stu_id}续费应用优惠券{best_coupon_id}，优惠金额{best_discount}，原价{total_sale_price}")
            
            # 计算优惠后价格
            final_sale_price = MoneyHandler.subtract(total_sale_price, best_discount)
            
            # 验证电子钱包
            ewallet_money_decimal = MoneyHandler.to_decimal(ewallet_money)
            actual_ewallet_usage = Decimal('0.00')
            
            if MoneyHandler.is_greater_than(ewallet_money_decimal, 0):
                ewallet_residue = MoneyHandler.to_decimal(stu_obj.stu_wallet_amount)
                if MoneyHandler.is_less_than(ewallet_residue, ewallet_money_decimal):
                    return False, f"电子钱包余额不足，当前余额: {ewallet_residue}, 需要使用: {ewallet_money_decimal}"
                
                # 电子钱包使用金额不能超过订单应付金额
                if MoneyHandler.is_greater_than(ewallet_money_decimal, final_sale_price):
                    actual_ewallet_usage = final_sale_price
                    settings.logger.info(f"电子钱包使用金额超过订单金额，调整为订单金额：原计划使用{ewallet_money_decimal}，实际使用{actual_ewallet_usage}")
                else:
                    actual_ewallet_usage = ewallet_money_decimal
            
            # 最终应付金额
            final_amount = MoneyHandler.subtract(final_sale_price, actual_ewallet_usage)
            
            # 验证金额计算结果
            if MoneyHandler.is_less_than(final_amount, 0):
                settings.logger.error(f"续费订单金额计算错误，最终金额为负数: {final_amount}, 原价: {total_sale_price}, 优惠: {best_discount}, 电子钱包: {actual_ewallet_usage}")
                return False, f"订单金额计算错误，请联系管理员"
            
            # 计算原价（按次收费：课节原价 = 班级原价 / 总课节数）
            lesson_original_price = MoneyHandler.divide(
                MoneyHandler.to_decimal(class_obj.original_price), 
                MoneyHandler.to_decimal(class_obj.planning_class_times)
            )
            total_original_price = MoneyHandler.multiply(lesson_original_price, MoneyHandler.to_decimal(renew_lessons))
            
            # 直接创建续费报价单和订单
            # 生成订单号
            order_no = datetime.now().strftime('%Y%m%d%H%M%S%f')[:17] + (str(order_student_obj.stu_id) or '0000') + str(random.randint(1000, 9999))
            
            # 生效时间
            effective_start = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            effective_end = (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d %H:%M:%S")
            
            # 创建报价单
            offer_obj = await erp_order_offer.create(db, commit=False, **{
                "name": f"{order_no}续费报价单",
                "effective_start": effective_start,
                "effective_end": effective_end,
                "total_original_price": total_original_price,
                "total_sale_price": final_amount,
                "total_discount_price": best_discount,
                "integral_money": 0,
                "ewallet_money": actual_ewallet_usage,
                "inner_remark": inner_remark or f"续费订单-学生订单ID:{order_student_id}",
                "out_remark": out_remark or f"续费{renew_lessons}课节",
                "cash_voucher": cash_voucher,
                "cash_pay_remark": cash_pay_remark,
                "create_by": user.uid,
                "update_by": user.uid,
                "campus_id": 1,
                "order_no": order_no,
                "offer_type": OfferType.RENEW_ORDER.value,  # 续费订单
                "offer_state": OfferState.NOT_PAY.value,
                "stu_id": order_student_obj.stu_id,
                "order_from": 1,  # ERP系统
                "cash_receive_way": cash_receive_way,
            })
            
            # 更新电子钱包
            if MoneyHandler.is_greater_than(actual_ewallet_usage, 0):
                original_wallet_amount = MoneyHandler.to_decimal(stu_obj.stu_wallet_amount)
                new_wallet_amount = MoneyHandler.subtract(original_wallet_amount, actual_ewallet_usage)
                stu_obj.stu_wallet_amount = new_wallet_amount
                settings.logger.info(
                    f"学生{stu_obj.id}电子钱包续费支付，使用金额: {actual_ewallet_usage}, "
                    f"原余额: {original_wallet_amount}, 剩余金额: {new_wallet_amount}"
                )
            
            # 更新优惠券状态
            if best_coupon_id:
                await erp_student_discount_coupon.update_one(
                    db, best_coupon_id,
                    {"status": DiscountStatus.USED.value},
                    commit=False
                )
                settings.logger.info(f"使用优惠券: {best_coupon_id}, 续费报价单ID: {offer_obj.id}")
            
            # 创建续费订单
            await create_renew_course_order(
                db, class_obj, order_student_obj, renew_lessons,
                best_discount, best_coupon_id, actual_ewallet_usage,
                offer_obj, user
            )
            
            offer_id = offer_obj.id
            
            # 判断是否为现金收款或金额为0，自动设置为已支付状态
            is_cash_payment = cash_receive_way == 1
            
            if MoneyHandler.is_less_than(final_amount, 0) or MoneyHandler.is_equal(final_amount, 0) or is_cash_payment:
                # 订单金额为0或者是现金收款，直接设置为已支付状态
                if is_cash_payment:
                    settings.logger.info(f"现金收款续费订单，自动设置为已支付状态, 报价单ID: {offer_obj.id}")
                else:
                    settings.logger.info(f"续费订单金额为0，自动设置为已支付状态, 报价单ID: {offer_obj.id}")
                
                # 更新报价单状态
                offer_obj.offer_state = OfferState.PAID.value
                
                # 获取并更新相关订单状态
                orders = await erp_order.get_many(db, {"offer_id": offer_obj.id})
                for order in orders:
                    order.order_state = OrderState.PAID.value
                    
                # 更新学生订单课节数（续费成功后增加课节）
                for order in orders:
                    if order.join_type == 3:  # 续费订单
                        # 将续费课节数加到原有课节数上
                        current_total_hours = MoneyHandler.to_decimal(order_student_obj.total_hours or 0)
                        new_total_hours = MoneyHandler.add(current_total_hours, MoneyHandler.to_decimal(order.buy_num))
                        order_student_obj.total_hours = new_total_hours
                        settings.logger.info(f"学生续费成功: 学生ID={order_student_obj.stu_id}, 班级ID={order_student_obj.class_id}, 原课节数={current_total_hours}, 续费课节数={order.buy_num}, 新课节数={new_total_hours}")
                
                # 如果是现金收款，需要创建收入单据并发起工作流
                if is_cash_payment and MoneyHandler.is_greater_than(final_amount, 0):
                    try:
                        receipt_id = await create_cash_income_receipt_and_workflow(db, offer_obj, orders, user, stu_obj)
                        settings.logger.info(f"现金收款续费单据创建成功，单据ID: {receipt_id}, 报价单ID: {offer_obj.id}")
                    except Exception as e:
                        settings.logger.error(f"创建现金收款续费单据失败: {str(e)}")
                        # 现金收款单据创建失败不影响订单创建，只记录错误日志
                
                # 推送支付成功通知
                if is_push:
                    asyncio.create_task(push_order_modules(offer_obj.id, is_paid=True))
            elif is_push:
                # 推送订单通知
                asyncio.create_task(push_order_modules(offer_obj.id))
            
            flag, offer_id = True, offer_id
            
            if flag:
                settings.logger.info(f"续费报价单创建成功 - 学生订单ID:{order_student_id}, 报价单ID:{offer_id}, 续费课节数:{renew_lessons}")
                return True, offer_id
            else:
                settings.logger.error(f"续费报价单创建失败 - 学生订单ID:{order_student_id}, 错误:{offer_id}")
                return False, offer_id
                
        finally:
            # 释放锁
            await redis_client.delete(order_unique_key)
            
    except Exception as e:
        settings.logger.error(f"创建续费报价单失败: {str(e)}", exc_info=True)
        return False, str(e)
