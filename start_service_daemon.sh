#!/bin/bash

# 后台服务守护进程启动脚本
# 使用方法: ./start_service_daemon.sh [start|stop|restart|status]

SERVICE_NAME="jjsw-backend-service"
PID_FILE="service.pid"
LOG_FILE="log/service_daemon.log"
PYTHON_CMD="python3 service.py"

# 创建日志目录
mkdir -p log

# 获取当前时间
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# 记录日志
log_message() {
    echo "[$(get_timestamp)] $1" >> "$LOG_FILE"
    echo "$1"
}

# 检查服务是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            return 0
        else
            # PID文件存在但进程不存在，删除无效的PID文件
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 启动服务
start_service() {
    if is_running; then
        log_message "❌ 服务已在运行中 (PID: $(cat $PID_FILE))"
        return 1
    fi
    
    log_message "🚀 启动后台服务..."
    
    # 使用nohup在后台运行服务
    nohup $PYTHON_CMD > log/service_output.log 2>&1 &
    PID=$!
    
    # 保存PID
    echo $PID > "$PID_FILE"
    
    # 等待一下确认服务启动成功
    sleep 3
    
    if is_running; then
        log_message "✅ 服务启动成功 (PID: $PID)"
        log_message "📝 日志文件: $LOG_FILE"
        log_message "📝 输出日志: log/service_output.log"
        return 0
    else
        log_message "❌ 服务启动失败"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止服务
stop_service() {
    if ! is_running; then
        log_message "⚠️  服务未在运行"
        return 1
    fi
    
    PID=$(cat "$PID_FILE")
    log_message "🛑 停止服务 (PID: $PID)..."
    
    # 发送SIGTERM信号优雅停止
    kill -TERM $PID
    
    # 等待最多30秒让服务优雅停止
    for i in {1..30}; do
        if ! ps -p $PID > /dev/null 2>&1; then
            break
        fi
        sleep 1
    done
    
    # 如果还在运行，强制杀死
    if ps -p $PID > /dev/null 2>&1; then
        log_message "⚠️  服务未能优雅停止，强制终止..."
        kill -KILL $PID
        sleep 2
    fi
    
    # 清理PID文件
    rm -f "$PID_FILE"
    log_message "✅ 服务已停止"
}

# 重启服务
restart_service() {
    log_message "🔄 重启服务..."
    stop_service
    sleep 2
    start_service
}

# 查看服务状态
check_status() {
    if is_running; then
        PID=$(cat "$PID_FILE")
        UPTIME=$(ps -o etime= -p $PID | tr -d ' ')
        log_message "✅ 服务正在运行 (PID: $PID, 运行时间: $UPTIME)"
        
        # 显示最近的日志
        echo ""
        echo "📝 最近的服务日志:"
        tail -n 10 log/service.log 2>/dev/null || echo "暂无服务日志"
        
        return 0
    else
        log_message "❌ 服务未在运行"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {start|stop|restart|status|help}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动后台服务"
    echo "  stop    - 停止后台服务"
    echo "  restart - 重启后台服务"
    echo "  status  - 查看服务状态"
    echo "  help    - 显示帮助信息"
    echo ""
    echo "日志文件:"
    echo "  $LOG_FILE - 守护进程日志"
    echo "  log/service.log - 服务运行日志"
    echo "  log/service_output.log - 服务输出日志"
}

# 主函数
case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        check_status
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ 无效的命令: $1"
        show_help
        exit 1
        ;;
esac

exit $? 