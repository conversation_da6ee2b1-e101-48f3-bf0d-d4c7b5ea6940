from importlib import import_module

# 定义模块路径的列表
from fastapi import APIRouter

modules = [
    "public_api.api.file",
    "public_api.api.settings",
    "public_api.api.workflow",
    "public_api.api.message",
    "public_api.api.eeo",
    "public_api.api.qy_wechat",

    # 可以继续添加更多模块
]
router = APIRouter(prefix='/public_api')

for module_path in modules:
    module = import_module(module_path)
    router.include_router(module.router)
