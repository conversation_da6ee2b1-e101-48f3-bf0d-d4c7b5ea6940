from datetime import datetime
from decimal import Decimal
from typing import List,  Tuple
import asyncio
import logging
from collections import defaultdict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from settings import logger
from models.m_finance import ErpBankAccount, ErpFinanceTradePayment, ErpStudentEwalletLog, ErpBankAccountLog
from models.m_mall import MallMerchantConfig
from models.m_order import ErpOrderRefund, ErpOrderRefundDetail, ErpOrder, ErpOrderStudent, ErpOrderOffer
from models.m_student import ErpStudent
from models.m_workflow import (
    ErpCostTypeBind, ErpWorkflowCostType, ErpWorkflowDef, ErpWorkflowNode, ErpWorkflowInstance, ErpReceipt,
    ErpReceiptFinance, ErpReceiptDetail, ErpWorkflowRecord, ErpPaymentObj, ErpWorkflowInstanceReviewer
)
from models.models import ErpAccountDepartment, ErpAccount
import settings
from utils.db.model_handler import <PERSON><PERSON>ata<PERSON>elper
from utils.enum.enum_approval import ApproverType, NodeType, NodeAction, InstanceStatus, AuditState, CostTypeBind
from utils.enum.enum_order import OfferState, OrderState, StudentState, RefundDetailState, RefundWay
from utils.message.QyWechatMessage import send_message_to_users, send_markdown_to_users
from utils.message.workflow_message_queue import add_cc_message_to_queue
from settings import CF
from public_api.crud import (
    erp_workflow_def, erp_workflow_node, erp_workflow_instance, erp_receipt,
    erp_receipt_finance, erp_receipt_detail, erp_workflow_record, erp_account,
    erp_workflow_instance_reviewer, erp_workflow_cost_type, erp_payment_obj, format_workflow_list_data, get_cc_to_me_instances, get_my_submit_workflows, get_pending_approve_instances
)
from utils.response.response_handler import ApiFailedResponse


# 获取CRUD操作对象
erp_workflow_cost_type = CF.get_crud(ErpWorkflowCostType)
erp_cost_type_bind = CF.get_crud(ErpCostTypeBind)
erp_bank_account = CF.get_crud(ErpBankAccount)
erp_bank_account_log = CF.get_crud(ErpBankAccountLog)
erp_order_refund_detail = CF.get_crud(ErpOrderRefundDetail)
erp_finance_trade_payment = CF.get_crud(ErpFinanceTradePayment)
mall_merchant_config = CF.get_crud(MallMerchantConfig)
erp_student = CF.get_crud(ErpStudent)
erp_order = CF.get_crud(ErpOrder)
erp_order_student = CF.get_crud(ErpOrderStudent)
erp_order_offer = CF.get_crud(ErpOrderOffer)



async def _send_cc_message_background(cc_user_ids: List[str], markdown_content: str):
    """
    后台发送抄送消息，不阻塞主流程
    
    Args:
        cc_user_ids: 企业微信用户ID列表
        markdown_content: Markdown格式的消息内容
    """
    try:
        logger.info(f"将抄送消息添加到队列，接收用户: {cc_user_ids}")
        # 使用Redis队列替代直接发送，支持失败重试
        result = await add_cc_message_to_queue(cc_user_ids, markdown_content)
        if result:
            logger.info("抄送消息成功添加到队列")
        else:
            logger.error("添加抄送消息到队列失败")
    except Exception as e:
        # 记录错误但不影响流程
        logger.error(f"将抄送消息添加到队列失败: {e}")

async def _auto_approve_creator_node(db: AsyncSession, instance_id: int, node: ErpWorkflowNode, 
                                   creator_id: int, creator_name: str) -> bool:
    """
    自动审批创建人的节点
    
    Args:
        db: 数据库会话
        instance_id: 工作流实例ID
        node: 当前节点
        creator_id: 创建人ID
        creator_name: 创建人姓名
        
    Returns:
        bool: 是否自动审批了节点
    """
    try:
        # 创建审批人记录并直接设置为已同意
        await erp_workflow_instance_reviewer.create(db, commit=False, **{
            "instance_id": instance_id,
            "node_id": node.id,
            "reviewer_id": creator_id,
            "reviewer_name": creator_name,
            "status": NodeAction.APPROVE.value,  # 已同意
            "review_time": datetime.now(),
            "comments": "创建人自动通过"
        })
        
        # 创建审批记录
        await erp_workflow_record.create(db, commit=False, **{
            "instance_id": instance_id,
            "node_id": node.id,
            "node_name": node.node_name,
            "approver_id": creator_id,
            "approver_name": creator_name,
            "approve_status": NodeAction.APPROVE.value,  # 自动同意
            "approve_time": datetime.now(),
            "approve_opinion": "创建人自动通过",
            "sort_no": node.sort_no
        })
        
        logger.info(f"自动审批创建人节点: instance_id={instance_id}, node_id={node.id}, creator_id={creator_id}")
        return True
    except Exception as e:
        logger.error(f"自动审批创建人节点失败: {str(e)}")
        return False

async def _check_and_auto_approve_creator(db: AsyncSession, instance_id: int, node: ErpWorkflowNode, 
                                        reviewers: List[str], creator_id: int) -> Tuple[List[str], bool]:
    """
    检查审批人中是否包含创建人，如果包含则自动审批
    
    Args:
        db: 数据库会话
        instance_id: 工作流实例ID
        node: 当前节点
        reviewers: 审批人ID列表
        creator_id: 创建人ID
        
    Returns:
        Tuple[List[str], bool]: (过滤后的审批人列表, 是否自动审批了创建人)
    """
    creator_id_str = str(creator_id)
    auto_approved = False
    filtered_reviewers = []
    
    for reviewer_id in reviewers:
        if str(reviewer_id) == creator_id_str:
            # 如果审批人是创建人，自动审批
            creator = await erp_account.get_by_id(db, creator_id)
            creator_name = creator.employee_name if creator else "未知"
            
            auto_approved = await _auto_approve_creator_node(db, instance_id, node, creator_id, creator_name)
            if auto_approved:
                logger.info(f"创建人({creator_name})自动通过审批节点: {node.node_name}")
            else:
                # 如果自动审批失败，仍然加入审批人列表
                filtered_reviewers.append(reviewer_id)
        else:
            filtered_reviewers.append(reviewer_id)
    
    return filtered_reviewers, auto_approved

def _build_cc_message_content(node_name: str, workflow_name: str, receipt_id: int, apply_reason: str, 
                             creator_name: str, create_time_str: str) -> str:
    """
    构建抄送消息内容
    
    Args:
        node_name: 抄送节点名称
        workflow_name: 工作流名称
        receipt_id: 单据ID
        apply_reason: 申请原因/单据名称
        creator_name: 创建人姓名
        create_time_str: 创建时间字符串
        
    Returns:
        markdown格式的消息内容
    """
    return f"""# 工作流抄送通知
## {workflow_name}
### 基本信息
- **抄送节点**: {node_name}
- **单据名称**: {apply_reason}
- **创建人**: {creator_name}
- **创建时间**: {create_time_str}

您收到此消息是因为您被设置为流程的抄送人。
[点击查看详情]({settings.FRONTEND_URL}/#/approvalCenter/approvalDetails?receipt_id={receipt_id}&type=4)
"""

def _build_approval_message_content(node_name: str, workflow_name: str, receipt_id: int, apply_reason: str, 
                                  creator_name: str, create_time_str: str) -> str:
    """
    构建审批消息内容
    
    Args:
        node_name: 审批节点名称
        workflow_name: 工作流名称
        receipt_id: 单据ID
        apply_reason: 申请原因/单据名称
        creator_name: 创建人姓名
        create_time_str: 创建时间字符串
        
    Returns:
        markdown格式的消息内容
    """
    return f"""# 工作流审批通知
## {workflow_name}
### 基本信息
- **审批节点**: {node_name}
- **单据名称**: {apply_reason}
- **创建人**: {creator_name}
- **创建时间**: {create_time_str}

您有一个待审批的工作，请及时处理。
[点击查看并处理]({settings.FRONTEND_URL}/#/approvalCenter/approvalDetails?receipt_id={receipt_id}&type=4)
"""

def _build_complete_message_content(workflow_name: str, receipt_id: int, apply_reason: str, 
                                  creator_name: str, create_time_str: str) -> str:
    """
    构建流程完成通知内容
    
    Args:
        workflow_name: 工作流名称
        receipt_id: 单据ID
        apply_reason: 申请原因/单据名称
        creator_name: 创建人姓名
        create_time_str: 创建时间字符串
        
    Returns:
        markdown格式的消息内容
    """
    return f"""# 工作流完成通知
## {workflow_name}
### 基本信息
- **单据名称**: {apply_reason}
- **创建人**: {creator_name}
- **创建时间**: {create_time_str}

您发起的工作流已审批通过！
[点击查看详情]({settings.FRONTEND_URL}/#/approvalCenter/approvalDetails?receipt_id={receipt_id}&type=4)
"""

async def _send_approval_message_background(approval_user_ids: List[str], markdown_content: str):
    """
    后台发送审批消息，不阻塞主流程
    
    Args:
        approval_user_ids: 企业微信用户ID列表
        markdown_content: Markdown格式的消息内容
    """
    try:
        logger.info(f"将审批消息添加到队列，接收用户: {approval_user_ids}")
        # 使用Redis队列替代直接发送，支持失败重试
        result = await add_cc_message_to_queue(approval_user_ids, markdown_content)
        if result:
            logger.info("审批消息成功添加到队列")
        else:
            logger.error("添加审批消息到队列失败")
    except Exception as e:
        # 记录错误但不影响流程
        logger.error(f"将审批消息添加到队列失败: {e}")

async def get_user_superior(db: AsyncSession, user_id: int, level: int):
    """
    获取当前用户的第level级上级
    ErpAccount.id  user_id - int
    ErpAccount.qy_wechat_userid 字段为用户表中的企业微信用户ID，字段值eg. str - '123'
    ErpAccount.qy_wechat_direct_leader 字段为用户表中的直属上级字段，字段值eg. json - ['123']
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        level: 上级层级，1表示直属上级，2表示上上级，以此类推
        
    Returns:
        返回上级的ErpAccount.id列表（整数型，可能有多个上级）
    """
    # 获取当前用户
    user = await erp_account.get_by_id(db, user_id)
    if not user:
        raise Exception("用户不存在")
    
    # 获取当前用户的直属上级
    direct_leader_wechat_ids = user.qy_wechat_direct_leader
    if not direct_leader_wechat_ids:
        raise Exception("当前用户没有直属上级")
    
    # 将企业微信ID转换为系统用户ID
    direct_leader_account_ids = []
    for wechat_id in direct_leader_wechat_ids:
        # 查找匹配的账号
        accounts = await erp_account.get_many(db, {"qy_wechat_userid": wechat_id})
        if accounts:
            direct_leader_account_ids.extend([account.id for account in accounts])
    
    # 如果没有找到对应的账号，抛出异常
    if not direct_leader_account_ids:
        raise Exception("无法找到直属上级的系统账号")
    
    # 如果只需要第一级上级，直接返回
    if level == 1:
        return direct_leader_account_ids
    
    # 递归获取更高级别的上级
    current_level = 1
    current_leader_ids = direct_leader_account_ids
    
    while current_level < level:
        if not current_leader_ids:
            raise Exception(f"无法获取第{level}级上级，当前只能获取到第{current_level}级")
        
        # 获取下一级的上级
        next_level_leader_ids = []
        for leader_id in current_leader_ids:
            leader = await erp_account.get_by_id(db, leader_id)
            
            if leader and leader.qy_wechat_direct_leader:
                # 查找上级的系统账号ID
                for wechat_id in leader.qy_wechat_direct_leader:
                    accounts = await erp_account.get_many(db, {"qy_wechat_userid": wechat_id})
                    if accounts:
                        next_level_leader_ids.extend([account.id for account in accounts])
        
        # 更新当前上级和级别
        current_leader_ids = next_level_leader_ids
        current_level += 1
        
        # 如果找不到更高级别的上级
        if not current_leader_ids and current_level < level:
            raise Exception(f"无法获取第{level}级上级，当前只能获取到第{current_level-1}级")
    
    return current_leader_ids

async def start_workflow(db: AsyncSession, workflow_id: int, business_id: int, user_id: int, business_type: str = "receipt"):
    """
    启动工作流
    
    Args:
        db: 数据库会话
        workflow_id: 工作流定义ID
        business_id: 业务单据ID, 例如receipt_id
        user_id: 发起人ID
        business_type: 业务类型
    
    Returns:
        工作流实例ID
    """
    # 获取工作流定义
    workflow_def = await erp_workflow_def.get_by_id(db, workflow_id)
    if not workflow_def:
        raise Exception("工作流定义不存在")
    
    # 获取工作流节点，按照排序号排序
    nodes = await erp_workflow_node.get_many(db, {
        "workflow_id": workflow_id
    }, orderby=["sort_no"])
     
    if not nodes:
        raise Exception("工作流节点不存在")
    
    # 创建工作流实例
    instance_data = {
        "workflow_id": workflow_id,
        "workflow_name": workflow_def.workflow_name,
        "business_id": business_id,
        "business_type": business_type,
        "status": InstanceStatus.PROCESSING.value,  # 进行中
        "current_node_id": nodes[0].id,
        "current_node_name": nodes[0].node_name,
        "submit_time": datetime.now(),
        "create_by": user_id,
        "update_by": user_id,
    }
    
    workflow_instance = await erp_workflow_instance.create(db, commit=False, **instance_data)
    
    # 更新单据状态和审批记录ID
    await erp_receipt.update_one(db, business_id, {
        "audit_state": AuditState.AUDITING.value,  # 审批中状态
        "workflow_instance_id": workflow_instance.id,
        "update_by": user_id
    }, commit=False)
    
    # 处理审批人
    current_node = nodes[0]
    reviewers = []
    
    # 根据审批人类型获取审批人ID列表
    if current_node.approver_type == ApproverType.CONTINUOUS_SUPERIOR.value:
        # 连续多级上级
        level_count = int(current_node.approver_ids) if current_node.approver_ids else 1
        try:
            superior_ids = await get_user_superior(db, user_id, level_count)
            reviewers = superior_ids
        except Exception as e:
            # 如果获取上级失败
            raise Exception(f"获取上级失败: {e}")
            
    elif current_node.approver_type == ApproverType.SPECIFIED_MEMBER.value:
        # 指定人员
        reviewers = current_node.approver_ids.split(',') if current_node.approver_ids else []
    elif current_node.approver_type == ApproverType.SPECIFIED_POSITION.value:
        # 指定岗位
        position = current_node.approver_ids
        if position:
            # 查询该岗位的所有人员
            position_users = await erp_account.get_many(db, {"qy_wechat_position": position})
            reviewers = [str(user.id) for user in position_users]
    elif current_node.approver_type == ApproverType.TEACHER.value:
        # 授课老师
        # 获取单据信息
        receipt = await erp_receipt.get_by_id(db, business_id)
        if receipt and receipt.class_id and receipt.class_id > 0:
            # 获取班级信息
            from models.m_class import ErpClass
            class_obj = await db.execute(select(ErpClass).where(ErpClass.id == receipt.class_id))
            class_obj = class_obj.scalar_one_or_none()
            
            if class_obj and class_obj.teacher_id:
                # 获取老师的账号ID
                from models.m_teacher import ErpAccountTeacher
                teacher_obj = await db.execute(select(ErpAccountTeacher).where(ErpAccountTeacher.id == class_obj.teacher_id))
                teacher_obj = teacher_obj.scalar_one_or_none()
                
                if teacher_obj and teacher_obj.account_id:
                    reviewers = [str(teacher_obj.account_id)]
                else:
                    # 直接使用class表中的teacher_id，假定它可能直接是account_id
                    reviewers = [str(class_obj.teacher_id)]
            else:
                raise Exception(f"找不到班级信息或班级没有关联授课老师")
    
    # 如果没有找到审批人，抛出异常
    if not reviewers:
        raise Exception(f"节点'{current_node.node_name}'未找到合适的审批人1")
    
    # 检查并自动审批创建人
    filtered_reviewers, creator_auto_approved = await _check_and_auto_approve_creator(
        db, workflow_instance.id, current_node, reviewers, user_id
    )
    
    # 创建审批人记录
    reviewer_accounts = []  # 保存审批人账户信息
    
    for reviewer_id in filtered_reviewers:
        try:
            reviewer_id_int = int(reviewer_id)
            reviewer = await erp_account.get_by_id(db, reviewer_id_int)
            reviewer_name = reviewer.employee_name if reviewer else ""
            
            # 保存审批人账户信息用于发送通知
            if reviewer:
                reviewer_accounts.append(reviewer)
                
            await erp_workflow_instance_reviewer.create(db, commit=False, **{
                "instance_id": workflow_instance.id,
                "node_id": current_node.id,
                "reviewer_id": reviewer_id_int,
                "reviewer_name": reviewer_name,
                "status": 0  # 待审批
            })
        except (ValueError, TypeError):
            # 处理reviewer_id不是有效整数的情况
            continue
    
    # 创建第一个节点的工作流记录
    # 如果创建人被自动审批且没有其他审批人，则节点状态为已审批
    node_approve_status = 0  # 待审批
    if creator_auto_approved and not filtered_reviewers:
        node_approve_status = NodeAction.APPROVE.value  # 已同意
    
    await erp_workflow_record.create(db, commit=False, **{
        "instance_id": workflow_instance.id,
        "node_id": current_node.id,
        "node_name": current_node.node_name,
        "approve_status": node_approve_status,
        "sort_no": current_node.sort_no
    })

    # 检查是否需要自动进入下一个节点
    need_auto_advance = False
    
    # 情况1：首个节点是抄送节点
    if current_node.node_type == NodeType.CC.value:
        need_auto_advance = True
    # 情况2：创建人被自动审批且没有其他审批人
    elif creator_auto_approved and not filtered_reviewers:
        need_auto_advance = True
        logger.info(f"创建人自动审批且无其他审批人，自动进入下一节点: {current_node.node_name}")
    
    if need_auto_advance:  # 需要自动处理的节点
        # 获取所有节点，找到下一个节点
        if len(nodes) > 1:
            next_node = nodes[1]  # 获取第二个节点
            
            # 更新工作流实例指向下一个节点
            await erp_workflow_instance.update_one(db, workflow_instance.id, {
                "current_node_id": next_node.id,
                "current_node_name": next_node.node_name,
                "update_by": user_id
            }, commit=False)
            
            # 更新抄送节点的审批记录为已处理
            await erp_workflow_record.update_many(db, condition={
                "instance_id": workflow_instance.id,
                "node_id": current_node.id
            }, new_values={
                "approve_status": NodeAction.APPROVE.value,  # 自动同意
                "approve_time": datetime.now(),
                "approve_opinion": "抄送节点自动处理"
            }, commit=False)
            
            # 更新审批人状态
            reviewers = await erp_workflow_instance_reviewer.get_many(db, {
                "instance_id": workflow_instance.id,
                "node_id": current_node.id,
            })
            
            # 收集抄送人的企业微信用户ID
            cc_user_ids = []
            for reviewer in reviewers:
                await erp_workflow_instance_reviewer.update_one(db, reviewer.id, {
                    "status": NodeAction.APPROVE.value,  # 已同意
                    "review_time": datetime.now(),
                    "comments": "抄送节点自动处理"
                }, commit=False)
                
                # 获取用户的企业微信ID
                if reviewer.reviewer_id:
                    user_obj = await erp_account.get_by_id(db, reviewer.reviewer_id)
                    if user_obj and user_obj.qy_wechat_userid and user_obj.qy_wechat_userid.strip():
                        cc_user_ids.append(user_obj.qy_wechat_userid)
            
            # 如果有抄送人，发送通知
            if cc_user_ids:
                # 获取单据信息
                receipt = await erp_receipt.get_by_id(db, business_id)
                creator = None
                if receipt and receipt.create_by:
                    creator = await erp_account.get_by_id(db, receipt.create_by)
                
                # 构建消息参数
                creator_name = creator.employee_name if creator else "未知"
                workflow_name = workflow_def.workflow_name if workflow_def else "未知"
                apply_reason = receipt.apply_reason if receipt else "未知"
                create_time_str = receipt.create_time.strftime("%Y-%m-%d %H:%M") if receipt and receipt.create_time else "未知"
                
                # 构建消息内容
                markdown_content = _build_cc_message_content(
                    current_node.node_name,
                    workflow_name,
                    business_id,
                    apply_reason,
                    creator_name,
                    create_time_str
                )
                
                # 使用消息队列发送通知
                asyncio.create_task(_send_cc_message_background(cc_user_ids, markdown_content))
            
            # 为下一个节点设置审批人
            next_reviewers = []
            
            # 根据审批人类型获取审批人ID列表
            if next_node.approver_type == ApproverType.CONTINUOUS_SUPERIOR.value:
                if next_node.is_continuous_approval:
                    # 获取用户的多级上级
                    level_count = int(next_node.approver_ids) if next_node.approver_ids else 1
                    try:
                        superior_ids = await get_user_superior(db, user_id, level_count)
                        next_reviewers = superior_ids
                    except Exception as e:
                        # 如果获取上级失败，则使用备用审批人
                        next_reviewers = next_node.approver_ids.split(',') if next_node.approver_ids else []
                else:
                    # 从节点配置中获取审批人
                    next_reviewers = next_node.approver_ids.split(',') if next_node.approver_ids else []
            elif next_node.approver_type == ApproverType.SPECIFIED_MEMBER.value:
                # 指定人员
                next_reviewers = next_node.approver_ids.split(',') if next_node.approver_ids else []
            elif next_node.approver_type == ApproverType.SPECIFIED_POSITION.value:
                # 指定岗位
                position = next_node.approver_ids
                if position:
                    # 查询该岗位的所有人员
                    position_users = await erp_account.get_many(db, {"qy_wechat_position": position})
                    next_reviewers = [str(user.id) for user in position_users]
            elif next_node.approver_type == ApproverType.TEACHER.value:
                # 授课老师
                # 获取单据信息
                receipt = await erp_receipt.get_by_id(db, business_id)
                if receipt and receipt.class_id and receipt.class_id > 0:
                    # 获取班级信息
                    from models.m_class import ErpClass
                    class_obj = await db.execute(select(ErpClass).where(ErpClass.id == receipt.class_id))
                    class_obj = class_obj.scalar_one_or_none()
                    
                    if class_obj and class_obj.teacher_id:
                        # 获取老师的账号ID
                        from models.m_teacher import ErpAccountTeacher
                        teacher_obj = await db.execute(select(ErpAccountTeacher).where(ErpAccountTeacher.id == class_obj.teacher_id))
                        teacher_obj = teacher_obj.scalar_one_or_none()
                        
                        if teacher_obj and teacher_obj.account_id:
                            next_reviewers = [str(teacher_obj.account_id)]
                        else:
                            # 直接使用class表中的teacher_id，假定它可能直接是account_id
                            next_reviewers = [str(class_obj.teacher_id)]
                    else:
                        raise Exception(f"找不到班级信息或班级没有关联授课老师")
                else:
                    raise Exception(f"单据不存在或未关联班级")
            
            # 如果没有找到审批人且不是抄送节点，抛出异常
            if not next_reviewers and next_node.node_type != NodeType.CC.value:
                raise Exception(f"节点'{next_node.node_name}'未找到合适的审批人")
            
            # 如果下个节点是抄送节点且没有审批人，则不需要创建审批人记录
            next_filtered_reviewers = []
            next_creator_auto_approved = False
            
            if next_reviewers or next_node.node_type != NodeType.CC.value:
                # 检查并自动审批创建人（针对下一个节点）
                next_filtered_reviewers, next_creator_auto_approved = await _check_and_auto_approve_creator(
                    db, workflow_instance.id, next_node, next_reviewers, user_id
                )
                
                # 创建审批人记录
                for reviewer_id in next_filtered_reviewers:
                    try:
                        reviewer_id_int = int(reviewer_id)
                        reviewer = await erp_account.get_by_id(db, reviewer_id_int)
                        reviewer_name = reviewer.employee_name if reviewer else ""
                        
                        # 创建审批人记录
                        await erp_workflow_instance_reviewer.create(db, commit=False, **{
                            "instance_id": workflow_instance.id,
                            "node_id": next_node.id,
                            "reviewer_id": reviewer_id_int,
                            "reviewer_name": reviewer_name,
                            "status": 0  # 待审批
                        })
                    except (ValueError, TypeError):
                        # 处理reviewer_id不是有效整数的情况
                        continue
            
            # 创建下一个节点的工作流记录
            # 如果创建人被自动审批且没有其他审批人，则节点状态为已审批
            next_node_approve_status = 0  # 待审批
            if next_creator_auto_approved and not next_filtered_reviewers:
                next_node_approve_status = NodeAction.APPROVE.value  # 已同意
            
            await erp_workflow_record.create(db, commit=False, **{
                "instance_id": workflow_instance.id,
                "node_id": next_node.id,
                "node_name": next_node.node_name,
                "approve_status": next_node_approve_status,
                "sort_no": next_node.sort_no
            })
            
            # 如果下一个节点也是抄送节点，递归处理
            if next_node.node_type == NodeType.CC.value:
                # 这里暂不实现递归，避免逻辑过于复杂，以后可以根据需要实现
                pass

    # 如果当前节点是审批节点（非抄送节点）且有待审批的人员，发送通知给审批人
    if current_node.node_type != NodeType.CC.value and filtered_reviewers:
        # 获取单据信息
        receipt = await erp_receipt.get_by_id(db, business_id)
        if receipt:
            # 获取创建人信息
            creator = await erp_account.get_by_id(db, user_id)
            
            # 构建消息参数
            creator_name = creator.employee_name if creator else "未知"
            workflow_name = workflow_def.workflow_name
            apply_reason = receipt.apply_reason or "未提供原因"
            create_time_str = receipt.create_time.strftime("%Y-%m-%d %H:%M") if receipt.create_time else datetime.now().strftime("%Y-%m-%d %H:%M")
            
            # 收集审批人的企业微信用户ID
            approval_user_ids = []
            for reviewer in reviewer_accounts:
                if reviewer.qy_wechat_userid and reviewer.qy_wechat_userid.strip():
                    approval_user_ids.append(reviewer.qy_wechat_userid)
            
            # 构建消息内容
            if approval_user_ids:
                markdown_content = _build_approval_message_content(
                    current_node.node_name,
                    workflow_name,
                    receipt.id,
                    apply_reason,
                    creator_name,
                    create_time_str
                )
                
                # 使用消息队列发送通知
                asyncio.create_task(_send_approval_message_background(approval_user_ids, markdown_content))

    return workflow_instance.id


async def handle_workflow_action(db: AsyncSession, receipt_id: int, user_id: int, action: int, comments: str = None, bank_account_id: int = None):
    # 在函数内部导入，避免循环导入
    from app_finance.modules import bill_account_change, stu_ewallet_change
    """
    处理工作流动作
    
    Args:
        db: 数据库会话
        receipt_id: 单据ID
        user_id: 用户ID (审批人ID)
        action: 动作类型 (NodeAction)
        comments: 审批意见
        
    Returns:
        处理结果
    """
    
    # 获取单据
    receipt = await erp_receipt.get_by_id(db, receipt_id)
    if not receipt:
        return {"message": "单据不存在"}
    
    # 提前保存所有可能需要的对象属性，避免在commit后访问导致懒加载问题
    receipt_create_by = receipt.create_by
    receipt_apply_reason = receipt.apply_reason
    receipt_create_time = receipt.create_time
    receipt_receipt_id = receipt.id
    receipt_refund_id = receipt.refund_id  # 预加载refund_id字段
    receipt_is_transfer = receipt.is_transfer  # 预加载is_transfer字段
    receipt_class_id = receipt.class_id  # 预加载class_id字段
    
    # 获取工作流实例
    instance_id = receipt.workflow_instance_id
    instance = await erp_workflow_instance.get_by_id(db, instance_id)
    if not instance:
        return {"message": "工作流实例不存在"}
    
    # 提前保存工作流实例的属性
    instance_workflow_name = instance.workflow_name
    
    # 验证工作流状态
    if instance.status != InstanceStatus.PROCESSING.value:
        return {"message": "当前工作流不是进行中状态，无法处理"}
    
    # 验证用户是否有权限操作
    current_node = await erp_workflow_node.get_by_id(db, instance.current_node_id)
    if not current_node:
        return {"message": "当前节点不存在"}
    
    # 检查用户是否是当前节点的审批人
    reviewer = await erp_workflow_instance_reviewer.get_one(db, **{
        "instance_id": instance.id,
        "node_id": current_node.id,
        "reviewer_id": user_id,
        "status": 0  # 待审批
    })
    
    if not reviewer:
        return {"message": f"您不是当前节点的审批人或已处理过该节点, 当前节点: {current_node.node_name}"}
    
    # 获取所有节点，用于确定下一个节点
    all_nodes = await erp_workflow_node.get_many(db, {
        "workflow_id": instance.workflow_id
    }, orderby=["sort_no"])
    
    # 找到当前节点在工作流中的位置
    current_index = -1
    for i, node in enumerate(all_nodes):
        if node.id == current_node.id:
            current_index = i
            break
    
    if current_index == -1:
        raise Exception("无法找到当前节点在工作流中的位置")
    
    # 更新审批人状态
    await erp_workflow_instance_reviewer.update_one(db, reviewer.id, {
        "status": action,  # 1-已同意，2-已驳回
        "review_time": datetime.now(),
        "comments": comments
    }, commit=False)
    
    # 添加审批记录
    await erp_workflow_record.create(db, commit=False, **{
        "instance_id": instance.id,
        "node_id": current_node.id,
        "node_name": current_node.node_name,
        "approver_id": user_id,
        "approver_name": reviewer.reviewer_name,
        "approve_status": action,  # 1-同意，2-驳回
        "approve_time": datetime.now(),
        "approve_opinion": comments,
        "sort_no": current_node.sort_no
    })
    
    # 根据动作处理工作流
    if action == NodeAction.APPROVE.value:  # 同意
        # 检查当前节点的动作类型，更新相应的财务字段
        if current_node.node_action == 2:  # 财务节点
            # 获取财务单信息
            finance_obj = await erp_receipt_finance.get_one(db, receipt_id=receipt_id)
            if finance_obj:
                # 更新财务审核人ID
                await erp_receipt_finance.update_one(db, finance_obj.id, {
                    "financer_id": user_id,
                    "update_by": user_id
                }, commit=False)
        elif current_node.node_action == 3:  # 出纳节点
            await handle_cashier_node_processing(db, receipt_id, user_id, bank_account_id, receipt, instance)

        # 检查是否为会签节点
        if current_node.is_countersign:
            # 检查是否所有人都已审批
            all_reviewers = await erp_workflow_instance_reviewer.get_many(db, {
                "instance_id": instance.id,
                "node_id": current_node.id
            })
            
            # 检查是否所有人都已同意
            pending_reviewers = [r for r in all_reviewers if r.status == 0]
            if pending_reviewers:
                # 还有人未审批，等待其他人审批
                await db.commit()
                return {"message": "审批已提交，等待其他审批人处理"}
        
        # 如果是最后一个节点或非会签节点已审批完成
        if current_index == len(all_nodes) - 1:
            # 流程结束，更新状态
            await erp_workflow_instance.update_one(db, instance.id, {
                "status": InstanceStatus.COMPLETED.value,  # 已完成
                "finish_time": datetime.now(),
                "update_by": user_id
            }, commit=False)
            
            # 更新单据状态
            await erp_receipt.update_one(db, receipt_id, {
                "audit_state": AuditState.PASS.value,
                "update_by": user_id,
                "workflow_instance_id": 0
            }, commit=False)
            
            # 处理调拨单账户余额变更
            if receipt_is_transfer and receipt_is_transfer > 0:
                try:
                    # 获取明细项
                    details = await erp_receipt_detail.get_many(db, {"receipt_id": receipt_id})
                    
                    for detail in details:
                        if detail.transfer_type == 1:  # 付款方（出账）
                            # 获取账户信息
                            bank_account = await erp_bank_account.get_by_id(db, detail.transfer_account_id)
                            if bank_account:
                                # 确保账户有足够余额
                                if bank_account.balance < detail.amount:
                                    msg = f"账户余额不足，无法完成调拨: 账户ID={bank_account.id}, 余额={bank_account.balance}, 需要金额={detail.amount}"
                                    logger.error(msg)
                                    return {"message": msg}
                                
                                # 使用统一的账户变动函数，避免重复计算
                                await bill_account_change(db, 
                                                        bank_account.id, 
                                                        2,  # 减少
                                                        detail.amount, 
                                                        f"调拨单出账：单据ID={receipt_id}, 明细ID={detail.id}", 
                                                        user_id, 
                                                        commit=False, 
                                                        receipt_id=receipt_id)
                                
                        elif detail.transfer_type == 2:  # 收款方（入账）
                            # 获取账户信息
                            bank_account = await erp_bank_account.get_by_id(db, detail.transfer_account_id)
                            if bank_account:
                                # 使用统一的账户变动函数，避免重复计算
                                await bill_account_change(db, 
                                                        bank_account.id, 
                                                        1,  # 增加
                                                        detail.amount, 
                                                        f"调拨单入账：单据ID={receipt_id}, 明细ID={detail.id}", 
                                                        user_id, 
                                                        commit=False, 
                                                        receipt_id=receipt_id)
                except Exception as e:
                    logger.error(f"处理调拨单账户变更失败: {str(e)}")
                    return {"message": f"处理调拨单账户变更失败: {str(e)}"}
            
            # 检查是否为班级开班审批，如果是，则处理班级状态
            if receipt_class_id and receipt_class_id > 0:
                try:
                    # 导入班级审核处理函数
                    from app_teach.modules import handle_class_approval
                    
                    # 处理班级审核结果
                    await handle_class_approval(
                        db=db,
                        receipt_id=receipt_id,
                        status=AuditState.PASS.value  # 2表示通过
                    )
                    logger.info(f"班级审核通过处理完成: receipt_id={receipt_id}, class_id={receipt_class_id}")
                except Exception as e:
                    logger.error(f"处理班级审核出错2: {str(e)}")
            
            # 给发起人发送流程完成通知
            creator = await erp_account.get_by_id(db, receipt_create_by) if receipt_create_by else None
            if creator and creator.qy_wechat_userid and creator.qy_wechat_userid.strip():
                # 构建消息参数
                creator_name = creator.employee_name if creator else "未知"
                creator_qy_wechat_userid = creator.qy_wechat_userid
                workflow_name = instance_workflow_name
                apply_reason = receipt_apply_reason or "未提供原因"
                create_time_str = receipt_create_time.strftime("%Y-%m-%d %H:%M") if receipt_create_time else "未知"
                
                # 构建消息内容
                markdown_content = _build_complete_message_content(
                    workflow_name,
                    receipt_receipt_id,
                    apply_reason,
                    creator_name,
                    create_time_str
                )
                
                # 使用消息队列发送通知
                asyncio.create_task(_send_approval_message_background([creator_qy_wechat_userid], markdown_content))
            
            # 更新退款单状态
            if receipt_refund_id and receipt_refund_id > 0:
                erp_order_refund = CF.get_crud(ErpOrderRefund)
                refund_obj = await erp_order_refund.get_by_id(db, receipt_refund_id)
                if refund_obj:
                    refund_obj.audit_state = AuditState.PASS.value
                    refund_obj.update_by = user_id
                    
                    # 根据退费方式进行不同处理
                    refund_details = await erp_order_refund_detail.get_many(db, {"refund_id": refund_obj.id})
                    for detail in refund_details:
                        if detail.refund_way in (RefundWay.CASH.value, RefundWay.TRANSFER.value):
                            # 转账/现金退费：直接更新为成功状态
                            detail.refund_state = RefundDetailState.SUCCESS.value
                            detail.refund_money = detail.apply_money
                            detail.pay_time = datetime.now()
                            settings.logger.info(f"转账/现金退费明细状态更新为成功: 退费单号{detail.refund_order_no}, 退费方式{detail.refund_way}")
                        # 有定时任务进行检查退款状态， 这里不进行处理,避免重复处理或进程数据冲突
                        # elif detail.refund_way == RefundWay.ORIGINAL.value:
                        #     # 原路退费：发起银行退款申请， 后续还有定时任务进行检查退款状态
                        #     try:
                        #         from tasks.sync_refund import apply_refund_by_refund_order_no
                        #         asyncio.create_task(apply_refund_by_refund_order_no(detail.refund_order_no))
                        #         settings.logger.info(f"原路退费已发起银行退款申请: 退费单号{detail.refund_order_no}")
                        #     except Exception as e:
                        #         settings.logger.error(f"原路退费发起银行退款失败: 退费单号{detail.refund_order_no}, 错误: {str(e)}")
                    
                    settings.logger.info(f"退费工作流完成，退费单ID: {refund_obj.id}")
        
        else:
            # 进入下一个节点
            next_node = all_nodes[current_index + 1]
            
            # 检查下一个节点是否为抄送节点，如果是则自动处理
            if next_node.node_type == NodeType.CC.value:
                # 自动处理抄送节点并找到下一个非抄送节点
                next_node = await handle_cc_node(db, instance.id, next_node, all_nodes, current_index + 1, user_id, receipt_create_by)
                # 如果已经处理到最后一个节点且该节点是抄送节点，则直接完成工作流
                if next_node is None:
                    # 流程结束，更新状态
                    await erp_workflow_instance.update_one(db, instance.id, {
                        "status": InstanceStatus.COMPLETED.value,  # 已完成
                        "finish_time": datetime.now(),
                        "update_by": user_id
                    }, commit=False)
                    
                    # 更新单据状态
                    await erp_receipt.update_one(db, receipt_id, {
                        "audit_state": AuditState.PASS.value,
                        "update_by": user_id,
                        "workflow_instance_id": 0
                    }, commit=False)
                    
                    # 给发起人发送流程完成通知
                    creator = await erp_account.get_by_id(db, receipt_create_by) if receipt_create_by else None
                    if creator and creator.qy_wechat_userid and creator.qy_wechat_userid.strip():
                        # 构建消息参数
                        creator_name = creator.employee_name if creator else "未知"
                        creator_qy_wechat_userid = creator.qy_wechat_userid
                        workflow_name = instance_workflow_name
                        apply_reason = receipt_apply_reason or "未提供原因"
                        create_time_str = receipt_create_time.strftime("%Y-%m-%d %H:%M") if receipt_create_time else "未知"
                        
                        # 构建消息内容
                        markdown_content = _build_complete_message_content(
                            workflow_name,
                            receipt_receipt_id,
                            apply_reason,
                            creator_name,
                            create_time_str
                        )
                        
                        # 使用消息队列发送通知
                        asyncio.create_task(_send_approval_message_background([creator_qy_wechat_userid], markdown_content))
                    
                    # 更新退款单状态
                    if receipt_refund_id and receipt_refund_id > 0:
                        erp_order_refund = CF.get_crud(ErpOrderRefund)
                        refund_obj = await erp_order_refund.get_by_id(db, receipt_refund_id)
                        if refund_obj:
                            refund_obj.audit_state = AuditState.CANCEL.value
                            refund_obj.update_by = user_id
                            
                            # 根据退费方式进行不同处理
                            refund_details = await erp_order_refund_detail.get_many(db, {"refund_id": refund_obj.id})
                            for detail in refund_details:
                                if detail.refund_way in (RefundWay.CASH.value, RefundWay.TRANSFER.value):
                                    # 转账/现金退费：直接更新为成功状态
                                    detail.refund_state = RefundDetailState.SUCCESS.value
                                    detail.refund_money = detail.apply_money
                                    detail.pay_time = datetime.now()
                                    settings.logger.info(f"转账/现金退费明细状态更新为成功: 退费单号{detail.refund_order_no}, 退费方式{detail.refund_way}")
                                elif detail.refund_way == RefundWay.ORIGINAL.value:
                                    # 原路退费：发起银行退款申请
                                    try:
                                        from tasks.sync_refund import apply_refund_by_refund_order_no
                                        asyncio.create_task(apply_refund_by_refund_order_no(detail.refund_order_no))
                                        settings.logger.info(f"原路退费已发起银行退款申请: 退费单号{detail.refund_order_no}")
                                    except Exception as e:
                                        settings.logger.error(f"原路退费发起银行退款失败: 退费单号{detail.refund_order_no}, 错误: {str(e)}")
                            
                            settings.logger.info(f"退费工作流完成，退费单ID: {refund_obj.id}")
                    
                    await db.commit()
                    return {"message": "审批已完成"}
            
            # 更新工作流实例到下一个节点
            await erp_workflow_instance.update_one(db, instance.id, {
                "current_node_id": next_node.id,
                "current_node_name": next_node.node_name,
                "update_by": user_id
            }, commit=False)
            
            # 处理下一个节点的审批人
            reviewers = []
            
            # 根据审批人类型获取审批人ID列表
            if next_node.approver_type == ApproverType.CONTINUOUS_SUPERIOR.value:
                if next_node.is_continuous_approval:
                    # 获取用户的多级上级
                    level_count = int(next_node.approver_ids) if next_node.approver_ids else 1
                    try:
                        superior_ids = await get_user_superior(db, receipt_create_by, level_count)
                        reviewers = superior_ids
                    except Exception as e:
                        # 如果获取上级失败，则使用备用审批人
                        reviewers = next_node.approver_ids.split(',') if next_node.approver_ids else []
                else:
                    # 从节点配置中获取审批人
                    reviewers = next_node.approver_ids.split(',') if next_node.approver_ids else []
            elif next_node.approver_type == ApproverType.SPECIFIED_MEMBER.value:
                # 指定人员
                reviewers = next_node.approver_ids.split(',') if next_node.approver_ids else []
            elif next_node.approver_type == ApproverType.SPECIFIED_POSITION.value:
                # 指定岗位
                position = next_node.approver_ids
                if position:
                    # 查询该岗位的所有人员
                    position_users = await erp_account.get_many(db, {"qy_wechat_position": position})
                    reviewers = [str(user.id) for user in position_users]
            elif next_node.approver_type == ApproverType.TEACHER.value:
                # 授课老师
                # 获取单据信息
                receipt = await erp_receipt.get_by_id(db, receipt_id)
                if receipt and receipt.class_id and receipt.class_id > 0:
                    # 获取班级信息
                    from models.m_class import ErpClass
                    class_obj = await db.execute(select(ErpClass).where(ErpClass.id == receipt.class_id))
                    class_obj = class_obj.scalar_one_or_none()
                    
                    if class_obj and class_obj.teacher_id:
                        # 获取老师的账号ID
                        from models.m_teacher import ErpAccountTeacher
                        teacher_obj = await db.execute(select(ErpAccountTeacher).where(ErpAccountTeacher.id == class_obj.teacher_id))
                        teacher_obj = teacher_obj.scalar_one_or_none()
                        
                        if teacher_obj and teacher_obj.account_id:
                            reviewers = [str(teacher_obj.account_id)]
                        else:
                            # 直接使用class表中的teacher_id，假定它可能直接是account_id
                            reviewers = [str(class_obj.teacher_id)]
                    else:
                        return {"message": f"找不到班级信息或班级没有关联授课老师"}
                else:
                    return {"message": f"单据不存在或未关联班级"}
            
            # 如果没有找到审批人且不是抄送节点，抛出异常
            if not reviewers and next_node.node_type != NodeType.CC.value:
                return {"message": f"节点'{next_node.node_name}'未找到合适的审批人2"}
            
            # 检查并自动审批创建人
            filtered_reviewers, creator_auto_approved = await _check_and_auto_approve_creator(
                db, instance.id, next_node, reviewers, receipt_create_by
            )
            
            # 创建审批人记录
            reviewer_accounts = []  # 保存审批人账户信息
            
            for reviewer_id in filtered_reviewers:
                try:
                    reviewer_id_int = int(reviewer_id)
                    reviewer = await erp_account.get_by_id(db, reviewer_id_int)
                    reviewer_name = reviewer.employee_name if reviewer else ""
                    
                    # 保存审批人账户信息用于发送通知
                    if reviewer:
                        reviewer_accounts.append(reviewer)
                    
                    # 创建审批人记录
                    await erp_workflow_instance_reviewer.create(db, commit=False, **{
                        "instance_id": instance.id,
                        "node_id": next_node.id,
                        "reviewer_id": reviewer_id_int,
                        "reviewer_name": reviewer_name,
                        "status": 0  # 待审批
                    })
                except (ValueError, TypeError):
                    # 处理reviewer_id不是有效整数的情况
                    continue
            
            # 创建下一个节点的工作流记录
            # 如果创建人被自动审批且没有其他审批人，则节点状态为已审批
            next_node_approve_status = 0  # 待审批
            if creator_auto_approved and not filtered_reviewers:
                next_node_approve_status = NodeAction.APPROVE.value  # 已同意
            
            await erp_workflow_record.create(db, commit=False, **{
                "instance_id": instance.id,
                "node_id": next_node.id,
                "node_name": next_node.node_name,
                "approve_status": next_node_approve_status,
                "sort_no": next_node.sort_no
            })
            
            # 如果下一个节点是审批节点（非抄送节点）且有待审批的人员，发送通知给审批人
            if next_node.node_type != NodeType.CC.value and filtered_reviewers:
                # 获取单据创建人信息
                creator = await erp_account.get_by_id(db, receipt_create_by) if receipt_create_by else None
                
                # 构建消息参数
                creator_name = creator.employee_name if creator else "未知"
                workflow_name = instance_workflow_name
                apply_reason = receipt_apply_reason or "未提供原因"
                create_time_str = receipt_create_time.strftime("%Y-%m-%d %H:%M") if receipt_create_time else "未知"
                
                # 收集审批人的企业微信用户ID
                approval_user_ids = []
                for reviewer in reviewer_accounts:
                    if reviewer.qy_wechat_userid and reviewer.qy_wechat_userid.strip():
                        approval_user_ids.append(reviewer.qy_wechat_userid)
                
                # 构建消息内容
                if approval_user_ids:
                    markdown_content = _build_approval_message_content(
                        next_node.node_name,
                        workflow_name,
                        receipt_receipt_id,
                        apply_reason,
                        creator_name,
                        create_time_str
                    )
                    
                    # 使用消息队列发送通知
                    asyncio.create_task(_send_approval_message_background(approval_user_ids, markdown_content))
            
            # 如果创建人被自动审批且没有其他审批人，需要检查是否自动进入下一个节点
            if creator_auto_approved and not filtered_reviewers:
                logger.info(f"下一节点创建人自动审批且无其他审批人，可能需要继续自动处理: {next_node.node_name}")
                # 这里可以添加递归处理逻辑，但为了避免复杂度，暂时记录日志
    
    elif action == NodeAction.REJECT.value:  # 驳回
        # 流程驳回，更新状态
        await erp_workflow_instance.update_one(db, instance.id, {
            "status": InstanceStatus.REJECTED.value,  # 已驳回
            "finish_time": datetime.now(),
            "update_by": user_id
        }, commit=False)
        
        # 更新单据状态，并清除workflow_instance_id
        await erp_receipt.update_one(db, receipt_id, {
            "audit_state": AuditState.REJECT.value,  # 驳回
            "update_by": user_id,
            "workflow_instance_id": 0  # 清除工作流实例ID，以便重新发起
        }, commit=False)
        
        # 处理电子钱包反向操作
        await revert_ewallet_changes(db, receipt_id, user_id, "驳回")
        
        # 检查是否为班级开班审批，如果是，则处理班级状态
        if receipt_class_id and receipt_class_id > 0:
            try:
                # 导入班级审核处理函数
                from app_teach.modules import handle_class_approval
                
                # 处理班级审核结果
                await handle_class_approval(
                    db=db,
                    receipt_id=receipt_id,
                    status=AuditState.REJECT.value  # 3表示驳回
                )
                logger.info(f"班级审核驳回处理完成: receipt_id={receipt_id}, class_id={receipt_class_id}")
            except Exception as e:
                logger.error(f"处理班级审核驳回出错: {str(e)}")
        
        # 更新退款单状态
        if receipt_refund_id and receipt_refund_id > 0:
            erp_order_refund = CF.get_crud(ErpOrderRefund)
            refund_obj = await erp_order_refund.get_by_id(db, receipt_refund_id)
            if refund_obj:
                refund_obj.audit_state = AuditState.CANCEL.value
                refund_obj.update_by = user_id
                
                
                
                # 获取退款详情
                refund_details = await erp_order_refund_detail.get_many(db, {"refund_id": refund_obj.id})
                if refund_details:
                    # 收集需要恢复的订单IDs
                    order_ids = [detail.order_id for detail in refund_details if detail.order_id]
                    order_student_ids = [detail.order_student_id for detail in refund_details if detail.order_student_id]
                    orders = []
                    
                    if order_ids:
                        # 获取订单信息
                        orders = await erp_order.get_many(db, raw=[ErpOrder.id.in_(order_ids)])
                        # 恢复订单状态为已支付
                        for order in orders:
                            order.order_state = OrderState.PAID.value
                    
                    if order_student_ids:
                        # 恢复学生订单状态为正常，并恢复课节
                        for order_student_id in order_student_ids:
                            order_student_obj = await erp_order_student.get_by_id(db, order_student_id)
                            if order_student_obj:
                                order_student_obj.student_state = StudentState.NORMAL.value
                                
                                # 恢复课节：查找对应的退费明细，恢复被扣减的课节
                                for detail in refund_details:
                                    if detail.order_student_id == order_student_id and detail.refund_num:
                                        # 恢复课节数
                                        current_hours = float(order_student_obj.total_hours or 0)
                                        refund_hours = float(detail.refund_num or 0)
                                        order_student_obj.total_hours = current_hours + refund_hours
                                        settings.logger.info(f"恢复学生订单课节: 学生订单ID{order_student_id}, 恢复课节{refund_hours}, 当前总课节{order_student_obj.total_hours}")
                    
                    # 恢复报价单状态
                    if orders and len(orders) > 0:
                        offer_ids = list(set([order.offer_id for order in orders if order.offer_id]))
                        for offer_id in offer_ids:
                            offer_obj = await erp_order_offer.get_by_id(db, offer_id)
                            if offer_obj:
                                offer_obj.offer_state = OfferState.PAID.value
                
                settings.logger.info(f"驳回退款申请，恢复订单状态和课节: 退款单ID: {refund_obj.id}")
    
    await db.commit()
    return {"message": "审批处理完成"}


async def cancel_workflow(db: AsyncSession, receipt_id: int, user_id: int):
    """
    取消工作流
    
    Args:
        db: 数据库会话
        receipt_id: 单据ID
        user_id: 用户ID
    
    Returns:
        处理结果
    """
    # 获取单据
    receipt = await erp_receipt.get_by_id(db, receipt_id)
    if not receipt:
        raise Exception("单据不存在")
    
    # 提前保存需要的属性值
    receipt_create_by = receipt.create_by
    receipt_class_id = receipt.class_id
    receipt_refund_id = receipt.refund_id
    
    # 只有创建人可以取消
    print(receipt_create_by, user_id)
    if receipt_create_by != user_id:
        raise Exception("只有创建人可以取消工作流")
    
    # 获取工作流实例
    instance = await erp_workflow_instance.get_by_id(db, receipt.workflow_instance_id)
    if not instance:
        raise Exception("工作流实例不存在")
    
    # 只有进行中的工作流可以取消
    if instance.status != InstanceStatus.PROCESSING.value:
        raise Exception("只有进行中的工作流可以取消")
    
    # 更新工作流实例状态
    await erp_workflow_instance.update_one(db, instance.id, {
        "status": 3,  # 已取消
        "finish_time": datetime.now(),
        "update_by": user_id
    }, commit=False)
    
    # 更新单据状态，同时清除 workflow_instance_id
    await erp_receipt.update_one(db, receipt_id, {
        "audit_state": AuditState.CANCEL.value,  # 取消
        "update_by": user_id,
        "workflow_instance_id": 0  # 清除工作流实例ID
    }, commit=False)
    
    # 处理电子钱包反向操作
    await revert_ewallet_changes(db, receipt_id, user_id, "取消")
    
    # 检查是否为班级开班审批，如果是，则处理班级状态
    if receipt_class_id and receipt_class_id > 0:
        try:
            # 导入班级审核处理函数
            from app_teach.modules import handle_class_approval
            
            # 处理班级审核结果
            await handle_class_approval(
                db=db,
                receipt_id=receipt_id,
                status=AuditState.CANCEL.value  # 4表示取消
            )
            logger.info(f"班级审核取消处理完成: receipt_id={receipt_id}, class_id={receipt_class_id}")
        except Exception as e:
            logger.error(f"处理班级审核取消出错: {str(e)}")
    
    # 检查是否为退费申请，如果是，则恢复相关状态
    if receipt_refund_id and receipt_refund_id > 0:
        try:
            erp_order_refund = CF.get_crud(ErpOrderRefund)
            refund_obj = await erp_order_refund.get_by_id(db, receipt_refund_id)
            if refund_obj:
                refund_obj.audit_state = AuditState.CANCEL.value
                refund_obj.update_by = user_id
                
                # 获取退款详情
                refund_details = await erp_order_refund_detail.get_many(db, {"refund_id": refund_obj.id})
                if refund_details:
                    # 收集需要恢复的订单IDs
                    order_ids = [detail.order_id for detail in refund_details if detail.order_id]
                    order_student_ids = [detail.order_student_id for detail in refund_details if detail.order_student_id]
                    orders = []
                    
                    if order_ids:
                        # 获取订单信息
                        orders = await erp_order.get_many(db, raw=[ErpOrder.id.in_(order_ids)])
                        # 恢复订单状态为已支付
                        for order in orders:
                            order.order_state = OrderState.PAID.value
                    
                    # 收集涉及的班级ID，用于后续班级状态检查
                    affected_class_ids = set()
                    
                    if order_student_ids:
                        # 恢复学生订单状态为正常，并恢复课节
                        for order_student_id in order_student_ids:
                            order_student_obj = await erp_order_student.get_by_id(db, order_student_id)
                            if order_student_obj:
                                order_student_obj.student_state = StudentState.NORMAL.value
                                
                                # 收集班级ID
                                if order_student_obj.class_id:
                                    affected_class_ids.add(order_student_obj.class_id)
                                
                                # 恢复课节：查找对应的退费明细，恢复被扣减的课节
                                for detail in refund_details:
                                    if detail.order_student_id == order_student_id and detail.refund_num:
                                        # 恢复课节数
                                        current_hours = float(order_student_obj.total_hours or 0)
                                        refund_hours = float(detail.refund_num or 0)
                                        order_student_obj.total_hours = current_hours + refund_hours
                                        settings.logger.info(f"取消退费申请，恢复学生订单课节: 学生订单ID{order_student_id}, 恢复课节{refund_hours}, 当前总课节{order_student_obj.total_hours}")
                    
                    # 恢复报价单状态
                    if orders and len(orders) > 0:
                        offer_ids = list(set([order.offer_id for order in orders if order.offer_id]))
                        for offer_id in offer_ids:
                            offer_obj = await erp_order_offer.get_by_id(db, offer_id)
                            if offer_obj:
                                offer_obj.offer_state = OfferState.PAID.value
                    
                settings.logger.info(f"取消退费申请，恢复订单状态和课节: 退款单ID: {refund_obj.id}")
        except Exception as e:
            logger.error(f"处理退费申请取消出错: {str(e)}")
    
    # 记录取消操作的审批记录
    current_node = await erp_workflow_node.get_by_id(db, instance.current_node_id)
    if current_node:
        user = await erp_account.get_by_id(db, user_id)
        user_name = user.employee_name if user else ""
        
        await erp_workflow_record.create(db, commit=False, **{
            "instance_id": instance.id,
            "node_id": current_node.id,
            "node_name": current_node.node_name,
            "approver_id": user_id,
            "approver_name": user_name,
            "approve_status": 4,  # 撤销
            "approve_time": datetime.now(),
            "approve_opinion": "用户取消工作流",
            "sort_no": current_node.sort_no
        })
    
    # 更新所有待审批的审批人状态
    pending_reviewers = await erp_workflow_instance_reviewer.get_many(db, {
        "instance_id": instance.id,
        "status": 0  # 待审批
    })
    
    for reviewer in pending_reviewers:
        await erp_workflow_instance_reviewer.update_one(db, reviewer.id, {
            "status": 3,  # 已取消
            "review_time": datetime.now(),
            "comments": "工作流已取消"
        }, commit=False)
    
    await db.commit()
    return True



# 新增用于生成工作流预览的函数
async def generate_workflow_preview(db: AsyncSession, receipt_id: int, workflow_id: int, user_id: int):
    """
    生成工作流预览信息
    Args:
        db: 数据库会话
        receipt_id: 单据ID
        workflow_id: 工作流定义ID 
        user_id: 当前用户ID
    Returns:
        工作流预览信息，包含所有节点和审批人
    """
    from public_api.modules import get_user_superior
    
    # 获取工作流定义
    workflow_def = await erp_workflow_def.get_by_id(db, workflow_id)
    if not workflow_def:
        return {"error": "工作流定义不存在"}
    
    # 获取工作流节点，按照排序号排序
    nodes = await erp_workflow_node.get_many(db, {
        "workflow_id": workflow_id
    }, orderby=["sort_no"])
    
    if not nodes:
        return {"error": f"工作流'{workflow_def.workflow_name} ({workflow_def.id})'节点不存在"}
    
    # 获取单据信息（用于查询创建人等信息）
    receipt = await erp_receipt.get_by_id(db, receipt_id)
    
    # 构建预览信息
    preview_info = {
        "workflow_id": workflow_id,
        "workflow_name": workflow_def.workflow_name,
        "nodes": []
    }
    
    # 获取企业微信岗位信息（用于缓存后续查询）
    positions_cache = {}
    
    # 处理所有节点
    for node in nodes:
        node_info = {
            "id": node.id,
            "node_name": node.node_name,
            "node_desc": node.node_desc,
            "node_type": node.node_type,  # 1审批节点 2抄送节点
            "sort_no": node.sort_no,
            "is_countersign": node.is_countersign,  # 0否 1是(会签)
            "approvers": []
        }
        
        # 判断是否是审批节点
        if node.node_type == NodeType.APPROVAL.value:  # 审批节点
            # 根据审批人类型获取审批人列表
            if node.approver_type == ApproverType.CONTINUOUS_SUPERIOR.value:  # 连续多级上级
                if node.is_continuous_approval:
                    # 获取用户的多级上级
                    level_count = int(node.approver_ids) if node.approver_ids else 1
                    try:
                        # 使用get_user_superior函数获取上级
                        superior_ids = await get_user_superior(db, user_id, level_count)
                        
                        # 获取上级的账号信息
                        for superior_id in superior_ids:
                            try:
                                superior_id_int = int(superior_id)
                                superior = await erp_account.get_by_id(db, superior_id_int)
                                if superior:
                                    node_info["approvers"].append({
                                        "id": superior_id_int,
                                        "name": superior.employee_name,
                                        "type": "多级上级",
                                        "level": level_count
                                    })
                            except (ValueError, TypeError):
                                continue
                        
                        if not node_info["approvers"]:
                            node_info["approvers"].append({
                                "type": "连续多级上级",
                                "level": level_count,
                                "message": f"将查找申请人的{level_count}级上级作为审批人"
                            })
                    except Exception as e:
                        node_info["approvers"].append({
                            "type": "连续多级上级",
                            "message": "无法获取上级信息，将使用备用审批人",
                            "error": str(e)
                        })
                        # 使用备用审批人
                        reviewer_ids = node.approver_ids.split(',') if node.approver_ids else []
                        for reviewer_id in reviewer_ids:
                            try:
                                reviewer_id_int = int(reviewer_id)
                                reviewer = await erp_account.get_by_id(db, reviewer_id_int)
                                if reviewer:
                                    node_info["approvers"].append({
                                        "id": reviewer_id_int,
                                        "name": reviewer.employee_name,
                                        "type": "备用审批人"
                                    })
                            except (ValueError, TypeError):
                                continue
                else:
                    # 从节点配置中获取审批人
                    reviewer_ids = node.approver_ids.split(',') if node.approver_ids else []
                    for reviewer_id in reviewer_ids:
                        try:
                            reviewer_id_int = int(reviewer_id)
                            reviewer = await erp_account.get_by_id(db, reviewer_id_int)
                            if reviewer:
                                node_info["approvers"].append({
                                    "id": reviewer_id_int,
                                    "name": reviewer.employee_name,
                                    "type": "指定人员"
                                })
                        except (ValueError, TypeError):
                            continue
                    
            elif node.approver_type == ApproverType.SPECIFIED_MEMBER.value:  # 指定人员
                reviewer_ids = node.approver_ids.split(',') if node.approver_ids else []
                for reviewer_id in reviewer_ids:
                    try:
                        reviewer_id_int = int(reviewer_id)
                        reviewer = await erp_account.get_by_id(db, reviewer_id_int)
                        if reviewer:
                            node_info["approvers"].append({
                                "id": reviewer_id_int,
                                "name": reviewer.employee_name,
                                "type": "指定人员"
                            })
                    except (ValueError, TypeError):
                        continue
                
            elif node.approver_type == ApproverType.SPECIFIED_POSITION.value:  # 指定岗位
                position = node.approver_ids
                if position:
                    # 如果缓存中没有该岗位信息，则查询
                    if position not in positions_cache:
                        position_users = await erp_account.get_many(db, {"qy_wechat_position": position})
                        positions_cache[position] = position_users
                    else:
                        position_users = positions_cache[position]
                    
                    # 添加所有该岗位的人员
                    for user in position_users:
                        node_info["approvers"].append({
                            "id": user.id,
                            "name": user.employee_name,
                            "type": "岗位",
                            "position": position
                        })
            
            elif node.approver_type == ApproverType.TEACHER.value:  # 授课老师
                # 获取单据信息
                if receipt and receipt.class_id and receipt.class_id > 0:
                    # 获取班级信息
                    from models.m_class import ErpClass
                    class_obj = await db.execute(select(ErpClass).where(ErpClass.id == receipt.class_id))
                    class_obj = class_obj.scalar_one_or_none()
                    
                    if class_obj and class_obj.teacher_id:
                        # 获取老师的账号ID
                        from models.m_teacher import ErpAccountTeacher
                        teacher_obj = await db.execute(select(ErpAccountTeacher).where(ErpAccountTeacher.id == class_obj.teacher_id))
                        teacher_obj = teacher_obj.scalar_one_or_none()
                        
                        # 如果找到了授课老师的账号信息
                        if teacher_obj and teacher_obj.account_id:
                            teacher_account = await erp_account.get_by_id(db, teacher_obj.account_id)
                            if teacher_account:
                                node_info["approvers"].append({
                                    "id": teacher_account.id,
                                    "name": teacher_account.employee_name,
                                    "type": "授课老师"
                                })
                        else:
                            # 直接使用class表中的teacher_id
                            teacher_account = await erp_account.get_by_id(db, class_obj.teacher_id)
                            if teacher_account:
                                node_info["approvers"].append({
                                    "id": teacher_account.id,
                                    "name": teacher_account.employee_name,
                                    "type": "授课老师"
                                })
                            else:
                                node_info["approvers"].append({
                                    "type": "授课老师",
                                    "message": "将查找班级的授课老师作为审批人"
                                })
                    else:
                        node_info["approvers"].append({
                            "type": "授课老师",
                            "message": "找不到班级信息或班级没有关联授课老师"
                        })
                else:
                    node_info["approvers"].append({
                        "type": "授课老师",
                        "message": "单据未关联班级，将无法找到授课老师"
                    })
            
            # 特别处理会签和或签
            if node.is_countersign == 1:  # 会签
                node_info["approval_type"] = "会签"
                node_info["approval_rule"] = "需要所有审批人同意才能通过"
            else:  # 或签
                node_info["approval_type"] = "或签"
                node_info["approval_rule"] = "任一审批人同意即可通过"
        
        # 抄送节点处理
        elif node.node_type == 2:  # 抄送节点
            reviewer_ids = node.approver_ids.split(',') if node.approver_ids else []
            for reviewer_id in reviewer_ids:
                try:
                    reviewer_id_int = int(reviewer_id)
                    reviewer = await erp_account.get_by_id(db, reviewer_id_int)
                    if reviewer:
                        node_info["approvers"].append({
                            "id": reviewer_id_int,
                            "name": reviewer.employee_name,
                            "type": "抄送"
                        })
                except (ValueError, TypeError):
                    continue
            
            node_info["approval_type"] = "抄送"
            node_info["approval_rule"] = "仅作通知，不需要审批操作"
        
        preview_info["nodes"].append(node_info)
    
    return preview_info

# 在handle_workflow_action函数之后添加处理抄送节点的辅助函数
async def handle_cc_node(db: AsyncSession, instance_id: int, cc_node: ErpWorkflowNode, all_nodes: list, current_index: int, operator_id: int, receipt_creator_id: int):
    """
    处理抄送节点并找到下一个非抄送节点
    
    Args:
        db: 数据库会话
        instance_id: 工作流实例ID
        cc_node: 当前抄送节点
        all_nodes: 所有节点列表
        current_index: 当前节点索引
        operator_id: 操作人ID
        receipt_creator_id: 单据创建人ID
        
    Returns:
        下一个非抄送节点，如果已是最后一个节点则返回None
    """
    # 处理当前抄送节点
    # 1. 创建抄送记录
    await erp_workflow_record.create(db, commit=False, **{
        "instance_id": instance_id,
        "node_id": cc_node.id,
        "node_name": cc_node.node_name,
        "approver_id": operator_id,
        "approver_name": "系统自动处理",
        "approve_status": NodeAction.APPROVE.value,  # 自动同意
        "approve_time": datetime.now(),
        "approve_opinion": "抄送节点自动处理",
        "sort_no": cc_node.sort_no
    })
    
    # 2. 创建抄送人记录
    reviewers = []
    # 根据审批人类型获取审批人ID列表
    if cc_node.approver_type == ApproverType.CONTINUOUS_SUPERIOR.value:
        if cc_node.is_continuous_approval:
            # 获取用户的多级上级
            level_count = int(cc_node.approver_ids) if cc_node.approver_ids else 1
            try:
                superior_ids = await get_user_superior(db, receipt_creator_id, level_count)
                reviewers = superior_ids
            except Exception as e:
                # 如果获取上级失败，则使用备用审批人
                reviewers = cc_node.approver_ids.split(',') if cc_node.approver_ids else []
        else:
            # 从节点配置中获取审批人
            reviewers = cc_node.approver_ids.split(',') if cc_node.approver_ids else []
    elif cc_node.approver_type == ApproverType.SPECIFIED_MEMBER.value:
        # 指定人员
        reviewers = cc_node.approver_ids.split(',') if cc_node.approver_ids else []
    elif cc_node.approver_type == ApproverType.SPECIFIED_POSITION.value:
        # 指定岗位
        position = cc_node.approver_ids
        if position:
            # 查询该岗位的所有人员
            position_users = await erp_account.get_many(db, {"qy_wechat_position": position})
            reviewers = [str(user.id) for user in position_users]
    elif cc_node.approver_type == ApproverType.TEACHER.value:
        # 授课老师
        # 获取单据信息
        instance = await erp_workflow_instance.get_by_id(db, instance_id)
        receipt = await erp_receipt.get_by_id(db, instance.business_id) if instance else None
        if receipt and receipt.class_id and receipt.class_id > 0:
            # 获取班级信息
            from models.m_class import ErpClass
            class_obj = await db.execute(select(ErpClass).where(ErpClass.id == receipt.class_id))
            class_obj = class_obj.scalar_one_or_none()
            
            if class_obj and class_obj.teacher_id:
                # 获取老师的账号ID
                from models.m_teacher import ErpAccountTeacher
                teacher_obj = await db.execute(select(ErpAccountTeacher).where(ErpAccountTeacher.id == class_obj.teacher_id))
                teacher_obj = teacher_obj.scalar_one_or_none()
                
                if teacher_obj and teacher_obj.account_id:
                    reviewers = [str(teacher_obj.account_id)]
                else:
                    # 直接使用class表中的teacher_id，假定它可能直接是account_id
                    reviewers = [str(class_obj.teacher_id)]
            else:
                logger.error(f"找不到班级信息或班级没有关联授课老师: instance_id={instance_id}, class_id={receipt.class_id}")
        else:
            logger.error(f"单据不存在或未关联班级: instance_id={instance_id}")
    
    # 创建审批人记录
    cc_user_ids = []  # 保存抄送人的企业微信用户ID
    
    # 获取工作流实例和相关单据信息，用于组装消息内容
    instance = await erp_workflow_instance.get_by_id(db, instance_id)
    receipt = None
    if instance and instance.business_id:
        receipt = await erp_receipt.get_by_id(db, instance.business_id)
    
    # 获取创建人信息
    creator = None
    if receipt and receipt.create_by:
        creator = await erp_account.get_by_id(db, receipt.create_by)
    
    # 检查并自动审批创建人（抄送节点也适用自动跳过逻辑）
    filtered_reviewers, creator_auto_approved = await _check_and_auto_approve_creator(
        db, instance_id, cc_node, reviewers, receipt_creator_id
    )
    
    # 处理剩余的抄送人
    for reviewer_id in filtered_reviewers:
        try:
            reviewer_id_int = int(reviewer_id)
            reviewer = await erp_account.get_by_id(db, reviewer_id_int)
            reviewer_name = reviewer.employee_name if reviewer else ""
            
            # 创建审批人记录并设置为已处理
            await erp_workflow_instance_reviewer.create(db, commit=False, **{
                "instance_id": instance_id,
                "node_id": cc_node.id,
                "reviewer_id": reviewer_id_int,
                "reviewer_name": reviewer_name,
                "status": NodeAction.APPROVE.value,  # 自动同意
                "review_time": datetime.now(),
                "comments": "抄送节点自动处理"
            })
            
            # 收集抄送人的企业微信用户ID
            if reviewer and reviewer.qy_wechat_userid and reviewer.qy_wechat_userid.strip():
                cc_user_ids.append(reviewer.qy_wechat_userid)
        except (ValueError, TypeError):
            # 处理reviewer_id不是有效整数的情况
            continue
    
    # 3. 向抄送人发送企业微信消息
    if cc_user_ids and receipt and instance:
        # 构建消息参数
        creator_name = creator.employee_name if creator else "未知"
        workflow_name = instance.workflow_name if instance else "未知"
        apply_reason = receipt.apply_reason if receipt else "未知"
        create_time_str = receipt.create_time.strftime("%Y-%m-%d %H:%M") if receipt and receipt.create_time else "未知"
        
        # 构建消息内容
        markdown_content = _build_cc_message_content(
            cc_node.node_name,
            workflow_name,
            receipt.id,
            apply_reason,
            creator_name,
            create_time_str
        )
        
        # 使用消息队列发送通知
        asyncio.create_task(_send_cc_message_background(cc_user_ids, markdown_content))
    
    # 4. 检查是否有下一个节点
    if current_index >= len(all_nodes) - 1:
        # 已经是最后一个节点
        return None
    
    # 5. 获取下一个节点
    next_node = all_nodes[current_index + 1]
    
    # 6. 如果下一个节点也是抄送节点，递归处理
    if next_node.node_type == NodeType.CC.value:
        return await handle_cc_node(db, instance_id, next_node, all_nodes, current_index + 1, operator_id, receipt_creator_id)
    
    # 返回下一个非抄送节点
    return next_node


# 添加辅助函数
async def process_workflow_instances(db: AsyncSession, instances, business_ids):
    """处理工作流实例数据，获取关联信息并整合结果"""
    result_data = []
    if not instances:
        return result_data
    
    # 批量查询单据信息
    receipts = await erp_receipt.get_many(
        db, 
        raw=[
            ErpReceipt.id.in_(business_ids),
            ErpReceipt.disable == 0
        ]
    )
    receipt_map = {receipt.id: receipt for receipt in receipts}
    
    # 收集涉及到的工作流定义ID
    workflow_ids = set(instance.workflow_id for instance in instances)
    
    # 批量获取工作流定义
    workflow_defs = await erp_workflow_def.get_many(
        db, 
        raw=[
            ErpWorkflowDef.id.in_(workflow_ids),
            ErpWorkflowDef.disable == 0
        ]
    )
    workflow_def_map = {wf.id: wf for wf in workflow_defs}
    
    # 收集发起人ID
    creator_ids = set(receipt.create_by for receipt in receipts if receipt.create_by)
    
    # 批量获取发起人信息
    creators = await erp_account.get_many(
        db,
        raw=[
            ErpAccount.id.in_(creator_ids),
            ErpAccount.disable == 0
        ]
    )
    creator_map = {creator.id: creator for creator in creators}
    
    # 批量获取单据对应的财务信息，获取ie_type
    finance_infos = await erp_receipt_finance.get_many(
        db,
        raw=[
            ErpReceiptFinance.receipt_id.in_(business_ids),
            ErpReceiptFinance.disable == 0
        ]
    )
    finance_map = {finance.receipt_id: finance for finance in finance_infos}
    
    # 整合数据
    for instance in instances:
        receipt = receipt_map.get(instance.business_id)
        workflow_def = workflow_def_map.get(instance.workflow_id) if instance.workflow_id else None
        
        if receipt:
            # 获取发起人信息（增加空值检查）
            creator = creator_map.get(receipt.create_by) if receipt.create_by else None
            creator_name = creator.employee_name if creator else None
            creator_avatar = creator.avatar if creator else None
            
            # 关联绑定的单据费用类型
            cost_type_bind= await erp_cost_type_bind.get_one(db, workflow_id=instance.workflow_id)


            # 构建返回数据
            result_item = {
                "receipt_id": receipt.id,
                # "ie_type": ie_type,
                "apply_reason": receipt.apply_reason,
                "workflow_name": workflow_def.workflow_name if workflow_def else None,
                "workflow_desc": workflow_def.workflow_desc if workflow_def else None,
                "workflow_type": workflow_def.workflow_type if workflow_def else None,
                "current_node_name": instance.current_node_name,
                "audit_state": receipt.audit_state,
                "create_by": receipt.create_by,
                "creator_name": creator_name,
                "creator_avatar": creator_avatar,
                "create_time": receipt.create_time,
                "workflow_id": instance.workflow_id,
                "instance_id": instance.id,
                "relate_finance_id": receipt.relate_finance_id,
                "workflow_cost_type": cost_type_bind,
            }
            result_data.append(result_item)
    
    return result_data



async def get_workflow_list_data(
    db: AsyncSession,
    page: int,
    page_size: int,
    user_id: int,
    query_type: str,  # 'submit', 'pending', 'approved', 'cc'
    date_start: str = None,
    date_end: str = None,
    status: int = None,
):
    """通用工作流列表查询函数
    
    Args:
        db: 数据库会话
        page: 页码
        page_size: 每页大小
        user_id: 用户ID
        query_type: 查询类型, 'submit'/'pending'/'approved'/'cc'
        date_start: 开始日期（可选）
        date_end: 结束日期（可选）
        status: 状态筛选（仅用于submit类型）
    """
    # 根据查询类型获取相应的实例ID或单据列表
    if query_type == 'submit':
        # 查询我发起的工作流
        receipts, total_count = await get_my_submit_workflows(
            db, user_id, page, page_size, status, date_start, date_end
        )
        # 格式化数据
        result_data = await format_workflow_list_data(db, receipts, user_id)
        return result_data, total_count
        
    # 针对其他三种类型，获取实例ID
    if query_type == 'pending':
        # 获取待我审批的工作流实例ID
        instance_ids = await get_pending_approve_instances(db, user_id)
    elif query_type == 'approved':
        # 获取我已审批的工作流实例ID
        from sqlalchemy import select
        record_query = select(ErpWorkflowRecord.instance_id).where(
            ErpWorkflowRecord.approver_id == user_id,
            ErpWorkflowRecord.approve_status.in_([1, 2]),
            ErpWorkflowRecord.disable == 0
        ).distinct()
        record_result = await db.execute(record_query)
        instance_ids = [row[0] for row in record_result.fetchall()]
    else:  # query_type == 'cc'
        # 获取抄送给我的工作流实例ID
        instance_ids = await get_cc_to_me_instances(db, user_id)
    
    if not instance_ids:
        return [], 0
    
    # 查询这些实例
    raw_query_params = [
        erp_workflow_instance.model.id.in_(instance_ids),
        erp_workflow_instance.model.disable == 0,
    ]
    
    # 添加日期过滤条件
    if date_start and date_end:
        raw_query_params.append(erp_workflow_instance.model.create_time.between(date_start, date_end))
    elif date_start:
        raw_query_params.append(erp_workflow_instance.model.create_time >= date_start)
    elif date_end:
        raw_query_params.append(erp_workflow_instance.model.create_time <= date_end)
    
    # 查询符合条件的实例总数
    from sqlalchemy import func, select, text
    count_stmt = select(func.count()).select_from(erp_workflow_instance.model).where(*raw_query_params)
    count_result = await db.execute(count_stmt)
    total_count = count_result.scalar() or 0
    
    # 查询工作流实例列表
    instances = await erp_workflow_instance.get_many_with_pagination(
        db, 
        page=page, 
        page_size=page_size, 
        raw=raw_query_params,
        orderby=[text("create_time DESC")]
    )
    
    # 收集业务单据ID
    business_ids = [instance.business_id for instance in instances]
    
    # 批量获取各种关联数据，整合生成结果
    result_data = await process_workflow_instances(db, instances, business_ids)
    
    return result_data, total_count

async def handle_cashier_node_processing(db: AsyncSession, receipt_id: int, user_id: int, bank_account_id: int, receipt: ErpReceipt, instance: ErpWorkflowInstance):
    """
    处理出纳节点的各种业务类型
    
    Args:
        db: 数据库会话
        receipt_id: 单据ID
        user_id: 出纳用户ID
        bank_account_id: 银行账户ID（可选）
        receipt: 单据对象
        instance: 工作流实例对象
    """
    from public_api.crud import get_workflow_cost_type_related
    
    # 获取财务单信息并更新出纳审核人ID
    finance_obj = await erp_receipt_finance.get_one(db, receipt_id=receipt_id)
    if finance_obj:
        finance_obj.cashier_id = user_id
        finance_obj.update_by = user_id
    
    # 获取工作流的费用类型绑定，以确定业务类型
    cost_type_bind_obj = None
    if instance.workflow_id:
        cost_type_bind_obj = await get_workflow_cost_type_related(db, workflow_id=instance.workflow_id)
    
    # 根据不同的业务类型处理出纳操作
    if receipt.refund_id and receipt.refund_id > 0:
        # 退款类型：课程退款、教材退款、电子钱包退款等
        await handle_refund_cashier_processing(db, receipt_id, user_id, bank_account_id, receipt, finance_obj)
    elif cost_type_bind_obj:
        # 通过费用类型绑定判断业务类型
        if cost_type_bind_obj.id == CostTypeBind.Reimbursement.value:  # 报销单
            await handle_reimbursement_cashier_processing(db, receipt_id, user_id, bank_account_id, receipt, finance_obj)
        elif cost_type_bind_obj.id == CostTypeBind.Expense.value:  # 支出单
            await handle_expense_cashier_processing(db, receipt_id, user_id, bank_account_id, receipt, finance_obj)
        elif cost_type_bind_obj.id == CostTypeBind.CourseCashRefund.value:  # 课程费用现金退款
            await handle_cash_refund_cashier_processing(db, receipt_id, user_id, bank_account_id, receipt, finance_obj)
        elif cost_type_bind_obj.id == CostTypeBind.TextbookRefund.value:  # 教材退款
            await handle_textbook_refund_cashier_processing(db, receipt_id, user_id, bank_account_id, receipt, finance_obj)
        elif cost_type_bind_obj.id == CostTypeBind.Purchase.value:  # 采购单
            await handle_purchase_cashier_processing(db, receipt_id, user_id, bank_account_id, receipt, finance_obj)
        elif cost_type_bind_obj.id == CostTypeBind.Income.value:  # 收入单
            await handle_income_cashier_processing(db, receipt_id, user_id, receipt, finance_obj)
        else:
            # 其他类型按照财务单的ie_type处理
            await handle_generic_cashier_processing(db, receipt_id, user_id, bank_account_id, receipt, finance_obj)
    else:
        # 如果没有费用类型绑定，按照财务单的ie_type处理
        await handle_generic_cashier_processing(db, receipt_id, user_id, bank_account_id, receipt, finance_obj)


async def handle_refund_cashier_processing(db: AsyncSession, receipt_id: int, user_id: int, bank_account_id: int, receipt: ErpReceipt, finance_obj: ErpReceiptFinance):
    """处理各种退款类型的出纳操作"""
    
    # 在函数开始就导入bill_account_change函数，避免循环导入
    from app_finance.modules import bill_account_change
    
    if not bank_account_id or bank_account_id == 0:  # 原路退款
        # 查询退款明细
        refund_detail_objs = await erp_order_refund_detail.get_many(db, {
            "refund_id": receipt.refund_id
        })
        if not refund_detail_objs:
            raise Exception(f"单据{receipt.id}不存在退款明细")
        
        # 获取银行账户和商户配置
        bank_account_objs = await erp_bank_account.get_many(db, {"account_type": 1})
        bank_account_dict = {bank_account_obj.cmb_merchant_id: bank_account_obj for bank_account_obj in bank_account_objs}
        mall_merchant_objs = await mall_merchant_config.get_many(db)
        mall_merchant_dict = {str(i.Id): i for i in mall_merchant_objs}
        
        for refund_detail_obj in refund_detail_objs:
            # 根据退款明细获取支付记录
            trade_payment_obj = await erp_finance_trade_payment.get_one(db, payment_order_no=refund_detail_obj.order_no)
            if not trade_payment_obj:
                return await ApiFailedResponse(f"明细{refund_detail_obj.order_no}对应的支付记录不存在")
            
            # 获取对应的银行账户
            mall_config_id = trade_payment_obj.merchant_id
            if not mall_config_id:
                return await ApiFailedResponse(f"明细{refund_detail_obj.order_no}对应的支付记录不存在商户ID")
            mall_config_obj = mall_merchant_dict[str(mall_config_id)]
            cmb_mer_id = str(mall_config_obj.CmbMerId)
            bank_account_obj = bank_account_dict.get(cmb_mer_id)
            
            if not bank_account_obj:
                return await ApiFailedResponse(f"找不到对应的银行账户，商户ID: {cmb_mer_id}, 请先在系统中配置银行账户")
            
            # 获取学生信息
            stu_obj = await erp_student.get_by_id(db, trade_payment_obj.stu_id)
            stu_name = stu_obj.stu_name if stu_obj else "未知学生"
            
            # 更新财务单的账户信息
            finance_obj.bank_account_id = bank_account_obj.id
            finance_obj.bank_account_name = bank_account_obj.account_alias
            
            # 记录账户变动（统一使用bill_account_change函数，避免重复计算）
            try:
                await bill_account_change(db, 
                                        bank_account_obj.id, 
                                        2,  # 减少
                                        refund_detail_obj.apply_money, 
                                        f"{stu_name}原路退款-退款单: {refund_detail_obj.id}", 
                                        user_id, 
                                        commit=False, 
                                        receipt_id=receipt_id)
            except Exception as e:
                settings.logger.error(f'出纳节点记录账户变动失败:单据{receipt_id}，账户id:{bank_account_obj.id}，错误:{str(e)}')
    
    else:  # 转账退款
        bank_account_obj = await erp_bank_account.get_by_id(db, bank_account_id)
        if not bank_account_obj:
            raise Exception("指定的银行账户不存在")
        
        # 更新财务单的账户信息
        finance_obj.bank_account_id = bank_account_id
        finance_obj.bank_account_name = bank_account_obj.account_alias
        
        # 获取退款明细计算总金额
        refund_detail_objs = await erp_order_refund_detail.get_many(db, {
            "refund_id": receipt.refund_id
        })
        apply_money = sum([i.apply_money for i in refund_detail_objs])
        
        # 获取学生信息
        stu_name = "未知学生"
        if refund_detail_objs:
            trade_payment_obj = await erp_finance_trade_payment.get_one(db, payment_order_no=refund_detail_objs[0].order_no)
            if trade_payment_obj and trade_payment_obj.stu_id:
                stu_obj = await erp_student.get_by_id(db, trade_payment_obj.stu_id)
                if stu_obj:
                    stu_name = stu_obj.stu_name
        
        # 记录账户变动
        try:
            await bill_account_change(db, 
                                    bank_account_id, 
                                    2,  # 减少
                                    apply_money, 
                                    f"{stu_name}转账退款-退款单: {receipt.id}", 
                                    user_id, 
                                    commit=False, 
                                    receipt_id=receipt_id)
        except Exception as e:
            settings.logger.error(f'出纳节点记录账户变动失败:单据{receipt_id}，账户id:{bank_account_id}，错误:{str(e)}')


async def handle_reimbursement_cashier_processing(db: AsyncSession, receipt_id: int, user_id: int, bank_account_id: int, receipt: ErpReceipt, finance_obj: ErpReceiptFinance):
    """处理报销单的出纳操作"""
    
    if not bank_account_id:
        raise Exception("报销单必须指定付款银行账户")
    
    bank_account_obj = await erp_bank_account.get_by_id(db, bank_account_id)
    if not bank_account_obj:
        raise Exception("指定的银行账户不存在")
    
    # 更新财务单的账户信息
    finance_obj.bank_account_id = bank_account_id
    finance_obj.bank_account_name = bank_account_obj.account_alias
    
    # 获取申请金额
    apply_money = finance_obj.apply_money or finance_obj.trade_money or 0
    if apply_money <= 0:
        raise Exception("报销金额必须大于0")
    
    # 获取关联对象信息用于描述
    related_obj_name = receipt.related_obj_name or "未知对象"
    
    # 记录账户变动（支出，减少账户余额）
    try:
        from app_finance.modules import bill_account_change
        await bill_account_change(db, 
                                bank_account_id, 
                                2,  # 减少
                                apply_money, 
                                f"报销单支出-{related_obj_name}: 单据{receipt_id}", 
                                user_id, 
                                commit=False, 
                                receipt_id=receipt_id)
    except Exception as e:
        settings.logger.error(f'报销单出纳节点记录账户变动失败:单据{receipt_id}，账户id:{bank_account_id}，错误:{str(e)}')
        raise Exception(f"记录账户变动失败: {str(e)}")


async def handle_expense_cashier_processing(db: AsyncSession, receipt_id: int, user_id: int, bank_account_id: int, receipt: ErpReceipt, finance_obj: ErpReceiptFinance):
    """处理支出单的出纳操作"""
    
    if not bank_account_id:
        raise Exception("支出单必须指定付款银行账户")
    
    bank_account_obj = await erp_bank_account.get_by_id(db, bank_account_id)
    if not bank_account_obj:
        raise Exception("指定的银行账户不存在")
    
    # 更新财务单的账户信息
    finance_obj.bank_account_id = bank_account_id
    finance_obj.bank_account_name = bank_account_obj.account_alias
    
    # 获取申请金额
    apply_money = finance_obj.apply_money or finance_obj.trade_money or 0
    if apply_money <= 0:
        raise Exception("支出金额必须大于0")
    
    # 获取关联对象信息用于描述
    related_obj_name = receipt.related_obj_name or "未知对象"
    
    # 记录账户变动（支出，减少账户余额）
    try:
        from app_finance.modules import bill_account_change
        await bill_account_change(db, 
                                bank_account_id, 
                                2,  # 减少
                                apply_money, 
                                f"支出单-{related_obj_name}: 单据{receipt_id}", 
                                user_id, 
                                commit=False, 
                                receipt_id=receipt_id)
    except Exception as e:
        settings.logger.error(f'支出单出纳节点记录账户变动失败:单据{receipt_id}，账户id:{bank_account_id}，错误:{str(e)}')
        raise Exception(f"记录账户变动失败: {str(e)}")


async def handle_cash_refund_cashier_processing(db: AsyncSession, receipt_id: int, user_id: int, bank_account_id: int, receipt: ErpReceipt, finance_obj: ErpReceiptFinance):
    """处理课程费用现金退款和转账退款的出纳操作"""
    
    # 获取退费详情以确定具体的退费方式
    refund_details = await erp_order_refund_detail.get_many(db, {"refund_id": receipt.refund_id})
    if not refund_details:
        raise Exception(f"单据{receipt.id}不存在退款明细")
    
    # 获取退费方式（假设同一退费单的所有明细退费方式相同）
    refund_way = refund_details[0].refund_way
    
    # 根据退费方式处理账户选择
    if not bank_account_id:
        if refund_way == 2:  # 现金退款
            # 尝试获取默认现金账户
            cash_accounts = await erp_bank_account.get_many(db, {"account_type": 2, "disable": 0})  # 现金账户且未禁用
            if not cash_accounts:
                raise Exception("现金退款必须指定现金账户，且系统中无可用现金账户")
            # 选择余额最多的现金账户
            cash_accounts_sorted = sorted(cash_accounts, key=lambda x: float(x.balance or 0), reverse=True)
            bank_account_id = cash_accounts_sorted[0].id
            settings.logger.info(f"现金退款自动选择现金账户: {cash_accounts_sorted[0].account_alias} (余额: {cash_accounts_sorted[0].balance})")
        else:  # 转账退款
            raise Exception("转账退款必须指定付款银行账户")
    
    bank_account_obj = await erp_bank_account.get_by_id(db, bank_account_id)
    if not bank_account_obj:
        raise Exception("指定的银行账户不存在")
    
    # 验证账户状态
    if bank_account_obj.disable == 1:
        raise Exception(f"银行账户已被禁用: {bank_account_obj.account_alias}")
    
    # 根据退费方式验证账户类型
    if refund_way == 2 and bank_account_obj.account_type != 2:  # 现金退款但不是现金账户
        settings.logger.warning(f"现金退款使用了非现金账户: {bank_account_obj.id} ({bank_account_obj.account_alias})")
    elif refund_way == 3 and bank_account_obj.account_type == 2:  # 转账退款但是现金账户
        settings.logger.warning(f"转账退款使用了现金账户: {bank_account_obj.id} ({bank_account_obj.account_alias})")
    
    # 获取申请金额
    apply_money = finance_obj.apply_money or finance_obj.trade_money or 0
    if apply_money <= 0:
        refund_type_name = "现金退款" if refund_way == 2 else "转账退款"
        raise Exception(f"{refund_type_name}金额必须大于0")
    
    # 检查账户余额是否充足
    current_balance = float(bank_account_obj.balance or 0)
    apply_money_float = float(apply_money)
    if current_balance < apply_money_float:
        raise Exception(f"账户余额不足，当前余额: {current_balance}，申请退款: {apply_money_float}")
    
    # 更新财务单的账户信息
    finance_obj.bank_account_id = bank_account_id
    finance_obj.bank_account_name = bank_account_obj.account_alias
    
    # 获取关联对象信息用于描述
    related_obj_name = receipt.related_obj_name or "未知学生"
    refund_type_name = "现金退款" if refund_way == 2 else "转账退款"
    
    # 记录详细的操作日志
    settings.logger.info(f"{refund_type_name}处理开始 - 单据ID: {receipt_id}, "
                        f"学生: {related_obj_name}, "
                        f"退款金额: {apply_money}, "
                        f"使用账户: {bank_account_obj.account_alias}, "
                        f"账户余额: {current_balance}")
    
    # 记录账户变动（退款，减少账户余额）
    try:
        from app_finance.modules import bill_account_change
        await bill_account_change(db, 
                                bank_account_id, 
                                2,  # 减少
                                apply_money, 
                                f"课程费用{refund_type_name}-{related_obj_name}: 单据{receipt_id}", 
                                user_id, 
                                commit=False, 
                                receipt_id=receipt_id)
        
        settings.logger.info(f"{refund_type_name}账户变动记录成功 - 单据ID: {receipt_id}, "
                            f"账户: {bank_account_obj.account_alias}, "
                            f"变动金额: -{apply_money}")
                            
    except Exception as e:
        settings.logger.error(f'{refund_type_name}出纳节点记录账户变动失败:单据{receipt_id}，账户id:{bank_account_id}，错误:{str(e)}')
        raise Exception(f"记录账户变动失败: {str(e)}")


async def handle_textbook_refund_cashier_processing(db: AsyncSession, receipt_id: int, user_id: int, bank_account_id: int, receipt: ErpReceipt, finance_obj: ErpReceiptFinance):
    """处理教材退款的出纳操作"""
    
    if not bank_account_id:
        raise Exception("教材退款必须指定付款银行账户")
    
    bank_account_obj = await erp_bank_account.get_by_id(db, bank_account_id)
    if not bank_account_obj:
        raise Exception("指定的银行账户不存在")
    
    # 更新财务单的账户信息
    finance_obj.bank_account_id = bank_account_id
    finance_obj.bank_account_name = bank_account_obj.account_alias
    
    # 获取申请金额
    apply_money = finance_obj.apply_money or finance_obj.trade_money or 0
    if apply_money <= 0:
        raise Exception("教材退款金额必须大于0")
    
    # 获取关联对象信息用于描述
    related_obj_name = receipt.related_obj_name or "未知学生"
    
    # 记录账户变动（退款，减少账户余额）
    try:
        from app_finance.modules import bill_account_change
        await bill_account_change(db, 
                                bank_account_id, 
                                2,  # 减少
                                apply_money, 
                                f"教材退款-{related_obj_name}: 单据{receipt_id}", 
                                user_id, 
                                commit=False, 
                                receipt_id=receipt_id)
    except Exception as e:
        settings.logger.error(f'教材退款出纳节点记录账户变动失败:单据{receipt_id}，账户id:{bank_account_id}，错误:{str(e)}')
        raise Exception(f"记录账户变动失败: {str(e)}")


async def handle_purchase_cashier_processing(db: AsyncSession, receipt_id: int, user_id: int, bank_account_id: int, receipt: ErpReceipt, finance_obj: ErpReceiptFinance):
    """处理采购单的出纳操作"""
    
    if not bank_account_id:
        raise Exception("采购单必须指定付款银行账户")
    
    bank_account_obj = await erp_bank_account.get_by_id(db, bank_account_id)
    if not bank_account_obj:
        raise Exception("指定的银行账户不存在")
    
    # 更新财务单的账户信息
    finance_obj.bank_account_id = bank_account_id
    finance_obj.bank_account_name = bank_account_obj.account_alias
    
    # 获取申请金额
    apply_money = finance_obj.apply_money or finance_obj.trade_money or 0
    if apply_money <= 0:
        raise Exception("采购金额必须大于0")
    
    # 获取关联对象信息用于描述
    related_obj_name = receipt.related_obj_name or "未知供应商"
    
    # 记录账户变动（采购支出，减少账户余额）
    try:
        from app_finance.modules import bill_account_change
        await bill_account_change(db, 
                                bank_account_id, 
                                2,  # 减少
                                apply_money, 
                                f"采购单支出-{related_obj_name}: 单据{receipt_id}", 
                                user_id, 
                                commit=False, 
                                receipt_id=receipt_id)
    except Exception as e:
        settings.logger.error(f'采购单出纳节点记录账户变动失败:单据{receipt_id}，账户id:{bank_account_id}，错误:{str(e)}')
        raise Exception(f"记录账户变动失败: {str(e)}")


async def handle_income_cashier_processing(db: AsyncSession, receipt_id: int, user_id: int, receipt: ErpReceipt, finance_obj: ErpReceiptFinance):
    """处理收入单的出纳操作"""
    
    # 收入单的账户ID从财务单的bank_account_id字段获取（申请时已选择）
    bank_account_id = finance_obj.bank_account_id
    if not bank_account_id:
        raise Exception("收入单必须在申请时指定收入银行账户")
    
    bank_account_obj = await erp_bank_account.get_by_id(db, bank_account_id)
    if not bank_account_obj:
        raise Exception("指定的银行账户不存在")
    
    # 验证账户状态
    if bank_account_obj.disable == 1:
        raise Exception(f"银行账户已被禁用: {bank_account_obj.account_alias}")
    
    # 更新财务单的账户信息（确保账户名称是最新的）
    finance_obj.bank_account_id = bank_account_id
    finance_obj.bank_account_name = bank_account_obj.account_alias
    
    # 获取申请金额
    apply_money = finance_obj.apply_money or finance_obj.trade_money or 0
    if apply_money <= 0:
        raise Exception("收入金额必须大于0")
    
    # 获取关联对象信息用于描述
    related_obj_name = receipt.related_obj_name or "未知来源"
    
    # 记录详细的操作日志
    settings.logger.info(f"收入单处理开始 - 单据ID: {receipt_id}, "
                        f"收入来源: {related_obj_name}, "
                        f"收入金额: {apply_money}, "
                        f"入账账户: {bank_account_obj.account_alias}, "
                        f"账户余额: {bank_account_obj.balance}")
    
    # 记录账户变动（收入，增加账户余额）
    try:
        from app_finance.modules import bill_account_change
        await bill_account_change(db, 
                                bank_account_id, 
                                1,  # 增加
                                apply_money, 
                                f"收入单入账-{related_obj_name}: 单据{receipt_id}", 
                                user_id, 
                                commit=False, 
                                receipt_id=receipt_id)
        
        settings.logger.info(f"收入单账户变动记录成功 - 单据ID: {receipt_id}, "
                            f"账户: {bank_account_obj.account_alias}, "
                            f"变动金额: +{apply_money}")
                            
    except Exception as e:
        settings.logger.error(f'收入单出纳节点记录账户变动失败:单据{receipt_id}，账户id:{bank_account_id}，错误:{str(e)}')
        raise Exception(f"记录账户变动失败: {str(e)}")


async def handle_generic_cashier_processing(db: AsyncSession, receipt_id: int, user_id: int, bank_account_id: int, receipt: ErpReceipt, finance_obj: ErpReceiptFinance):
    """处理通用的出纳操作（根据workflow_type和ie_type判断）"""
    
    if not finance_obj:
        raise Exception("财务单信息不存在，无法处理出纳操作")
    
    if not bank_account_id:
        raise Exception("必须指定银行账户")
    
    bank_account_obj = await erp_bank_account.get_by_id(db, bank_account_id)
    if not bank_account_obj:
        raise Exception("指定的银行账户不存在")
    
    # 更新财务单的账户信息
    finance_obj.bank_account_id = bank_account_id
    finance_obj.bank_account_name = bank_account_obj.account_alias
    
    # 获取申请金额
    apply_money = finance_obj.apply_money or finance_obj.trade_money or 0
    if apply_money <= 0:
        raise Exception("金额必须大于0")
    
    # 获取工作流定义以确定收支类型
    workflow_def = await erp_workflow_def.get_by_id(db, receipt.workflow_id)
    if not workflow_def:
        raise Exception("找不到工作流定义")
    
    # 优先根据workflow_type判断收支类型，其次根据ie_type
    if workflow_def.workflow_type == 1:  # 收入类型工作流
        change_type = 1  # 增加账户余额
        operation_desc = "收入"
    elif workflow_def.workflow_type == 2:  # 支出类型工作流
        change_type = 2  # 减少账户余额
        operation_desc = "支出"
    elif workflow_def.workflow_type == 3:  # 收支相抵类型工作流
        # 收支相抵类型根据ie_type判断
        ie_type = finance_obj.ie_type or 2  # 默认为支出
        change_type = 1 if ie_type == 1 else 2  # 1收入-增加账户，2支出-减少账户
        operation_desc = "收入" if ie_type == 1 else "支出"
    elif workflow_def.workflow_type == 4:  # 非财务流程
        raise Exception("非财务流程不应该进入出纳审核节点")
    else:
        # 兜底逻辑：根据ie_type判断
        ie_type = finance_obj.ie_type or 2  # 默认为支出
        change_type = 1 if ie_type == 1 else 2  # 1收入-增加账户，2支出-减少账户
        operation_desc = "收入" if ie_type == 1 else "支出"
        settings.logger.warning(f"工作流类型未知({workflow_def.workflow_type})，使用ie_type({ie_type})作为兜底逻辑")
    
    # 获取关联对象信息用于描述
    related_obj_name = receipt.related_obj_name or "未知对象"
    
    # 记录详细的操作日志
    settings.logger.info(f"通用出纳处理 - 单据ID: {receipt_id}, "
                        f"工作流类型: {workflow_def.workflow_type}, "
                        f"操作类型: {operation_desc}, "
                        f"变动类型: {change_type}, "
                        f"金额: {apply_money}, "
                        f"账户: {bank_account_obj.account_alias}")
    
    # 记录账户变动
    try:
        from app_finance.modules import bill_account_change
        await bill_account_change(db, 
                                bank_account_id, 
                                change_type, 
                                apply_money, 
                                f"{operation_desc}-{related_obj_name}: 单据{receipt_id}", 
                                user_id, 
                                commit=False, 
                                receipt_id=receipt_id)
        
        settings.logger.info(f"通用出纳账户变动记录成功 - 单据ID: {receipt_id}, "
                            f"账户: {bank_account_obj.account_alias}, "
                            f"变动金额: {'+' if change_type == 1 else '-'}{apply_money}")
                            
    except Exception as e:
        settings.logger.error(f'通用出纳节点记录账户变动失败:单据{receipt_id}，账户id:{bank_account_id}，错误:{str(e)}')
        raise Exception(f"记录账户变动失败: {str(e)}")


async def revert_ewallet_changes(db: AsyncSession, receipt_id: int, user_id: int, action_type: str = "驳回"):
    # 在函数内部导入，避免循环导入
    from app_finance.modules import stu_ewallet_change
    """
    处理电子钱包的反向操作
    
    Args:
        db: 数据库会话
        receipt_id: 单据ID
        user_id: 操作用户ID
        action_type: 操作类型，如"驳回"、"取消"等
        
    Returns:
        None
    """
    from sqlalchemy import select
    
    # 查询与该单据相关的电子钱包变动记录
    stmt = select(ErpStudentEwalletLog).where(
        ErpStudentEwalletLog.receipt_id == receipt_id,
        ErpStudentEwalletLog.disable == 0
    )
    result = await db.execute(stmt)
    ewallet_logs = result.scalars().all()
    
    # 如果存在电子钱包变动记录，则进行反向操作
    for log in ewallet_logs:
        try:
            # 反向操作：原来是增加(1)现在就减少(2)，原来是减少(2)现在就增加(1)
            reverse_change_type = 2 if log.change_type == 1 else 1
            
            # 使用原有金额进行反向操作
            await stu_ewallet_change(
                db,
                stu_id=log.stu_id,
                change_type=reverse_change_type,
                amount=log.amount,
                uid=user_id,
                desc=f"单据{receipt_id}被{action_type}，电子钱包反向调整",
                receipt_id=receipt_id,
                commit=False
            )
            
            settings.logger.info(f'{action_type}单据，执行电子钱包反向操作:单据{receipt_id}，学员id:{log.stu_id}，反向金额:{log.amount}，变动类型:{reverse_change_type}')
        except Exception as e:
            settings.logger.error(f'{action_type}单据，电子钱包反向操作失败:单据{receipt_id}，错误:{str(e)}')
            # 不要因为电子钱包操作失败而中断整个流程



async def get_receipt_list_data(
    db: AsyncSession,
    page: int,
    page_size: int,
    is_transfer: int = None,
    audit_state: int = None,
    start_time: str = None,
    end_time: str = None,
    keyword: str = None,
    create_by: int = None,
    receipt_id: int = None,
    workflow_id: int = None,
) -> Tuple[list, int]:
    """
    获取单据列表数据
    
    Args:
        db: 数据库会话
        page: 页码
        page_size: 每页数量
        is_transfer: 是否为调拨单 (1是0否)
        audit_state: 审核状态
        start_time: 开始日期
        end_time: 结束日期
        keyword: 关键词搜索
        create_by: 创建人ID
        receipt_id: 单据ID
    Returns:
        Tuple[list, int]: 单据列表数据和总数
    """
    from sqlalchemy.orm import aliased

    CreateBy = aliased(ErpAccount, name='create_by')
    # 构建查询条件
    selects = [
        ErpReceipt.id,
        ErpReceipt.apply_reason,
        ErpReceipt.create_time,
        ErpReceipt.audit_state,
        ErpReceipt.workflow_id,
        ErpReceipt.relate_finance_id,
        ErpReceipt.related_obj_id,
        ErpReceipt.related_obj_type,
        ErpReceipt.related_obj_name,
        ErpReceipt.dept_id,
        ErpReceipt.dept_name,
        ErpReceiptFinance,
        CreateBy.employee_name.label('create_by_name')
    ]
    conditions = []
    if is_transfer:
        conditions.append(ErpReceipt.is_transfer == is_transfer)
    if audit_state:
        conditions.append(ErpReceipt.audit_state == audit_state)
    if start_time:
        conditions.append(ErpReceipt.create_time >= start_time)
    if end_time:
        conditions.append(ErpReceipt.create_time <= end_time)
    if keyword:
        conditions.append(ErpReceipt.apply_reason.like(f"%{keyword}%"))
    if create_by:
        conditions.append(ErpReceipt.create_by == create_by)
    if receipt_id:
        conditions.append(ErpReceipt.id == receipt_id)
    if workflow_id:
        conditions.append(ErpReceipt.workflow_id == workflow_id)
    
    # 构建查询
    query = (select(selects)
             .select_from(ErpReceipt)
             .outerjoin(CreateBy, ErpReceipt.create_by == CreateBy.id)
             .outerjoin(ErpReceiptFinance, ErpReceipt.relate_finance_id == ErpReceiptFinance.id)
             
             .where(*conditions)
             .order_by(ErpReceipt.create_time.desc())
            )
    
    # 添加分页
    if page and page_size:
        query = query.offset((page - 1) * page_size).limit(page_size)

    # 执行查询
    result = await db.execute(query)
    receipts = result.fetchall()

    # 获取总数
    count_query = select(func.count()).select_from(ErpReceipt).outerjoin(
                ErpReceiptFinance, 
                ErpReceipt.relate_finance_id == ErpReceiptFinance.id
            ).where(*conditions)
    count_result = await db.execute(count_query)
    total_count = count_result.scalar() or 0
    
    # 如果没有结果，直接返回
    if not receipts:
        return [], total_count
    
    # 提取所有的receipt_id用于批量查询明细
    receipt_ids = [receipt[0] for receipt in receipts]
    
    # 批量查询所有相关明细
    erp_receipt_detail = CF.get_crud(ErpReceiptDetail)
    details = await erp_receipt_detail.get_many(db, raw=[
        ErpReceiptDetail.receipt_id.in_(receipt_ids),
        # ErpReceiptDetail.disable == 0
    ])
    
    # 使用ModelDataHelper将ORM对象转换为字典
    model_helper = ModelDataHelper()
    details_dict = [model_helper.model_to_dict(detail) for detail in details]
    
    # 将明细按receipt_id分组
    details_map = defaultdict(list)
    for detail in details_dict:
        details_map[detail["receipt_id"]].append(detail)
    
    # 构建结果数据，添加明细表信息
    result_data = []
    for receipt in receipts:
        receipt_id = receipt[0]
        # 处理财务信息对象
        finance_info = receipt[11]
        finance_dict = model_helper.model_to_dict(finance_info) if finance_info else None
        
        receipt_data = {
            "id": receipt_id,
            "apply_reason": receipt[1],
            "create_time": receipt[2],
            "audit_state": receipt[3],
            "workflow_id": receipt[4],
            "relate_finance_id": receipt[5],
            "related_obj_id": receipt[6],
            "related_obj_type": receipt[7],
            "related_obj_name": receipt[8],
            "dept_id": receipt[9],
            "dept_name": receipt[10],
            "finance_info": finance_dict,
            "create_by_name": receipt[12],
            # 添加明细信息
            "details": details_map.get(receipt_id, [])
        }
        result_data.append(receipt_data)
    
    return result_data, total_count


