from pydantic import BaseModel, Field
from typing import List, Optional, Union, Dict, Any
from datetime import date, datetime

class SettingEntity(BaseModel):
    dict_key: str
    dict_value: Union[List, Dict, None] = None


class ErpLabelBase(BaseModel):
    title: str


class CourseLabel(BaseModel):
    label_id: int
    course_ids: List[int]
    

class WorkflowNodeCreate(BaseModel):
    """工作流节点创建"""
    id: Optional[int] = None
    workflow_id: Optional[int] = None
    node_name: str
    node_desc: Optional[str] = None
    node_type: int = Field(1, description="节点类型：1审批节点 2抄送节点")
    approver_type: int = Field(description="审批人类型：审批人类型 1连续多级上级 2指定成员 3指定岗位 4 授课老师")
    approver_ids: Optional[str] = None
    approver_names: Optional[str] = None
    sort_no: int = Field(0, description="排序号")
    is_countersign: int = Field(0, description="是否会签：0否 1是")
    node_action: Optional[int] = None
    is_finance_related: int = Field(0, description="是否涉及出入账：0否 1是")
    is_continuous_approval: int = Field(0, description="是否连续多级上级：0否 1是")
 

class WorkflowCcInfo(BaseModel):
    """抄送信息"""
    cc_type: int
    cc_value: str
    cc_names: Optional[str] = None


class WorkflowDefCreate(BaseModel):
    """工作流定义创建"""
    workflow_name: str
    workflow_desc: str
    workflow_type: Optional[int] = None
    status: int = Field(1, description="状态：0禁用 1启用")
    remark: Optional[str] = None
    cost_type_ids: Optional[List[int]] = None
    nodes: Optional[List[WorkflowNodeCreate]] = None
    # cc_info: Optional[WorkflowCcInfo] = None


class WorkflowDefUpdate(BaseModel):
    """工作流定义更新"""
    workflow_name: Optional[str] = None
    workflow_desc: Optional[str] = None
    workflow_type: Optional[int] = None
    status: Optional[int] = None
    remark: Optional[str] = None
    cost_type_ids: Optional[List[int]] = None
    nodes: Optional[List[WorkflowNodeCreate]] = None
    # cc_info: Optional[WorkflowCcInfo] = None


class ReceiptCreate(BaseModel):
    """单据创建"""
    apply_reason: str
    related_obj_id: Optional[int] = None
    related_obj_type: Optional[int] = None  # 关联对象类型 1 学生 2 员工 3 供应商
    related_obj_name: Optional[str] = None
    apply_remark: Optional[str] = None
    attachment: Optional[List[str]] = None
    dept_id: Optional[int] = None  # 部门ID
    dept_name: Optional[str] = None  # 部门名称
    # receipt_type: int
    workflow_id: int
    expect_time: Optional[datetime] = None  # 期望时间:交付/以及其他类型的期望日期
    
    # 财务信息
    order_no: Optional[str] = None  # 订单号
    bank_account_id: Optional[int] = None  # 关联账户ID
    bank_account_name: Optional[str] = None  # 关联账户名
    apply_money: Optional[float] = None  # 申请金额
    trade_money: Optional[float] = None  # 实际金额
    ie_type: Optional[int] = None  # 收支类型 1 退费单 2 采购单 3 报销单 4 支出单
    desc: Optional[str] = None  # 描述
    pre_pay_time: Optional[date] = None  # 预计付款日期
    invoice_type: Optional[int] = None  # 发票类型：1专票 2普票
    invoice_money: Optional[float] = None  # 发票金额
    invoice_remark: Optional[str] = None  # 发票备注
    
    # 明细列表
    detail_list: Optional[List["ReceiptDetailCreate"]] = None

    # 关联电子钱包信息
    ewallet_stu_id: Optional[int] = None  # 电子钱包学生ID
    ewallet_amount: Optional[float] = None  # 电子钱包变动金额
    ewallet_change_type: Optional[int] = None  # 电子钱包变动类型 1 增加 2 减少

    # 采购信息
    purchasing_ids: Optional[List[int]] = None

    


class ReceiptDetailCreate(BaseModel):
    """单据明细创建"""
    item_type: Optional[int] = None  # 明细类型:1单据明细 2财务明细
    item_name: Optional[str] = None  # 项目名称
    item_num: Optional[int] = None  # 数量
    item_unit_price: Optional[float] = None  # 单价
    item_total_price: Optional[float] = None  # 总价
    cost_type_id: Optional[int] = None  # 费用类型ID
    cost_type_name: Optional[str] = None  # 费用类型名称
    amount: Optional[float] = None  # 金额
    tax_rate: Optional[float] = None  # 税率
    tax_amount: Optional[float] = None  # 税额
    remark: Optional[str] = None  # 备注/描述
    sort_no: Optional[int] = 0  # 排序号
    item_source: Optional[str] = None  # 来源
    attachment: Optional[List[str]] = None
    purchasing_id: Optional[int] = None  # 采购项ID
    # 调拨单信息
    transfer_type: Optional[int] = None  # 调拨类型 1 付款方 2 收款方
    transfer_income: Optional[int] = None  # 是否汇兑收益/损失
    transfer_comments: Optional[str] = None  # 调拨备注
    transfer_date: Optional[str] = None  # 调拨日期
    transfer_account_id: Optional[int] = None  # 付款/收款对象
    transfer_account_type: Optional[int] = None  # 账户类型
    transfer_account_number: Optional[str] = None  # 付/收款对象账号
    transfer_way_id: Optional[int] = None  # 收/付款方式


class ReceiptUpdate(BaseModel):
    """单据更新"""
    apply_reason: Optional[str] = None
    related_obj_id: Optional[int] = None
    related_obj_type: Optional[int] = None  # 关联对象类型 1 学生 2 员工 3 供应商
    related_obj_name: Optional[str] = None
    apply_remark: Optional[str] = None
    attachment: Optional[List[str]] = None
    dept_id: Optional[int] = None  # 部门ID
    dept_name: Optional[str] = None  # 部门名称
    workflow_id: Optional[int] = None
    expect_time: Optional[datetime] = None  # 期望时间:交付/以及其他类型的期望日期
    
    # 财务信息
    order_no: Optional[str] = None  # 订单号
    bank_account_id: Optional[int] = None  # 关联账户ID
    bank_account_name: Optional[str] = None  # 关联账户名
    apply_money: Optional[float] = None  # 申请金额
    trade_money: Optional[float] = None  # 实际金额
    ie_type: Optional[int] = None  # 收支类型 1 退费单 2 采购单 3 报销单 4 支出单
    desc: Optional[str] = None  # 描述
    pre_pay_time: Optional[date] = None  # 预计付款日期
    invoice_type: Optional[int] = None  # 发票类型：1专票 2普票
    invoice_money: Optional[float] = None  # 发票金额
    invoice_remark: Optional[str] = None  # 发票备注
    
    # 明细列表
    detail_list: Optional[List["ReceiptDetailUpdate"]] = None

    # 关联电子钱包信息
    ewallet_stu_id: Optional[int] = None  # 电子钱包学生ID
    ewallet_amount: Optional[float] = None  # 电子钱包变动金额
    ewallet_change_type: Optional[int] = None  # 电子钱包变动类型 1 增加 2 减少


class ReceiptDetailUpdate(BaseModel):
    """单据明细更新"""
    id: Optional[int] = None  # 明细ID，存在则更新，不存在则新建，传入的明细ID不在列表中的将被删除
    item_type: Optional[int] = None  # 明细类型:1单据明细 2财务明细
    item_name: Optional[str] = None  # 项目名称
    item_num: Optional[int] = None  # 数量
    item_unit_price: Optional[float] = None  # 单价
    item_total_price: Optional[float] = None  # 总价
    cost_type_id: Optional[int] = None  # 费用类型ID
    cost_type_name: Optional[str] = None  # 费用类型名称
    amount: Optional[float] = None  # 金额
    tax_rate: Optional[float] = None  # 税率
    tax_amount: Optional[float] = None  # 税额
    remark: Optional[str] = None  # 备注/描述
    sort_no: Optional[int] = 0  # 排序号
    item_source: Optional[str] = None  # 来源
    attachment: Optional[List[str]] = None
    purchasing_id: Optional[int] = None  # 采购项ID
    # 调拨单信息
    transfer_type: Optional[int] = None  # 调拨类型 1 付款方 2 收款方
    transfer_income: Optional[int] = None  # 是否汇兑收益/损失
    transfer_comments: Optional[str] = None  # 调拨备注
    transfer_date: Optional[str] = None  # 调拨日期
    transfer_account_id: Optional[int] = None  # 付款/收款对象
    transfer_account_type: Optional[int] = None  # 账户类型
    transfer_account_number: Optional[str] = None  # 付/收款对象账号
    transfer_way_id: Optional[int] = None  # 收/付款方式


class WorkflowAction(BaseModel):
    """工作流动作"""
    receipt_id: int
    action: int = Field(description="动作：2同意 3驳回")
    comments: Optional[str] = None
    bank_account_id: Optional[int] = None  # 关联银行账户ID


class PaymentObjCreate(BaseModel):
    obj_name: str
    obj_type: int
    obj_related_id: Optional[int] = None
    account_type: int
    account_no: str = None
    bank_name: str = None
    remark: str = None
    phone: Optional[str] = None
    obj_related_name: Optional[str] = None


class WorkflowCostTypeBindUpdate(BaseModel):
    """费用类型绑定工作流更新"""
    workflow_id: int
    default_cost_type_id: Optional[int] = None
    receipt_category_name: Optional[str] = None


class MessageCreate(BaseModel):
    message_type: Optional[str] = None
    message_content: Optional[str] = None
    message_to: List[str]


class RelatedObjUpdate(BaseModel):
    """关联对象更新"""
    obj_name: Optional[str] = None
    obj_type: Optional[int] = None
    account_type: Optional[int] = None
    account_no: Optional[str] = None
    bank_name: Optional[str] = None
    remark: Optional[str] = None


# 前向引用解决方案
ReceiptCreate.update_forward_refs()
ReceiptUpdate.update_forward_refs()


class RelatedObjTagCreate(BaseModel):
    """关联对象标签创建"""
    tag: str


class MessageSubscribe(BaseModel):
    # 机构信息
    SID: int
    # 消息类型
    Cmd: Optional[str] = None
    # 消息内容
    Msg: Optional[str] = None
    # 安全密钥
    SafeKey: Optional[str] = None
    # 时间戳
    TimeStamp: Optional[int] = None
    
    # 课程信息
    CourseID: Optional[int] = None
    CourseName: Optional[str] = None
    # 数据
    Data: Optional[dict] = None
    # 更换手机号码操作时间
    ReplaceTime: Optional[int] = None   
    # UID对应的新手机号码
    Telephone: Optional[str] = None   
    # UID以及对应的邮箱
    UID: Optional[int] = None  
    Email: Optional[str] = None   
    
    
    
    # 视频信息
    VUrl: Optional[str] = None    # 视频URL
    VET: Optional[int] = None    # 视频开始时间
    VST: Optional[int] = None    # 视频结束时间
    Duration: Optional[int] = None    # 视频时长
    FileId: Optional[str] = None    # 文件ID
    Size: Optional[int] = None    # 视频大小
    CIDExt: Optional[str] = None    # MP4数据来源 ClassRoom：教室；Camera.3：教师摄像头（现场）
    ClassID: Optional[int] = None    # 班级ID
    ActionTime: Optional[int] = None    # 操作时间


    # 新增EEO消息类型相关字段
    opType: Optional[int] = None    # 操作类型 1:增加，2:删除，3:修改
    opTime: Optional[int] = None    # 操作时间
    opSource: Optional[int] = None    # 操作来源 1:EEO后台，2:classIn客户端，4:机构API
    CourseId: Optional[int] = None    # 课程id（注意与CourseID的区别）
    opData: Optional[dict] = None    # 操作数据

