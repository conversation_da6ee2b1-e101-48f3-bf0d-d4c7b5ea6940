from collections import defaultdict
import copy
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from models.m_office import ErpOfficePurchasingList
from models.m_workflow import ErpCostTypeBind,  ErpPaymentObjType, ErpWorkflowCostType
from models.models import ErpAccount
from public_api.crud import (
    get_related_obj_list, get_user_dept, get_workflow_cost_type_by_workflow_id_module,  get_workflow_cost_type_related, get_workflow_def_list, get_workflow_def_with_details, 
    get_receipt_with_workflow, get_workflow_instance_with_details,
    erp_workflow_def, erp_workflow_node, erp_workflow_instance, erp_receipt,
    erp_payment_obj, erp_receipt_finance, erp_receipt_detail,
    erp_account, erp_finance_cost_type
)
from public_api.serializer import PaymentObj<PERSON><PERSON>, RelatedObjTagCreate, RelatedObjUpdate, WorkflowCostTypeB<PERSON>Update, WorkflowDef<PERSON>reate, WorkflowDef<PERSON>pdate, WorkflowNode<PERSON>reate, WorkflowAction, ReceiptCreate, ReceiptUpdate
from public_api.modules import generate_workflow_preview, get_workflow_list_data, start_workflow, handle_workflow_action, cancel_workflow, get_user_superior, handle_cc_node, get_receipt_list_data
from settings import CF
from utils.db.account_handler import UserDict, get_current_active_user
from utils.db.db_handler import get_default_db
from utils.db.model_handler import ModelDataHelper
from utils.enum.enum_approval import ApproverType, AuditState, CostTypeBind, InstanceStatus, NodeType
from utils.response.response_handler import ApiFailedResponse, ApiSuccessResponse

router = APIRouter(prefix="/workflow", tags=["新版工作流"])

erp_cost_type_bind = CF.get_crud(ErpCostTypeBind)
erp_workflow_cost_type = CF.get_crud(ErpWorkflowCostType)
erp_payment_obj_type = CF.get_crud(ErpPaymentObjType)

erp_office_purchasing_list = CF.get_crud(ErpOfficePurchasingList)

@router.get("/workflow_def/")
async def query_workflow_def(
        page: int, 
        page_size: int,
        workflow_type: int = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    查询工作流定义列表
    """
    workflows, total_count = await get_workflow_def_list(db, page, page_size, workflow_type)
    
    return await ApiSuccessResponse({
        "data": workflows or [],
        "count": total_count
    })


@router.get("/workflow_def/{workflow_id}")
async def get_workflow_def(
        workflow_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    获取工作流定义详情
    """
    workflow = await get_workflow_def_with_details(db, workflow_id)
    if not workflow:
        return await ApiFailedResponse("工作流定义不存在")
    
    return await ApiSuccessResponse(workflow)


@router.post("/workflow_def/")
async def create_workflow_def(
        workflow_def: WorkflowDefCreate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    创建工作流定义
    - approver_type  审批人类型 1连续多级上级 2指定成员 3指定岗位 4 授课老师
    - approver_ids 审批人账号，存储连续多级上级的级数 | 指定成员ID | 岗位的ID
    """
    # 创建工作流定义
    workflow_data = {
        "workflow_name": workflow_def.workflow_name,
        "workflow_desc": workflow_def.workflow_desc,
        "workflow_type": workflow_def.workflow_type,
        "status": workflow_def.status,
        "remark": workflow_def.remark,
        "create_by": user.uid,
        "update_by": user.uid,
    }
    
    workflow_obj = await erp_workflow_def.create(db, commit=False, **workflow_data)
    if workflow_def.cost_type_ids:
        # 批量查询所有费用类型，避免在循环中查询
        cost_types = await erp_finance_cost_type.get_many(db, raw=[
            erp_finance_cost_type.model.id.in_(workflow_def.cost_type_ids)
        ])
        # 创建ID到名称的映射
        cost_type_map = {ct.id: ct.name for ct in cost_types}
        
        for cost_type_id in workflow_def.cost_type_ids:
            # 从映射中获取名称，如果不存在则使用空字符串
            cost_type_name = cost_type_map.get(cost_type_id, "")
            
            await erp_workflow_cost_type.create(db, commit=False, **{
                "workflow_id": workflow_obj.id,
                "cost_type_id": cost_type_id,
                "cost_type_name": cost_type_name,
            })
    
    # 创建工作流节点
    if workflow_def.nodes:
        for node in workflow_def.nodes:
            node_data = {
                "workflow_id": workflow_obj.id,
                "node_name": node.node_name,
                "node_desc": node.node_desc,
                "node_type": node.node_type,
                "approver_type": node.approver_type,
                "approver_ids": node.approver_ids,
                "approver_names": node.approver_names,
                "sort_no": node.sort_no,
                "is_countersign": node.is_countersign,
                "node_action": node.node_action,
                "is_finance_related": node.is_finance_related,
                "is_continuous_approval": node.is_continuous_approval,
                "create_by": user.uid,
                "update_by": user.uid,
            }
            await erp_workflow_node.create(db, commit=False, **node_data)
    
    await db.commit()
    return await ApiSuccessResponse(True)


# 启用和禁用工作流
@router.put("/workflow_def/status/{workflow_id}")
async def update_workflow_def_status(
        workflow_id: int,
        status: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    启用和禁用工作流
    """
    def_obj = await erp_workflow_def.get_by_id(db, workflow_id)
    if not def_obj:
        return await ApiFailedResponse("工作流定义不存在")
    
    def_obj.status = status
    await db.commit()
    return await ApiSuccessResponse(True)

@router.put("/workflow_def/{workflow_id}")
async def update_workflow_def(
        workflow_id: int,
        workflow_def: WorkflowDefUpdate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    更新工作流定义
    - workflow_id 节点中的workflow_id不传
    - 节点中的id不传会被删除，存在则更新，不存在则新建（根据node - id判断）
    """
    # 获取工作流定义
    exist_workflow = await erp_workflow_def.get_by_id(db, workflow_id)
    if not exist_workflow:
        return await ApiFailedResponse("工作流定义不存在")
    
    # 更新工作流定义
    update_data = {k: v for k, v in {
        "workflow_name": workflow_def.workflow_name,
        "workflow_desc": workflow_def.workflow_desc,
        "workflow_type": workflow_def.workflow_type,
        "status": workflow_def.status,
        "remark": workflow_def.remark,
    }.items() if v is not None}
    
    # 如果更新项目不为空才更新定义表
    if update_data:
        update_data["update_by"] = user.uid
        await erp_workflow_def.update_one(db, workflow_id, update_data, commit=False)
    
    # 更新费用类型，取传入的为最新的进行合并（只删除和新增）
    if workflow_def.cost_type_ids:
        # 获取现有费用类型
        exist_cost_types = await erp_workflow_cost_type.get_many(db, {"workflow_id": workflow_id})
        exist_cost_type_ids = [cost_type.cost_type_id for cost_type in exist_cost_types]
        
        # 找出传入的ids中不存在的费用类型，删除
        delete_cost_type_ids = set(exist_cost_type_ids) - set(workflow_def.cost_type_ids)
        for cost_type_id in delete_cost_type_ids:
            await erp_workflow_cost_type.delete_many(db, {"workflow_id": workflow_id, "cost_type_id": cost_type_id}, commit=False)
        
        # 找出传入的ids中存在的费用类型，新增
        create_cost_type_ids = set(workflow_def.cost_type_ids) - set(exist_cost_type_ids)
        if create_cost_type_ids:
            # 批量查询所有需要新增的费用类型，避免在循环中查询
            cost_types = await erp_finance_cost_type.get_many(db, raw=[
                erp_finance_cost_type.model.id.in_(create_cost_type_ids)
            ])
            # 创建ID到名称的映射
            cost_type_map = {ct.id: ct.name for ct in cost_types}
            
            for cost_type_id in create_cost_type_ids:
                # 从映射中获取名称，如果不存在则使用空字符串
                cost_type_name = cost_type_map.get(cost_type_id, "")
                
                await erp_workflow_cost_type.create(db, commit=False, **{
                    "workflow_id": workflow_id,
                    "cost_type_id": cost_type_id,
                    "cost_type_name": cost_type_name,
                })
        
    # 处理工作流节点
    if workflow_def.nodes:
        # 获取现有节点
        exist_nodes = await erp_workflow_node.get_many(db, {"workflow_id": workflow_id})
        exist_node_map = {node.id: node for node in exist_nodes}
        
        # 构建节点基础数据函数
        def build_node_base_data(node):
            return {
                "node_name": node.node_name,
                "node_desc": node.node_desc,
                "node_type": node.node_type,
                "approver_type": node.approver_type,
                "approver_ids": node.approver_ids,
                "approver_names": node.approver_names,
                "sort_no": node.sort_no,
                "is_countersign": node.is_countersign,
                "node_action": node.node_action,
                "is_finance_related": node.is_finance_related,
                "is_continuous_approval": node.is_continuous_approval,
            }

        # 批处理操作
        nodes_to_update = []
        nodes_to_create = []
        nodes_to_delete = []
        
        # 分类需要更新和创建的节点
        update_node_ids = set()
        for node in workflow_def.nodes:
            if node.id and node.id > 0:
                update_node_ids.add(node.id)
                node_data = build_node_base_data(node)
                node_data["update_by"] = user.uid
                nodes_to_update.append((node.id, node_data))
            else:
                node_data = build_node_base_data(node)
                node_data.update({
                    "workflow_id": workflow_id,
                    "create_by": user.uid,
                    "update_by": user.uid,
                })
                nodes_to_create.append(node_data)
        
        # 确定需要删除的节点
        delete_node_ids = set(exist_node_map.keys()) - update_node_ids
        for node_id in delete_node_ids:
            nodes_to_delete.append(node_id)
        
        # 批量执行操作
        # 1. 删除节点
        for node_id in nodes_to_delete:
            await erp_workflow_node.delete_one(db, node_id, commit=False)
        
        # 2. 更新节点 
        for node_id, node_data in nodes_to_update:
            await erp_workflow_node.update_one(db, node_id, node_data, commit=False)
        
        # 3. 创建新节点
        for node_data in nodes_to_create:
            await erp_workflow_node.create(db, commit=False, **node_data)
    
    await db.commit()
    return await ApiSuccessResponse(True)


@router.delete("/workflow_def/{workflow_id}")
async def delete_workflow_def(
        workflow_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    删除工作流定义
    """
    # 检查是否有关联的工作流实例
    instances = await erp_workflow_instance.get_many(db, {"workflow_id": workflow_id})
    if instances:
        return await ApiFailedResponse("该工作流已被使用，无法删除")
    
    # 删除工作流节点
    nodes = await erp_workflow_node.get_many(db, {"workflow_id": workflow_id})
    for node in nodes:
        await erp_workflow_node.delete_one(db, node.id, commit=False)
    
    # 删除工作流定义
    await erp_workflow_def.delete_one(db, workflow_id, commit=False)
    
    await db.commit()
    return await ApiSuccessResponse(True)


@router.post("/workflow_node/")
async def create_workflow_node(
        node: WorkflowNodeCreate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    创建工作流节点
    """
    # 检查工作流是否存在
    workflow = await erp_workflow_def.get_by_id(db, node.workflow_id)
    if not workflow:
        return await ApiFailedResponse("工作流定义不存在")
    
    # 创建节点
    node_data = {
        "workflow_id": node.workflow_id,
        "node_name": node.node_name,
        "node_desc": node.node_desc,
        "node_type": node.node_type,
        "approver_type": node.approver_type,
        "approver_ids": node.approver_ids,
        "approver_names": node.approver_names,
        "sort_no": node.sort_no,
        "is_countersign": node.is_countersign,
        "node_action": node.node_action,
        "is_finance_related": node.is_finance_related,
        "is_continuous_approval": node.is_continuous_approval,
        "create_by": user.uid,
        "update_by": user.uid,
    }
    
    node_obj = await erp_workflow_node.create(db, **node_data)
    return await ApiSuccessResponse({"id": node_obj.id})


@router.put("/workflow_node/{node_id}")
async def update_workflow_node(
        node_id: int,
        node: WorkflowNodeCreate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    更新工作流节点
    """
    # 检查节点是否存在
    exist_node = await erp_workflow_node.get_by_id(db, node_id)
    if not exist_node:
        return await ApiFailedResponse("工作流节点不存在")
    
    # 更新节点
    node_data = {
        "node_name": node.node_name,
        "node_desc": node.node_desc,
        "node_type": node.node_type,
        "approver_type": node.approver_type,
        "approver_ids": node.approver_ids,
        "approver_names": node.approver_names,
        "sort_no": node.sort_no,
        "is_countersign": node.is_countersign,
        "node_action": node.node_action,
        "is_finance_related": node.is_finance_related,
        "is_continuous_approval": node.is_continuous_approval,
        "update_by": user.uid,
    }
    
    await erp_workflow_node.update_one(db, node_id, node_data)
    return await ApiSuccessResponse(True)


@router.delete("/workflow_node/{node_id}")
async def delete_workflow_node(
        node_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    删除工作流节点
    """
    # 检查节点是否存在
    exist_node = await erp_workflow_node.get_by_id(db, node_id)
    if not exist_node:
        return await ApiFailedResponse("工作流节点不存在")
    
    # 检查是否有关联的工作流实例
    instances = await erp_workflow_instance.get_many(db, {
        "workflow_id": exist_node.workflow_id,
        "current_node_id": node_id
    })
    if instances:
        return await ApiFailedResponse("该节点正在被工作流实例使用，无法删除")
    
    # 删除节点
    await erp_workflow_node.delete_one(db, node_id)
    return await ApiSuccessResponse(True)


# 查询所有单据
@router.get("/receipt/")
async def query_receipt(
    page: int, 
    page_size: int,
    is_transfer: int = None,
    audit_state: int = None,
    date_start: str = None,
    date_end: str = None,
    keyword: str = None,
    create_by: int = None,
    receipt_id: int = None,
    workflow_id: int = None,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    查询所有单据
    参数：
    - page: 页码
    - page_size: 每页大小
    - is_transfer: 是否为调拨单 1 是 0 否（可选）
    - audit_state: 审核状态（可选）
    - date_start: 开始日期（可选）
    - date_end: 结束日期（可选）
    - keyword: 关键词搜索（可选）
    - create_by: 创建人ID（可选）
    - receipt_id: 单据ID（可选）
    返回：
    - data: 单据列表，包含每个单据的明细数据
    - count: 总数量
    """
    result_data, total_count = await get_receipt_list_data(
        db, page, page_size, is_transfer, audit_state, date_start, date_end, 
        keyword, create_by, receipt_id, workflow_id
    )
    
    return await ApiSuccessResponse({
        "data": result_data,
        "count": total_count
    })


@router.post("/receipt/")
async def create_receipt(
        receipt: ReceiptCreate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    创建单据
    - related_obj 关联对象
    - dept_id 部门ID
    - dept_name 部门名称
    - bank_account 关联银行账户bill_account中 account_type=1的银行账户
    - ie_type 收支类型：1 退费单 2 采购单 3 报销单 4 支出单
    - related_obj_type 关联对象类型 1 学生 2 员工 3 供应商(payment_obj表)
    - detail_list
        - item_type: 1单据明细 2财务明细
        - item_name: 项目名称
        - item_num: 数量
        - item_unit_price: 单价
        - item_total_price: 总价
        - cost_type_id: 费用类型ID
        - cost_type_name: 费用类型名称
        - amount: 金额
        - tax_rate: 税率
        - tax_amount: 税额
        - remark: 备注
        - sort_no: 排序号,如果没有指定排序号，使用传入的顺序索引作为排序号
        - purchasing_id: 教学点采购项id 先只做记录，不做任何后续关联及审核成功回调
        - attachment: 附件

        - transfer_type: 调拨类型 1 出账 2 入账
        - transfer_income: 调拨收入 1 现金 2 银行
        - transfer_comments: 调拨备注
        - transfer_date: 调拨日期
        - transfer_account_id: 调拨账户ID
        - transfer_account_type: 调拨账户类型
        - transfer_account_number: 调拨账户账号
        - transfer_way_id: 调拨方式ID

    """
    from app_finance.modules import stu_ewallet_change
    # 获取用户部门
    # user_dept = await get_user_dept(db, user.uid)

    # 获取工作流定义
    workflow_def = await erp_workflow_def.get_by_id(db, receipt.workflow_id)
    if not workflow_def:
        return await ApiFailedResponse("工作流定义不存在")
    
    # 检查是否为调拨单
    transfer_flag = False
    transfer_erp_cost_type = await erp_cost_type_bind.get_by_id(db, CostTypeBind.Transfer.value)
    if not transfer_erp_cost_type:
        return await ApiFailedResponse("调拨单的预制工作流不存在，请完善后继续流程")
    # print(int(transfer_erp_cost_type.workflow_id), receipt.workflow_id)
    if int(transfer_erp_cost_type.workflow_id) == int(receipt.workflow_id):
        # 说明是调拨单
        transfer_flag = True
    # 检查是否关联财务附表
    finance_flag = False
    if int(workflow_def.workflow_type) in [1, 2, 3]:   # 1 收入 2 支出 3 收支相抵 4 不涉及财务
        # 获取费用类型
        finance_flag = True
    
    # 创建单据
    receipt_data = {
        "workflow_id": receipt.workflow_id,
        "apply_reason": receipt.apply_reason,
        "related_obj_id": receipt.related_obj_id,
        "related_obj_type": receipt.related_obj_type,
        "apply_remark": receipt.apply_remark,
        "audit_state": AuditState.DRAFT.value,  # 暂存状态
        "dept_id": receipt.dept_id,
        "dept_name": receipt.dept_name,
        "attachment": receipt.attachment,
        "expect_time": receipt.expect_time,  # 期望时间
        "create_by": user.uid,
        "update_by": user.uid,
    }

    # 调拨单标记
    if transfer_flag:
        receipt_data["is_transfer"] = 1
    



    # 创建单据基本信息
    receipt_obj = await erp_receipt.create(db, commit=False, **receipt_data)
    
    # 创建财务信息
    if finance_flag:
        finance_data = {
            "receipt_id": receipt_obj.id,
            "order_no": receipt.order_no,
            "bank_account_id": receipt.bank_account_id,
            "bank_account_name": receipt.bank_account_name,
            "apply_money": receipt.apply_money,
            "trade_money": receipt.trade_money or 0.00,
            "ie_type": receipt.ie_type,
            "desc": receipt.desc,
            "pre_pay_time": receipt.pre_pay_time,
            "invoice_type": receipt.invoice_type,
            "invoice_money": receipt.invoice_money,
            "invoice_remark": receipt.invoice_remark,
            "create_by": user.uid,
            "update_by": user.uid,
        }

        if receipt.bank_account_id and receipt.bank_account_id > 0:
            finance_data["bank_account_id"] = receipt.bank_account_id
            finance_data["bank_account_name"] = receipt.bank_account_name
        
        finance_obj = await erp_receipt_finance.create(db, commit=False, **finance_data)
        # 更新单据关联财务ID
        receipt_obj.relate_finance_id = finance_obj.id
       
    # 创建明细列表
    if hasattr(receipt, 'detail_list') and receipt.detail_list:
        for index, detail in enumerate(receipt.detail_list):
            # 通用明细
            detail_data = {
                "receipt_id": receipt_obj.id,
                "item_type": detail.item_type,
                "item_name": detail.item_name,
                "item_num": detail.item_num,
                "item_unit_price": detail.item_unit_price,
                "item_total_price": detail.item_total_price,
                "cost_type_id": detail.cost_type_id,
                "cost_type_name": detail.cost_type_name,
                "amount": detail.amount,
                "tax_rate": detail.tax_rate,
                "tax_amount": detail.tax_amount,
                "remark": detail.remark,
                "sort_no": detail.sort_no or index + 1,  # 如果没有指定排序号，使用索引作为排序号
                "create_by": user.uid,
                "update_by": user.uid,
                "attachment": detail.attachment,
                "item_source": detail.item_source,
                "purchasing_id": detail.purchasing_id,
            }
            # 调拨单明细
            if transfer_flag:
                detail_data["transfer_type"] = detail.transfer_type
                detail_data["transfer_income"] = detail.transfer_income
                detail_data["transfer_comments"] = detail.transfer_comments
                detail_data["transfer_date"] = detail.transfer_date
                detail_data["transfer_account_id"] = detail.transfer_account_id
                detail_data["transfer_account_type"] = detail.transfer_account_type
                detail_data["transfer_account_number"] = detail.transfer_account_number
                detail_data["transfer_way_id"] = detail.transfer_way_id
            await erp_receipt_detail.create(db, commit=False, **detail_data)
    receipt_id = copy.deepcopy(receipt_obj.id)
    try:
        if receipt.ewallet_stu_id and receipt.ewallet_amount and receipt.ewallet_change_type:
            await stu_ewallet_change(db, 
                                    stu_id=receipt.ewallet_stu_id, 
                                    change_type=receipt.ewallet_change_type, 
                                    amount=receipt.ewallet_amount, 
                                    uid=user.uid, 
                                    desc=f"单据{receipt_id}电子钱包提现申请", 
                                    receipt_id=receipt_id,
                                    commit=False)
    except Exception as e:
        return await ApiFailedResponse(f"电子钱包变动失败: {str(e)}")
    
    # 提交事务以确保ID生成
    await db.commit()
    return await ApiSuccessResponse({"receipt_id": receipt_id})



# 修改单据
@router.put("/receipt/{receipt_id}")
async def update_receipt(
    receipt_id: int,
    params: ReceiptUpdate,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(get_current_active_user),
):
    """
    修改单据
    - 只有暂存状态(0)或驳回状态(3)的单据可以修改
    - 修改后单据仍为暂存状态，需要重新发起工作流
    - 明细列表采用差异更新：传入id的更新，未传入id的新建，现有但未在传入列表中的删除
    """
    from app_finance.modules import stu_ewallet_change
    
    # 验证单据存在
    receipt_obj = await erp_receipt.get_by_id(db, receipt_id)
    if not receipt_obj:
        return await ApiFailedResponse("单据不存在")
    
    # 验证单据状态（只有暂存或驳回状态可以修改）
    if receipt_obj.audit_state not in [AuditState.DRAFT.value, AuditState.REJECT.value]:
        return await ApiFailedResponse("只有暂存或驳回状态的单据可以修改")
    
    # 验证修改权限（只有创建人可以修改）
    if receipt_obj.create_by != user.uid:
        return await ApiFailedResponse("只有单据创建人可以修改")
    
    # 获取工作流定义（如果要修改工作流）
    workflow_def = None
    transfer_flag = False
    finance_flag = False
    
    if params.workflow_id and params.workflow_id != receipt_obj.workflow_id:
        workflow_def = await erp_workflow_def.get_by_id(db, params.workflow_id)
        if not workflow_def:
            return await ApiFailedResponse("指定的工作流定义不存在")
    else:
        # 如果没有修改工作流，获取原工作流定义
        workflow_def = await erp_workflow_def.get_by_id(db, receipt_obj.workflow_id)
    
    if workflow_def:
        # 检查是否为调拨单
        transfer_erp_cost_type = await erp_cost_type_bind.get_by_id(db, CostTypeBind.Transfer.value)
        if transfer_erp_cost_type and int(transfer_erp_cost_type.workflow_id) == int(workflow_def.id):
            transfer_flag = True
        
        # 检查是否关联财务附表
        if int(workflow_def.workflow_type) in [1, 2, 3]:   # 1 收入 2 支出 3 收支相抵 4 不涉及财务
            finance_flag = True
    
    # 如果存在旧的工作流实例，先清理
    if receipt_obj.workflow_instance_id and receipt_obj.workflow_instance_id > 0:
        await erp_receipt.update_one(db, receipt_id, {
            "workflow_instance_id": 0
        }, commit=False)
    
    # 更新单据基本信息
    update_data = {}
    for field in ['apply_reason', 'related_obj_id', 'related_obj_type', 'related_obj_name', 
                  'apply_remark', 'attachment', 'dept_id', 'dept_name', 'workflow_id', 'expect_time']:
        value = getattr(params, field, None)
        if value is not None:
            update_data[field] = value
    
    # 调拨单标记
    if transfer_flag:
        update_data["is_transfer"] = 1
    elif "workflow_id" in update_data:
        # 如果修改了工作流且不是调拨单，清除调拨标记
        update_data["is_transfer"] = 0
    
    # 更新审核状态为暂存
    update_data["audit_state"] = AuditState.DRAFT.value
    update_data["update_by"] = user.uid
    
    # 应用更新到单据
    for key, value in update_data.items():
        setattr(receipt_obj, key, value)
    
    # 处理财务信息
    if finance_flag:
        finance_obj = None
        if receipt_obj.relate_finance_id:
            finance_obj = await erp_receipt_finance.get_by_id(db, receipt_obj.relate_finance_id)
        
        # 构建财务更新数据
        finance_update_data = {}
        for field in ['order_no', 'bank_account_id', 'bank_account_name', 'apply_money', 
                      'trade_money', 'ie_type', 'desc', 'pre_pay_time', 'invoice_type', 
                      'invoice_money', 'invoice_remark']:
            value = getattr(params, field, None)
            if value is not None:
                finance_update_data[field] = value
        
        if finance_update_data:
            finance_update_data["update_by"] = user.uid
            
            if finance_obj:
                # 更新现有财务记录
                for key, value in finance_update_data.items():
                    setattr(finance_obj, key, value)
            else:
                # 创建新的财务记录
                finance_update_data["receipt_id"] = receipt_id
                finance_update_data["create_by"] = user.uid
                finance_obj = await erp_receipt_finance.create(db, commit=False, **finance_update_data)
                receipt_obj.relate_finance_id = finance_obj.id
    
    # 处理明细列表更新
    if params.detail_list is not None:
        # 获取现有明细
        existing_details = await erp_receipt_detail.get_many(db, {"receipt_id": receipt_id})
        existing_detail_map = {detail.id: detail for detail in existing_details}
        
        # 收集要保留的明细ID
        keep_detail_ids = set()
        
        # 处理传入的明细
        for index, detail_params in enumerate(params.detail_list):
            detail_data = {
                "receipt_id": receipt_id,
                "item_type": detail_params.item_type,
                "item_name": detail_params.item_name,
                "item_num": detail_params.item_num,
                "item_unit_price": detail_params.item_unit_price,
                "item_total_price": detail_params.item_total_price,
                "cost_type_id": detail_params.cost_type_id,
                "cost_type_name": detail_params.cost_type_name,
                "amount": detail_params.amount,
                "tax_rate": detail_params.tax_rate,
                "tax_amount": detail_params.tax_amount,
                "remark": detail_params.remark,
                "sort_no": detail_params.sort_no or index + 1,
                "item_source": detail_params.item_source,
                "attachment": detail_params.attachment,
                "purchasing_id": detail_params.purchasing_id,
                "update_by": user.uid,
            }
            
            # 调拨单明细
            if transfer_flag:
                detail_data.update({
                    "transfer_type": detail_params.transfer_type,
                    "transfer_income": detail_params.transfer_income,
                    "transfer_comments": detail_params.transfer_comments,
                    "transfer_date": detail_params.transfer_date,
                    "transfer_account_id": detail_params.transfer_account_id,
                    "transfer_account_type": detail_params.transfer_account_type,
                    "transfer_account_number": detail_params.transfer_account_number,
                    "transfer_way_id": detail_params.transfer_way_id,
                })
            
            if detail_params.id and detail_params.id > 0:
                # 更新现有明细
                if detail_params.id in existing_detail_map:
                    existing_detail = existing_detail_map[detail_params.id]
                    for key, value in detail_data.items():
                        if value is not None:
                            setattr(existing_detail, key, value)
                    keep_detail_ids.add(detail_params.id)
            else:
                # 创建新明细
                detail_data["create_by"] = user.uid
                await erp_receipt_detail.create(db, commit=False, **detail_data)
        
        # 删除不在保留列表中的明细
        delete_detail_ids = set(existing_detail_map.keys()) - keep_detail_ids
        for detail_id in delete_detail_ids:
            await erp_receipt_detail.delete_one(db, detail_id, commit=False)
    
    # 处理电子钱包变动（需要先撤销原有变动，再应用新变动）
    old_ewallet_logs = []
    # 查询原有电子钱包变动记录
    from sqlalchemy import select
    from models.m_finance import ErpStudentEwalletLog
    stmt = select(ErpStudentEwalletLog).where(
        ErpStudentEwalletLog.receipt_id == receipt_id,
        ErpStudentEwalletLog.disable == 0
    )
    result = await db.execute(stmt)
    old_ewallet_logs = result.scalars().all()
    
    # 撤销原有电子钱包变动
    if old_ewallet_logs:
        for log in old_ewallet_logs:
            try:
                # 反向操作
                reverse_change_type = 2 if log.change_type == 1 else 1
                await stu_ewallet_change(
                    db,
                    stu_id=log.stu_id,
                    change_type=reverse_change_type,
                    amount=log.amount,
                    uid=user.uid,
                    desc=f"单据{receipt_id}修改，撤销原电子钱包变动",
                    receipt_id=receipt_id,
                    commit=False
                )
            except Exception as e:
                return await ApiFailedResponse(f"撤销原电子钱包变动失败: {str(e)}")
    
    # 应用新的电子钱包变动
    if params.ewallet_stu_id and params.ewallet_amount and params.ewallet_change_type:
        try:
            await stu_ewallet_change(
                db,
                stu_id=params.ewallet_stu_id,
                change_type=params.ewallet_change_type,
                amount=params.ewallet_amount,
                uid=user.uid,
                desc=f"单据{receipt_id}修改，电子钱包变动",
                receipt_id=receipt_id,
                commit=False
            )
            # 电子钱包信息已通过ErpStudentEwalletLog记录，无需在单据中存储
        except Exception as e:
            return await ApiFailedResponse(f"电子钱包变动失败: {str(e)}")
    # 如果取消了电子钱包变动但之前有记录，则已通过撤销操作处理
    # 电子钱包信息通过ErpStudentEwalletLog管理，无需在单据中清除
    
    # 提交事务
    await db.commit()
    return await ApiSuccessResponse({"receipt_id": receipt_id, "message": "单据修改成功"})



# 预览工作流
@router.get("/workflow_preview/{receipt_id}")
async def get_workflow_preview(
        receipt_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """ 
    预览工作流
    - 暂存以后，可以预览工作流
    """
    # 验证单据存在
    receipt_obj = await erp_receipt.get_by_id(db, receipt_id)
    if not receipt_obj:
        return await ApiFailedResponse("单据不存在")
    
    workflow_id = receipt_obj.workflow_id
    if not workflow_id:
        return await ApiFailedResponse("单据没有关联工作流")
    
    workflow_preview = await generate_workflow_preview(db, receipt_id, workflow_id, user.uid)
    return await ApiSuccessResponse(workflow_preview)


# 发起工作流
@router.post("/start_workflow")
async def start_workflow_api(
        receipt_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    发起工作流
    """
    # 验证单据存在
    receipt_obj = await erp_receipt.get_by_id(db, receipt_id)
    if not receipt_obj:
        return await ApiFailedResponse("单据不存在")
    workflow_id = receipt_obj.workflow_id

    # 验证单据状态
    if receipt_obj.audit_state not in [AuditState.DRAFT.value, AuditState.REJECT.value]:
        return await ApiFailedResponse("单据不是暂存或驳回状态，无法重新发起审批")
    
    # 验证工作流定义存在
    workflow_obj = await erp_workflow_def.get_by_id(db, workflow_id)
    if not workflow_obj:
        return await ApiFailedResponse("工作流定义不存在")
    
    # 检查是否存在旧的工作流实例关联，如果有则先清理
    if receipt_obj.workflow_instance_id != 0:
        old_instance = await erp_workflow_instance.get_by_id(db, receipt_obj.workflow_instance_id)
        if old_instance and old_instance.status in [InstanceStatus.REJECTED.value, InstanceStatus.CANCELLED.value]:
            # 为安全起见，再次清除旧的关联
            await erp_receipt.update_one(db, receipt_id, {
                "workflow_instance_id": 0
            }, commit=False)
            await db.commit()
    
    # 发起工作流
    try:
        instance_id = await start_workflow(db, workflow_id, receipt_id, user.uid)
        
        # 获取当前工作流实例
        instance = await erp_workflow_instance.get_by_id(db, instance_id)
        
        # 获取当前节点
        if instance:
            current_node = await erp_workflow_node.get_by_id(db, instance.current_node_id)
            
            # 检查当前节点是否为抄送节点
            if current_node and current_node.node_type == NodeType.CC.value:
                # 如果是抄送节点，获取所有节点
                all_nodes = await erp_workflow_node.get_many(db, {
                    "workflow_id": workflow_id
                }, orderby=["sort_no"])
                
                # 找到当前节点在工作流中的位置
                current_index = -1
                for i, node in enumerate(all_nodes):
                    if node.id == current_node.id:
                        current_index = i
                        break
                
                # 如果能找到当前抄送节点的位置，自动处理到下一个非抄送节点
                if current_index != -1 and current_index < len(all_nodes) - 1:
                    # 使用辅助函数处理抄送节点
                    next_node = await handle_cc_node(
                        db, 
                        instance.id, 
                        current_node, 
                        all_nodes, 
                        current_index, 
                        user.uid, 
                        receipt_obj.create_by
                    )
                    
                    # 如果有下一个非抄送节点，更新实例指向该节点
                    if next_node:
                        await erp_workflow_instance.update_one(db, instance.id, {
                            "current_node_id": next_node.id,
                            "current_node_name": next_node.node_name,
                            "update_by": user.uid
                        }, commit=False)
                    else:
                        # 如果没有下一个非抄送节点（所有后续节点都是抄送或已是最后节点），则完成工作流
                        await erp_workflow_instance.update_one(db, instance.id, {
                            "status": InstanceStatus.COMPLETED.value,  # 已完成
                            "finish_time": datetime.now(),
                            "update_by": user.uid
                        }, commit=False)
                        
                        # 更新单据状态
                        await erp_receipt.update_one(db, receipt_id, {
                            "audit_state": AuditState.PASS.value,  # 通过
                            "update_by": user.uid
                        }, commit=False)
        
        await db.commit()
        return await ApiSuccessResponse({"instance_id": instance_id})
    except Exception as e:
        # 出现异常，回滚事务
        await db.rollback()
        return await ApiFailedResponse(f"启动工作流失败: {str(e)}")


# 查询我发起的，待我审批，抄送我的


@router.get("/my_submit")
async def query_my_submit(
        page: int, 
        page_size: int,
        status: int = None,
        date_start: str = None,
        date_end: str = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    查询我发起的工作流
    参数：
    - page: 页码
    - page_size: 每页大小
    - status: 筛选状态（可选）
    - date_start: 开始日期（可选）
    - date_end: 结束日期（可选）
    """
    result_data, total_count = await get_workflow_list_data(
        db, page, page_size, user.uid, 'submit', date_start, date_end, status
    )
    return await ApiSuccessResponse({
        "data": result_data,
        "count": total_count
    })


@router.get("/pending_approve")
async def query_pending_approve(
        page: int, 
        page_size: int,
        date_start: str = None,
        date_end: str = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    查询待我审批的工作流
    参数：
    - page: 页码
    - page_size: 每页大小
    - date_start: 开始日期（可选）
    - date_end: 结束日期（可选）
    """
    result_data, total_count = await get_workflow_list_data(
        db, page, page_size, user.uid, 'pending', date_start, date_end
    )
    
    return await ApiSuccessResponse({
        "data": result_data,
        "count": total_count
    })


# 我已审批的
@router.get("/my_approved")
async def query_my_approved(
        page: int, 
        page_size: int,
        date_start: str = None,
        date_end: str = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    查询我已审批的工作流
    参数：
    - page: 页码
    - page_size: 每页大小
    - date_start: 开始日期（可选）
    - date_end: 结束日期（可选）
    """
    result_data, total_count = await get_workflow_list_data(
        db, page, page_size, user.uid, 'approved', date_start, date_end
    )
    
    return await ApiSuccessResponse({
        "data": result_data,
        "count": total_count
    })


@router.get("/cc_to_me")
async def query_cc_to_me(
        page: int, 
        page_size: int,
        date_start: str = None,
        date_end: str = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    查询抄送给我的工作流
    参数：
    - page: 页码
    - page_size: 每页大小
    - date_start: 开始日期（可选）
    - date_end: 结束日期（可选）
    """
    result_data, total_count = await get_workflow_list_data(
        db, page, page_size, user.uid, 'cc', date_start, date_end
    )
    
    return await ApiSuccessResponse({
        "data": result_data,
        "count": total_count
    })

@router.post("/action_workflow")
async def action_workflow_api(
        action_data: WorkflowAction,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    处理工作流动作
    - action: 动作类型 1 同意 2 驳回 
    """
    result = await handle_workflow_action(
        db, 
        action_data.receipt_id, 
        user.uid, 
        action_data.action, 
        action_data.comments,
        action_data.bank_account_id
    )
    return await ApiSuccessResponse(result)

@router.post("/cancel_workflow")
async def cancel_workflow_api(
        receipt_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    取消工作流
    """
    try:
        result = await cancel_workflow(db, receipt_id, user.uid)
        return await ApiSuccessResponse(result)
    except Exception as e:
        return await ApiFailedResponse(str(e))


@router.get("/workflow_instance/{instance_id}")
async def get_workflow_instance(
        instance_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    获取工作流实例详情
    """
    instance = await get_workflow_instance_with_details(db, instance_id)
    if not instance:
        return await ApiFailedResponse("工作流实例不存在")
    
    return await ApiSuccessResponse(instance)


@router.get("/receipt/{receipt_id}")
async def get_receipt_workflow(
        receipt_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    获取单据对应的工作流全部信息
    - payment_obj: 付款对象信息, <=3 为付款对象表中的信息， 4 为内置学生信息， 5 为内置员工信息
    """
    receipt_data = await get_receipt_with_workflow(db, receipt_id)
    if not receipt_data:
        return await ApiFailedResponse("单据不存在")
    
    return await ApiSuccessResponse(receipt_data)


# 常用付款对象的增删改查ErpPaymentObj，支持按照类型查询

@router.get("/related_obj")
async def get_related_obj(
        page: int,
        page_size: int,
        keyword: str = None,
        obj_type: int = None,
        obj_name: str = None,
        account_type: int = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    获取关联对象信息分页
    支持按照类型查询
    参数：
    - page: 页码
    - page_size: 每页大小
    - obj_type: 对象类型（可选）
    - account_type: 账户类型（可选）
    - obj_name: 对象名称（可选）
    """
    # 查询对象并分页
    objects = await get_related_obj_list(db, page, page_size, obj_type, account_type, keyword, obj_name, count=False)
    
    count = await get_related_obj_list(db, page, page_size, obj_type, account_type, keyword, obj_name, count=True)
    
    return await ApiSuccessResponse({
        "data": objects,
        "count": count
    })


@router.get("/related_obj/{obj_id}")
async def get_related_obj_detail(
        obj_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    获取付款对象详情
    """
    obj = await erp_payment_obj.get_by_id(db, obj_id)
    if not obj:
        return await ApiFailedResponse("付款对象不存在")
    
    return await ApiSuccessResponse(obj)



@router.post("/related_obj/")
async def create_related_obj(
        params: PaymentObjCreate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    创建付款对象
    参数：
    - obj_name: 对象名称
    - obj_related_id: 学生需要学生id, 员工需要员工id
    - obj_type: 对象类型  /related_obj_type接口的id
    - account_type: 账户类型 /bank_account_type接口的id
    - account_no: 账号（可选）
    - bank_name: 银行名称（可选）
    - remark: 备注（可选）
    """
    # 先检查付款对象的account_no是否已存在
    exist_obj = await erp_payment_obj.get_one(db, account_no=params.account_no)
    if exist_obj:
        return await ApiFailedResponse("该账号的付款对象已存在")

    # 创建付款对象
    obj_data = {
        "obj_name": params.obj_name,
        "obj_type": params.obj_type,
        "obj_related_id": params.obj_related_id,
        "account_type": params.account_type,
        "account_no": params.account_no,
        "bank_name": params.bank_name,
        "remark": params.remark,
        "create_by": user.uid,
        "update_by": user.uid,
        "obj_related_name": params.obj_related_name,
        "phone": params.phone,
    }
    
    obj = await erp_payment_obj.create(db, commit=True, **obj_data)
    return await ApiSuccessResponse(obj)



@router.put("/related_obj/{obj_id}")
async def update_related_obj(
        obj_id: int,
        params: RelatedObjUpdate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    更新付款对象
    """
    # 检查付款对象是否存在
    obj = await erp_payment_obj.get_by_id(db, obj_id)
    if not obj:
        return await ApiFailedResponse("付款对象不存在")
    
    if params.obj_name:
        obj.obj_name = params.obj_name
    if params.obj_type:
        obj.obj_type = params.obj_type
    if params.account_type:
        obj.account_type = params.account_type
    if params.account_no:
        obj.account_no = params.account_no
    if params.bank_name:
        obj.bank_name = params.bank_name
    if params.remark:
        obj.remark = params.remark
    # if params.obj_related_name:
    #     obj.obj_name = params.obj_related_name
    # if params.phone:
    #     obj.phone = params.phone
    obj.update_by = user.uid
    await db.commit()
        
    return await ApiSuccessResponse(True)


@router.delete("/related_obj/{obj_id}")
async def delete_related_obj(
        obj_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    删除付款对象
    """
    # 检查对象是否存在
    obj = await erp_payment_obj.get_by_id(db, obj_id)
    if not obj:
        return await ApiFailedResponse("付款对象不存在")
    
    # 检查是否有关联的单据
    receipts = await erp_receipt.get_many(db, {"related_obj_id": obj_id})
    if receipts:
        return await ApiFailedResponse("该付款对象已被单据使用，无法删除")
    
    # 删除对象
    await erp_payment_obj.delete_one(db, obj_id)
    return await ApiSuccessResponse(True)



# 付款对象标签维护
@router.get("/related_obj_type")
async def get_related_obj_type(
    db: AsyncSession = Depends(get_default_db),
):
    """
    获取付款对象类型
    """
    types = await erp_payment_obj_type.get_many(db)
    return await ApiSuccessResponse(types)


@router.post("/related_obj_type/")
async def create_related_obj_type(
    params: RelatedObjTagCreate,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    创建付款对象类型
    """
    type = await erp_payment_obj_type.create(db, commit=True, **{
        "type_name": params.type_name,
        "create_by": user.uid,
        "update_by": user.uid,
    })
    return await ApiSuccessResponse(type)


@router.delete("/related_obj_type/{type_id}")
async def delete_related_obj_type(
    type_id: int,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    删除付款对象类型
    """
    await erp_payment_obj_type.delete_one(db, type_id)
    return await ApiSuccessResponse(True)


@router.put("/related_obj_type/{type_id}")
async def update_related_obj_type(
    type_id: int,
    params: RelatedObjTagCreate,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    更新付款对象类型
    """
    exist_type = await erp_payment_obj_type.get_by_id(db, type_id)
    if not exist_type:
        return await ApiFailedResponse("付款对象类型不存在")
    
    exist_type.type_name = params.type_name
    exist_type.update_by = user.uid
    await db.commit()
    return await ApiSuccessResponse(exist_type)

# 查询所有岗位
@router.get(f"/qy_wechat_position")
async def qy_wechat_position(
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(get_current_active_user),
):
    """
    # 查询所有岗位
    """
    accounts = await erp_account.get_many(db, raw=[
        ErpAccount.qy_wechat_position != None
    ])
    positions = list(set([account.qy_wechat_position for account in accounts]))
    return await ApiSuccessResponse(positions)


# 查询费用类型绑定工作流
@router.get("/type_bind_workflow")
async def cost_type_bind_workflow(
    bind_id:int=None,
    db: AsyncSession = Depends(get_default_db),
):
    """
    固定的单据绑定工作流
    - type 传参 修改为-> bind_id

    """
    data = await get_workflow_cost_type_related(db, cost_type_bind=bind_id)
    return await ApiSuccessResponse(data)



# 根据workflow_id获取费用类型
@router.get("/workflow_cost_type_by_workflow_id")
async def query_workflow_cost_type_by_workflow_id(
    workflow_id: int,
    db: AsyncSession = Depends(get_default_db),
):
    """
    根据workflow_id获取费用类型
    """
    cost_type_objs = await get_workflow_cost_type_by_workflow_id_module(db, workflow_id=workflow_id)
    return await ApiSuccessResponse(cost_type_objs)


# 修改费用类型绑定工作流
@router.put("/type_bind_workflow/{bind_id}") 
async def update_workflow_cost_type_bind(
    bind_id: int,
    params: WorkflowCostTypeBindUpdate,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(get_current_active_user),
):
    """
    修改费用类型绑定工作流
    """
    exist_bind = await erp_cost_type_bind.get_by_id(db, bind_id)
    if not exist_bind:
        return await ApiFailedResponse("费用类型绑定工作流不存在")
    
    # 修改费用类型绑定工作流
    if params.workflow_id:
        exist_bind.workflow_id = params.workflow_id
    if params.default_cost_type_id:
        exist_bind.default_cost_type_id = params.default_cost_type_id
    if params.receipt_category_name:
        exist_bind.receipt_category_name = params.receipt_category_name
    await db.commit()
    return await ApiSuccessResponse(True)
    

# 新增费用类型绑定工作流
@router.post("/type_bind_workflow")
async def create_workflow_cost_type_bind(
    params: WorkflowCostTypeBindUpdate,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(get_current_active_user),
):
    """
    新增费用类型绑定工作流
    """
    cost_type_obj = await erp_finance_cost_type.get_by_id(db, params.cost_type_id)
    if not cost_type_obj:
        return await ApiFailedResponse("费用类型不存在")
    
    cost_type_name = cost_type_obj.name
    # 新增费用类型绑定工作流
    await erp_cost_type_bind.create(db, commit=True, **{
        "workflow_id": params.workflow_id,
        "default_cost_type_id": params.default_cost_type_id,
        "cost_type_name": cost_type_name,
        "receipt_category_name": params.receipt_category_name,
    })
    return await ApiSuccessResponse(True)

