"""
eeo消息订阅模块
"""
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc
from public_api.serializer import MessageSubscribe
import settings
from utils.db.db_handler import get_default_db
from models.m_eeo import ErpEeoMessageLogs, ErpEeoExamScores, ErpEeoHomeworkScores, ErpEeoHomeworkSubmit, ErpEeoExamScoresDetail, ErpEeoAnswerSheetScores, ErpEeoAnswerSheetScoresDetail, ErpEeoCourseRecordings, ErpEeoPhoneNumberChange, ErpEeoCourseInfoChange, ErpEeoClassInfoChange, ErpEeoCourseStudentChange, ErpEeoClassStudentChange

router = APIRouter(prefix="/eeo", tags=["eeo"])

erp_eeo_message_logs = settings.CF.get_crud(ErpEeoMessageLogs)
erp_eeo_exam_scores = settings.CF.get_crud(ErpEeoExamScores)
erp_eeo_homework_scores = settings.CF.get_crud(ErpEeoHomeworkScores)
erp_eeo_homework_submit = settings.CF.get_crud(ErpEeoHomeworkSubmit)
erp_eeo_exam_scores_detail = settings.CF.get_crud(ErpEeoExamScoresDetail)
erp_eeo_answer_sheet_scores = settings.CF.get_crud(ErpEeoAnswerSheetScores)
erp_eeo_answer_sheet_scores_detail = settings.CF.get_crud(ErpEeoAnswerSheetScoresDetail)
erp_eeo_course_recordings = settings.CF.get_crud(ErpEeoCourseRecordings)
erp_eeo_phone_number_change = settings.CF.get_crud(ErpEeoPhoneNumberChange)
erp_eeo_course_info_change = settings.CF.get_crud(ErpEeoCourseInfoChange)
erp_eeo_class_info_change = settings.CF.get_crud(ErpEeoClassInfoChange)
erp_eeo_course_student_change = settings.CF.get_crud(ErpEeoCourseStudentChange)
erp_eeo_class_student_change = settings.CF.get_crud(ErpEeoClassStudentChange)


async def clean_duplicate_exam_scores(db: AsyncSession, condition: dict):
    """
    清理重复的考试成绩记录，保留最新的一条
    """
    try:
        # 构建查询条件
        conditions = []
        for key, value in condition.items():
            if value is not None:
                conditions.append(getattr(ErpEeoExamScores, key) == value)
        
        # 只有当disable字段存在时才添加条件
        if hasattr(ErpEeoExamScores, 'disable'):
            conditions.append(ErpEeoExamScores.disable == 0)
        
        # 查找重复记录
        stmt = select(ErpEeoExamScores).where(and_(*conditions)).order_by(desc(ErpEeoExamScores.updated_at))
        result = await db.execute(stmt)
        records = result.scalars().all()
        
        if len(records) > 1:
            settings.logger.warning(f"Found {len(records)} duplicate exam score records, keeping the latest one")
            # 保留第一条（最新的），处理其他的
            records_to_handle = records[1:]
            
            # 如果有disable字段，则标记为删除；否则直接删除记录
            if hasattr(ErpEeoExamScores, 'disable'):
                for record in records_to_handle:
                    record.disable = 1
                    db.add(record)
                    settings.logger.info(f"Marked duplicate exam score record {record.id} as disabled")
            else:
                # 如果没有disable字段，直接删除重复记录
                for record in records_to_handle:
                    await db.delete(record)
                    settings.logger.info(f"Deleted duplicate exam score record {record.id}")
            
            await db.flush()
            return records[0]  # 返回保留的记录
        elif len(records) == 1:
            return records[0]
        else:
            return None
            
    except Exception as e:
        settings.logger.error(f"Error cleaning duplicate exam scores: {e}")
        return None


async def clean_duplicate_homework_scores(db: AsyncSession, condition: dict):
    """
    清理重复的作业成绩记录，保留最新的一条
    """
    try:
        # 构建查询条件
        conditions = []
        for key, value in condition.items():
            if value is not None:
                conditions.append(getattr(ErpEeoHomeworkScores, key) == value)
        
        # 只有当disable字段存在时才添加条件
        if hasattr(ErpEeoHomeworkScores, 'disable'):
            conditions.append(ErpEeoHomeworkScores.disable == 0)
        
        # 查找重复记录
        stmt = select(ErpEeoHomeworkScores).where(and_(*conditions)).order_by(desc(ErpEeoHomeworkScores.updated_at))
        result = await db.execute(stmt)
        records = result.scalars().all()
        
        if len(records) > 1:
            settings.logger.warning(f"Found {len(records)} duplicate homework score records, keeping the latest one")
            # 保留第一条（最新的），处理其他的
            records_to_handle = records[1:]
            
            # 如果有disable字段，则标记为删除；否则直接删除记录
            if hasattr(ErpEeoHomeworkScores, 'disable'):
                for record in records_to_handle:
                    record.disable = 1
                    db.add(record)
                    settings.logger.info(f"Marked duplicate homework score record {record.id} as disabled")
            else:
                # 如果没有disable字段，直接删除重复记录
                for record in records_to_handle:
                    await db.delete(record)
                    settings.logger.info(f"Deleted duplicate homework score record {record.id}")
            
            await db.flush()
            return records[0]  # 返回保留的记录
        elif len(records) == 1:
            return records[0]
        else:
            return None
            
    except Exception as e:
        settings.logger.error(f"Error cleaning duplicate homework scores: {e}")
        return None


async def clean_duplicate_homework_submit(db: AsyncSession, condition: dict):
    """
    清理重复的作业提交记录，保留最新的一条
    """
    try:
        # 构建查询条件
        conditions = []
        for key, value in condition.items():
            if value is not None:
                conditions.append(getattr(ErpEeoHomeworkSubmit, key) == value)
        
        # 只有当disable字段存在时才添加条件
        if hasattr(ErpEeoHomeworkSubmit, 'disable'):
            conditions.append(ErpEeoHomeworkSubmit.disable == 0)
        
        # 查找重复记录
        stmt = select(ErpEeoHomeworkSubmit).where(and_(*conditions)).order_by(desc(ErpEeoHomeworkSubmit.updated_at))
        result = await db.execute(stmt)
        records = result.scalars().all()
        
        if len(records) > 1:
            settings.logger.warning(f"Found {len(records)} duplicate homework submit records, keeping the latest one")
            # 保留第一条（最新的），处理其他的
            records_to_handle = records[1:]
            
            # 如果有disable字段，则标记为删除；否则直接删除记录
            if hasattr(ErpEeoHomeworkSubmit, 'disable'):
                for record in records_to_handle:
                    record.disable = 1
                    db.add(record)
                    settings.logger.info(f"Marked duplicate homework submit record {record.id} as disabled")
            else:
                # 如果没有disable字段，直接删除重复记录
                for record in records_to_handle:
                    await db.delete(record)
                    settings.logger.info(f"Deleted duplicate homework submit record {record.id}")
            
            await db.flush()
            return records[0]  # 返回保留的记录
        elif len(records) == 1:
            return records[0]
        else:
            return None
            
    except Exception as e:
        settings.logger.error(f"Error cleaning duplicate homework submit: {e}")
        return None


async def clean_duplicate_answer_sheet_scores(db: AsyncSession, condition: dict):
    """
    清理重复的答题卡成绩记录，保留最新的一条
    """
    try:
        # 构建查询条件
        conditions = []
        for key, value in condition.items():
            if value is not None:
                conditions.append(getattr(ErpEeoAnswerSheetScores, key) == value)
        
        # 只有当disable字段存在时才添加条件
        if hasattr(ErpEeoAnswerSheetScores, 'disable'):
            conditions.append(ErpEeoAnswerSheetScores.disable == 0)
        
        # 查找重复记录
        stmt = select(ErpEeoAnswerSheetScores).where(and_(*conditions)).order_by(desc(ErpEeoAnswerSheetScores.updated_at))
        result = await db.execute(stmt)
        records = result.scalars().all()
        
        if len(records) > 1:
            settings.logger.warning(f"Found {len(records)} duplicate answer sheet score records, keeping the latest one")
            # 保留第一条（最新的），处理其他的
            records_to_handle = records[1:]
            
            # 如果有disable字段，则标记为删除；否则直接删除记录
            if hasattr(ErpEeoAnswerSheetScores, 'disable'):
                for record in records_to_handle:
                    record.disable = 1
                    db.add(record)
                    settings.logger.info(f"Marked duplicate answer sheet score record {record.id} as disabled")
            else:
                # 如果没有disable字段，直接删除重复记录
                for record in records_to_handle:
                    await db.delete(record)
                    settings.logger.info(f"Deleted duplicate answer sheet score record {record.id}")
            
            await db.flush()
            return records[0]  # 返回保留的记录
        elif len(records) == 1:
            return records[0]
        else:
            return None
            
    except Exception as e:
        settings.logger.error(f"Error cleaning duplicate answer sheet scores: {e}")
        return None


async def clean_duplicate_course_recordings(db: AsyncSession, condition: dict):
    """
    清理重复的录课记录，保留最新的一条
    """
    try:
        # 构建查询条件
        conditions = []
        for key, value in condition.items():
            if value is not None:
                conditions.append(getattr(ErpEeoCourseRecordings, key) == value)
        
        # 只有当disable字段存在时才添加条件
        if hasattr(ErpEeoCourseRecordings, 'disable'):
            conditions.append(ErpEeoCourseRecordings.disable == 0)
        
        # 查找重复记录
        stmt = select(ErpEeoCourseRecordings).where(and_(*conditions)).order_by(desc(ErpEeoCourseRecordings.updated_at))
        result = await db.execute(stmt)
        records = result.scalars().all()
        
        if len(records) > 1:
            settings.logger.warning(f"Found {len(records)} duplicate course recording records, keeping the latest one")
            # 保留第一条（最新的），处理其他的
            records_to_handle = records[1:]
            
            # 如果有disable字段，则标记为删除；否则直接删除记录
            if hasattr(ErpEeoCourseRecordings, 'disable'):
                for record in records_to_handle:
                    record.disable = 1
                    db.add(record)
                    settings.logger.info(f"Marked duplicate course recording record {record.id} as disabled")
            else:
                # 如果没有disable字段，直接删除重复记录
                for record in records_to_handle:
                    await db.delete(record)
                    settings.logger.info(f"Deleted duplicate course recording record {record.id}")
            
            await db.flush()
            return records[0]  # 返回保留的记录
        elif len(records) == 1:
            return records[0]
        else:
            return None
            
    except Exception as e:
        settings.logger.error(f"Error cleaning duplicate course recordings: {e}")
        return None

# 消息订阅接口
@router.post("/message_subscribe")
async def message_subscribe(
    message: MessageSubscribe,
    db: AsyncSession = Depends(get_default_db),
):
    """
    ## 消息订阅接口，前端请勿进行请求
    ### 参数:
        - SID: 机构id
        - Cmd: 消息类型
        - Msg: 消息内容
        - SafeKey: 安全密钥
        - TimeStamp: 时间戳
        - CourseID: 课程id
        - CourseName: 课程名称
        - Data: 具体数据
        - ReplaceTime: 更换手机号码操作时间
        - UID: 用户UID
        - Email: 用户邮箱
    """
    try:
        if message.Cmd == "Test":
            print(message)
            return {
                "error_info": {       
                    "errno": 1,
                    "error": "程序正常执行" 
                }
            }

        if message.Cmd not in ["Record", "ReplacePhoneNumber", "eeoCourseInfoChange", "eeoClassInfoChange", "eeoCourseStudentChange", "eeoClassStudentChange"]:    # 录课文件数据、更换手机号码消息和新增的4种消息类型不存储在总日志表
            await erp_eeo_message_logs.create(
                db,
                commit=False,
                **{
                "sid": str(message.SID) if message.SID else None,
                "cmd": message.Cmd,
                "msg": message.Msg,
                "safe_key": message.SafeKey,
                "time_stamp": message.TimeStamp,
                "course_id": str(message.CourseID) if message.CourseID else None,
                "course_name": message.CourseName,
                "data": message.Data,
                
                }
            )

        if message.Cmd == "ReplacePhoneNumber":
            # settings.logger.info(f"EEO-更换手机号码消息: {message}")
            # 处理更换手机号码消息，更新用户手机号
            await erp_eeo_phone_number_change.create(
                db,
                commit=False,
                **{
                    "sid": str(message.SID) if message.SID else None,
                    "uid": str(message.UID) if message.UID else None,
                    "telephone": message.Telephone,
                    "replace_time": message.ReplaceTime,
                    "processed": 0  # 初始状态为未处理
                }
            )
        elif message.Cmd == "Record":
            # settings.logger.info(f"EEO-录课文件数据推送: {message}")
            # 处理录课文件数据
            condition = {
                "sid": str(message.SID) if message.SID else None,
                "course_id": str(message.CourseID) if message.CourseID else None,
                "file_id": message.FileId if message.FileId else None,
                "action_time": message.ActionTime
            }
            values = {
                "vurl": message.VUrl,
                "vst": message.VST,
                "vet": message.VET,
                "duration": message.Duration,
                "size": message.Size,
                "cid_ext": message.CIDExt,
                "class_id": str(message.ClassID) if message.ClassID else None,
            }
            
            try:
                # 使用改进的处理逻辑
                course_recording = None
                
                # 方法1：尝试使用upsert方法
                try:
                    course_recording = await erp_eeo_course_recordings.upsert(
                        db,
                        condition=condition,
                        values=values,
                        commit=False
                    )
                    settings.logger.info(f"Successfully upserted course recording record {course_recording.id}")
                except Exception as upsert_error:
                    settings.logger.warning(f"Upsert failed, trying manual duplicate handling: {upsert_error}")
                    
                    # 方法2：手动处理重复记录
                    existing_record = await clean_duplicate_course_recordings(db, condition)
                    
                    if existing_record:
                        # 如果存在记录，直接更新
                        non_empty_values = {key: value for key, value in values.items() if value is not None}
                        for key, value in non_empty_values.items():
                            setattr(existing_record, key, value)
                        db.add(existing_record)
                        await db.flush()
                        await db.refresh(existing_record)
                        course_recording = existing_record
                        settings.logger.info(f"Updated existing course recording record {course_recording.id}")
                    else:
                        # 如果不存在记录，创建新记录
                        create_data = {**condition, **values}
                        course_recording = ErpEeoCourseRecordings(**create_data)
                        db.add(course_recording)
                        await db.flush()
                        await db.refresh(course_recording)
                        settings.logger.info(f"Created new course recording record {course_recording.id}")
            except Exception as e:
                settings.logger.error(f"EEO-Record处理失败，条件: {condition}, 错误: {e}")
                raise

        elif message.Cmd == "HomeworkSubmit":
            # settings.logger.info(f"EEO-作业提交消息: {message}")
            if message.Data:
                condition = {
                    "sid": str(message.SID),
                    "course_id": str(message.CourseID) if message.CourseID else None,
                    "activity_id": str(message.Data.get("ActivityId")) if message.Data.get("ActivityId") else None,
                    "unit_id": str(message.Data.get("UnitId")) if message.Data.get("UnitId") else None,
                    "student_uid": str(message.Data.get("StudentInfo", {}).get("StudentUid")) if message.Data.get("StudentInfo", {}).get("StudentUid") else None,
                    
                }
                values = {
                    "course_name": message.CourseName,
                    "activity_name": message.Data.get("ActivityName"),
                    "unit_name": message.Data.get("UnitName"),
                    "student_name": message.Data.get("StudentInfo", {}).get("StudentName"),
                    "student_account": message.Data.get("StudentInfo", {}).get("StudentAccount"),
                    "teacher_uid": str(message.Data.get("TeacherInfo", {}).get("TeacherUid")) if message.Data.get("TeacherInfo", {}).get("TeacherUid") else None,
                    "teacher_name": message.Data.get("TeacherInfo", {}).get("TeacherName"),
                    "teacher_account": message.Data.get("TeacherInfo", {}).get("TeacherAccount"),
                    "files": message.Data.get("Files"),
                    "content": message.Data.get("Content"),
                    "is_submit_late": message.Data.get("IsSubmitLate", 0),
                    "is_revision": message.Data.get("IsRevision", 0),
                    "student_total": message.Data.get("StudentTotal"),
                    "submit_total": message.Data.get("SubmitTotal"),
                    "submission_time": message.Data.get("SubmissionTime")
                }
                
                try:
                    # 使用改进的处理逻辑
                    homework_submit = None
                    
                    # 方法1：尝试使用upsert方法
                    try:
                        homework_submit = await erp_eeo_homework_submit.upsert(
                            db,
                            condition=condition,
                            values=values,
                            commit=False
                        )
                        settings.logger.info(f"Successfully upserted homework submit record {homework_submit.id}")
                    except Exception as upsert_error:
                        settings.logger.warning(f"Upsert failed, trying manual duplicate handling: {upsert_error}")
                        
                        # 方法2：手动处理重复记录
                        existing_record = await clean_duplicate_homework_submit(db, condition)
                        
                        if existing_record:
                            # 如果存在记录，直接更新
                            non_empty_values = {key: value for key, value in values.items() if value is not None}
                            for key, value in non_empty_values.items():
                                setattr(existing_record, key, value)
                            db.add(existing_record)
                            await db.flush()
                            await db.refresh(existing_record)
                            homework_submit = existing_record
                            settings.logger.info(f"Updated existing homework submit record {homework_submit.id}")
                        else:
                            # 如果不存在记录，创建新记录
                            create_data = {**condition, **values}
                            homework_submit = ErpEeoHomeworkSubmit(**create_data)
                            db.add(homework_submit)
                            await db.flush()
                            await db.refresh(homework_submit)
                            settings.logger.info(f"Created new homework submit record {homework_submit.id}")
                except Exception as e:
                    settings.logger.error(f"EEO-HomeworkSubmit处理失败，条件: {condition}, 错误: {e}")
                    raise

        elif message.Cmd == "HomeworkScore":
            # settings.logger.info(f"EEO-作业成绩推送: {message}")
            if message.Data:
                # 获取学生和老师信息
                student_info = message.Data.get("StudentInfo", {})
                teacher_info = message.Data.get("TeacherInfo", {})
                
                # 创建作业成绩记录
                condition = {
                    "sid": str(message.SID),
                    "course_id": str(message.CourseID) if message.CourseID else None,
                    "activity_id": str(message.Data.get("ActivityId")) if message.Data.get("ActivityId") else None,
                    "unit_id": str(message.Data.get("UnitId")) if message.Data.get("UnitId") else None,
                    "student_uid": str(student_info.get("StudentUid")) if student_info.get("StudentUid") else None,
                    
                }
                values = {
                    "course_name": message.CourseName,
                    "activity_name": message.Data.get("ActivityName"),
                    "unit_name": message.Data.get("UnitName"),
                    "student_name": student_info.get("StudentName"),
                    "student_account": student_info.get("StudentAccount"),
                    "teacher_uid": str(teacher_info.get("TeacherUid")) if teacher_info.get("TeacherUid") else None,
                    "teacher_name": teacher_info.get("TeacherName"),
                    "teacher_account": teacher_info.get("TeacherAccount"),
                    "score": message.Data.get("Score"),
                    "student_score": message.Data.get("StudentScore"),
                    "student_scoring_rate": message.Data.get("StudentScoringRate"),
                    "review_details": message.Data.get("ReviewDetails"),
                    "grading_plan": message.Data.get("GradingPlan"),
                    "correction_time": message.Data.get("CorrectionTime"),
                    "submission_time": message.Data.get("SubmissionTime")
                }
                
                try:
                    # 使用改进的处理逻辑
                    homework_score = None
                    
                    # 方法1：尝试使用upsert方法
                    try:
                        homework_score = await erp_eeo_homework_scores.upsert(
                            db,
                            condition=condition,
                            values=values,
                            commit=False
                        )
                        settings.logger.info(f"Successfully upserted homework score record {homework_score.id}")
                    except Exception as upsert_error:
                        settings.logger.warning(f"Upsert failed, trying manual duplicate handling: {upsert_error}")
                        
                        # 方法2：手动处理重复记录
                        existing_record = await clean_duplicate_homework_scores(db, condition)
                        
                        if existing_record:
                            # 如果存在记录，直接更新
                            non_empty_values = {key: value for key, value in values.items() if value is not None}
                            for key, value in non_empty_values.items():
                                setattr(existing_record, key, value)
                            db.add(existing_record)
                            await db.flush()
                            await db.refresh(existing_record)
                            homework_score = existing_record
                            settings.logger.info(f"Updated existing homework score record {homework_score.id}")
                        else:
                            # 如果不存在记录，创建新记录
                            create_data = {**condition, **values}
                            homework_score = ErpEeoHomeworkScores(**create_data)
                            db.add(homework_score)
                            await db.flush()
                            await db.refresh(homework_score)
                            settings.logger.info(f"Created new homework score record {homework_score.id}")
                    
                    # 处理作业明细数据
                    # 如果存在题目结果数据，插入到明细表
                    if homework_score and message.Data.get("TopicResults"):
                        for topic in message.Data.get("TopicResults", []):
                            condition_detail = {
                                "exam_score_id": homework_score.id,
                                "topic_id": topic.get("TopicId")
                            }
                            values_detail = {
                                "topic_type": topic.get("TopicType"),
                                "topic_max_score": topic.get("TopicMaxScore"),
                                "topic_score": topic.get("TopicScore"),
                                "topic_result": topic.get("TopicResult")
                            }
                            await erp_eeo_exam_scores_detail.upsert(
                                db,
                                condition=condition_detail,
                                values=values_detail,
                                commit=False
                            )
                except Exception as e:
                    settings.logger.error(f"EEO-HomeworkScore处理失败，条件: {condition}, 错误: {e}")
                    raise

        elif message.Cmd == "ExamScore":
            # settings.logger.info(f"EEO-测验成绩推送: {message}")
            if message.Data:
                # 创建考试成绩记录
                condition = {
                    "sid": str(message.SID),
                    "course_id": str(message.CourseID) if message.CourseID else None,
                    "activity_id": str(message.Data.get("ActivityId")) if message.Data.get("ActivityId") else None,
                    "unit_id": str(message.Data.get("UnitId")) if message.Data.get("UnitId") else None,
                    "student_uid": str(message.Data.get("StudentInfo", {}).get("StudentUid")) if message.Data.get("StudentInfo", {}).get("StudentUid") else None,
                    
                }
                values = {
                    "course_name": message.CourseName,
                    "activity_name": message.Data.get("ActivityName"),
                    "unit_name": message.Data.get("UnitName"),
                    "class_id": str(message.Data.get("ClassId")) if message.Data.get("ClassId") else None,
                    "student_name": message.Data.get("StudentInfo", {}).get("StudentName"),
                    "student_account": message.Data.get("StudentInfo", {}).get("StudentAccount"),
                    "teacher_uid": str(message.Data.get("TeacherInfo", {}).get("TeacherUid")) if message.Data.get("TeacherInfo", {}).get("TeacherUid") else None,
                    "teacher_name": message.Data.get("TeacherInfo", {}).get("TeacherName"),
                    "teacher_account": message.Data.get("TeacherInfo", {}).get("TeacherAccount"),
                    "score": message.Data.get("Score"),
                    "student_scoring_rate": round(message.Data.get("StudentScoringRate"), 4),
                    "answer_duration": message.Data.get("AnswerDuration"),
                    "correction_time": message.Data.get("CorrectionTime"),
                    "submission_time": message.Data.get("SubmissionTime")
                }
                
                try:
                    # 使用改进的处理逻辑
                    exam_score = None
                    
                    # 方法1：尝试使用upsert方法
                    try:
                        exam_score = await erp_eeo_exam_scores.upsert(
                            db,
                            condition=condition,
                            values=values,
                            commit=False
                        )
                        settings.logger.info(f"Successfully upserted exam score record {exam_score.id}")
                    except Exception as upsert_error:
                        settings.logger.warning(f"Upsert failed, trying manual duplicate handling: {upsert_error}")
                        
                        # 方法2：手动处理重复记录
                        existing_record = await clean_duplicate_exam_scores(db, condition)
                        
                        if existing_record:
                            # 如果存在记录，直接更新
                            non_empty_values = {key: value for key, value in values.items() if value is not None}
                            for key, value in non_empty_values.items():
                                setattr(existing_record, key, value)
                            db.add(existing_record)
                            await db.flush()
                            await db.refresh(existing_record)
                            exam_score = existing_record
                            settings.logger.info(f"Updated existing exam score record {exam_score.id}")
                        else:
                            # 如果不存在记录，创建新记录
                            create_data = {**condition, **values}
                            exam_score = ErpEeoExamScores(**create_data)
                            db.add(exam_score)
                            await db.flush()
                            await db.refresh(exam_score)
                            settings.logger.info(f"Created new exam score record {exam_score.id}")
                    
                    # 处理考试明细数据
                    if exam_score and message.Data.get("TopicDetails"):
                        for topic in message.Data.get("TopicDetails", []):
                            condition_detail = {
                                "exam_score_id": exam_score.id,
                                "topic_id": topic.get("TopicId")
                            }
                            values_detail = {
                                "topic_type": topic.get("TopicType"),
                                "topic_max_score": topic.get("TopicMaxScore"),
                                "topic_score": topic.get("TopicScore"),
                                "topic_result": topic.get("TopicResult")
                            }
                            await erp_eeo_exam_scores_detail.upsert(
                                db,
                                condition=condition_detail,
                                values=values_detail,
                                commit=False
                            )
                except Exception as e:
                    settings.logger.error(f"EEO-ExamScore处理失败，条件: {condition}, 错误: {e}")
                    raise

        elif message.Cmd == "AnswerSheetScore":
            # settings.logger.info(f"EEO-答题卡成绩推送: {message}")
            if message.Data:
                # 创建答题卡成绩记录
                condition = {
                    "sid": str(message.SID),
                    "course_id": str(message.CourseID) if message.CourseID else None,
                    "activity_id": str(message.Data.get("ActivityId")) if message.Data.get("ActivityId") else None,
                    "unit_id": str(message.Data.get("UnitId")) if message.Data.get("UnitId") else None,
                    "student_uid": str(message.Data.get("StudentInfo", {}).get("StudentUid")) if message.Data.get("StudentInfo", {}).get("StudentUid") else None,
                   
                }
                values = {
                    "course_name": message.CourseName,
                    "activity_name": message.Data.get("ActivityName"),
                    "unit_name": message.Data.get("UnitName"),
                    "class_id": str(message.Data.get("ClassId")) if message.Data.get("ClassId") else None,
                    "student_name": message.Data.get("StudentInfo", {}).get("StudentName"),
                    "student_account": message.Data.get("StudentInfo", {}).get("StudentAccount"),
                    "teacher_uid": str(message.Data.get("TeacherInfo", {}).get("TeacherUid")) if message.Data.get("TeacherInfo", {}).get("TeacherUid") else None,
                    "teacher_name": message.Data.get("TeacherInfo", {}).get("TeacherName"),
                    "teacher_account": message.Data.get("TeacherInfo", {}).get("TeacherAccount"),
                    "maximum_score": message.Data.get("MaximumScore"),
                    "score": message.Data.get("MaximumScore", 0) * message.Data.get("StudentScoringRate", 0) if message.Data.get("MaximumScore") is not None else 0,
                    "student_scoring_rate": message.Data.get("StudentScoringRate"),
                    "answer_duration": message.Data.get("AnswerDuration"),
                    "correction_time": message.Data.get("CorrectionTime"),
                     "submission_time": message.Data.get("SubmissionTime")
                }
                
                try:
                    # 使用改进的处理逻辑
                    sheet_score = None
                    
                    # 方法1：尝试使用upsert方法
                    try:
                        sheet_score = await erp_eeo_answer_sheet_scores.upsert(
                            db,
                            condition=condition,
                            values=values,
                            commit=False
                        )
                        settings.logger.info(f"Successfully upserted answer sheet score record {sheet_score.id}")
                    except Exception as upsert_error:
                        settings.logger.warning(f"Upsert failed, trying manual duplicate handling: {upsert_error}")
                        
                        # 方法2：手动处理重复记录
                        existing_record = await clean_duplicate_answer_sheet_scores(db, condition)
                        
                        if existing_record:
                            # 如果存在记录，直接更新
                            non_empty_values = {key: value for key, value in values.items() if value is not None}
                            for key, value in non_empty_values.items():
                                setattr(existing_record, key, value)
                            db.add(existing_record)
                            await db.flush()
                            await db.refresh(existing_record)
                            sheet_score = existing_record
                            settings.logger.info(f"Updated existing answer sheet score record {sheet_score.id}")
                        else:
                            # 如果不存在记录，创建新记录
                            create_data = {**condition, **values}
                            sheet_score = ErpEeoAnswerSheetScores(**create_data)
                            db.add(sheet_score)
                            await db.flush()
                            await db.refresh(sheet_score)
                            settings.logger.info(f"Created new answer sheet score record {sheet_score.id}")
                    
                    # 处理答题卡题目明细数据
                    if sheet_score and message.Data.get("TopicDetails"):
                        for topic in message.Data.get("TopicDetails", []):
                            condition_detail = {
                                "answer_sheet_score_id": sheet_score.id,
                                "topic_id": topic.get("TopicId")
                            }
                            values_detail = {
                                "topic_type": topic.get("TopicType"),
                                "topic_max_score": topic.get("TopicMaxScore"),
                                "topic_score": topic.get("TopicScore"),
                                "topic_result": topic.get("TopicResult")
                            }
                            await erp_eeo_answer_sheet_scores_detail.upsert(
                                db,
                                condition=condition_detail,
                                values=values_detail,
                                commit=False
                            )
                except Exception as e:
                    settings.logger.error(f"EEO-AnswerSheetScore处理失败，条件: {condition}, 错误: {e}")
                    raise

        elif message.Cmd == "eeoCourseInfoChange":
            # settings.logger.info(f"EEO-课程信息改动: {message}")
            # 处理课程信息改动消息
            op_data = message.Data if hasattr(message, 'Data') and message.Data else {}
            main_teacher_info = op_data.get("mainTeacherInfo", {}) if op_data else {}
            label_infos = op_data.get("labelInfos", []) if op_data else []
            
            await erp_eeo_course_info_change.create(
                db,
                commit=False,
                **{
                    "sid": str(message.SID) if message.SID else None,
                    "op_type": message.opType if hasattr(message, 'opType') else None,
                    "op_time": message.opTime if hasattr(message, 'opTime') else None,
                    "uid": str(message.UID) if message.UID else None,
                    "course_id": str(message.CourseId) if message.CourseId else None,
                    "op_source": message.opSource if hasattr(message, 'opSource') else None,
                    "course_name": op_data.get("courseName"),
                    "expiry_time": op_data.get("expiryTime"),
                    "course_type": op_data.get("courseType"),
                    "course_status": op_data.get("courseStatus"),
                    "category": op_data.get("category"),
                    "category_id": str(op_data.get("categoryId")) if op_data.get("categoryId") else None,
                    "subject_id": op_data.get("subjectId"),
                    "main_teacher_name": main_teacher_info.get("teacherName"),
                    "main_teacher_uid": str(main_teacher_info.get("teacherUid")) if main_teacher_info.get("teacherUid") else None,
                    "main_teacher_no": main_teacher_info.get("teacherNo"),
                    "label_infos": label_infos,
                }
            )

        elif message.Cmd == "eeoClassInfoChange":
            # settings.logger.info(f"EEO-课节信息改动: {message}")
            # 处理课节信息改动消息
            op_data = message.Data if hasattr(message, 'Data') and message.Data else {}
            teacher_info = op_data.get("teacherInfo", {}) if op_data else {}
            assistant_teacher_infos = op_data.get("assistantTeacherInfos", []) if op_data else []
            label_infos = op_data.get("labelInfos", []) if op_data else []
            
            await erp_eeo_class_info_change.create(
                db,
                commit=False,
                **{
                    "sid": str(message.SID) if message.SID else None,
                    "op_type": message.opType if hasattr(message, 'opType') else None,
                    "op_time": message.opTime if hasattr(message, 'opTime') else None,
                    "uid": str(message.UID) if message.UID else None,
                    "course_id": str(message.CourseId) if message.CourseId else None,
                    "class_id": str(message.ClassID) if message.ClassID else None,
                    "op_source": message.opSource if hasattr(message, 'opSource') else None,
                    "class_name": op_data.get("className"),
                    "course_name": op_data.get("courseName"),
                    "begin_time": op_data.get("beginTime"),
                    "end_time": op_data.get("endTime"),
                    "class_type": op_data.get("classType"),
                    "class_status": op_data.get("classStatus"),
                    "seat_num": op_data.get("seatNum"),
                    "is_auto_onstage": op_data.get("isAutoOnstage"),
                    "teacher_name": teacher_info.get("teacherName"),
                    "teacher_uid": str(teacher_info.get("teacherUid")) if teacher_info.get("teacherUid") else None,
                    "teacher_no": teacher_info.get("teacherNo"),
                    "assistant_teacher_infos": assistant_teacher_infos,
                    "label_infos": label_infos,
                }
            )

        elif message.Cmd == "eeoCourseStudentChange":
            # settings.logger.info(f"EEO-课程学生信息改动: {message}")
            # 处理课程学生信息改动消息
            op_data = message.Data if hasattr(message, 'Data') and message.Data else {}
            student_infos = op_data.get("studentInfos", []) if op_data else []
            
            await erp_eeo_course_student_change.create(
                db,
                commit=False,
                **{
                    "sid": str(message.SID) if message.SID else None,
                    "op_type": message.opType if hasattr(message, 'opType') else None,
                    "op_time": message.opTime if hasattr(message, 'opTime') else None,
                    "uid": str(message.UID) if message.UID else None,
                    "course_id": str(message.CourseId) if message.CourseId else None,
                    "op_source": message.opSource if hasattr(message, 'opSource') else None,
                    "course_name": op_data.get("courseName"),
                    "student_infos": student_infos,
                }
            )

        elif message.Cmd == "eeoClassStudentChange":
            # settings.logger.info(f"EEO-课节学生信息改动: {message}")
            # 处理课节学生信息改动消息
            op_data = message.Data if hasattr(message, 'Data') and message.Data else {}
            student_infos = op_data.get("studentInfos", []) if op_data else []
            
            await erp_eeo_class_student_change.create(
                db,
                commit=False,
                **{
                    "sid": str(message.SID) if message.SID else None,
                    "op_type": message.opType if hasattr(message, 'opType') else None,
                    "op_time": message.opTime if hasattr(message, 'opTime') else None,
                    "uid": str(message.UID) if message.UID else None,
                    "course_id": str(message.CourseId) if message.CourseId else None,
                    "class_id": str(message.ClassID) if message.ClassID else None,
                    "op_source": message.opSource if hasattr(message, 'opSource') else None,
                    "class_name": op_data.get("className"), 
                    "student_infos": student_infos,
                }
            )
        
        else:
            settings.logger.error(f"EEO-未知消息类型: {message}")
        await db.commit()
        return {
            "error_info": {       
                "errno": 1,
                "error": "程序正常执行" 
            }
        }
    except Exception as e:
        settings.logger.error(f"EEO-消息订阅接口异常 异常信息：{e}, 消息内容：{message}")
        return {
            "error_info": {       
                "errno": 0,
                "error": f"程序异常: {e}" 
            }
        }
