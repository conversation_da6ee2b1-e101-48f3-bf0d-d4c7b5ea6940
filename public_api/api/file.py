from fastapi import APIRouter, UploadFile
from models.models import ErpAccount
from settings import CF, FILE_SERVER
from utils.file.minio_handler import S3Client
from utils.response.response_handler import ApiSuccessResponse

erp_account = CF.get_crud(ErpAccount)

router = APIRouter(prefix="/excel", tags=["上传下载"])


@router.post("/upload_file/")
async def upload_file(
        file: UploadFile,
):
    """
    # 上传文件
    """
    s3 = S3Client()
    file_content = await file.read()
    url = await s3.upload_file(FILE_SERVER['bucket'], file.filename, file_content)
    return await ApiSuccessResponse(url, '上传文件')
