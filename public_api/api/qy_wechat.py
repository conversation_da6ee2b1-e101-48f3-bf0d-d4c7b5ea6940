"""
企业微信回调接口
"""

import json
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Dict, Any, Optional, List
import urllib.parse
import asyncio
import time

from fastapi import APIRouter, Request, Query, Depends
from fastapi.responses import PlainTextResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from models.models import ErpAccount
from models.m_student import ErpStudent
from models.m_teacher import ErpAccountTeacher
from models.m_class import ErpClass
from models.m_wechat import (
    ErpWechatChannelrecord, ErpWechatChannel, ErpWechatExternaluser,
    ErpWechatClassuser, ErpQwechatContact
)
from utils.db.db_handler import get_default_db
from utils.tencent.wx_biz_msg_crypt import WXBizMsgCrypt
from modules.qy_wechat.welcome_helper import QyWechatWelcomeHelper
from settings import CF, logger, QY_WECHAT_CONFIG
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

router = APIRouter(prefix="/qy_wechat", tags=["企业微信"])

# 初始化CRUD实例
erp_account = CF.get_crud(ErpAccount)
erp_student = CF.get_crud(ErpStudent)
erp_account_teacher = CF.get_crud(ErpAccountTeacher)
erp_class = CF.get_crud(ErpClass)
erp_wechat_channelrecord = CF.get_crud(ErpWechatChannelrecord)
erp_wechat_channel = CF.get_crud(ErpWechatChannel)
erp_wechat_externaluser = CF.get_crud(ErpWechatExternaluser)
erp_wechat_classuser = CF.get_crud(ErpWechatClassuser)
erp_qwechat_contact = CF.get_crud(ErpQwechatContact)

# 初始化企业微信欢迎语助手
welcome_helper = QyWechatWelcomeHelper()


@router.get("/receive_customer_add")
async def wechat_receive_customer_add_verify(
    request: Request,
    msg_signature: str = Query(..., description="消息签名"),
    timestamp: str = Query(..., description="时间戳"),
    nonce: str = Query(..., description="随机数"),
    echostr: str = Query(None, description="验证字符串"),
    db: AsyncSession = Depends(get_default_db)
):
    """
    企业微信回调接口URL验证
    处理企业微信的GET请求验证
    
    Args:
        msg_signature: 消息签名
        timestamp: 时间戳
        nonce: 随机数
        echostr: 验证字符串（仅GET请求时存在）
    """
    try:
        # 获取企业微信配置
        config = await get_wechat_config()
        if not config or not config.get('enable'):
            logger.warning(f"企业微信配置不存在或未启用")
            return PlainTextResponse("")

        # 如果是GET请求且包含echostr参数，则进行URL验证
        if request.method == "GET" and echostr:
            logger.info(f"收到企业微信URL验证请求: msg_signature={msg_signature}, timestamp={timestamp}, nonce={nonce}")
            
            # 对echostr进行URL解码
            decoded_echostr = urllib.parse.unquote(echostr)
            
            # 创建加密解密实例
            wxcpt = WXBizMsgCrypt(
                config['token'], 
                config['encoding_aes_key'], 
                config['corp_id']
            )
            
            # 验证URL并解密echostr
            ret_code, reply_echostr = wxcpt.verify_url(msg_signature, timestamp, nonce, decoded_echostr)
            print(ret_code, reply_echostr)
            if ret_code == 0:
                logger.info(f"企业微信URL验证成功，返回明文: {reply_echostr}")
                # 使用PlainTextResponse直接返回明文消息内容
                return PlainTextResponse(reply_echostr)
            else:
                logger.error(f"企业微信URL验证失败，错误码: {ret_code}")
                return PlainTextResponse("")
        
        # 如果不是验证请求，返回空字符串
        return PlainTextResponse("")
        
    except Exception as e:
        logger.error("企业微信URL验证处理异常: %s", str(e), exc_info=True)
        return PlainTextResponse("")


@router.post("/receive_customer_add")
async def wechat_receive_customer_add(
    request: Request,
    msg_signature: str = Query(..., description="消息签名"),
    timestamp: str = Query(..., description="时间戳"),
    nonce: str = Query(..., description="随机数"),
    db: AsyncSession = Depends(get_default_db)
):
    """
    企业微信欢迎语回调接口
    处理外部联系人添加事件
    
    Args:
        msg_signature: 消息签名
        timestamp: 时间戳
        nonce: 随机数
    """
    # 获取企业微信配置
    # 根据获取对应的企业微信配置
    # 这里需要根据实际数据库结构来获取配置信息
    config = await get_wechat_config()
    if not config or not config.get('enable'):
        logger.warning(f"企业微信配置不存在或未启用")
        return ""

    # 获取POST请求的密文数据
    post_data = await request.body()
    if not post_data:
        logger.error("POST请求数据为空")
        return ""
    
    # 生成请求唯一标识，用于防重复处理
    request_id = f"wechat_callback_{msg_signature}_{timestamp}_{nonce}"
    
    # 简单的内存缓存防重复（在生产环境中建议使用Redis）
    if not hasattr(wechat_receive_customer_add, '_processed_requests'):
        wechat_receive_customer_add._processed_requests = {}
    
    current_time = time.time()
    # 清理超过60秒的旧记录
    wechat_receive_customer_add._processed_requests = {
        k: v for k, v in wechat_receive_customer_add._processed_requests.items()
        if current_time - v < 60
    }
    
    # 检查是否已经处理过
    if request_id in wechat_receive_customer_add._processed_requests:
        logger.info(f"重复请求，跳过处理: {request_id}")
        return ""
    
    # 标记为正在处理
    wechat_receive_customer_add._processed_requests[request_id] = current_time
    
    # 增加全局锁机制，防止并发处理相同的回调
    processing_lock_key = f"processing_{msg_signature}_{timestamp}"
    if not hasattr(wechat_receive_customer_add, '_processing_locks'):
        wechat_receive_customer_add._processing_locks = set()
    
    # 清理超过30秒的锁
    if not hasattr(wechat_receive_customer_add, '_lock_timestamps'):
        wechat_receive_customer_add._lock_timestamps = {}
    
    expired_locks = [
        lock for lock, lock_time in wechat_receive_customer_add._lock_timestamps.items()
        if current_time - lock_time > 30
    ]
    for lock in expired_locks:
        wechat_receive_customer_add._processing_locks.discard(lock)
        del wechat_receive_customer_add._lock_timestamps[lock]
    
    # 检查是否有并发处理
    if processing_lock_key in wechat_receive_customer_add._processing_locks:
        logger.info(f"检测到并发处理，跳过: {processing_lock_key}")
        return ""
    
    # 设置处理锁
    wechat_receive_customer_add._processing_locks.add(processing_lock_key)
    wechat_receive_customer_add._lock_timestamps[processing_lock_key] = current_time
    
    # 解密消息，支持重试机制
    wxcpt = WXBizMsgCrypt(
        config['token'], 
        config['encoding_aes_key'], 
        config['corp_id']
    )
    
    # 解密重试机制，最多重试1次
    max_retries = 1
    decrypted_msg = None
    
    for attempt in range(max_retries):
        ret, decrypted_msg = wxcpt.decrypt_msg(msg_signature, timestamp, nonce, post_data.decode('utf-8'))
        if ret == 0:
            # 解密成功
            break
        elif ret == -40007:
            # 解密失败，可能是时间戳或签名问题，等待一秒后重试
            if attempt < max_retries - 1:
                logger.warning(f"解密失败(错误码: {ret})，第{attempt + 1}次重试...")
                await asyncio.sleep(1)
                continue
            else:
                logger.error(f"解密失败，已重试{max_retries}次，错误码: {ret}")
                return ""
        else:
            # 其他错误，不重试
            logger.error(f"解密失败，错误码: {ret}")
            return ""
    
    if decrypted_msg is None:
        logger.error("解密失败，未获取到解密消息")
        return ""
    
    # logger.info(f"WeChatReceiveCustomerAdd解密消息: {decrypted_msg}")
    
    # 解析XML消息
    try:
        root = ET.fromstring(decrypted_msg)
        msg_data = {
            'ToUserName': root.find('ToUserName').text if root.find('ToUserName') is not None else "",
            'FromUserName': root.find('FromUserName').text if root.find('FromUserName') is not None else "",
            'MsgType': root.find('MsgType').text if root.find('MsgType') is not None else "",
            'Event': root.find('Event').text if root.find('Event') is not None else "",
            'ChangeType': root.find('ChangeType').text if root.find('ChangeType') is not None else "",
            'UserID': root.find('UserID').text if root.find('UserID') is not None else "",
            'ExternalUserID': root.find('ExternalUserID').text if root.find('ExternalUserID') is not None else "",
            'State': root.find('State').text if root.find('State') is not None else "",
            'WelcomeCode': root.find('WelcomeCode').text if root.find('WelcomeCode') is not None else "",
            'ChatId': root.find('ChatId').text if root.find('ChatId') is not None else "",
            'UpdateDetail': root.find('UpdateDetail').text if root.find('UpdateDetail') is not None else "",
        }
    except ET.ParseError as e:
        logger.error(f"XML解析失败: {e}")
        return ""

    logger.info(f"WeChatReceiveCustomerAdd解密消息: {msg_data}")
    # 处理新添加客人事件
    if (msg_data['WelcomeCode'] and 
        msg_data['MsgType'] == "event" and 
        msg_data['Event'] == "change_external_contact" and 
        msg_data['ChangeType'] == "add_external_contact"):
        
        await handle_add_external_contact(db, config, msg_data)
    
    # 处理成员删除外部联系人或被外部联系人删除事件
    elif (msg_data['MsgType'] == "event" and 
            msg_data['Event'] == "change_external_contact" and 
            msg_data['ChangeType'] in ["del_external_contact", "del_follow_user"]):
        
        await handle_del_external_contact(db, msg_data)
    
    # 处理学生入群/退群事件
    elif (msg_data['MsgType'] == "event" and 
            msg_data['Event'] == "change_external_chat" and 
            msg_data['ChatId'] and 
            msg_data['ChangeType'] == "update" and 
            msg_data['UpdateDetail'] in ["add_member", "del_member"]):
        
        await handle_group_member_change(db, msg_data)
    
    # 处理修改群名事件
    elif (msg_data['MsgType'] == "event" and 
            msg_data['Event'] == "change_external_chat" and 
            msg_data['ChatId'] and 
            msg_data['ChangeType'] == "update" and 
            msg_data['UpdateDetail'] == "change_name"):
        
        await handle_group_name_change(db, msg_data)
    
    # 处理创建群聊事件
    elif (msg_data['MsgType'] == "event" and 
            msg_data['Event'] == "change_external_chat" and 
            msg_data['ChatId'] and 
            msg_data['ChangeType'] == "create"):
        
        await handle_group_create(db, msg_data)
    
    # 统一提交所有数据库操作
    try:
        await db.commit()
        logger.info("企业微信回调处理完成，数据库事务已提交")
    except Exception as commit_error:
        await db.rollback()
        logger.error(f"数据库事务提交失败，已回滚: {str(commit_error)}")
    finally:
        # 清理处理锁
        if processing_lock_key in wechat_receive_customer_add._processing_locks:
            wechat_receive_customer_add._processing_locks.discard(processing_lock_key)
        if processing_lock_key in wechat_receive_customer_add._lock_timestamps:
            del wechat_receive_customer_add._lock_timestamps[processing_lock_key]
    
    return ""
    



async def handle_add_external_contact(db: AsyncSession, config: Dict, msg_data: Dict):
    """
    处理添加外部联系人事件
    """
    try:
        user_id = msg_data['UserID']
        external_user_id = msg_data['ExternalUserID']
        state = msg_data['State']
        welcome_code = msg_data['WelcomeCode']
        
        # 获取员工信息
        # 根据企业微信UserID获取对应的系统用户
        emp_model = await erp_account.get_one(db, qy_wechat_userid=user_id)
        if not emp_model:
            logger.warning(f"未找到对应的员工信息，UserID: {user_id}")
            return
        
        # 获取外部联系人信息
        contact_info = await welcome_helper.get_external_contact(external_user_id)
        contact_name = ""
        if contact_info.get('errcode') == 0:
            contact_name = contact_info.get('external_contact', {}).get('name', "")
        
        # 处理不同的State参数
        if state and "cid=" in state:
            # 从渠道码进来的，老师添加学生
            await handle_channel_contact(db, state, emp_model, external_user_id, 
                                        contact_name, welcome_code, user_id)
        elif state and "ChannelId=" in state:
            # 从渠道码进来的，普通渠道
            await handle_normal_channel_contact(db, state, emp_model, external_user_id, 
                                              contact_name, welcome_code, user_id)
        else:
            # 正常的添加客户好友
            await handle_normal_contact_add(db, emp_model, external_user_id, 
                                          contact_name, welcome_code, user_id)
    
    except Exception as e:
        logger.error("处理添加外部联系人事件异常: %s", str(e), exc_info=True)


async def handle_channel_contact(db: AsyncSession, state: str, emp_model: Dict, 
                                external_user_id: str, contact_name: str, welcome_code: str, user_id: str):
    """
    处理渠道码添加的联系人（老师添加学生）
    """
    try:
        # 解析State参数：cid=123|tid=456|sid=789
        state_parts = state.split('|')
        channel_id = int(state_parts[0].split("=")[1])
        teacher_id = int(state_parts[1].split("=")[1])  # 这里的teacher_id应该是ErpAccountTeacher表的id
        stu_id = int(state_parts[2].split("=")[1])
        
        if channel_id > 0:
            # 获取渠道配置
            channel_model = await get_wechat_channel_config(db, channel_id)
            if not channel_model:
                return
            
            # 发送欢迎语
            await send_channel_welcome_message(channel_model, emp_model, contact_name, 
                                             welcome_code, user_id)
            
            # 给客户打标签
            if channel_model.get('label_list'):
                await welcome_helper.add_external_contact_tag(user_id, external_user_id, 
                                                            channel_model['label_list'])
            
            # 添加客户记录
            await add_wechat_client_record(db, {
                'channel_id': channel_id,
                'emp_id': emp_model.id if hasattr(emp_model, 'id') else emp_model.get('id', 0),
                'external_user_id': external_user_id,
                'customer_name': contact_name,
                'work_emp_id': user_id,
                'type': 1,  # 添加类型
                'status': 0,
                'create_time': datetime.now(),
                'update_time': datetime.now()
            })
            
            # 修改客户备注
            stu_model = await get_student_info(db,  stu_id)
            if stu_model:
                remark = f"{stu_model['stu_name']}家长"
                await welcome_helper.update_external_contact_remark(user_id, external_user_id, remark)
            
            # 添加老师学生关联，这里的teacher_id是ErpAccountTeacher表的id
            await add_external_user_relation(db, {
                'teacher_id': teacher_id,  # 使用ErpAccountTeacher表的id
                'stu_id': stu_id,
                'external_user_id': external_user_id,   
                'customer_name': contact_name,
                'create_time': datetime.now()
            })
            
            # 处理人员添加限制
            await handle_employee_limit(db, channel_model, emp_model)
    
    except Exception as e:
        logger.error("处理渠道码联系人异常: %s", str(e), exc_info=True)


async def handle_normal_channel_contact(db: AsyncSession, state: str, emp_model: Dict,
                                      external_user_id: str, contact_name: str, welcome_code: str, user_id: str):
    """
    处理普通渠道码添加的联系人
    """
    try:
        # 解析State参数：ChannelId=123
        channel_id = int(state.split("=")[1])
        
        if channel_id > 0:
            # 获取渠道配置
            channel_model = await get_wechat_channel_config(db, channel_id)
            if not channel_model:
                return
            
            # 发送欢迎语
            await send_channel_welcome_message(channel_model, emp_model, contact_name, 
                                             welcome_code, user_id)
            
            # 给客户打标签
            if channel_model.get('label_list'):
                await welcome_helper.add_external_contact_tag(user_id, external_user_id, 
                                                            channel_model['label_list'])
            
            # 添加客户记录
            await add_wechat_client_record(db, {
                'channel_id': channel_id,
                'emp_id': emp_model.id if hasattr(emp_model, 'id') else emp_model.get('id', 0),
                'external_user_id': external_user_id,
                'customer_name': contact_name,
                'work_emp_id': user_id,
                'type': 1,  # 添加类型
                'status': 0,
                'create_time': datetime.now(),
                'update_time': datetime.now()
            })
            
            # 处理人员添加限制
            await handle_employee_limit(db, channel_model, emp_model)
    
    except Exception as e:
        logger.error("处理普通渠道码联系人异常: %s", str(e), exc_info=True)


async def handle_normal_contact_add(db: AsyncSession, emp_model: Dict,
                                  external_user_id: str, contact_name: str, welcome_code: str, user_id: str):
    """
    处理正常添加客户好友
    """
    try:
        # 添加基础客户记录 - 即使没有渠道ID也要记录
        await add_wechat_client_record(db, {
            'channel_id': 0,  # 正常添加没有渠道ID
            'emp_id': emp_model.id if hasattr(emp_model, 'id') else emp_model.get('id', 0),
            'external_user_id': external_user_id,
            'customer_name': contact_name,
            'work_emp_id': user_id,
            'type': 1,  # 添加类型
            'status': 0,
            'create_time': datetime.now(),
            'update_time': datetime.now()
        })
        
        # 检查是否启用欢迎语功能
        if not QY_WECHAT_CONFIG.get('ENABLE_WELCOME_MESSAGE', False):
            # logger.info(f"欢迎语功能未启用，跳过发送欢迎语: {contact_name}")
            return
        
        # 获取欢迎语列表
        welcome_list = await get_wechat_welcomes_list(db, emp_model.id)
        
        if welcome_list:
            # 优先使用配置人员的欢迎语
            user_specific_welcomes = [w for w in welcome_list if w.get('user_ids') != "-1"]
            if user_specific_welcomes:
                welcome_list = user_specific_welcomes
            
            # 使用第一个欢迎语
            welcome_model = welcome_list[0]
            
            # 替换欢迎语中的变量
            content = welcome_model.get('content', '')
            if "##客户名称##" in content:
                content = content.replace("##客户名称##", contact_name)
            if "##员工姓名##" in content:
                content = content.replace("##员工姓名##", emp_model.employee_name or '')
            if "##员工别名##" in content:
                # 获取员工别名
                user_info = await welcome_helper.get_user_info(user_id)
                alias = user_info.get('alias', '') if user_info.get('errcode') == 0 else ''
                content = content.replace("##员工别名##", alias)
            
            # 发送欢迎语，使用重试机制
            try:
                result = await welcome_helper.send_welcome_msg(welcome_code, content)
                if result.get('errcode') == 0:
                    logger.info(f"普通欢迎语发送成功: {contact_name}")
                else:
                    logger.warning(f"普通欢迎语发送失败，但不影响整体流程: {result.get('errmsg', '未知错误')}")
            except Exception as send_error:
                logger.warning(f"普通欢迎语发送异常，但不影响整体流程: {str(send_error)}")
    
    except Exception as e:
        logger.error("处理正常添加客户好友异常: %s", str(e), exc_info=True)


async def send_channel_welcome_message(channel_model: Dict, emp_model: Dict, contact_name: str,
                                     welcome_code: str, user_id: str):
    """
    发送渠道欢迎语
    """
    try:
        # 检查是否启用欢迎语功能
        if not QY_WECHAT_CONFIG.get('ENABLE_WELCOME_MESSAGE', False):
            logger.info(f"欢迎语功能未启用，跳过发送渠道欢迎语: {contact_name}")
            return
        
        # 根据渠道配置的时间段和特殊配置来选择合适的欢迎语
        # 这里简化处理，使用默认欢迎语
        welcome_content = ""
        
        # 获取当前时间
        now = datetime.now()
        
        # 检查特殊时间段配置
        if channel_model.get('welcome_special_enable') == 1:
            # 检查特殊时间段配置 - 这里可以根据实际需求实现特殊时间段的欢迎语逻辑
            pass
        
        # 检查周期配置
        if not welcome_content and channel_model.get('welcome_week_enable') == 1:
            # 检查周期配置 - 这里可以根据实际需求实现周期性欢迎语逻辑
            pass
        
        # 使用默认欢迎语
        if not welcome_content and channel_model.get('welcome_list'):
            welcome_item = channel_model['welcome_list'][0]
            welcome_content = welcome_item.get('content', '')
        
        if welcome_content:
            # 替换变量
            if "##客户名称##" in welcome_content:
                welcome_content = welcome_content.replace("##客户名称##", contact_name)
            if "##员工姓名##" in welcome_content:
                employee_name = emp_model.employee_name if hasattr(emp_model, 'employee_name') else emp_model.get('employee_name', '')
                welcome_content = welcome_content.replace("##员工姓名##", employee_name or '')
            if "##员工别名##" in welcome_content:
                # 获取员工别名
                user_info = await welcome_helper.get_user_info(user_id)
                alias = user_info.get('alias', '') if user_info.get('errcode') == 0 else ''
                welcome_content = welcome_content.replace("##员工别名##", alias)
            
            # 发送欢迎语，使用重试机制
            try:
                result = await welcome_helper.send_welcome_msg(welcome_code, welcome_content)
                if result.get('errcode') == 0:
                    logger.info(f"渠道欢迎语发送成功: {contact_name}")
                else:
                    logger.warning(f"渠道欢迎语发送失败，但不影响整体流程: {result.get('errmsg', '未知错误')}")
            except Exception as send_error:
                logger.warning(f"渠道欢迎语发送异常，但不影响整体流程: {str(send_error)}")
        else:
            logger.info(f"没有配置欢迎语内容，跳过发送: {contact_name}")
    
    except Exception as e:
        logger.error("发送渠道欢迎语异常: %s", str(e), exc_info=True)


async def handle_del_external_contact(db: AsyncSession, msg_data: Dict):
    """
    处理删除外部联系人事件
    """
    try:
        user_id = msg_data['UserID']
        external_user_id = msg_data['ExternalUserID']
        change_type = msg_data['ChangeType']
        
        # 获取渠道ID
        channel_id = 0
        state = msg_data.get('State', '')
        if state and "ChannelId=" in state:
            channel_id = int(state.split("=")[1])
        else:
            # 根据员工和客人的企业微信ID获取记录
            record = await get_wechat_client_record(db, user_id, external_user_id)
            if record:
                channel_id = record.get('channel_id', 0)
        
        # 获取员工信息
        emp_model = await get_user_info_by_wechat_userid(db, user_id)
        
        # 获取客户信息
        contact_info = await welcome_helper.get_external_contact(external_user_id)
        contact_name = ""
        if contact_info.get('errcode') == 0:
            contact_name = contact_info.get('external_contact', {}).get('name', "")
        
        # 添加删除记录
        await add_wechat_client_record(db, {
            'channel_id': channel_id,
            'emp_id': (emp_model.id if hasattr(emp_model, 'id') else emp_model.get('id', 0)) if emp_model else 0,
            'external_user_id': external_user_id,
            'customer_name': contact_name,
            'work_emp_id': user_id,
            'type': 3 if change_type == "del_external_contact" else 2,  # 3=成员删除客户，2=被客户删除
            'status': 0,
            'create_time': datetime.now(),
            'update_time': datetime.now()
        })
    
    except Exception as e:
        logger.error("处理删除外部联系人事件异常: %s", str(e), exc_info=True)


async def handle_group_member_change(db: AsyncSession, msg_data: Dict):
    """
    处理群成员变化事件
    """
    try:
        chat_id = msg_data['ChatId']
        update_detail = msg_data.get('UpdateDetail', '')
        
        logger.info(f"群成员变化事件开始处理: chat_id={chat_id}, update_detail={update_detail}")
        
        # 获取群聊详情，获取当前群成员列表
        chat_detail = await welcome_helper.get_groupchat_detail(chat_id)
        if not chat_detail or chat_detail.get('errcode') != 0:
            logger.error(f"获取群聊详情失败: {str(chat_detail)}")
            return
        
        group_chat = chat_detail.get('group_chat', {})
        member_list = group_chat.get('member_list', [])
        
        # 提取外部联系人
        current_external_members = [
            member.get('userid') for member in member_list 
            if member.get('type') == 2 and member.get('userid')  # 外部联系人且userid不为空
        ]
        
        # 获取数据库中现有的群成员记录
        existing_records = await erp_wechat_classuser.get_many(db, raw=[
            ErpWechatClassuser.wechat_id == chat_id,
            ErpWechatClassuser.disable == 0
        ])
        
        existing_external_users = {record.external_user_id for record in existing_records if record.external_user_id}
        
        logger.info(f"群聊当前外部成员数: {len(current_external_members)}, 数据库记录数: {len(existing_external_users)}")
        
        # 批量处理，避免重复操作
        processed_users = set()
        
        if update_detail == 'add_member':
            # 成员入群，找出新增的成员
            new_members = set(current_external_members) - existing_external_users
            logger.info(f"新增成员数: {len(new_members)}")
            
            for external_user_id in new_members:
                if external_user_id and external_user_id not in processed_users:
                    processed_users.add(external_user_id)
                    await update_class_chat_external_user(db, chat_id, external_user_id, 0)
                    logger.info(f"新成员入群处理完成: chat_id={chat_id}, external_user_id={external_user_id}")
                    
        elif update_detail == 'del_member':
            # 成员退群，找出退出的成员
            left_members = existing_external_users - set(current_external_members)
            logger.info(f"退出成员数: {len(left_members)}")
            
            for external_user_id in left_members:
                if external_user_id and external_user_id not in processed_users:
                    processed_users.add(external_user_id)
                    await update_class_chat_external_user(db, chat_id, external_user_id, 1)
                    # logger.info(f"成员退群处理完成: chat_id={chat_id}, external_user_id={external_user_id}")
        
        # 同时更新班级ID关联
        class_obj = await erp_class.get_one(db, qwechat_id=chat_id)
        if class_obj:
            # 批量更新所有记录的class_id
            class_id_update_count = 0
            for record in existing_records:
                if record.class_id != class_obj.id:
                    record.class_id = class_obj.id
                    record.update_time = datetime.now()
                    class_id_update_count += 1
            
            if class_id_update_count > 0:
                logger.info(f"更新班级ID关联: {class_id_update_count} 条记录")
            
            # 为新增的成员记录设置class_id
            if update_detail == 'add_member':
                new_records = await erp_wechat_classuser.get_many(db, raw=[
                    ErpWechatClassuser.wechat_id == chat_id,
                    ErpWechatClassuser.class_id == 0,
                    ErpWechatClassuser.disable == 0
                ])
                new_class_id_count = 0
                for record in new_records:
                    record.class_id = class_obj.id
                    record.update_time = datetime.now()
                    new_class_id_count += 1
                
                if new_class_id_count > 0:
                    logger.info(f"为新增成员设置班级ID: {new_class_id_count} 条记录")
        
        # 不在这里提交事务，在主函数统一提交
        logger.info(f"群成员变化事件处理完成: chat_id={chat_id}, update_detail={update_detail}, 处理用户数: {len(processed_users)}")
                    
    except Exception as e:
        logger.error("处理群成员变化事件异常: %s", str(e), exc_info=True)
        # 不在这里回滚，让主函数统一处理


async def handle_group_name_change(db: AsyncSession, msg_data: Dict):
    """
    处理群名称变化事件
    """
    try:
        chat_id = msg_data['ChatId']
        # 群名称变化事件，暂时只记录日志
        # 如果需要更新群名称，可以调用企业微信API获取最新群信息
        logger.info(f"群名称变化事件: chat_id={chat_id}")
    except Exception as e:
        logger.error("处理群名称变化事件异常: %s", str(e), exc_info=True)


async def handle_group_create(db: AsyncSession, msg_data: Dict):
    """
    处理群创建事件
    """
    try:
        chat_id = msg_data['ChatId']
        # 群创建事件，可以在这里初始化群相关信息
        logger.info(f"群创建事件: chat_id={chat_id}")
    except Exception as e:
        logger.error("处理群创建事件异常: %s", str(e), exc_info=True)


async def handle_employee_limit(db: AsyncSession, channel_model: Dict, emp_model: Dict):
    """
    处理员工添加限制
    """
    try:
        if channel_model.get('emp_add_limit') == 1:
            # 检查员工添加客户数量限制
            # 这里可以实现具体的限制逻辑，比如：
            # 1. 查询当前员工已添加的客户数量
            # 2. 与配置的限制数量进行比较
            # 3. 如果超过限制，可以进行相应处理（如通知、禁止等）
            employee_name = emp_model.employee_name if hasattr(emp_model, 'employee_name') else emp_model.get('employee_name', '未知员工')
            logger.info(f"员工 {employee_name} 触发添加限制检查")
    except Exception as e:
        logger.error("处理员工添加限制异常: %s", str(e), exc_info=True)


# TODO: 以下函数需要根据实际的数据库结构和业务逻辑来实现

async def get_wechat_config() -> Optional[Dict]:
    """获取企业微信配置"""
    return {
        'enable': True,
        'token': QY_WECHAT_CONFIG.get('MSG_TOKEN', ''),
        'encoding_aes_key': QY_WECHAT_CONFIG.get('MSG_ENCODING_AES_KEY', ''),
        'corp_id': QY_WECHAT_CONFIG.get('CORPID', '')
    }


async def get_user_info_by_wechat_userid(db: AsyncSession, user_id: str) -> Optional[Dict]:
    """根据企业微信UserID获取用户信息"""
    try:
        user = await erp_account.get_one(db, qy_wechat_userid=user_id)
        if user:
            return {
                'id': user.id,
                'employee_name': user.employee_name,
                'username': user.username,
                'qy_wechat_userid': user.qy_wechat_userid
            }
        return None
    except Exception as e:
        logger.error("根据企业微信UserID获取用户信息异常: %s", str(e), exc_info=True)
        return None


async def get_wechat_channel_config(db: AsyncSession, channel_id: int) -> Optional[Dict]:
    """获取企业微信渠道配置"""
    try:
        channel = await erp_wechat_channel.get_by_id(db, channel_id)
        if channel:
            return {
                'id': channel.id,
                'name': channel.name,
                'wx_config_id': channel.wx_config_id,
                'wx_qrcode': channel.wx_qrcode,
                'lable_ids': channel.lable_ids,
                'label_list': channel.lable_ids.split(',') if channel.lable_ids else [],
                'welcome_enable': channel.welcome_enable,
                'welcome_week_enable': channel.welcome_week_enable,
                'welcome_special_enable': channel.welcome_special_enable,
                'emp_add_limit': channel.emp_add_limit,
                'welcome_list': [{'content': f'欢迎使用{channel.name}'}],  # 简化处理
                'teacher_id': channel.teacher_id,  # 这里的teacher_id是ErpAccountTeacher表的id
                'stu_id': channel.stu_id,
                'class_id': channel.class_id
            }
        return None
    except Exception as e:
        logger.error("获取企业微信渠道配置异常: %s", str(e), exc_info=True)
        return None


async def get_student_info(db: AsyncSession, stu_id: int) -> Optional[Dict]:
    """获取学生信息"""
    try:
        student = await erp_student.get_by_id(db, stu_id)
        if student:
            return {
                'id': student.id,
                'name': student.stu_name,
                'stu_name': student.stu_name,
                'stu_username': student.stu_username
            }
        return None
    except Exception as e:
        logger.error("获取学生信息异常: %s", str(e), exc_info=True)
        return None


async def get_wechat_welcomes_list(db: AsyncSession, user_id: int) -> List[Dict]:
    """获取企业微信欢迎语列表"""
    try:
        # 由于没有专门的欢迎语表，这里返回默认欢迎语
        # 实际项目中可能需要创建专门的欢迎语表
        return [
            {
                'id': 1,
                'content': '您好,##客户名称##，我是##员工姓名##，很高兴为您服务！',
                'user_ids': str(user_id)
            }
        ]
    except Exception as e:
        logger.error("获取企业微信欢迎语列表异常: %s", str(e), exc_info=True)
        return []


async def add_wechat_client_record(db: AsyncSession, record_data: Dict):
    """添加企业微信客户记录"""
    try:
        # 检查是否已存在相同的记录，避免重复插入
        # 使用 external_user_id + work_emp_id + type 作为唯一性判断
        external_user_id = record_data.get('external_user_id')
        work_emp_id = record_data.get('work_emp_id')
        record_type = record_data.get('type')
        
        # 生成唯一标识符用于防重复
        unique_key = f"{external_user_id}_{work_emp_id}_{record_type}"
        
        # 使用内存缓存防止短时间内的重复插入
        if not hasattr(add_wechat_client_record, '_processing_records'):
            add_wechat_client_record._processing_records = {}
        
        current_time = time.time()
        # 清理超过30秒的旧记录
        add_wechat_client_record._processing_records = {
            k: v for k, v in add_wechat_client_record._processing_records.items()
            if current_time - v < 30
        }
        
        # 检查是否正在处理相同的记录
        if unique_key in add_wechat_client_record._processing_records:
            logger.info(f"检测到重复处理请求，跳过插入: {unique_key}")
            # 查询已存在的记录并返回
            existing_record = await erp_wechat_channelrecord.get_one(db,
                external_user_id=external_user_id,
                work_emp_id=work_emp_id,
                type=record_type,
                disable=0
            )
            return existing_record
        
        # 标记为正在处理
        add_wechat_client_record._processing_records[unique_key] = current_time
        
        try:
            # 更严格的唯一性检查：增加时间窗口检查（5分钟内的重复记录）
            from datetime import datetime, timedelta
            five_minutes_ago = datetime.now() - timedelta(minutes=5)
            
            # 查询是否已存在相同的记录（包括时间窗口检查）
            existing_record = await erp_wechat_channelrecord.get_one(db,
                external_user_id=external_user_id,
                work_emp_id=work_emp_id,
                type=record_type,
                disable=0
            )
            
            # 如果找到记录，检查是否是最近5分钟内的重复记录
            if existing_record:
                # 如果是最近5分钟内的记录，直接返回，不重复插入
                if existing_record.create_time and existing_record.create_time >= five_minutes_ago:
                    logger.info(f"检测到5分钟内的重复记录，跳过插入: external_user_id={external_user_id}, work_emp_id={work_emp_id}, type={record_type}")
                    return existing_record
                else:
                    # 如果是较早的记录，更新记录而不是创建新的
                    logger.info(f"客户记录已存在，更新记录: external_user_id={external_user_id}, work_emp_id={work_emp_id}, type={record_type}")
                    # 更新现有记录的时间和其他信息
                    existing_record.customer_name = record_data.get('customer_name', existing_record.customer_name)
                    existing_record.channel_id = record_data.get('channel_id', existing_record.channel_id)
                    existing_record.update_time = datetime.now()
                    await db.flush()
                    return existing_record
            
            # 如果不存在，创建新记录
            # 使用 try-except 处理可能的并发插入冲突
            try:
                new_record = await erp_wechat_channelrecord.create(db, commit=False, **record_data)
                logger.info(f"添加企业微信客户记录成功: {record_data}")
                return new_record
            except Exception as create_error:
                # 如果创建失败（可能是并发插入导致的重复），再次查询现有记录
                if "Duplicate entry" in str(create_error) or "duplicate key" in str(create_error).lower():
                    logger.warning(f"检测到并发插入冲突，查询现有记录: {str(create_error)}")
                    existing_record = await erp_wechat_channelrecord.get_one(db,
                        external_user_id=external_user_id,
                        work_emp_id=work_emp_id,
                        type=record_type,
                        disable=0
                    )
                    if existing_record:
                        return existing_record
                # 如果不是重复键错误，重新抛出异常
                raise create_error
        
        finally:
            # 清理处理标记
            if unique_key in add_wechat_client_record._processing_records:
                del add_wechat_client_record._processing_records[unique_key]
        
    except Exception as e:
        logger.error("添加企业微信客户记录异常: %s", str(e), exc_info=True)
        return None


async def add_external_user_relation(db: AsyncSession, relation_data: Dict):
    """添加外部用户关联"""
    try:
        # 检查是否已存在相同的记录，避免重复插入
        external_user_id = relation_data.get('external_user_id')
        teacher_id = relation_data.get('teacher_id')
        
        # 使用upsert方法，存在则更新，不存在则创建
        condition = {
            'external_user_id': external_user_id,
            'teacher_id': teacher_id,
            'disable': 0
        }
        
        relation_obj = await erp_wechat_externaluser.upsert(db, condition, relation_data, commit=False)
        logger.info(f"添加/更新外部用户关联成功: teacher_id={teacher_id}, external_user_id={external_user_id}")
        
        # 同时在ErpQwechatContact表中添加记录
        if teacher_id:
            # 获取teacher的account_id
            teacher_obj = await erp_account_teacher.get_by_id(db, teacher_id)
            if teacher_obj:
                contact_condition = {
                    'teacher_account_id': teacher_obj.account_id,
                    'external_userid': external_user_id,
                    'disable': 0
                }
                
                contact_data = {
                    'teacher_account_id': teacher_obj.account_id,
                    'teacher_id': teacher_id,  # 使用ErpAccountTeacher表的id
                    'external_userid': external_user_id,
                    'external_name': relation_data.get('customer_name'),
                    'stu_id': relation_data.get('stu_id', 0),
                    'create_time': datetime.now(),
                    'update_time': datetime.now()
                }
                
                contact_obj = await erp_qwechat_contact.upsert(db, contact_condition, contact_data, commit=False)
                logger.info(f"添加/更新企微教师客户记录成功: teacher_account_id={teacher_obj.account_id}, external_userid={external_user_id}")
        
    except Exception as e:
        logger.error("添加外部用户关联异常: %s", str(e), exc_info=True)


async def get_wechat_client_record(db: AsyncSession, user_id: str, external_user_id: str) -> Optional[Dict]:
    """获取企业微信客户记录"""
    try:
        # 查询客户记录
        record = await erp_wechat_channelrecord.get_one(db, 
            work_emp_id=user_id, 
            external_user_id=external_user_id
        )
        
        if record:
            return {
                'id': record.id,
                'channel_id': record.channel_id,
                'type': record.type,
                'emp_id': record.emp_id,
                'work_emp_id': record.work_emp_id,
                'external_user_id': record.external_user_id,
                'customer_name': record.customer_name
            }
        return None
        
    except Exception as e:
        logger.error("获取企业微信客户记录异常: %s", str(e), exc_info=True)
        return None


async def update_class_chat_external_user(db: AsyncSession, chat_id: str, external_user_id: str, state: int = 0):
    """更新班级群外部用户状态"""

    # 查询现有记录（包含disable=0的条件）
    existing_record = await erp_wechat_classuser.get_one(db, 
        wechat_id=chat_id, 
        external_user_id=external_user_id,
        disable=0
    )
    
    if existing_record:
        # 更新状态
        existing_record.state = state
        existing_record.update_time = datetime.now()
        await db.flush()
        # logger.info(f"更新班级群外部用户状态: chat_id={chat_id}, external_user_id={external_user_id}, state={state}")
    else:
        # 如果是新增成员，创建记录
        if state == 0:  # 在群状态
            # 使用**解包参数创建记录
            await erp_wechat_classuser.create(db, commit=False, **{
                'wechat_id': chat_id,
                'external_user_id': external_user_id,
                'state': state,
                'create_time': datetime.now(),
                'update_time': datetime.now()
            })
            logger.info(f"创建班级群外部用户记录: chat_id={chat_id}, external_user_id={external_user_id}")
                


async def add_qwechat_contact_record(db: AsyncSession, contact_data: Dict):
    """添加企微教师客户记录"""
    try:
        # 检查是否已存在相同的记录，避免重复插入
        teacher_account_id = contact_data.get('teacher_account_id')
        external_userid = contact_data.get('external_userid')
        
        # 使用upsert方法，存在则更新，不存在则创建
        condition = {
            'teacher_account_id': teacher_account_id,
            'external_userid': external_userid,
            'disable': 0
        }
        
        contact_obj = await erp_qwechat_contact.upsert(db, condition, contact_data, commit=False)
        logger.info(f"添加/更新企微教师客户记录成功: teacher_account_id={teacher_account_id}, external_userid={external_userid}")
        
    except Exception as e:
        logger.error("添加企微教师客户记录异常: %s", str(e), exc_info=True)
