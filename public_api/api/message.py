from fastapi import APIRouter

from public_api.serializer import MessageCreate
from utils.message.QyWechatMessage import send_message_to_users


router = APIRouter(prefix="/message", tags=["消息"])


@router.post("/send_message")
async def send_message(message: MessageCreate):
    result = await send_message_to_users(
        message.message_to,
        message.message_content
    )

    # 向多个员工发送消息 test
    # result = send_message_to_users(["13523_202304240950232615@6855", "14295_202306121344156656@1423", "14234_202306081608327905@1103"], "这是一条群发消息")

    return result


