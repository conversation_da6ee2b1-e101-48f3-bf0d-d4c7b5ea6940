from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text, or_, and_
from typing import List, Dict, Any, Optional, Union
from collections import defaultdict

from models.m_order import ErpOrder, ErpOrderRefundDetail, ErpOrderStudent
from models.m_student import ErpStudent
from models.m_class import ErpClass, ErpCourse, ErpCourseTextbook
from models.m_teacher import ErpAccountTeacher
from models.models import ErpAccountDepartment, ErpDepartment, ErpAccount
from models.m_workflow import (
    ErpCostTypeBind, ErpPaymentObjType, ErpWorkflowDef, ErpWorkflowNode, ErpWorkflowInstance, ErpReceipt, 
    ErpReceiptFinance, ErpReceiptDetail, ErpWorkflowRecord, ErpPaymentObj, 
    ErpWorkflowCostType, ErpWorkflowInstanceReviewer
)
from models.m_finance import ErpBankAccount, ErpBankAccountType, ErpFinanceCostType
from settings import CF
from utils.db.model_handler import ModelDataHelper
from utils.enum.enum_approval import ApproverType, NodeType, InstanceStatus, AuditState, RelatedObjType

# 初始化CRUD操作对象
erp_workflow_def = CF.get_crud(ErpWorkflowDef)
erp_workflow_node = CF.get_crud(ErpWorkflowNode)
erp_workflow_instance = CF.get_crud(ErpWorkflowInstance)
erp_receipt = CF.get_crud(ErpReceipt)
erp_payment_obj = CF.get_crud(ErpPaymentObj)
erp_receipt_finance = CF.get_crud(ErpReceiptFinance)
erp_receipt_detail = CF.get_crud(ErpReceiptDetail)
erp_workflow_cost_type = CF.get_crud(ErpWorkflowCostType)
erp_cost_type_bind = CF.get_crud(ErpCostTypeBind)
erp_account = CF.get_crud(ErpAccount)
erp_finance_cost_type = CF.get_crud(ErpFinanceCostType)
erp_workflow_instance_reviewer = CF.get_crud(ErpWorkflowInstanceReviewer)
erp_workflow_record = CF.get_crud(ErpWorkflowRecord)
erp_student = CF.get_crud(ErpStudent)
erp_bank_account = CF.get_crud(ErpBankAccount)
erp_order_refund_detail = CF.get_crud(ErpOrderRefundDetail)

# 根据用户id获取用户部门信息
async def get_user_dept(db: AsyncSession, user_id: int):
    selects = [
        ErpDepartment.id,
        ErpDepartment.dept_name
    ]
    stmt = (select(*selects)
            .select_from(ErpDepartment)
            .join(ErpAccountDepartment, ErpDepartment.id == ErpAccountDepartment.dept_id)
            .where(ErpAccountDepartment.account_id == user_id))
    result = await db.execute(stmt)
    return result.fetchone()

# 工作流节点记录相关查询
async def get_workflow_nodes_for_def(db: AsyncSession, workflow_id: int) -> List[ErpWorkflowNode]:
    """获取指定工作流定义的所有节点信息，按排序号排序"""
    return await erp_workflow_node.get_many(db, {"workflow_id": workflow_id}, orderby=["sort_no"])

async def get_workflow_cost_types(db: AsyncSession, workflow_id: int) -> List[ErpCostTypeBind]:
    """获取工作流关联的费用类型"""
    return await erp_cost_type_bind.get_many(db, {"workflow_id": workflow_id})

async def get_workflow_def_with_details(db: AsyncSession, workflow_id: int) -> Dict:
    """获取工作流定义详情，包括节点和费用类型"""
    workflow = await erp_workflow_def.get_by_id(db, workflow_id)
    if not workflow:
        return None
    
    # 获取节点信息
    nodes = await get_workflow_nodes_for_def(db, workflow_id)
    workflow.nodes = [ModelDataHelper.model_to_dict(node) for node in nodes]
    
    # 获取费用类型
    cost_types = await get_workflow_cost_types(db, workflow_id)
    if cost_types:
        cost_type_ids = [i.default_cost_type_id for i in cost_types]
        cost_type_objs = await erp_finance_cost_type.get_many(db, raw=[ErpFinanceCostType.id.in_(cost_type_ids)])
        workflow.cost_types = [{"id": cost_type.id, "name": cost_type.name} for cost_type in cost_type_objs]
    
    return workflow

async def get_workflow_def_list(db: AsyncSession, page: int, page_size: int, workflow_type: Optional[int] = None) -> tuple:
    """获取工作流定义列表，带分页和类型过滤"""
    raw_query_params = []
    if workflow_type:
        raw_query_params.append(ErpWorkflowDef.workflow_type == workflow_type)
    
    # 查询工作流定义
    workflows = await erp_workflow_def.get_many_with_pagination(db, page=page, page_size=page_size, raw=raw_query_params)
    
    # 高效统计总数，使用COUNT函数而不是获取所有记录
    count_stmt = select(func.count()).select_from(ErpWorkflowDef).where(*raw_query_params)
    count_result = await db.execute(count_stmt)
    total_count = count_result.scalar()
    
    if workflows:
        # 收集所有工作流ID
        workflow_ids = [workflow.id for workflow in workflows]
        
        # 一次性查询所有相关节点
        all_nodes = await erp_workflow_node.get_many(db, raw=[ErpWorkflowNode.workflow_id.in_(workflow_ids)])
        # 按工作流ID组织节点数据
        nodes_map = defaultdict(list)
        for node in all_nodes:
            nodes_map[node.workflow_id].append(ModelDataHelper.model_to_dict(node))
        
        # 一次性查询所有相关费用类型
        all_cost_types = await erp_workflow_cost_type.get_many(db, raw=[ErpWorkflowCostType.workflow_id.in_(workflow_ids)])
        # 按工作流ID组织费用类型数据
        cost_types_map = defaultdict(list)
        for cost_type in all_cost_types:
            cost_types_map[cost_type.workflow_id].append({
                "id": cost_type.id, 
                "cost_type_id": cost_type.cost_type_id, 
                "cost_type_name": cost_type.cost_type_name
            })
        
        # 为每个工作流添加节点和费用类型信息
        for workflow in workflows:
            workflow.nodes = nodes_map.get(workflow.id, [])
            workflow.cost_types = cost_types_map.get(workflow.id, [])
    
    return workflows, total_count


# 获取退款明细
async def get_refund_detail(db: AsyncSession, refund_id: int) -> List[ErpOrderRefundDetail]:
    """获取退款明细，包含班级信息"""
    selects = [
        ErpOrderRefundDetail.id,
        ErpOrderRefundDetail.refund_id,
        ErpOrderRefundDetail.order_no,
        ErpOrderRefundDetail.refund_order_no,
        ErpOrderRefundDetail.order_student_id,
        ErpOrderRefundDetail.order_id,
        ErpOrderRefundDetail.unit_price,
        ErpOrderRefundDetail.refund_num,
        ErpOrderRefundDetail.total_money,
        ErpOrderRefundDetail.unit,
        ErpOrderRefundDetail.apply_money,
        ErpOrderRefundDetail.refund_money,
        ErpOrderRefundDetail.pay_time,
        ErpOrderRefundDetail.refund_type,
        ErpOrderRefundDetail.refund_state,
        ErpOrderRefundDetail.refund_ewallet,
        ErpOrderRefundDetail.refund_way,
        ErpOrderRefundDetail.cost_type_id,
        ErpOrderRefundDetail.cost_type_name,
        ErpOrderRefundDetail.create_time,
        ErpOrderRefundDetail.create_by,
        ErpOrderRefundDetail.update_time,
        ErpOrderRefundDetail.update_by,
        ErpOrderRefundDetail.act_money,

        ErpOrder.buy_num,
        ErpOrder.create_time.label("order_create_time"),
        ErpOrder.order_class_type,

        ErpStudent.stu_name,
        
        # 班级相关信息
        ErpClass.id.label('class_id'),
        ErpClass.class_name,
        ErpClass.class_status,
        ErpClass.audit_status,
        ErpCourse.id.label('course_id'),
        ErpCourse.course_name,
        ErpCourse.original_price,
        ErpCourse.sale_price,
        ErpAccountTeacher.id.label('teacher_id'),
        ErpAccount.employee_name.label('teacher_name'),
        
        # 教材信息
        ErpCourseTextbook.id.label('textbook_id'),
        ErpCourseTextbook.name.label('textbook_name'),
        ErpCourseTextbook.origin_price.label('textbook_origin_price'),
        ErpCourseTextbook.sale_price.label('textbook_sale_price')
    ]
    conditions = [
        ErpOrderRefundDetail.refund_id == refund_id,
        ErpOrderRefundDetail.disable == 0
    ]
    stmt = (select(*selects)
            .select_from(ErpOrderRefundDetail)
            .outerjoin(ErpOrder, ErpOrderRefundDetail.order_id == ErpOrder.id)
            .outerjoin(ErpOrderStudent, ErpOrderRefundDetail.order_student_id == ErpOrderStudent.id)
            .outerjoin(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
            .outerjoin(ErpClass, and_(ErpOrderStudent.class_id == ErpClass.id, ErpOrderStudent.order_class_type == 1))
            .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
            .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
            .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
            .outerjoin(ErpCourseTextbook, and_(ErpOrderStudent.class_id == ErpCourseTextbook.id, ErpOrderStudent.order_class_type == 3))
            .where(*conditions))
    result = await db.execute(stmt)
    return result.fetchall()

async def get_workflow_node_record(db: AsyncSession, business_id: int, status: Optional[int] = None) -> List:
    """查询工作流节点记录"""
    selects = [
        ErpWorkflowNode.node_name,
        ErpWorkflowNode.node_type,
        ErpWorkflowNode.approver_type,
        ErpWorkflowNode.approver_ids,
        ErpWorkflowNode.approver_names,
        ErpWorkflowNode.sort_no,
        ErpWorkflowNode.is_countersign,
        ErpWorkflowNode.node_action,
        ErpWorkflowNode.is_finance_related,
        ErpWorkflowInstance.business_id,
        ErpWorkflowInstance.create_by,
        ErpWorkflowInstance.update_by,
        ErpWorkflowInstance.status,
        ErpWorkflowInstance.workflow_id,
        ErpWorkflowInstance.current_node_id,
        ErpWorkflowInstance.id
    ]
    conditions = [
        ErpWorkflowInstance.business_id == business_id,
    ]
    if status is not None:
        conditions.append(ErpWorkflowInstance.status == status)
    stmt = (
        select(*selects)
        .select_from(ErpWorkflowInstance)
        .outerjoin(ErpWorkflowNode, ErpWorkflowInstance.current_node_id == ErpWorkflowNode.id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()

async def get_receipt_with_workflow(db: AsyncSession, receipt_id: int) -> Dict:
    """获取单据及其关联的工作流详细信息"""
    # 获取单据基本信息
    receipt = await erp_receipt.get_by_id(db, receipt_id)
    if not receipt:
        return None
    
    # 基础数据结构
    receipt_data = ModelDataHelper.model_to_dict(receipt)
    
    # 1. 获取工作流实例和定义信息
    instance_query = await erp_workflow_instance.get_many(db, {"business_id": receipt_id, "disable": 0})
    if instance_query:
        instance = instance_query[0]  # 通常一个单据只对应一个工作流实例
        receipt_data["workflow_instance"] = ModelDataHelper.model_to_dict(instance)
        
        # 获取工作流定义
        if instance.workflow_id:
            workflow_def = await erp_workflow_def.get_by_id(db, instance.workflow_id)
            if workflow_def:
                receipt_data["workflow_def"] = ModelDataHelper.model_to_dict(workflow_def)
                
                # 获取工作流的费用类型
                cost_types = await erp_cost_type_bind.get_many(db, {"workflow_id": workflow_def.id})
                receipt_data["workflow_cost_types"] = [ModelDataHelper.model_to_dict(ct) for ct in cost_types]
        
        # 获取工作流节点信息
        if instance.workflow_id:
            nodes = await erp_workflow_node.get_many(db, {"workflow_id": instance.workflow_id}, orderby=["sort_no"])
            nodes_dict = {node.id: ModelDataHelper.model_to_dict(node) for node in nodes}
            
            # 获取当前节点详细信息
            current_node_id = instance.current_node_id
            if current_node_id and current_node_id in nodes_dict:
                receipt_data["current_node_info"] = nodes_dict[current_node_id]
            
            # 2. 获取审批记录
            approval_records = await db.execute(
                select(ErpWorkflowRecord)
                .where(ErpWorkflowRecord.instance_id == instance.id, ErpWorkflowRecord.disable == 0)
                .order_by(ErpWorkflowRecord.sort_no)
            )
            approval_records = approval_records.scalars().all()
            approval_records_list = [ModelDataHelper.model_to_dict(record) for record in approval_records]
            receipt_data["approval_records"] = approval_records_list
            
            # 创建节点ID到审批记录的映射
            approved_node_map = {}
            for record in approval_records:
                approved_node_map[record.node_id] = {
                    "status": record.approve_status,
                    "time": record.approve_time,
                    "opinion": record.approve_opinion,
                    "approver_name": record.approver_name,
                    "approver_id": record.approver_id
                }
            
            # 将节点分为已审批和未审批两组
            approved_nodes = []
            pending_nodes = []
            future_nodes = []
            
            # 已经过的节点排序号
            current_sort_no = float('inf')
            if current_node_id and current_node_id in nodes_dict:
                current_sort_no = nodes_dict[current_node_id].get("sort_no", 0)
            
            for node in nodes:
                node_data = ModelDataHelper.model_to_dict(node)
                
                # 添加审批状态信息
                if node.id in approved_node_map:
                    node_data["approval_info"] = approved_node_map[node.id]
                    approve_status = approved_node_map[node.id].get("status")
                    
                    # 只有审批状态为1(同意)或2(驳回)时才标记为已审批并添加到approved_nodes
                    if approve_status in [1, 2]:
                        if node.node_type == 2:
                            node_data["approval_status"] = "已抄送"
                        else:
                            node_data["approval_status"] = "已审批"
                        approved_nodes.append(node_data)
                    else:
                        # 有记录但状态不是已审批的，标记为未审批并加入pending_nodes
                        node_data["approval_status"] = "未审批"
                        pending_nodes.append(node_data)
                elif node.id == current_node_id:
                    node_data["approval_status"] = "当前节点"
                    pending_nodes.append(node_data)
                elif node.sort_no < current_sort_no:
                    # 排序号小于当前节点但未审批的节点（异常情况）
                    node_data["approval_status"] = "未审批"
                    pending_nodes.append(node_data)
                else:
                    # 排序号大于当前节点的节点
                    node_data["approval_status"] = "未到达"
                    future_nodes.append(node_data)
            
            # 将划分的节点添加到返回数据中
            receipt_data["approved_nodes"] = approved_nodes
            receipt_data["pending_nodes"] = pending_nodes
            receipt_data["future_nodes"] = future_nodes
            
            # 同时保留原始节点列表，按顺序排列，方便前端展示完整流程
            # 优化：收集所有需要查询的用户ID，一次性查询
            all_user_ids = set()
            node_approver_ids_map = {}
            
            # 第一轮循环：收集所有需要查询的用户ID
            for node in nodes:
                # 收集指定成员类型节点的审批人IDs
                if node.approver_type == 2 and node.approver_ids:  # 指定成员
                    try:
                        approver_ids = [int(id_str) for id_str in node.approver_ids.split(',') if id_str.strip().isdigit()]
                        if approver_ids:
                            node_approver_ids_map[node.id] = approver_ids
                            all_user_ids.update(approver_ids)
                    except Exception:
                        pass
                
                # 收集已有审批记录的审批人ID
                if node.id in approved_node_map and approved_node_map[node.id].get("approver_id"):
                    approver_id = approved_node_map[node.id].get("approver_id")
                    if approver_id:
                        all_user_ids.add(approver_id)
            
            # 一次性查询所有用户信息
            user_map = {}
            if all_user_ids:
                users = await erp_account.get_many(db, raw=[
                    ErpAccount.id.in_(all_user_ids),
                    ErpAccount.disable == 0
                ])
                user_map = {user.id: user for user in users}
            
            # 第二轮循环：处理每个节点，添加头像信息
            workflow_nodes = []
            for node in nodes:
                node_data = ModelDataHelper.model_to_dict(node)
                
                # 添加指定成员类型节点的审批人头像
                approver_avatars = []
                if node.id in node_approver_ids_map:
                    for approver_id in node_approver_ids_map[node.id]:
                        if approver_id in user_map:
                            approver_avatars.append({
                                "id": approver_id,
                                "avatar": user_map[approver_id].avatar
                            })
                
                node_data["approver_avatars"] = approver_avatars
                
                # 添加已有审批记录节点的审批人头像
                if node.id in approved_node_map and approved_node_map[node.id].get("approver_id"):
                    approver_id = approved_node_map[node.id].get("approver_id")
                    if approver_id in user_map:
                        node_data["approver_avatar"] = user_map[approver_id].avatar
                
                workflow_nodes.append(node_data)
            
            receipt_data["workflow_nodes"] = workflow_nodes
        
            # 3. 获取当前审批人信息
            current_reviewers = await db.execute(
                select(ErpWorkflowInstanceReviewer)
                .where(
                    ErpWorkflowInstanceReviewer.instance_id == instance.id,
                    ErpWorkflowInstanceReviewer.status == 0,  # 待审批状态
                    ErpWorkflowInstanceReviewer.disable == 0
                )
            )
            current_reviewers = current_reviewers.scalars().all()
            reviewers_data = [ModelDataHelper.model_to_dict(reviewer) for reviewer in current_reviewers]
            
            # 优化：收集所有审批人ID，一次性查询
            reviewer_ids = [reviewer.get("reviewer_id") for reviewer in reviewers_data if reviewer.get("reviewer_id")]
            reviewer_user_map = {}
            if reviewer_ids:
                reviewer_users = await erp_account.get_many(db, raw=[
                    ErpAccount.id.in_(reviewer_ids),
                    ErpAccount.disable == 0
                ])
                reviewer_user_map = {user.id: user for user in reviewer_users}
            
            # 为每个待审批人添加详细信息
            for reviewer in reviewers_data:
                if reviewer.get("reviewer_id") and reviewer.get("reviewer_id") in reviewer_user_map:
                    user_info = reviewer_user_map[reviewer.get("reviewer_id")]
                    reviewer["user_info"] = {
                        "avatar": user_info.avatar,
                        "position": user_info.qy_wechat_position,
                    }
            
            receipt_data["current_reviewers"] = reviewers_data
    
    # 4. 获取财务信息
    finance = await erp_receipt_finance.get_one(db, receipt_id=receipt_id)
    if finance:
        bank_account = await erp_bank_account.get_by_id(db, finance.bank_account_id)
        if bank_account:
            finance.bank_account_info = ModelDataHelper.model_to_dict(bank_account)
        receipt_data["finance_info"] = ModelDataHelper.model_to_dict(finance)
    
    # 5. 获取明细列表
    details = await erp_receipt_detail.get_many(db, {"receipt_id": receipt_id}, orderby=["sort_no"])
    if details:
        receipt_data["detail_list"] = [ModelDataHelper.model_to_dict(detail) for detail in details]
    
    # 5.1 如果单据类型为退费单，则获取退费明细列表
    if receipt.refund_id:
        refund_details = await get_refund_detail(db, refund_id=receipt.refund_id)
        if refund_details:
            receipt_data["refund_details"] = refund_details

    
    # 6. 获取付款对象详情
    if receipt.related_obj_id:
        # print(f'receipt.related_obj_type: {receipt.related_obj_type}, receipt.related_obj_id: {receipt.related_obj_id}')
        item = {}
        if int(receipt.related_obj_type) == RelatedObjType.INTERNAL_STUDENT.value:
            student = await erp_student.get_by_id(db, receipt.related_obj_id)
            # print(f'student: {student}')
            if student:
                obj_item = ModelDataHelper.model_to_dict(student)
                item["obj_name"] = obj_item["stu_name"]
                item["obj_type"] = int(receipt.related_obj_type)
                # item['account_type'] = 1

        elif int(receipt.related_obj_type) == RelatedObjType.INTERNAL_EMPLOYEE.value:
            employee = await erp_account.get_by_id(db, receipt.related_obj_id)
            if employee:
                obj_item = ModelDataHelper.model_to_dict(employee)
                item["obj_name"] = obj_item["employee_name"]
                item["obj_type"] = int(receipt.related_obj_type)
                
        else:
            payment_obj = await erp_payment_obj.get_by_id(db, receipt.related_obj_id)
            if payment_obj:
                obj_item = ModelDataHelper.model_to_dict(payment_obj)
                item = obj_item
        # print(f'item: {item}')
        receipt_data["payment_obj"] = item

    # 7. 获取创建人信息
    if receipt.create_by:
        creator = await erp_account.get_by_id(db, receipt.create_by)
        if creator:
            receipt_data["creator_info"] = {
                "id": creator.id,
                "name": creator.employee_name,
                "avatar": creator.avatar,
                "position": creator.qy_wechat_position,
            }
    
    # 8. 解析附件信息
    if receipt.attachment:
        try:
            import json
            attachment_data = json.loads(receipt.attachment)
            receipt_data["attachment_info"] = attachment_data
        except:
            receipt_data["attachment_info"] = receipt.attachment
    
    # 9. 获取部门详情信息
    if receipt.dept_id:
        from models.models import ErpDepartment
        dept = await db.execute(
            select(ErpDepartment).where(ErpDepartment.id == receipt.dept_id)
        )
        dept = dept.scalar_one_or_none()
        if dept:
            receipt_data["dept_info"] = ModelDataHelper.model_to_dict(dept)
    
    # 10. 添加单据状态的中文名称，方便前端展示
    audit_state_dict = {
        0: "暂存",
        1: "待审核",
        2: "已通过",
        3: "已驳回",
        4: "已取消"
    }
    receipt_data["audit_state_name"] = audit_state_dict.get(receipt.audit_state, "未知状态")
    
    # 11. 获取费用类型
    if receipt.workflow_id:
        cost_type_bind = await erp_cost_type_bind.get_one(db,workflow_id=receipt.workflow_id)
        receipt_data["workflow_cost_type"] = cost_type_bind
    return receipt_data

async def get_my_submit_workflows(db: AsyncSession, user_id: int, page: int, page_size: int, 
                                status: Optional[int] = None, date_start: Optional[str] = None, 
                                date_end: Optional[str] = None) -> tuple:
    """获取用户发起的工作流列表"""
    # 构建查询条件
    raw_query_params = [
        ErpReceipt.create_by == user_id,  # 我发起的
        ErpReceipt.disable == 0,  # 未删除的
    ]
    
    # 添加可选筛选条件
    if status is not None:
        raw_query_params.append(ErpReceipt.audit_state == status)
    
    if date_start and date_end:
        raw_query_params.append(ErpReceipt.create_time.between(date_start, date_end))
    elif date_start:
        raw_query_params.append(ErpReceipt.create_time >= date_start)
    elif date_end:
        raw_query_params.append(ErpReceipt.create_time <= date_end)
    
    # 查询符合条件的单据总数
    count_stmt = select(func.count()).select_from(ErpReceipt).where(*raw_query_params)
    count_result = await db.execute(count_stmt)
    total_count = count_result.scalar() or 0
    
    # 查询单据列表
    receipts = await erp_receipt.get_many_with_pagination(
        db, 
        page=page, 
        page_size=page_size, 
        raw=raw_query_params,
        orderby=[text("create_time DESC")]  # 使用text函数包装SQL表达式
    )
    
    return receipts, total_count

async def format_workflow_list_data(db: AsyncSession, receipts: List, user_id: int) -> List[Dict]:
    """格式化工作流列表数据，包括工作流实例、定义和创建人信息"""
    result_data = []
    if not receipts:
        return result_data
        
    # 收集单据ID
    receipt_ids = [receipt.id for receipt in receipts]
    
    # 按照ID批量获取工作流实例信息
    instances = await erp_workflow_instance.get_many(
        db, 
        raw=[
            ErpWorkflowInstance.business_id.in_(receipt_ids),
            ErpWorkflowInstance.disable == 0
        ]
    )
    
    # 创建单据ID到工作流实例的映射
    receipt_to_instance = {instance.business_id: instance for instance in instances}
    
    # 收集涉及到的工作流定义ID
    workflow_ids = set()
    for receipt in receipts:
        if receipt.workflow_id:
            workflow_ids.add(receipt.workflow_id)
    
    # 批量获取工作流定义
    workflow_defs = await erp_workflow_def.get_many(
        db, 
        raw=[
            ErpWorkflowDef.id.in_(workflow_ids),
            ErpWorkflowDef.disable == 0
        ]
    )
    workflow_def_map = {wf.id: wf for wf in workflow_defs}
    
    # 获取当前用户信息（发起人信息或创建者信息）
    creator_ids = set([receipt.create_by for receipt in receipts if receipt.create_by])
    creators = await erp_account.get_many(db, raw=[ErpAccount.id.in_(creator_ids)])
    creator_map = {creator.id: creator for creator in creators}
    
    # 收集工作流实例的当前节点ID和实例ID
    instance_ids = []
    current_node_ids = []
    for instance in instances:
        if instance and instance.current_node_id:
            instance_ids.append(instance.id)
            current_node_ids.append(instance.current_node_id)
    
    # 批量获取单据对应的财务信息，获取ie_type
    finance_infos = await erp_receipt_finance.get_many(
        db,
        raw=[
            ErpReceiptFinance.receipt_id.in_(receipt_ids),
            ErpReceiptFinance.disable == 0
        ]
    )
    finance_map = {finance.receipt_id: finance for finance in finance_infos}
    
    # 批量获取当前节点的审批人信息
    reviewer_map = {}
    if instance_ids:
        reviewers = await erp_workflow_instance_reviewer.get_many(
            db, 
            raw=[
                ErpWorkflowInstanceReviewer.instance_id.in_(instance_ids),
                ErpWorkflowInstanceReviewer.status == 0,  # 待审批状态
                ErpWorkflowInstanceReviewer.disable == 0
            ]
        )
        
        # 获取所有审批人ID
        reviewer_ids = set()
        for reviewer in reviewers:
            if reviewer.reviewer_id:
                reviewer_ids.add(reviewer.reviewer_id)
        
        # 批量获取审批人账号信息（包含头像）
        reviewer_accounts = await erp_account.get_many(db, raw=[ErpAccount.id.in_(reviewer_ids)])
        reviewer_account_map = {account.id: account for account in reviewer_accounts}
        
        # 创建实例ID到审批人信息的映射
        for reviewer in reviewers:
            if reviewer.instance_id not in reviewer_map:
                reviewer_map[reviewer.instance_id] = []
            
            reviewer_info = {
                "id": reviewer.reviewer_id,
                "name": reviewer.reviewer_name,
                "avatar": None
            }
            
            # 添加审批人头像
            if reviewer.reviewer_id in reviewer_account_map:
                reviewer_info["avatar"] = reviewer_account_map[reviewer.reviewer_id].avatar
            
            reviewer_map[reviewer.instance_id].append(reviewer_info)
    
    # 整合数据
    for receipt in receipts:
        instance = receipt_to_instance.get(receipt.id)
        workflow_def = workflow_def_map.get(receipt.workflow_id) if receipt.workflow_id else None
        creator = creator_map.get(receipt.create_by)
        
        # 获取收支类型
        ie_type = None
        if receipt.id in finance_map:
            ie_type = finance_map[receipt.id].ie_type
        
        # 获取当前节点的审批人头像
        current_node_avatar = None
        if instance and instance.id in reviewer_map and reviewer_map[instance.id]:
            # 使用第一个审批人的头像作为当前节点头像
            current_node_avatar = reviewer_map[instance.id][0].get("avatar")
        
        # 获取费用类型
        cost_type_bind = await erp_cost_type_bind.get_one(db,workflow_id=receipt.workflow_id)
        
        
        # 构建返回数据
        result_item = {
            "receipt_id": receipt.id,                       # 单据ID
            "ie_type": ie_type,                            # 收支类型
            "relate_finance_id": receipt.relate_finance_id,  # 关联财务单号
            "apply_reason": receipt.apply_reason,           # 单据名称
            "workflow_desc": workflow_def.workflow_desc if workflow_def else None,  # 类别
            "workflow_type": workflow_def.workflow_type if workflow_def else None,      # 类型
            "current_node_name": instance.current_node_name if instance else None,  # 当前节点
            "current_node_avatar": current_node_avatar,     # 当前节点审批人头像
            "audit_state": receipt.audit_state,          # 状态
            "create_by": receipt.create_by,         # 创建人ID
            "creator_name": creator.employee_name if creator else None,        # 创建人姓名
            "creator_avatar": creator.avatar if creator else None,             # 创建人头像
            "create_time": receipt.create_time,     # 创建时间
            "workflow_id": receipt.workflow_id,     # 工作流定义ID
            "instance_id": instance.id if instance else None,  # 工作流实例ID
            "workflow_cost_type": cost_type_bind
        }
        result_data.append(result_item)
    
    return result_data

async def get_pending_approve_instances(db: AsyncSession, user_id: int) -> List[int]:
    """获取待用户审批的工作流实例ID列表"""
    # 方法1：查询实例审批人表，找出显式指定我为审批人的实例
    reviewer_query = select(ErpWorkflowInstanceReviewer.instance_id).where(
        ErpWorkflowInstanceReviewer.reviewer_id == user_id,
        ErpWorkflowInstanceReviewer.status == 0,  # 0表示待审批
        ErpWorkflowInstanceReviewer.disable == 0
    )
    reviewer_result = await db.execute(reviewer_query)
    direct_instance_ids = [row[0] for row in reviewer_result.fetchall()]
    
    # 方法2：查询正在进行中的流程实例，然后根据节点类型判断当前用户是否为审批人
    from public_api.modules import get_user_superior  # 避免循环导入
    
    # 查询所有进行中的工作流实例
    active_instances_query = select(ErpWorkflowInstance).where(
        ErpWorkflowInstance.status == InstanceStatus.PROCESSING.value,  # 进行中
        ErpWorkflowInstance.disable == 0
    )
    active_instances_result = await db.execute(active_instances_query)
    active_instances = active_instances_result.scalars().all()
    
    # 对于每个活跃实例，检查当前节点的审批人类型
    potential_instance_ids = []
    for instance in active_instances:
        # 获取当前节点信息
        node = await erp_workflow_node.get_by_id(db, instance.current_node_id)
        if not node:
            continue
        
        # 根据不同审批人类型判断当前用户是否为审批人
        is_approver = False
        
        # 审批人类型：1连续多级上级 2指定成员 3指定岗位
        if node.approver_type == ApproverType.CONTINUOUS_SUPERIOR.value:  # 连续多级上级
            # 获取申请人，然后查找其上级链
            receipt = await erp_receipt.get_by_id(db, instance.business_id)
            if receipt:
                # 获取申请人的上级链，检查当前用户是否在其中
                try:
                    superior_chain = await get_user_superior(db, receipt.create_by, int(node.approver_ids or 1))
                    if user_id in superior_chain:
                        is_approver = True
                except:
                    pass
        
        elif node.approver_type == ApproverType.SPECIFIED_MEMBER.value:  # 指定成员
            # 检查审批人IDs中是否包含当前用户
            approver_ids = node.approver_ids.split(',') if node.approver_ids else []
            if str(user_id) in approver_ids:
                is_approver = True
        
        elif node.approver_type == ApproverType.SPECIFIED_POSITION.value:  # 指定岗位
            # 需要查询用户岗位表，判断用户是否具有指定岗位
            position_ids = node.approver_ids.split(',') if node.approver_ids else []
            # 获取用户岗位信息
            user_account = await erp_account.get_by_id(db, user_id)
            if user_account and user_account.qy_wechat_position:
                if user_account.qy_wechat_position in position_ids:
                    is_approver = True
        
        if is_approver:
            potential_instance_ids.append(instance.id)
    
    # 合并两种方法的结果
    all_instance_ids = list(set(direct_instance_ids + potential_instance_ids))
    return all_instance_ids

async def get_cc_to_me_instances(db: AsyncSession, user_id: int) -> List[int]:
    """获取抄送给当前用户的工作流实例ID列表"""
    # 查询所有抄送节点
    cc_nodes_query = select(ErpWorkflowNode).where(
        ErpWorkflowNode.node_type == NodeType.CC.value,  # 抄送节点
        ErpWorkflowNode.disable == 0
    )
    cc_nodes_result = await db.execute(cc_nodes_query)
    cc_nodes = cc_nodes_result.scalars().all()
    
    # 筛选我是抄送人的节点ID
    my_cc_node_ids = []
    for node in cc_nodes:
        is_cc_receiver = False
        
        # 审批人类型：1连续多级上级 2指定成员 3指定岗位
        if node.approver_type == ApproverType.CONTINUOUS_SUPERIOR.value:  # 连续多级上级
            # 由于是抄送节点，这个判断较复杂，需要获取工作流实例才能确定当前用户是否在上级链中
            # 简化处理：先不考虑这种情况，在具体抄送记录中再判断
            pass
        
        elif node.approver_type == ApproverType.SPECIFIED_MEMBER.value:  # 指定成员
            # 检查抄送人IDs中是否包含当前用户
            approver_ids = node.approver_ids.split(',') if node.approver_ids else []
            if str(user_id) in approver_ids:
                is_cc_receiver = True
        
        elif node.approver_type == ApproverType.SPECIFIED_POSITION.value:  # 指定岗位
            # 需要查询用户岗位表，判断用户是否具有指定岗位
            position_ids = node.approver_ids.split(',') if node.approver_ids else []
            # 获取用户岗位信息
            user_account = await erp_account.get_by_id(db, user_id)
            if user_account and user_account.qy_wechat_position:
                if user_account.qy_wechat_position in position_ids:
                    is_cc_receiver = True
        
        if is_cc_receiver:
            my_cc_node_ids.append(node.id)
    
    if not my_cc_node_ids:
        return []
    
    # 查询已经经过抄送节点的工作流实例
    # 这里设计的查询逻辑是：流程已完成或流程当前节点之前的某个节点是我被抄送的节点
    # 先查找所有流程实例中，已经经过的抄送节点实例（通过审批记录表）
    cc_record_query = select(ErpWorkflowRecord.instance_id).where(
        ErpWorkflowRecord.node_id.in_(my_cc_node_ids),
        ErpWorkflowRecord.disable == 0
    )
    cc_record_result = await db.execute(cc_record_query)
    cc_instance_ids = [row[0] for row in cc_record_result.fetchall()]
    
    return cc_instance_ids

async def get_workflow_instance_with_details(db: AsyncSession, instance_id: int) -> Dict:
    """获取工作流实例详情，包括节点信息"""
    instance = await erp_workflow_instance.get_by_id(db, instance_id)
    if not instance:
        return None
    
    # 获取节点信息
    nodes = await erp_workflow_node.get_many(db, {"workflow_id": instance.workflow_id}, orderby=["sort_no"])
    instance.nodes = [ModelDataHelper.model_to_dict(node) for node in nodes]
    
    # 获取当前节点
    current_node = await erp_workflow_node.get_by_id(db, instance.current_node_id)
    if current_node:
        instance.current_node_info = ModelDataHelper.model_to_dict(current_node)
    
    return instance

async def get_related_obj_list(db: AsyncSession, page: int, page_size: int, 
                             obj_type: Optional[int] = None, 
                             account_type: Optional[int] = None) -> tuple:
    """获取关联对象信息分页列表"""
    # 构建查询条件
    raw_query_params = []
    if obj_type is not None:
        raw_query_params.append(ErpPaymentObj.obj_type == obj_type)
    if account_type is not None:
        raw_query_params.append(ErpPaymentObj.account_type == account_type)
    
    # 查询对象并分页
    objects = await erp_payment_obj.get_many_with_pagination(
        db, 
        page=page,
        page_size=page_size,
        raw=raw_query_params
    )
    
    # 统计符合条件的总数
    count_stmt = select(func.count()).select_from(ErpPaymentObj).where(*raw_query_params)
    
    count_result = await db.execute(count_stmt)
    total_count = count_result.scalar()
    
    return objects, total_count

async def get_all_positions(db: AsyncSession) -> List[str]:
    """获取所有岗位列表"""
    accounts = await erp_account.get_many(db, raw=[
        ErpAccount.qy_wechat_position != None
    ])
    positions = list(set([account.qy_wechat_position for account in accounts if account.qy_wechat_position]))
    return positions


async def get_workflow_cost_type_related(db: AsyncSession, cost_type_bind: Optional[int] = None, workflow_id: Optional[int] = None):
    """获取费用类型绑定的工作流"""
    selects = [
        ErpCostTypeBind.id,
        ErpCostTypeBind.workflow_id,
        ErpCostTypeBind.receipt_category_name,
        ErpCostTypeBind.default_cost_type_id,
        ErpWorkflowDef.workflow_name,
        ErpFinanceCostType.name.label('finance_cost_type_name')
    ]
    conditions = [
        ErpCostTypeBind.disable == 0,
        # ErpCostTypeBind.workflow_id >0
    ]
    if cost_type_bind:
        conditions.append(ErpCostTypeBind.id == cost_type_bind)
    if workflow_id:
        conditions.append(ErpCostTypeBind.workflow_id == workflow_id)
    stmt = (select(*selects)
            .select_from(ErpCostTypeBind)
            .outerjoin(ErpWorkflowDef, ErpCostTypeBind.workflow_id == ErpWorkflowDef.id)
            .outerjoin(ErpFinanceCostType, ErpCostTypeBind.default_cost_type_id == ErpFinanceCostType.id)
            .where(*conditions))
    result = await db.execute(stmt)
    if workflow_id:
        return result.fetchone()
    # 统一返回单个对象
    if cost_type_bind:
        return result.fetchone()
    else:
        return result.fetchall()
    

async def get_workflow_cost_type_by_workflow_id_module(db: AsyncSession, workflow_id: int):
    """根据workflow_id获取费用类型"""
    selects = [
        ErpFinanceCostType.id.label('cost_type_id'),
        ErpFinanceCostType.name.label('cost_type_name')
    ]
    conditions = [
        ErpWorkflowCostType.disable == 0,
        ErpWorkflowCostType.workflow_id == workflow_id,
        ErpWorkflowCostType.cost_type_id > 0,
        ErpFinanceCostType.disable == 0
    ]
    stmt = (select(*selects)
            .select_from(ErpWorkflowCostType)
            .outerjoin(ErpFinanceCostType, ErpWorkflowCostType.cost_type_id == ErpFinanceCostType.id)
            .where(*conditions))    
    result = await db.execute(stmt)
    return result.fetchall()

async def get_related_obj_list(db: AsyncSession, page: int, page_size: int, obj_type: Optional[int] = None, account_type: Optional[int] = None, keyword: Optional[str] = None, obj_name: Optional[str] = None, count: bool = True) -> List[Dict]:
    """获取付款对象列表"""

    if count:
        selects = [
            func.count()
        ]
    else:
        selects = [
            ErpPaymentObj.id,
            ErpPaymentObj.obj_name,
            ErpPaymentObj.obj_type,
            ErpPaymentObj.account_type,
            ErpPaymentObj.account_no,
            ErpPaymentObj.bank_name,
            ErpPaymentObj.remark,
            ErpPaymentObj.phone,
            ErpPaymentObj.obj_related_name,
            ErpPaymentObjType.type_name,
            ErpBankAccountType.account_type_name

        ]
    conditions = [
        ErpPaymentObj.disable == 0
    ]
    if obj_type:
        conditions.append(ErpPaymentObj.obj_type == obj_type)
    if account_type:
        conditions.append(ErpPaymentObj.account_type == account_type)
    if keyword:
        conditions.append(ErpPaymentObj.obj_name.like(f"%{keyword}%"))
    if obj_name:
        conditions.append(ErpPaymentObj.obj_name == obj_name)
    stmt = (select(*selects)
            .select_from(ErpPaymentObj)
            .outerjoin(ErpPaymentObjType, ErpPaymentObj.obj_type == ErpPaymentObjType.id)
            .outerjoin(ErpBankAccountType, ErpPaymentObj.account_type == ErpBankAccountType.id)
            .where(*conditions))
    
    if count:
        result = await db.execute(stmt)
        return result.scalar()
    else:
        if page and page_size:
            stmt = stmt.offset((page - 1) * page_size).limit(page_size)
        result = await db.execute(stmt)
        return result.fetchall()

