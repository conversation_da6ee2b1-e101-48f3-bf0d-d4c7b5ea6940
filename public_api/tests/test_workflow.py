import json
import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from main import app
from sqlalchemy.ext.asyncio import AsyncSession

# 创建测试客户端
client = TestClient(app)

# 模拟用户信息
mock_user = {
    "uid": "test_user",
    "user_name": "Test User",
    "email": "<EMAIL>",
    "role_ids": ["admin"],
    "departments": [{"id": 1, "name": "测试部门"}],
    "is_admin": True
}

# 测试数据
test_workflow_def = {
    "workflow_name": "测试工作流",
    "workflow_desc": "这是一个测试工作流",
    "workflow_type": "expense",
    "status": 1,
    "remark": "测试备注",
    "nodes": [
        {
            "node_name": "节点1",
            "node_desc": "节点1描述",
            "node_type": 1,
            "approver_type": 1,
            "approver_ids": "1,2,3",
            "approver_names": "审批人1,审批人2,审批人3",
            "sort_no": 1,
            "is_countersign": False,
            "node_action": "approve",
            "is_finance_related": False,
            "is_continuous_approval": False,
        },
        {
            "node_name": "节点2",
            "node_desc": "节点2描述",
            "node_type": 2,
            "approver_type": 2,
            "approver_ids": "4,5",
            "approver_names": "审批人4,审批人5",
            "sort_no": 2,
            "is_countersign": True,
            "node_action": "review",
            "is_finance_related": True,
            "is_continuous_approval": True,
        }
    ]
}

test_workflow_node = {
    "workflow_id": 1,
    "node_name": "新测试节点",
    "node_desc": "新测试节点描述",
    "node_type": 1,
    "approver_type": 1,
    "approver_ids": "1,2",
    "approver_names": "审批人1,审批人2",
    "sort_no": 3,
    "is_countersign": False,
    "node_action": "approve",
    "is_finance_related": False,
    "is_continuous_approval": False,
}

test_receipt = {
    "apply_reason": "测试原因",
    "related_obj_id": 1,
    "related_obj_type": "expense",
    "apply_remark": "测试备注",
    "dept_id": 1,
    "dept_name": "测试部门",
    "attachment": "[]",
    "receipt_type": "expense"
}

test_workflow_action = {
    "receipt_id": 1,
    "action": "approve",
    "comments": "同意"
}


# 模拟认证依赖
@pytest.fixture(autouse=True)
def mock_deps():
    with patch("public_api.api.workflow.get_current_active_user", return_value=mock_user), \
         patch("public_api.api.workflow.get_default_db", return_value=MagicMock(spec=AsyncSession)), \
         patch("public_api.api.workflow.erp_workflow_def"), \
         patch("public_api.api.workflow.erp_workflow_node"), \
         patch("public_api.api.workflow.erp_workflow_instance"), \
         patch("public_api.api.workflow.erp_receipt"), \
         patch("public_api.api.workflow.start_workflow"), \
         patch("public_api.api.workflow.handle_workflow_action"), \
         patch("public_api.api.workflow.cancel_workflow"), \
         patch("public_api.api.workflow.ModelDataHelper"):
        yield


# 测试查询工作流定义列表
async def test_query_workflow_def(mock_deps):
    from public_api.api.workflow import erp_workflow_def, erp_workflow_node, ModelDataHelper
    
    # 模拟数据库返回
    mock_workflows = [MagicMock(id=1), MagicMock(id=2)]
    erp_workflow_def.get_many_with_pagination.return_value = mock_workflows
    
    # 模拟数据库count返回
    mock_count = MagicMock()
    mock_count.scalar.return_value = 2
    
    mock_db = MagicMock()
    mock_db.execute.return_value = mock_count
    
    # 模拟节点返回
    mock_nodes = [MagicMock(), MagicMock()]
    erp_workflow_node.get_many.return_value = mock_nodes
    
    # 模拟模型转字典
    ModelDataHelper.model_to_dict.return_value = {"id": 1}
    
    # 发送请求
    response = await client.get("/api/workflow/workflow_def/?page=1&page_size=10")
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    assert len(data["data"]["data"]) == 2
    assert data["data"]["count"] == 2
    
    # 验证函数调用
    erp_workflow_def.get_many_with_pagination.assert_called_with(mock_db, page=1, page_size=10)


# 测试获取工作流定义详情
async def test_get_workflow_def(mock_deps):
    from public_api.api.workflow import erp_workflow_def, erp_workflow_node, ModelDataHelper
    
    # 模拟数据库返回
    mock_workflow = MagicMock(id=1)
    erp_workflow_def.get_by_id.return_value = mock_workflow
    
    # 模拟节点返回
    mock_nodes = [MagicMock(), MagicMock()]
    erp_workflow_node.get_many.return_value = mock_nodes
    
    # 模拟模型转字典
    ModelDataHelper.model_to_dict.return_value = {"id": 1}
    
    # 发送请求
    response = await client.get("/api/workflow/workflow_def/1")
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    
    # 验证函数调用
    erp_workflow_def.get_by_id.assert_called_with(mock_db, 1)
    erp_workflow_node.get_many.assert_called_with(mock_db, {"workflow_id": 1}, orderby=["sort_no"])


# 测试获取不存在的工作流定义
async def test_get_workflow_def_not_exist(mock_deps):
    from public_api.api.workflow import erp_workflow_def
    
    # 模拟数据库返回
    erp_workflow_def.get_by_id.return_value = None
    
    # 发送请求
    response = await client.get("/api/workflow/workflow_def/999")
    
    # 验证请求结果
    assert response.status_code == 200  # API 总是返回200，但包含错误代码
    data = response.json()
    assert data["code"] == -1
    assert "不存在" in data["msg"]


# 测试创建工作流定义
async def test_create_workflow_def(mock_deps):
    from public_api.api.workflow import erp_workflow_def, erp_workflow_node
    
    # 模拟数据库返回
    mock_workflow = MagicMock(id=1)
    erp_workflow_def.create.return_value = mock_workflow
    
    # 发送请求
    response = await client.post(
        "/api/workflow/workflow_def/",
        json=test_workflow_def
    )
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    assert data["data"]["id"] == 1
    
    # 验证函数调用
    erp_workflow_def.create.assert_called_once()
    assert erp_workflow_node.create.call_count == 2  # 应该创建两个节点


# 测试更新工作流定义
async def test_update_workflow_def(mock_deps):
    from public_api.api.workflow import erp_workflow_def, erp_workflow_node
    
    # 模拟数据库返回
    mock_workflow = MagicMock(id=1)
    erp_workflow_def.get_by_id.return_value = mock_workflow
    
    # 模拟现有节点
    mock_nodes = [MagicMock(id=1), MagicMock(id=2)]
    erp_workflow_node.get_many.return_value = mock_nodes
    
    # 准备更新数据 - 修改一个节点，添加一个新节点
    update_data = test_workflow_def.copy()
    update_data["nodes"][0]["id"] = 1  # 更新现有节点
    update_data["nodes"][1]["id"] = 0  # 添加新节点
    
    # 发送请求
    response = await client.put(
        "/api/workflow/workflow_def/1",
        json=update_data
    )
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    
    # 验证函数调用
    erp_workflow_def.update_one.assert_called_once()
    erp_workflow_node.update_one.assert_called_once()  # 更新一个节点
    erp_workflow_node.create.assert_called_once()  # 创建一个节点


# 测试删除工作流定义
async def test_delete_workflow_def(mock_deps):
    from public_api.api.workflow import erp_workflow_def, erp_workflow_node, erp_workflow_instance
    
    # 模拟没有关联的实例
    erp_workflow_instance.get_many.return_value = []
    
    # 模拟节点返回
    mock_nodes = [MagicMock(id=1), MagicMock(id=2)]
    erp_workflow_node.get_many.return_value = mock_nodes
    
    # 发送请求
    response = await client.delete("/api/workflow/workflow_def/1")
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    
    # 验证函数调用
    erp_workflow_instance.get_many.assert_called_with(mock_db, {"workflow_id": 1})
    erp_workflow_node.delete_one.assert_called()  # 删除节点
    erp_workflow_def.delete_one.assert_called_with(mock_db, 1, commit=False)  # 删除工作流定义


# 测试删除有关联实例的工作流定义
async def test_delete_workflow_def_with_instances(mock_deps):
    from public_api.api.workflow import erp_workflow_instance
    
    # 模拟有关联的实例
    erp_workflow_instance.get_many.return_value = [MagicMock()]
    
    # 发送请求
    response = await client.delete("/api/workflow/workflow_def/1")
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == -1
    assert "已被使用" in data["msg"]


# 测试创建工作流节点
async def test_create_workflow_node(mock_deps):
    from public_api.api.workflow import erp_workflow_def, erp_workflow_node
    
    # 模拟工作流定义存在
    mock_workflow = MagicMock(id=1)
    erp_workflow_def.get_by_id.return_value = mock_workflow
    
    # 模拟创建节点
    mock_node = MagicMock(id=3)
    erp_workflow_node.create.return_value = mock_node
    
    # 发送请求
    response = await client.post(
        "/api/workflow/workflow_node/",
        json=test_workflow_node
    )
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    assert data["data"]["id"] == 3
    
    # 验证函数调用
    erp_workflow_def.get_by_id.assert_called_with(mock_db, 1)
    erp_workflow_node.create.assert_called_once()


# 测试更新工作流节点
async def test_update_workflow_node(mock_deps):
    from public_api.api.workflow import erp_workflow_node
    
    # 模拟节点存在
    mock_node = MagicMock(id=1)
    erp_workflow_node.get_by_id.return_value = mock_node
    
    # 发送请求
    response = await client.put(
        "/api/workflow/workflow_node/1",
        json=test_workflow_node
    )
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    
    # 验证函数调用
    erp_workflow_node.get_by_id.assert_called_with(mock_db, 1)
    erp_workflow_node.update_one.assert_called_once()


# 测试删除工作流节点
async def test_delete_workflow_node(mock_deps):
    from public_api.api.workflow import erp_workflow_node, erp_workflow_instance
    
    # 模拟节点存在
    mock_node = MagicMock(id=1, workflow_id=1)
    erp_workflow_node.get_by_id.return_value = mock_node
    
    # 模拟没有关联的实例
    erp_workflow_instance.get_many.return_value = []
    
    # 发送请求
    response = await client.delete("/api/workflow/workflow_node/1")
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    
    # 验证函数调用
    erp_workflow_node.get_by_id.assert_called_with(mock_db, 1)
    erp_workflow_instance.get_many.assert_called_with(mock_db, {"workflow_id": 1, "current_node_id": 1})
    erp_workflow_node.delete_one.assert_called_with(mock_db, 1)


# 测试删除有关联实例的工作流节点
async def test_delete_workflow_node_with_instances(mock_deps):
    from public_api.api.workflow import erp_workflow_node, erp_workflow_instance
    
    # 模拟节点存在
    mock_node = MagicMock(id=1, workflow_id=1)
    erp_workflow_node.get_by_id.return_value = mock_node
    
    # 模拟有关联的实例
    erp_workflow_instance.get_many.return_value = [MagicMock()]
    
    # 发送请求
    response = await client.delete("/api/workflow/workflow_node/1")
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == -1
    assert "正在被" in data["msg"]


# 测试创建单据
async def test_create_receipt(mock_deps):
    from public_api.api.workflow import erp_receipt
    
    # 模拟创建单据
    mock_receipt = MagicMock(id=1)
    erp_receipt.create.return_value = mock_receipt
    
    # 发送请求
    response = await client.post(
        "/api/workflow/receipt/",
        json=test_receipt
    )
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    assert data["data"]["id"] == 1
    
    # 验证函数调用
    erp_receipt.create.assert_called_once()


# 测试发起工作流
async def test_start_workflow(mock_deps):
    from public_api.api.workflow import erp_receipt, erp_workflow_def, start_workflow
    
    # 模拟单据和工作流存在
    mock_receipt = MagicMock(id=1, audit_state=0)
    erp_receipt.get_by_id.return_value = mock_receipt
    
    mock_workflow = MagicMock(id=1)
    erp_workflow_def.get_by_id.return_value = mock_workflow
    
    # 模拟发起工作流
    start_workflow.return_value = 1
    
    # 发送请求
    response = await client.post(
        "/api/workflow/start_workflow?receipt_id=1&workflow_id=1"
    )
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    assert data["data"]["instance_id"] == 1
    
    # 验证函数调用
    erp_receipt.get_by_id.assert_called_with(mock_db, 1)
    erp_workflow_def.get_by_id.assert_called_with(mock_db, 1)
    start_workflow.assert_called_with(mock_db, 1, 1, "test_user")


# 测试处理工作流动作
async def test_action_workflow(mock_deps):
    from public_api.api.workflow import handle_workflow_action
    
    # 模拟处理工作流动作
    handle_workflow_action.return_value = True
    
    # 发送请求
    response = await client.post(
        "/api/workflow/action_workflow",
        json=test_workflow_action
    )
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    
    # 验证函数调用
    handle_workflow_action.assert_called_with(
        mock_db, 1, "test_user", "approve", "同意"
    )


# 测试取消工作流
async def test_cancel_workflow(mock_deps):
    from public_api.api.workflow import cancel_workflow
    
    # 模拟取消工作流
    cancel_workflow.return_value = True
    
    # 发送请求
    response = await client.post(
        "/api/workflow/cancel_workflow?receipt_id=1"
    )
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    
    # 验证函数调用
    cancel_workflow.assert_called_with(mock_db, 1, "test_user")


# 测试获取工作流实例详情
async def test_get_workflow_instance(mock_deps):
    from public_api.api.workflow import erp_workflow_instance, erp_workflow_node, ModelDataHelper
    
    # 模拟工作流实例存在
    mock_instance = MagicMock(id=1, workflow_id=1, current_node_id=1)
    erp_workflow_instance.get_by_id.return_value = mock_instance
    
    # 模拟节点返回
    mock_nodes = [MagicMock(), MagicMock()]
    erp_workflow_node.get_many.return_value = mock_nodes
    
    # 模拟当前节点
    mock_current_node = MagicMock(id=1)
    erp_workflow_node.get_by_id.return_value = mock_current_node
    
    # 模拟模型转字典
    ModelDataHelper.model_to_dict.return_value = {"id": 1}
    
    # 发送请求
    response = await client.get("/api/workflow/workflow_instance/1")
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    
    # 验证函数调用
    erp_workflow_instance.get_by_id.assert_called_with(mock_db, 1)
    erp_workflow_node.get_many.assert_called_with(mock_db, {"workflow_id": 1}, orderby=["sort_no"])
    erp_workflow_node.get_by_id.assert_called_with(mock_db, 1)


# 测试获取单据对应的工作流信息
async def test_get_receipt_workflow(mock_deps):
    from public_api.api.workflow import erp_receipt, erp_workflow_instance, erp_workflow_node, ModelDataHelper
    
    # 模拟单据存在
    mock_receipt = MagicMock(id=1, approval_record_id=1)
    erp_receipt.get_by_id.return_value = mock_receipt
    
    # 模拟工作流实例存在
    mock_instance = MagicMock(id=1, workflow_id=1, current_node_id=1)
    erp_workflow_instance.get_by_id.return_value = mock_instance
    
    # 模拟节点返回
    mock_nodes = [MagicMock(), MagicMock()]
    erp_workflow_node.get_many.return_value = mock_nodes
    
    # 模拟当前节点
    mock_current_node = MagicMock(id=1)
    erp_workflow_node.get_by_id.return_value = mock_current_node
    
    # 模拟模型转字典
    ModelDataHelper.model_to_dict.return_value = {"id": 1, "approval_record_id": 1}
    
    # 发送请求
    response = await client.get("/api/workflow/receipt/1")
    
    # 验证请求结果
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 0
    
    # 验证函数调用
    erp_receipt.get_by_id.assert_called_with(mock_db, 1)
    erp_workflow_instance.get_by_id.assert_called_with(mock_db, 1)
    erp_workflow_node.get_many.assert_called_with(mock_db, {"workflow_id": 1}, orderby=["sort_no"])
    erp_workflow_node.get_by_id.assert_called_with(mock_db, 1)
