from typing import List
from datetime import datetime
from models.m_teacher import Erp<PERSON>ccount<PERSON>eacher
from models.m_wechat import ErpQwechatContact
from models.models import ErpAccount
from modules.qy_wechat.qy_wechat_relate import QyWechatRelate
from sqlalchemy.ext.asyncio import AsyncSession
from settings import CF, logger
from utils.db.db_handler import get_default_db


erp_qwechat_contact = CF.get_crud(ErpQwechatContact)
erp_account = CF.get_crud(ErpAccount)
erp_account_teacher = CF.get_crud(ErpAccountTeacher)



async def sync_teacher_students(userids: List[str]):
    """
    同步老师账户下的所有企微学员并同步信息
    """
    qy_wechat_relate = QyWechatRelate()
    cursor = ""
    
    while True:
        result = await qy_wechat_relate.batch_get_external_contact(userids, cursor=cursor)
        if not result or result.get('errcode') != 0:
            logger.info(f"获取企微联系人失败: {result}")
            break
        print(result)
        # 处理当前批次的数据
        external_contacts = result.get('external_contact_list', [])
        if external_contacts:
            for account_userid in userids:
                await process_external_contacts(account_userid, external_contacts)
            
        # 获取下一页的游标
        next_cursor = result.get('next_cursor')
        if not next_cursor:
            break
            
        cursor = next_cursor


async def process_external_contacts(account_userid: str, contacts: List[dict]):
    """处理并保存企微联系人数据"""
    async for db in get_default_db():
        # 获取教师账号信息
        teacher_account = await erp_account.get_one(db, qy_wechat_userid=account_userid)
        if not teacher_account:
            logger.warning(f"未找到对应的教师账号: {account_userid}")
            return
        # 获取教师信息
        teacher_info = None
        if teacher_account.is_teacher:
            teacher_info = await erp_account_teacher.get_one(db, account_id=teacher_account.id)
        teacher_account_id = teacher_account.id
        teacher_id = teacher_info.id if teacher_info else None
            
        for contact in contacts:
            follow_info = contact.get('follow_info', {})
            external_contact = contact.get('external_contact', {})
            
            # 确保联系人属于当前教师
            if follow_info.get('userid') != account_userid:
                continue
            state = follow_info.get('state')

            if state and state.startswith('stuid_'):
                stu_id = state.split('_')[1]
            else:
                stu_id = 0
            # 构建联系人数据
            contact_data = {
                'teacher_account_id': teacher_account_id,
                'teacher_id': teacher_id,
                'account_userid': account_userid,
                'remark': follow_info.get('remark', ''),
                'stu_id': stu_id,
                'follow_create_time': datetime.fromtimestamp(follow_info.get('createtime', 0)) if follow_info.get('createtime') else None,
                'add_way': follow_info.get('add_way'),
                'external_userid': external_contact.get('external_userid', ''),
                'external_name': external_contact.get('name', ''),
                'external_type': external_contact.get('type'),
                'external_avatar': external_contact.get('avatar', ''),
                'external_gender': external_contact.get('gender'),
                'create_time': datetime.now(),
                'update_time': datetime.now(),
                'disable': 0
            }
            
            # 检查是否已存在该联系人， 第一轮同步，根据external_userid查询
            exist_contact = await erp_qwechat_contact.get_one(db,
                account_userid = account_userid,
                external_userid = external_contact.get('external_userid', '')
            )
            if not exist_contact and stu_id != 0:   # 第二轮同步，根据stu_id查询
                exist_contact = await erp_qwechat_contact.get_one(db,
                    account_userid = account_userid,
                    stu_id = stu_id
                )
            
            if exist_contact:
                # 更新已存在的联系人
                exist_contact.external_name = external_contact.get('name', '')
                exist_contact.external_avatar = external_contact.get('avatar', '')
                exist_contact.external_gender = external_contact.get('gender', '')
                exist_contact.remark = follow_info.get('remark', '')
                exist_contact.stu_id = stu_id
                exist_contact.external_userid = external_contact.get('external_userid', '')
                exist_contact.follow_create_time = datetime.fromtimestamp(follow_info.get('createtime', 0)) if follow_info.get('createtime') else None
                exist_contact.add_way = follow_info.get('add_way', '')
                exist_contact.update_time = datetime.now()
                logger.info(f"更新企微联系人: {external_contact.get('name')}")
            else:
                # 创建新联系人
                await erp_qwechat_contact.create(db, commit=False, **contact_data)
                logger.info(f"创建企微联系人: {external_contact.get('name')}")
        await db.commit()
