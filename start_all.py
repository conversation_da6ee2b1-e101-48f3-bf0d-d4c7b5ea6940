#!/usr/bin/env python3
"""
启动脚本 - 同时启动 FastAPI 应用和后台服务
"""
import subprocess
import sys
import signal
import time
from pathlib import Path

def signal_handler(sig, frame):
    """处理中断信号"""
    print('\n🛑 收到中断信号，正在关闭所有进程...')
    # 终止所有子进程
    for process in processes:
        if process.poll() is None:  # 进程仍在运行
            process.terminate()
    
    # 等待进程结束
    for process in processes:
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
    
    print('✅ 所有进程已关闭')
    sys.exit(0)

# 全局进程列表
processes = []

def main():
    """主函数"""
    print("🚀 启动 ERP 系统...")
    print("=" * 50)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动后台服务
        print("📋 启动后台服务进程...")
        service_process = subprocess.Popen([
            sys.executable, "service.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
           universal_newlines=True, bufsize=1)
        processes.append(service_process)
        
        # 等待一下确保后台服务启动
        time.sleep(2)
        
        # 启动 FastAPI 应用
        print("🌐 启动 FastAPI 应用...")
        api_process = subprocess.Popen([
            sys.executable, "main.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
           universal_newlines=True, bufsize=1)
        processes.append(api_process)
        
        print("✅ 所有服务已启动")
        print("=" * 50)
        print("📊 服务状态:")
        print(f"  - 后台服务 PID: {service_process.pid}")
        print(f"  - FastAPI 应用 PID: {api_process.pid}")
        print("=" * 50)
        print("💡 按 Ctrl+C 停止所有服务")
        
        # 监控进程状态
        while True:
            # 检查进程是否还在运行
            service_running = service_process.poll() is None
            api_running = api_process.poll() is None
            
            if not service_running:
                print("❌ 后台服务进程已退出")
                break
            
            if not api_running:
                print("❌ FastAPI 应用进程已退出")
                break
            
            # 读取并显示输出
            try:
                # 非阻塞读取输出
                import select
                ready, _, _ = select.select([service_process.stdout, api_process.stdout], [], [], 0.1)
                
                for stream in ready:
                    line = stream.readline()
                    if line:
                        if stream == service_process.stdout:
                            print(f"[后台服务] {line.strip()}")
                        else:
                            print(f"[FastAPI] {line.strip()}")
            except:
                pass
            
            time.sleep(0.1)
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main()) 