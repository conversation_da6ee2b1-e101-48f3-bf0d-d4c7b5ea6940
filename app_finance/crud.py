from sqlalchemy import select, and_, or_, func, literal_column, text, case
from models.m_finance import ErpBankAccountType, ErpFinanceTradeRefund, ErpFinanceTradePayment, ErpBankAccountLog, ErpBankAccount, \
    ErpFinanceCostType, ErpPerformanceRecords
from models.m_mall import MallMerchantConfig
from models.m_office import ErpOfficeCenter, ErpOfficeClassroom
from models.m_order import <PERSON>rp<PERSON><PERSON>rOffer, ErpOrderRefund, ErpOrderRefundDetail, ErpOrderStudent, ErpOrder
from models.m_student import ErpStudent
from models.m_teacher import ErpAccountTeacher, ErpAccountTeacherLog
from models.m_workflow import ErpCostTypeBind, ErpPaymentObj, ErpReceipt, ErpReceiptFinance, ErpReceiptDetail, ErpWorkflowCostType, ErpWorkflowDef, ErpWorkflowInstance, ErpWorkflowNode, ErpWorkflowR<PERSON>ord, ErpWorkflowInstanceReviewer
from models.models import ErpCampus, ErpAccount, ErpJobLevel, ErpSalaryBase, ErpSalaryDetail, ErpEnterprise, ErpSalary, ErpDepartment, ErpAccountDepartment
from models.m_class import ErpClass, ErpClassPlan, ErpCourse, ErpCourseTextbook
import sqlalchemy.orm
import settings
from utils.enum.enum_account import BillType
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased
from utils.db.model_handler import ModelDataHelper
from settings import CF
from utils.enum.enum_approval import NodeType
from utils.enum.enum_order import OrderType, StudentState, TradeStatus
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple, Union
import json
from models.m_finance import ErpFinanceCostType, ErpFinanceTradePayment, ErpFinanceTradeRefund
from models.m_student import ErpStudent
from utils.db.model_handler import ModelDataHelper
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.dialects.mysql import INTEGER
from utils.db.model_handler import ModelDataHelper
from utils.enum.enum_order import OrderType, StudentState, TradeStatus, CheckStatus
from utils.enum.enum_class import ClassStatus

erp_receipt = CF.get_crud(ErpReceipt)
erp_receipt_finance = CF.get_crud(ErpReceiptFinance)
erp_receipt_detail = CF.get_crud(ErpReceiptDetail)
erp_workflow_instance = CF.get_crud(ErpWorkflowInstance)
erp_workflow_def = CF.get_crud(ErpWorkflowDef)
erp_workflow_node = CF.get_crud(ErpWorkflowNode)
erp_order_refund = CF.get_crud(ErpOrderRefund)
erp_order_refund_detail = CF.get_crud(ErpOrderRefundDetail)
erp_account = CF.get_crud(ErpAccount)
erp_workflow_cost_type = CF.get_crud(ErpWorkflowCostType)
erp_cost_type_bind = CF.get_crud(ErpCostTypeBind)
erp_finance_cost_type = CF.get_crud(ErpFinanceCostType)
erp_payment_obj = CF.get_crud(ErpPaymentObj)
erp_student = CF.get_crud(ErpStudent)
erp_bill_account = CF.get_crud(ErpBankAccount)
erp_account_teacher = CF.get_crud(ErpAccountTeacher)
erp_account_teacher_log = CF.get_crud(ErpAccountTeacherLog)


async def original_refund_record(db, page=None, page_size=None, stu_name=None, order_no=None, refund_order_no=None, trade_status=None, receipt_id=None, count=False):
    # 构建基础查询条件
    base_conditions = []
    
    # 主表条件 - 这些条件可以有效使用索引
    if trade_status:
        base_conditions.append(ErpFinanceTradeRefund.trade_status == trade_status)
    if receipt_id:
        base_conditions.append(ErpFinanceTradeRefund.receipt_id == receipt_id)
    if refund_order_no:
        base_conditions.append(ErpFinanceTradeRefund.refund_order_no.like(f"%{refund_order_no}%"))
    if order_no:
        base_conditions.append(ErpFinanceTradeRefund.payment_order_no.like(f"%{order_no}%"))
    
    # 学生姓名条件需要关联查询
    student_conditions = []
    if stu_name:
        student_conditions.append(ErpStudent.stu_name.like(f"%{stu_name}%"))
    
    # 如果只需要统计总数，使用简化的查询
    if count:
        if student_conditions:
            # 需要关联学生表的计数查询
            count_stmt = (
                select(func.count(ErpFinanceTradeRefund.id))
                .select_from(ErpFinanceTradeRefund)
                .join(ErpStudent, ErpStudent.id == ErpFinanceTradeRefund.stu_id)
                .where(and_(*(base_conditions + student_conditions)))
            )
        else:
            # 不需要关联学生表的计数查询
            count_stmt = (
                select(func.count(ErpFinanceTradeRefund.id))
                .select_from(ErpFinanceTradeRefund)
                .where(and_(*base_conditions))
            )
        result = await db.execute(count_stmt)
        return result.scalar()
    
    # 数据查询 - 添加排序以利用索引
    all_conditions = base_conditions + student_conditions
    
    selects = [
        ErpFinanceTradeRefund.id,
        ErpFinanceTradeRefund.payment_order_no,
        ErpFinanceTradeRefund.refund_order_no,
        ErpFinanceTradeRefund.trade_type,
        ErpFinanceTradeRefund.money,
        ErpFinanceTradeRefund.money_refund,
        ErpFinanceTradeRefund.openid,
        ErpFinanceTradeRefund.trade_status,
        ErpFinanceTradeRefund.merchant_id,
        ErpFinanceTradeRefund.cmb_order_id,
        ErpFinanceTradeRefund.cmb_pay_time,
        ErpFinanceTradeRefund.cmb_trade_type,
        ErpFinanceTradeRefund.third_order_id,
        ErpFinanceTradeRefund.comments,
        ErpFinanceTradeRefund.stu_id,
        ErpFinanceTradeRefund.offer_id,
        ErpFinanceTradeRefund.campus_id,
        ErpFinanceTradeRefund.receipt_id,
        ErpFinanceTradeRefund.create_time,
        ErpFinanceTradeRefund.order_student_id,
        # 学生信息
        ErpStudent.stu_username,
        ErpStudent.stu_name,
        # 报价单信息
        ErpOrderOffer.total_sale_price,
        ErpOrderOffer.ewallet_money,
        ErpOrderOffer.integral_money,
        # 校区信息
        ErpCampus.campus_name,
        # 商户信息
        MallMerchantConfig.MerchantName,
        MallMerchantConfig.CmbMerId,
        MallMerchantConfig.Type,
        # 支付信息
        ErpFinanceTradePayment.money_income,
        ErpFinanceTradePayment.money_pay,
        ErpFinanceTradePayment.cmb_pay_time.label('payment_cmb_pay_time'),
        # 班级和课程信息 - 根据order_class_type选择不同的信息
        ErpOrderStudent.order_class_type,
        ErpClass.id.label('class_id'),
        ErpCourse.id.label('course_id'),
        # 根据order_class_type选择不同的名称字段
        case(
            (ErpOrderStudent.order_class_type == 1, ErpClass.class_name),
            (ErpOrderStudent.order_class_type == 3, ErpCourseTextbook.name),
            else_=None
        ).label('class_name'),
        case(
            (ErpOrderStudent.order_class_type == 1, ErpCourse.course_name),
            (ErpOrderStudent.order_class_type == 3, ErpCourseTextbook.name),
            else_=None
        ).label('course_name'),
    ]
    
    stmt = (
        select(*selects)
        .select_from(ErpFinanceTradeRefund)
        .outerjoin(ErpStudent, ErpStudent.id == ErpFinanceTradeRefund.stu_id)
        .outerjoin(ErpOrderOffer, ErpOrderOffer.id == ErpFinanceTradeRefund.offer_id)
        .outerjoin(ErpCampus, ErpCampus.id == ErpFinanceTradeRefund.campus_id)
        .outerjoin(MallMerchantConfig, MallMerchantConfig.Id == ErpFinanceTradeRefund.merchant_id)
        .outerjoin(ErpFinanceTradePayment, 
                   ErpFinanceTradeRefund.payment_order_no == ErpFinanceTradePayment.payment_order_no)
        .outerjoin(ErpOrderStudent, ErpOrderStudent.id == ErpFinanceTradeRefund.order_student_id)
        # 根据order_class_type条件连接不同的表
        .outerjoin(ErpClass, and_(ErpClass.id == ErpOrderStudent.class_id, ErpOrderStudent.order_class_type == 1))
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpCourseTextbook, and_(ErpCourseTextbook.id == ErpOrderStudent.class_id, ErpOrderStudent.order_class_type == 3))
        .where(and_(*all_conditions))
        .order_by(ErpFinanceTradeRefund.create_time.desc())  # 添加排序以利用索引
    )
    
    if page and page_size:
        stmt = stmt.limit(page_size).offset((page - 1) * page_size)
    
    result = await db.execute(stmt)
    return result.fetchall()


async def crud_bill_account_log(db, default_account_type, bank_account_id, receipt_id, audit_state, begin_time, end_time, cost_type_ids, page=None, page_size=None, count=False):

    # 制单人
    receipt_create_by = aliased(ErpAccount, name="receipt_create_by")

    selects = [
        ErpBankAccountLog.id.label('bank_account_log_id'),
        ErpBankAccountLog.bill_account_id,
        ErpBankAccountLog.change_type,
        ErpBankAccountLog.amount,
        ErpBankAccountLog.rest_balance,
        ErpBankAccountLog.desc,
        ErpBankAccountLog.create_by,
        ErpBankAccountLog.create_time,
        ErpBankAccountLog.update_time,
        ErpBankAccountLog.disable,
        ErpBankAccount.account_alias,
        ErpBankAccount.account_number,
        ErpBankAccount.bank_name,
        ErpBankAccount.bank_sub_name,
        ErpBankAccount.account_holder,
        ErpBankAccount.currency_type,
        ErpBankAccount.id.label('bank_account_id'),
        ErpReceipt.id.label('receipt_id'),
        ErpReceipt.create_by.label('receipt_create_by'),
        ErpAccount.employee_name.label('receipt_create_name'),
        ErpWorkflowDef.id.label('workflow_id'),
        ErpBankAccount.account_type,
        ErpBankAccount.default_account_type,
        ErpBankAccountType.account_type_name.label('account_type_name'),
        receipt_create_by.employee_name.label('receipt_create_by_name'),
        receipt_create_by.avatar.label('receipt_create_by_avatar'),
        ErpFinanceCostType.name.label('cost_type_name'),
    ]
    conditions = [
        # ErpBankAccount.account_type == account_type,
        ErpBankAccount.default_account_type == default_account_type,
    ]
    if begin_time:
        conditions.append(ErpBankAccountLog.create_time >= begin_time)
    if end_time:
        conditions.append(ErpBankAccountLog.create_time <= end_time)
    if cost_type_ids:
        conditions.append(ErpCostTypeBind.default_cost_type_id.in_(cost_type_ids))
    if bank_account_id:
        conditions.append(ErpBankAccountLog.bill_account_id == bank_account_id)
    if receipt_id:
        conditions.append(ErpBankAccountLog.receipt_id == receipt_id)
    if audit_state:
        conditions.append(ErpReceipt.audit_state == audit_state)
    
    # 如果是查询总数，只查询总数
    if count:
        stmt = (
            select(func.count())
            .select_from(ErpBankAccountLog)
            .outerjoin(ErpBankAccount, ErpBankAccount.id == ErpBankAccountLog.bill_account_id)
            .outerjoin(ErpReceipt, ErpBankAccountLog.receipt_id == ErpReceipt.id)
            .outerjoin(ErpWorkflowDef, ErpReceipt.workflow_id == ErpWorkflowDef.id)
            .outerjoin(ErpAccount, ErpAccount.id == ErpReceipt.create_by)
            .outerjoin(ErpBankAccountType, ErpBankAccount.account_type == ErpBankAccountType.id)
            .outerjoin(ErpCostTypeBind, ErpCostTypeBind.workflow_id == ErpReceipt.workflow_id)
            .outerjoin(ErpFinanceCostType, ErpFinanceCostType.id == ErpCostTypeBind.default_cost_type_id)
            .outerjoin(receipt_create_by, ErpReceipt.create_by == receipt_create_by.id)
            .where(and_(*conditions))
        )
        result = await db.execute(stmt)
        return result.scalar()
    
    # 正常查询数据
    stmt = (
        select(*selects)
        .select_from(ErpBankAccountLog)
        .outerjoin(ErpBankAccount, ErpBankAccount.id == ErpBankAccountLog.bill_account_id)
        .outerjoin(ErpReceipt, ErpBankAccountLog.receipt_id == ErpReceipt.id)
        .outerjoin(ErpWorkflowDef, ErpReceipt.workflow_id == ErpWorkflowDef.id)
        .outerjoin(ErpAccount, ErpAccount.id == ErpReceipt.create_by)
        .outerjoin(ErpBankAccountType, ErpBankAccount.account_type == ErpBankAccountType.id)
        .outerjoin(ErpCostTypeBind, ErpCostTypeBind.workflow_id == ErpReceipt.workflow_id)
        .outerjoin(ErpFinanceCostType, ErpFinanceCostType.id == ErpCostTypeBind.default_cost_type_id)
        .outerjoin(receipt_create_by, ErpReceipt.create_by == receipt_create_by.id)
        .where(and_(*conditions))
    )
    if page or page_size:
        stmt = stmt.limit(page_size).offset((page - 1) * page_size)
    result = await db.execute(stmt)
    return result.fetchall()


async def get_payment_account(db, type=None, to_type=None, keyword=None):
    selects = [
        ErpSalaryBase.id,
        ErpSalaryBase.account_id,
        ErpSalaryBase.bank_sub_name,
        ErpSalaryBase.bank_card_number,
        ErpSalaryBase.bank_city,
        ErpSalaryBase.type,
        ErpSalaryBase.to_type,
        ErpSalaryBase.to_name,
        ErpAccount.employee_name,
        ErpStudent.stu_name,

    ]
    conditions = []
    if type:
        conditions.append(ErpSalaryBase.type == type)
    if to_type:
        conditions.append(ErpSalaryBase.to_type == to_type)
    if keyword:
        conditions.append(
            or_(
                ErpAccount.employee_name.like(f"%{keyword}%"),
                ErpStudent.stu_name.like(f"%{keyword}%"),
                ErpSalaryBase.to_name.like(f"%{keyword}%"),
            )
        )
    stmt = (
        select(*selects)
        .select_from(ErpSalaryBase)
        .outerjoin(ErpAccount, ErpAccount.id == ErpSalaryBase.account_id)
        .outerjoin(ErpStudent, ErpStudent.id == ErpSalaryBase.account_id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()


async def stu_e_wallet_with_page(db, page=None, page_size=None, keyword=None, count=False):
    selects = [
        ErpStudent.id,
        ErpStudent.stu_name,
        ErpStudent.stu_gender,
        ErpStudent.stu_avatar,
        ErpStudent.stu_username,
        ErpStudent.stu_wallet_amount
    ]
    conditions = [
        ErpStudent.disable==0,
        ErpStudent.stu_wallet_amount >0
    ]
    if keyword:
        conditions.append(
            or_(
                ErpStudent.stu_name.like(f"%{keyword}%"),
                ErpStudent.stu_username.like(f"%{keyword}%"),
            )
        )
    if count:
        stmt = (
            select(func.count(ErpStudent.id))
            .select_from(ErpStudent)
            .where(and_(*conditions))
        )
        result = await db.execute(stmt)
        return result.scalar()
    else:
        stmt = (
            select(*selects)
            .select_from(ErpStudent)
            .where(and_(*conditions))
        )
        if page or page_size:
            stmt = stmt.limit(page_size).offset((page - 1) * page_size)
        stmt = stmt.order_by(ErpStudent.create_time.desc())
        result = await db.execute(stmt)
        return result.fetchall()


# 汇总统计电子钱包总额
async def get_e_wallet_total(db, campus_id=1):
    stmt = (
        select(
            func.sum(ErpStudent.stu_wallet_amount)
        )
        .select_from(ErpStudent)
        .where(
            and_(
                ErpStudent.disable == 0
            )
        )
    )
    result = await db.execute(stmt)
    return result.scalar()


async def get_salary_bank(db, salary_id):
    selects = [
        # ErpAccount.id.label('account_id'),
        ErpSalaryBase.bank_card_number,
        ErpAccount.employee_name,
        ErpSalaryBase.bank_sub_name,
        ErpSalaryBase.bank_city,
        ErpSalaryDetail.issued_salary,
        ErpEnterprise.enterprise_name,
        literal_column("''").label("comments"),

    ]
    conditions = [
        ErpSalaryDetail.salary_id == salary_id
    ]
    stmt = (
        select(*selects)
        .select_from(ErpSalaryDetail)
        .outerjoin(ErpSalaryBase, ErpSalaryDetail.account_id == ErpSalaryBase.account_id)
        .outerjoin(ErpAccount, ErpAccount.id == ErpSalaryDetail.account_id)
        .outerjoin(ErpEnterprise, ErpEnterprise.id == ErpSalaryBase.enterprise_id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()


async def salary_detail_by_salary_id(db, salary_id):
    selects = [
        ErpAccount.id,
        ErpAccount.username,
        ErpAccount.employee_name,
        ErpAccount.qy_wechat_userid,
        ErpSalaryDetail.id.label('salary_detail_id'),
        ErpSalary.ym
    ]
    conditions = [
        ErpSalaryDetail.salary_id == salary_id
    ]
    stmt = (
        select(*selects)
        .select_from(ErpSalaryDetail)
        .outerjoin(ErpAccount, ErpAccount.id == ErpSalaryDetail.account_id)
        .outerjoin(ErpSalary, ErpSalary.id == ErpSalaryDetail.salary_id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()


async def finance_income_expense_modules(db: AsyncSession, page=None, page_size=None, condition=None, count=False):
    """
    收支情况查询，根据参数count决定返回记录列表或总数
    """
    CreateBy = aliased(ErpAccount, name="CreateBy")
    UpdateBy = aliased(ErpAccount, name="UpdateBy")


    conditions = [
        ErpWorkflowDef.workflow_type.in_([1,2,3])   # 收支或收支相抵
    ]
    if condition.get("receipt_id"):
        conditions.append(ErpReceipt.id == condition.get("receipt_id"))
    if condition.get("dept_id"):
        conditions.append(ErpReceipt.dept_id == condition.get("dept_id"))
    if condition.get("create_by"):
        conditions.append(ErpReceipt.create_by == condition.get("create_by"))
    if condition.get("audit_state"):
        conditions.append(ErpReceipt.audit_state == condition.get("audit_state"))
    if condition.get("start_time"):
        conditions.append(ErpReceipt.create_time >= condition.get("start_time"))
    if condition.get("end_time"):
        conditions.append(ErpReceipt.create_time <= condition.get("end_time"))
    if condition.get("workflow_type"):
        conditions.append(ErpWorkflowDef.workflow_type == condition.get("workflow_type"))
    # 构建基础查询语句
    base_stmt = (
        select(
            ErpReceipt.id,
            ErpReceipt.workflow_instance_id,
            ErpReceipt.update_time,
            ErpReceipt.workflow_id,
            ErpReceipt.create_by,
            ErpReceipt.apply_reason,
            ErpReceipt.dept_id,
            ErpReceipt.update_by,
            ErpReceipt.dept_name,
            ErpReceipt.disable,
            ErpReceipt.related_obj_id,
            ErpReceipt.is_auto,
            ErpReceipt.refund_id,
            ErpReceipt.related_obj_type,
            ErpReceipt.attachment,
            ErpReceipt.payment_id,
            ErpReceipt.apply_remark,
            ErpReceipt.relate_finance_id,
            ErpReceipt.related_obj_name,
            ErpReceipt.audit_state,
            ErpReceipt.create_time,

            ErpReceiptFinance.id.label('receipt_finance_id'),
            ErpReceiptFinance.receipt_id,
            ErpReceiptFinance.bank_account_id,
            ErpReceiptFinance.bank_account_name,
            ErpReceiptFinance.apply_money,
            ErpReceiptFinance.trade_money,
            ErpReceiptFinance.desc,
            ErpReceiptFinance.cashier_id,
            ErpReceiptFinance.invoice_type,
            ErpReceiptFinance.invoice_money,
            ErpReceiptFinance.invoice_remark,

            CreateBy.employee_name.label('create_by_name'),
            UpdateBy.employee_name.label('update_by_name'),
            ErpWorkflowDef,

        )
        .select_from(ErpReceipt)
        .outerjoin(ErpReceiptFinance, ErpReceipt.id == ErpReceiptFinance.receipt_id)
        .outerjoin(ErpWorkflowDef, ErpReceipt.workflow_id == ErpWorkflowDef.id)
        .outerjoin(ErpWorkflowInstance, ErpReceipt.workflow_instance_id == ErpWorkflowInstance.id)
        .outerjoin(CreateBy, ErpReceipt.create_by == CreateBy.id)
        .outerjoin(UpdateBy, ErpReceipt.update_by == UpdateBy.id)
        .where(and_(*conditions))
        .order_by(ErpReceipt.create_time.desc()) 
    )

    # 如果需要统计总数，则修改查询为count查询
    if count:
        stmt = (
            select(func.count(ErpReceipt.id))
            .select_from(ErpReceipt)
            .outerjoin(ErpReceiptFinance, ErpReceipt.id == ErpReceiptFinance.receipt_id)
            .outerjoin(ErpWorkflowDef, ErpReceipt.workflow_id == ErpWorkflowDef.id)
            # .outerjoin(ErpWorkflowCostType, ErpWorkflowCostType.workflow_id == ErpWorkflowDef.id)
            # .outerjoin(ErpFinanceCostType, ErpWorkflowCostType.cost_type_id == ErpFinanceCostType.id)
            .outerjoin(ErpWorkflowInstance, ErpReceipt.workflow_instance_id == ErpWorkflowInstance.id)
            .outerjoin(CreateBy, ErpReceipt.create_by == CreateBy.id)
            .outerjoin(UpdateBy, ErpReceipt.update_by == UpdateBy.id)
            .where(and_(*conditions))
        )
        result = await db.execute(stmt)
        return result.scalar()
    else:
        # 分页查询
        if page or page_size:
            base_stmt = base_stmt.limit(page_size).offset((page - 1) * page_size)
        # compile_sql = base_stmt.compile(compile_kwargs={"literal_binds": True})
        # print(compile_sql)
        result = await db.execute(base_stmt)
        return result.fetchall()

async def finance_income_expense_detail_modules(db:AsyncSession, receipt_id:int):
    """
    收支情况详情查询
    - receipt_id: 单据ID
    
    返回数据包含:
    - 单据基本信息
    - 单据财务信息
    - 单据明细（退费单用ErpOrderRefundDetail，其他用ErpReceiptDetail）
    - 工作流信息
    - 所有关联人员的名称和头像
    - 关联对象信息(payment_obj)
    """
    # 获取单据基本信息
    receipt = await erp_receipt.get_by_id(db, receipt_id)
    if not receipt:
        return None
    
    # 基础数据结构
    result = ModelDataHelper.model_to_dict(receipt)
    
    # 1. 获取工作流实例和定义信息
    instance_query = await erp_workflow_instance.get_many(db, {"business_id": receipt_id, "disable": 0})
    instance = None
    
    if instance_query:
        instance = instance_query[0]  # 通常一个单据只对应一个工作流实例
        result["workflow_instance"] = ModelDataHelper.model_to_dict(instance)
        
        # 获取工作流定义
        if instance.workflow_id:
            workflow_def = await erp_workflow_def.get_by_id(db, instance.workflow_id)
            if workflow_def:
                result["workflow_def"] = ModelDataHelper.model_to_dict(workflow_def)
        
        # 获取工作流节点信息
        if instance.workflow_id:
            nodes = await erp_workflow_node.get_many(db, {"workflow_id": instance.workflow_id}, orderby=["sort_no"])
            result["workflow_nodes"] = [ModelDataHelper.model_to_dict(node) for node in nodes]
            
            # 获取当前节点详细信息
            current_node_id = instance.current_node_id
            if current_node_id:
                current_node = await erp_workflow_node.get_by_id(db, current_node_id)
                if current_node:
                    result["current_node_info"] = ModelDataHelper.model_to_dict(current_node)
            
            # 获取审批记录
            stmt = select(ErpWorkflowRecord).where(
                ErpWorkflowRecord.instance_id == instance.id,
                ErpWorkflowRecord.disable == 0
            ).order_by(ErpWorkflowRecord.sort_no)
            
            approval_records = await db.execute(stmt)
            approval_records = approval_records.scalars().all()
            approval_records_data = []
            
            # 为每个审批记录添加审批人详细信息
            for record in approval_records:
                record_data = ModelDataHelper.model_to_dict(record)
                if record.approver_id:
                    approver_info = await erp_account.get_by_id(db, record.approver_id)
                    if approver_info:
                        record_data["approver_info"] = {
                            "id": approver_info.id,
                            "name": approver_info.employee_name,
                            "avatar": approver_info.avatar,
                            "position": approver_info.qy_wechat_position,
                        }
                approval_records_data.append(record_data)
            
            result["approval_records"] = approval_records_data
            
            # 获取当前审批人信息
            stmt = select(ErpWorkflowInstanceReviewer).where(
                ErpWorkflowInstanceReviewer.instance_id == instance.id,
                ErpWorkflowInstanceReviewer.status == 0,  # 待审批状态
                ErpWorkflowInstanceReviewer.disable == 0
            )
            
            current_reviewers = await db.execute(stmt)
            current_reviewers = current_reviewers.scalars().all()
            reviewers_data = []
            
            # 为每个待审批人添加详细信息
            for reviewer in current_reviewers:
                reviewer_data = ModelDataHelper.model_to_dict(reviewer)
                if reviewer.reviewer_id:
                    user_info = await erp_account.get_by_id(db, reviewer.reviewer_id)
                    if user_info:
                        reviewer_data["user_info"] = {
                            "id": user_info.id,
                            "name": user_info.employee_name,
                            "avatar": user_info.avatar,
                            "position": user_info.qy_wechat_position,
                        }
                reviewers_data.append(reviewer_data)
            
            result["current_reviewers"] = reviewers_data
            
            # 获取抄送人信息(参考get_receipt_with_workflow)
            # 注意：ErpWorkflowInstanceReviewer没有node_type属性，需要通过节点信息或reviewer_type判断
            # 先获取所有CC类型的节点ID
            cc_node_ids = []
            for node in nodes:
                if node.node_type == NodeType.CC.value:
                    cc_node_ids.append(node.id)
            
            if cc_node_ids:
                # 查询与CC节点关联的审批人
                stmt = select(ErpWorkflowInstanceReviewer).where(
                    ErpWorkflowInstanceReviewer.instance_id == instance.id,
                    ErpWorkflowInstanceReviewer.node_id.in_(cc_node_ids),
                    ErpWorkflowInstanceReviewer.disable == 0
                )
                
                cc_reviewers = await db.execute(stmt)
                cc_reviewers = cc_reviewers.scalars().all()
                cc_data = []
                
                # 为每个抄送人添加详细信息
                for cc in cc_reviewers:
                    cc_data_item = ModelDataHelper.model_to_dict(cc)
                    if cc.reviewer_id:
                        user_info = await erp_account.get_by_id(db, cc.reviewer_id)
                        if user_info:
                            cc_data_item["user_info"] = {
                                "id": user_info.id,
                                "name": user_info.employee_name,
                                "avatar": user_info.avatar,
                                "position": user_info.qy_wechat_position,
                            }
                    cc_data.append(cc_data_item)
                
                result["cc_reviewers"] = cc_data
    
    # 2. 获取财务信息
    finance = await erp_receipt_finance.get_one(db, receipt_id=receipt_id)
    if finance:
        finance_data = ModelDataHelper.model_to_dict(finance)
        
        # 添加出纳审核人信息
        if finance.cashier_id and finance.cashier_id > 0:
            cashier_info = await erp_account.get_by_id(db, finance.cashier_id)
            if cashier_info:
                finance_data["cashier_info"] = {
                    "id": cashier_info.id,
                    "name": cashier_info.employee_name,
                    "avatar": cashier_info.avatar,
                    "position": cashier_info.qy_wechat_position,
                }
        
        # 添加财务审核人信息
        if finance.financer_id and finance.financer_id > 0:
            financer_info = await erp_account.get_by_id(db, finance.financer_id)
            if financer_info:
                finance_data["financer_info"] = {
                    "id": financer_info.id,
                    "name": financer_info.employee_name,
                    "avatar": financer_info.avatar,
                    "position": financer_info.qy_wechat_position,
                }
                
        # 添加银行账户信息
        if finance.bank_account_id:
            bank_account = await erp_bill_account.get_by_id(db, finance.bank_account_id)
            if bank_account:
                finance_data["bank_account_info"] = ModelDataHelper.model_to_dict(bank_account)
        
        result["finance_info"] = finance_data
    
    # 3. 获取明细列表 - 区分退费单和其他单据
    # 检查是否为退费单
    is_refund = False
    if receipt.refund_id > 0:
        is_refund = True
        
    if is_refund:
        # 退费单明细
        refund = await erp_order_refund.get_by_id(db, receipt.refund_id)
        if refund:
            refund_data = ModelDataHelper.model_to_dict(refund)
            
            # 添加创建人和更新人信息
            if refund.create_by:
                creator = await erp_account.get_by_id(db, refund.create_by)
                if creator:
                    refund_data["creator_info"] = {
                        "id": creator.id,
                        "name": creator.employee_name,
                        "avatar": creator.avatar,
                        "position": creator.qy_wechat_position,
                    }
            
            if refund.update_by:
                updater = await erp_account.get_by_id(db, refund.update_by)
                if updater:
                    refund_data["updater_info"] = {
                        "id": updater.id,
                        "name": updater.employee_name,
                        "avatar": updater.avatar,
                        "position": updater.qy_wechat_position,
                    }
                    
            result["refund_info"] = refund_data
            
            # 获取退费明细
            refund_details = await erp_order_refund_detail.get_many(db, {"refund_id": refund.id, "disable": 0}, orderby=["sort_no"])
            if refund_details:
                details_data = []
                for detail in refund_details:
                    detail_data = ModelDataHelper.model_to_dict(detail)
                    
                    # 添加创建人和更新人信息
                    if detail.create_by:
                        creator = await erp_account.get_by_id(db, detail.create_by)
                        if creator:
                            detail_data["creator_info"] = {
                                "id": creator.id,
                                "name": creator.employee_name,
                                "avatar": creator.avatar,
                                "position": creator.qy_wechat_position,
                            }
                    
                    if detail.update_by:
                        updater = await erp_account.get_by_id(db, detail.update_by)
                        if updater:
                            detail_data["updater_info"] = {
                                "id": updater.id,
                                "name": updater.employee_name,
                                "avatar": updater.avatar,
                                "position": updater.qy_wechat_position,
                            }
                    
                    details_data.append(detail_data)
                
                result["detail_list"] = details_data
    else:
        # 其他单据明细
        details = await erp_receipt_detail.get_many(db, {"receipt_id": receipt_id}, orderby=["sort_no"])
        if details:
            details_data = []
            for detail in details:
                detail_data = ModelDataHelper.model_to_dict(detail)
                
                # 添加创建人和更新人信息
                if detail.create_by:
                    creator = await erp_account.get_by_id(db, detail.create_by)
                    if creator:
                        detail_data["creator_info"] = {
                            "id": creator.id,
                            "name": creator.employee_name,
                            "avatar": creator.avatar,
                            "position": creator.qy_wechat_position,
                        }
                
                if detail.update_by:
                    updater = await erp_account.get_by_id(db, detail.update_by)
                    if updater:
                        detail_data["updater_info"] = {
                            "id": updater.id,
                            "name": updater.employee_name,
                            "avatar": updater.avatar,
                            "position": updater.qy_wechat_position,
                        }
                
                details_data.append(detail_data)
            
            result["detail_list"] = details_data
    
    # 4. 获取创建人和更新人信息
    if receipt.create_by:
        creator = await erp_account.get_by_id(db, receipt.create_by)
        if creator:
            result["creator_info"] = {
                "id": creator.id,
                "name": creator.employee_name,
                "avatar": creator.avatar,
                "position": creator.qy_wechat_position,
            }
    
    if receipt.update_by:
        updater = await erp_account.get_by_id(db, receipt.update_by)
        if updater:
            result["updater_info"] = {
                "id": updater.id,
                "name": updater.employee_name,
                "avatar": updater.avatar,
                "position": updater.qy_wechat_position,
            }
    
    # 5. 添加关联对象信息(payment_obj)
    if receipt.related_obj_id and receipt.related_obj_type:
        # related_obj_type: 1 学生 2 员工 3 供应商(payment_obj表) 4 内置学生 5 内置员工
        if receipt.related_obj_type <= 3:
            # 从payment_obj表获取关联对象信息
            payment_obj = await erp_payment_obj.get_by_id(db, receipt.related_obj_id)
            if payment_obj:
                result["payment_obj"] = ModelDataHelper.model_to_dict(payment_obj)
        elif receipt.related_obj_type == 4:
            # 获取内置学生信息
            student = await erp_student.get_by_id(db, receipt.related_obj_id)
            if student:
                student_data = ModelDataHelper.model_to_dict(student)
                result["payment_obj"] = {
                    "id": student.id,
                    "obj_name": student.stu_name,
                    "obj_type": 1,  # 学生
                    "obj_related_id": student.id,
                    "is_built_in": True,
                    # 其他学生信息
                    "student_info": student_data
                }
        elif receipt.related_obj_type == 5:
            # 获取内置员工信息
            employee = await erp_account.get_by_id(db, receipt.related_obj_id)
            if employee:
                employee_data = ModelDataHelper.model_to_dict(employee)
                result["payment_obj"] = {
                    "id": employee.id,
                    "obj_name": employee.name,
                    "obj_type": 2,  # 员工
                    "obj_related_id": employee.id,
                    "is_built_in": True,
                    # 其他员工信息
                    "employee_info": employee_data
                }
    
    # 6. 添加费用类型绑定 erp_cost_type_bind
    if receipt.workflow_id:
        cost_type_bind = await erp_cost_type_bind.get_one(db, workflow_id=receipt.workflow_id)
        if cost_type_bind:
            result["workflow_cost_type"] = ModelDataHelper.model_to_dict(cost_type_bind)
    
    return result

# ------------------------ 营收报表相关函数 ------------------------

async def get_class_student_count(db, class_ids):
    """获取班级的学生数量（正常状态的学生）"""
    if not class_ids:
        return {}
        
    selects = [
        ErpOrderStudent.class_id,
        func.count(ErpOrderStudent.id).label("student_count")
    ]
    
    stmt = (
        select(*selects)
        .select_from(ErpOrderStudent)
        .where(
            and_(
                ErpOrderStudent.class_id.in_(class_ids),
                ErpOrderStudent.student_state == StudentState.NORMAL.value,  # 正常状态
                ErpOrderStudent.disable == 0,
                ErpOrderStudent.order_class_type == 1  # 课程类型
            )
        )
        .group_by(ErpOrderStudent.class_id)
    )
    
    result = await db.execute(stmt)
    records = result.fetchall()
    
    # 转换为字典格式 {class_id: student_count}
    return {record.class_id: record.student_count for record in records}

async def get_class_flash_times(db, class_ids):
    """获取班级已完成的课次数量"""
    if not class_ids:
        return {}
        
    selects = [
        ErpClassPlan.class_id,
        func.count(ErpClassPlan.id).label("flash_class_times")
    ]
    
    stmt = (
        select(*selects)
        .select_from(ErpClassPlan)
        .where(
            and_(
                ErpClassPlan.class_id.in_(class_ids),
                ErpClassPlan.finish == 1,  # 已完成
                ErpClassPlan.disable == 0
            )
        )
        .group_by(ErpClassPlan.class_id)
    )
    
    result = await db.execute(stmt)
    records = result.fetchall()
    
    # 转换为字典格式 {class_id: flash_class_times}
    return {record.class_id: record.flash_class_times for record in records}

async def get_class_order_stats(db, class_ids):
    """获取班级的订单统计数据：应收、优惠、退款、实收"""
    if not class_ids:
        return {}
    
    # 查询订单数据
    selects = [
        ErpOrderStudent.class_id,
        func.sum(ErpOrder.total_receivable).label("accounts_receivable"),  # 应收
        func.sum(ErpOrder.discount).label("discount"),  # 优惠
        func.sum(ErpOrder.refund).label("refund_money"),  # 退款
        func.sum(ErpOrder.total_income).label("course_income_receipt"),  # 实收
    ]
    
    stmt = (
        select(*selects)
        .select_from(ErpOrder)
        .join(ErpOrderStudent, ErpOrder.order_student_id == ErpOrderStudent.id)
        .where(
            and_(
                ErpOrderStudent.class_id.in_(class_ids),
                ErpOrder.order_class_type == 1,  # 课程类型
                ErpOrder.disable == 0,
                ErpOrderStudent.disable == 0,
            )
        )
        .group_by(ErpOrderStudent.class_id)
    )
    
    result = await db.execute(stmt)
    records = result.fetchall()
    
    # 转换为字典格式 {class_id: {stats}}
    stats = {}
    for record in records:
        stats[record.class_id] = {
            "accounts_receivable": float(record.accounts_receivable) if record.accounts_receivable else 0,
            "discount": float(record.discount) if record.discount else 0,
            "refund_money": float(record.refund_money) if record.refund_money else 0,
            "course_income_receipt": float(record.course_income_receipt) if record.course_income_receipt else 0,
        }
    
    return stats

async def get_class_lecture_fees(db, class_ids):
    """获取班级的讲义费收入"""
    if not class_ids:
        return {}
    
    # 获取班级对应的学生ID列表
    student_query = (
        select(ErpOrderStudent.stu_id)
        .where(
            and_(
                ErpOrderStudent.class_id.in_(class_ids),
                ErpOrderStudent.student_state == StudentState.NORMAL.value,  # 正常状态
                ErpOrderStudent.disable == 0,
                ErpOrderStudent.order_class_type == 1  # 课程类型
            )
        )
    )
    
    result = await db.execute(student_query)
    student_ids = [record[0] for record in result.fetchall()]
    
    if not student_ids:
        return {class_id: 0 for class_id in class_ids}
    
    # 查询学生的讲义费订单
    lecture_fee_query = (
        select(
            ErpOrderStudent.class_id,
            func.sum(ErpOrder.total_income).label("lecture_fee_income")
        )
        .select_from(ErpOrder)
        .join(ErpOrderStudent, ErpOrder.order_student_id == ErpOrderStudent.id)
        .where(
            and_(
                ErpOrderStudent.stu_id.in_(student_ids),
                ErpOrder.order_class_type == 3,  # 讲义类型
                ErpOrder.disable == 0,
                ErpOrderStudent.disable == 0,
            )
        )
        .group_by(ErpOrderStudent.class_id)
    )
    
    result = await db.execute(lecture_fee_query)
    records = result.fetchall()
    
    # 转换为字典格式 {class_id: lecture_fee_income}
    lecture_fees = {class_id: 0 for class_id in class_ids}  # 默认值为0
    for record in records:
        if record.class_id in class_ids:
            lecture_fees[record.class_id] = float(record.lecture_fee_income) if record.lecture_fee_income else 0
    
    return lecture_fees

async def get_class_consumption_stats(db, class_ids):
    """获取班级的课消金额统计"""
    if not class_ids:
        return {}
    
    # 查询班级的订单数据和课时数据
    class_data = {}
    for class_id in class_ids:
        # 获取班级的所有订单
        order_query = (
            select(
                ErpOrder.id.label("order_id"),
                ErpOrder.total_income.label("income"),
                ErpOrderStudent.total_hours.label("total_hours"),
                ErpOrderStudent.complete_hours.label("complete_hours")
            )
            .select_from(ErpOrder)
            .join(ErpOrderStudent, ErpOrder.order_student_id == ErpOrderStudent.id)
            .where(
                and_(
                    ErpOrderStudent.class_id == class_id,
                    ErpOrder.order_class_type == 1,  # 课程类型
                    ErpOrder.disable == 0,
                    ErpOrderStudent.disable == 0,
                )
            )
        )
        
        result = await db.execute(order_query)
        orders = result.fetchall()
        
        # 计算已课消和未课消金额
        class_consumption_amount = 0  # i已课消金额
        un_consumption_amount = 0  # 未课消金额
        
        for order in orders:
            if order.total_hours > 0:
                # 单课节价值
                per_lesson_value = order.income / order.total_hours
                
                # 已课消金额
                consumed = per_lesson_value * order.complete_hours
                class_consumption_amount += consumed
                
                # 未课消金额
                un_consumed = per_lesson_value * (order.total_hours - order.complete_hours)
                un_consumption_amount += un_consumed
        
        class_data[class_id] = {
            "class_consumption_amount": round(class_consumption_amount, 2),
            "un_consumption_amount": round(un_consumption_amount, 2)
        }
    
    return class_data

async def get_class_other_expenses(db, class_ids):
    """获取班级的其他支出（运营成本）
    
    为费用类型为TagType.OTHER_EXPENSE 的费用类型的总和/班级数量
    """
    if not class_ids:
        return {}
    
    # 示例：简单地为每个班级分配固定的其他支出
    # 实际情况应当根据校区、教室、管理费用等多方面因素分摊计算
    default_expense = 1234  # 示例：默认每个班级的其他支出为1000元
    
    # 查询班级的学生数量作为分摊系数
    student_counts = await get_class_student_count(db, class_ids)
    
    # 计算其他支出 (示例逻辑：基础费用 + 按学生数量增加)
    other_expenses = {}
    for class_id in class_ids:
        student_count = student_counts.get(class_id, 0)
        # 示例计算公式：基础费用 + 每个学生100元管理费
        expense = default_expense + student_count * 100
        other_expenses[class_id] = expense
    
    return other_expenses

async def finance_simple_report_crud(db: AsyncSession, yyyy: int):
    """
    按标签大类查询财务简易报表数据
    
    Args:
        db: 数据库会话
        yyyy: 查询的年份，如2023
        
    Returns:
        tuple: (费用类型列表, 收据明细列表, 退费明细列表)
    """
    # 获取费用类型, 计入报表(add_finance=1)
    cost_type_objs = await erp_finance_cost_type.get_many(db, condition={"add_finance": 1})
    
    # 准备查询当年的数据
    start_date = datetime(yyyy, 1, 1)
    end_date = datetime(yyyy + 1, 1, 1)
    
    # 查询当年的收据明细
    receipt_details = await erp_receipt_detail.get_many(db, raw=[
        and_(
            ErpReceiptDetail.create_time >= start_date,
            ErpReceiptDetail.create_time < end_date,
            ErpReceiptDetail.cost_type_id != None,
            ErpReceiptDetail.item_total_price != None
        )
    ])
    
    # 查询当年的退费明细
    refund_details = await erp_order_refund_detail.get_many(db, raw=[
        and_(
            ErpOrderRefundDetail.create_time >= start_date,
            ErpOrderRefundDetail.create_time < end_date,
            ErpOrderRefundDetail.cost_type_id != None,
            ErpOrderRefundDetail.refund_money != None
        )
    ])
    
    return cost_type_objs, receipt_details, refund_details

async def get_special_cost_type_32_data(db: AsyncSession, yyyy: int):
    """
    获取费用类型32(预收)的特殊处理数据
    
    Args:
        db: 数据库会话
        yyyy: 查询的年份
        
    Returns:
        dict: 按月份、季度等时间维度组织的预收数据
    """
    from models.m_class import ErpClassPlan, ErpClass
    from models.m_order import ErpOrderStudent, ErpOrder
    
    # 初始化结果字典
    result = {
        "january": 0, "february": 0, "march": 0, "quarter1": 0,
        "april": 0, "may": 0, "june": 0, "quarter2": 0, "half_year1": 0,
        "july": 0, "august": 0, "september": 0, "quarter3": 0,
        "october": 0, "november": 0, "december": 0, "quarter4": 0, "half_year2": 0,
        "annual": 0, "total": 0
    }
    
    # 获取当年的所有班级计划
    start_date = datetime(yyyy, 1, 1)
    end_date = datetime(yyyy + 1, 1, 1)
    
    # 查询当年所有的班级课节计划
    class_plans = await db.execute(
        select(ErpClassPlan)
        .where(
            and_(
                ErpClassPlan.start_time >= start_date,
                ErpClassPlan.start_time < end_date,
                ErpClassPlan.disable == 0
            )
        )
    )
    class_plans = class_plans.scalars().all()
    
    # 按月份分组班级计划
    monthly_class_plans = {
        1: [], 2: [], 3: [], 4: [], 5: [], 6: [],
        7: [], 8: [], 9: [], 10: [], 11: [], 12: []
    }
    
    for plan in class_plans:
        month = plan.start_time.month
        monthly_class_plans[month].append(plan)
    
    # 处理每个月的数据
    for month, plans in monthly_class_plans.items():
        if not plans:
            continue
            
        # 获取班级ID列表
        class_ids = list(set([plan.class_id for plan in plans]))
        
        # 获取相关的订单学生记录
        order_students = await db.execute(
            select(ErpOrderStudent)
            .where(
                and_(
                    ErpOrderStudent.class_id.in_(class_ids),
                    ErpOrderStudent.disable == 0
                )
            )
        )
        order_students = order_students.scalars().all()
        
        # 计算每个月的预收
        month_income = 0
        
        for os in order_students:
            # 需要处理转班情况
            original_os = os
            processed_ids = set()  # 防止循环引用
            
            while os.student_state == 4 and os.p_id > 0 and os.p_id not in processed_ids:
                processed_ids.add(os.p_id)
                parent_os = await db.execute(
                    select(ErpOrderStudent)
                    .where(ErpOrderStudent.id == os.p_id)
                )
                parent_os = parent_os.scalar_one_or_none()
                if parent_os:
                    os = parent_os
                else:
                    break
            
            # 获取订单信息
            order = await db.execute(
                select(ErpOrder)
                .where(ErpOrder.order_student_id == os.id)
            )
            order = order.scalar_one_or_none()
            
            if not order:
                continue
                
            # 获取该班级在本月的课节数
            class_plan_count = len([p for p in plans if p.class_id == original_os.class_id])
            
            # 计算本月预收
            if order.buy_num > 0:
                month_revenue = (order.total_income / order.buy_num) * class_plan_count
                month_income += month_revenue
        
        # 更新月度数据
        month_mapping = {
            1: "january", 2: "february", 3: "march", 
            4: "april", 5: "may", 6: "june",
            7: "july", 8: "august", 9: "september", 
            10: "october", 11: "november", 12: "december"
        }
        result[month_mapping[month]] = month_income
    
    # 计算季度和半年数据
    result["quarter1"] = result["january"] + result["february"] + result["march"]
    result["quarter2"] = result["april"] + result["may"] + result["june"]
    result["quarter3"] = result["july"] + result["august"] + result["september"]
    result["quarter4"] = result["october"] + result["november"] + result["december"]
    
    result["half_year1"] = result["quarter1"] + result["quarter2"]
    result["half_year2"] = result["quarter3"] + result["quarter4"]
    
    # 计算年度数据
    result["annual"] = result["half_year1"] + result["half_year2"]
    result["total"] = result["annual"]
    
    return result

async def get_special_cost_type_33_data(db: AsyncSession, yyyy: int):
    """
    获取费用类型33(实际课程收入)的特殊处理数据
    
    Args:
        db: 数据库会话
        yyyy: 查询的年份
        
    Returns:
        dict: 按月份、季度等时间维度组织的实际课程收入数据
    """
    from models.m_class import ErpClassPlan, ErpClass, ErpClassChecking
    from models.m_order import ErpOrderStudent, ErpOrder
    
    # 初始化结果字典
    result = {
        "january": 0, "february": 0, "march": 0, "quarter1": 0,
        "april": 0, "may": 0, "june": 0, "quarter2": 0, "half_year1": 0,
        "july": 0, "august": 0, "september": 0, "quarter3": 0,
        "october": 0, "november": 0, "december": 0, "quarter4": 0, "half_year2": 0,
        "annual": 0, "total": 0
    }
    
    # 获取当年的所有班级计划和签到记录
    start_date = datetime(yyyy, 1, 1)
    end_date = datetime(yyyy + 1, 1, 1)
    
    # 查询当年所有的班级课节计划
    class_plans = await db.execute(
        select(ErpClassPlan)
        .where(
            and_(
                ErpClassPlan.start_time >= start_date,
                ErpClassPlan.start_time < end_date,
                ErpClassPlan.disable == 0
            )
        )
    )
    class_plans = class_plans.scalars().all()
    
    # 查询当年所有的签到记录
    class_checkings = await db.execute(
        select(ErpClassChecking)
        .where(
            and_(
                ErpClassChecking.create_time >= start_date,
                ErpClassChecking.create_time < end_date,
                ErpClassChecking.check_status != 2,  # 不为缺勤
                ErpClassChecking.disable == 0
            )
        )
    )
    class_checkings = class_checkings.scalars().all()
    
    # 按月份分组签到记录
    monthly_checkings = {
        1: [], 2: [], 3: [], 4: [], 5: [], 6: [],
        7: [], 8: [], 9: [], 10: [], 11: [], 12: []
    }
    
    for checking in class_checkings:
        month = checking.create_time.month
        monthly_checkings[month].append(checking)
    
    # 处理每个月的数据
    for month, checkings in monthly_checkings.items():
        if not checkings:
            continue
            
        # 按订单学生ID和班级分组签到记录
        student_class_checkings = {}
        for checking in checkings:
            key = (checking.order_student_id, checking.class_id)
            if key not in student_class_checkings:
                student_class_checkings[key] = []
            student_class_checkings[key].append(checking)
        
        # 计算每个月的实际收入
        month_income = 0
        
        for (order_student_id, class_id), stu_checkings in student_class_checkings.items():
            # 直接获取订单学生记录
            order_student = await db.execute(
                select(ErpOrderStudent)
                .where(
                    and_(
                        ErpOrderStudent.id == order_student_id,
                        ErpOrderStudent.class_id == class_id,
                        ErpOrderStudent.disable == 0
                    )
                )
            )
            order_student = order_student.scalar_one_or_none()
            
            if not order_student:
                continue
            
            # 需要处理转班情况
            original_os = order_student
            processed_ids = set()  # 防止循环引用
            
            while order_student.student_state == 4 and order_student.p_id > 0 and order_student.p_id not in processed_ids:
                processed_ids.add(order_student.p_id)
                parent_os = await db.execute(
                    select(ErpOrderStudent)
                    .where(ErpOrderStudent.id == order_student.p_id)
                )
                parent_os = parent_os.scalar_one_or_none()
                if parent_os:
                    order_student = parent_os
                else:
                    break
            
            # 获取订单信息
            order = await db.execute(
                select(ErpOrder)
                .where(ErpOrder.order_student_id == order_student.id)
            )
            order = order.scalar_one_or_none()
            
            if not order or order.buy_num == 0:
                continue
                
            # 计算本月实际收入 = (订单总金额/购买总课时) * 本月实际上课课时
            actual_class_count = len(stu_checkings)
            class_unit_price = order.total_income / order.buy_num
            month_revenue = class_unit_price * actual_class_count
            month_income += month_revenue
        
        # 更新月度数据
        month_mapping = {
            1: "january", 2: "february", 3: "march", 
            4: "april", 5: "may", 6: "june",
            7: "july", 8: "august", 9: "september", 
            10: "october", 11: "november", 12: "december"
        }
        result[month_mapping[month]] = month_income
    
    # 计算季度和半年数据
    result["quarter1"] = result["january"] + result["february"] + result["march"]
    result["quarter2"] = result["april"] + result["may"] + result["june"]
    result["quarter3"] = result["july"] + result["august"] + result["september"]
    result["quarter4"] = result["october"] + result["november"] + result["december"]
    
    result["half_year1"] = result["quarter1"] + result["quarter2"]
    result["half_year2"] = result["quarter3"] + result["quarter4"]
    
    # 计算年度数据
    result["annual"] = result["half_year1"] + result["half_year2"]
    result["total"] = result["annual"]
    
    return result

async def get_special_cost_type_32_detail(db: AsyncSession, month: int, yyyy: int):
    """
    获取费用类型32(预收)的特殊处理明细数据
    
    Args:
        db: 数据库会话
        month: 月份(1-12)
        yyyy: 查询的年份
        
    Returns:
        dict: 费用类型32的明细数据
    """
    from datetime import datetime
    from models.m_class import ErpClassPlan, ErpClass
    from models.m_finance import ErpFinanceCostType
    from decimal import Decimal, ROUND_HALF_UP
    
    # 查询费用类型信息
    cost_type = await db.execute(
        select(ErpFinanceCostType).where(ErpFinanceCostType.id == 32)
    )
    cost_type = cost_type.scalar_one_or_none()
    
    cost_type_info = {
        "id": cost_type.id if cost_type else 32,
        "name": cost_type.name if cost_type else "预收",
        "parent_id": cost_type.parent_id if cost_type else 0
    }
    
    # 准备查询月份的数据
    start_date = datetime(yyyy, month, 1)
    if month == 12:
        end_date = datetime(yyyy + 1, 1, 1)
    else:
        end_date = datetime(yyyy, month + 1, 1)
    
    # 查询当月所有的班级课节计划
    class_plans_query = (
        select(
            ErpClassPlan, 
            ErpClass.class_name,
            ErpClass.id.label("class_id")
        )
        .join(ErpClass, ErpClassPlan.class_id == ErpClass.id)
        .where(
            and_(
                ErpClassPlan.start_time >= start_date,
                ErpClassPlan.start_time < end_date,
                ErpClassPlan.disable == 0
            )
        )
    )
    
    class_plans_result = await db.execute(class_plans_query)
    class_plans_raw = class_plans_result.all()
    
    class_plans_data = []
    total_amount = Decimal('0.00')
    
    # 处理班级课节计划数据
    for row in class_plans_raw:
        class_plan = row[0]
        class_name = row[1]
        class_id = row[2]
        
        # 查询该班级的学生订单
        order_students_query = (
            select(
                ErpOrderStudent,
                ErpOrder.order_state,
                ErpStudent.stu_name
            )
            .join(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
            .join(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
            .where(
                and_(
                    ErpOrderStudent.class_id == class_id,
                    ErpOrderStudent.disable == 0,
                    ErpOrder.order_state.in_([1])  # 订单状态为已付款、部分退款、已完成
                )
            )
        )
        
        order_students_result = await db.execute(order_students_query)
        order_students = order_students_result.all()
        
        # 过滤掉已经转班的学生
        valid_order_students = []
        for os_row in order_students:
            order_student = os_row[0]
            # 检查该学生是否有有效的转班记录
            # (如果需要在这里加入检查转班的逻辑)
            valid_order_students.append(os_row)
        
        # 计算每个学生的课节单价
        for os_row in valid_order_students:
            order_student = os_row[0]
            order_state = os_row[1]
            stu_name = os_row[2]
            
            # 计算单价
            unit_price = Decimal('0.00')
            if hasattr(order_student, 'unit_price') and order_student.unit_price:
                unit_price = Decimal(str(order_student.unit_price))
            elif order_student.total_hours and order_student.total_hours > 0:
                # 获取订单信息
                order_query = select(ErpOrder.lesson_price, ErpOrder.total_income).where(ErpOrder.order_student_id == order_student.id)
                order_result = await db.execute(order_query)
                order = order_result.first()
                
                if order and order.lesson_price:
                    unit_price = Decimal(str(order.lesson_price))
                elif order and order.total_income and order_student.total_hours > 0:
                    unit_price = Decimal(str(order.total_income)) / Decimal(str(order_student.total_hours))
            
            unit_price = unit_price.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            total_amount += unit_price
            
            # 构建课节计划明细数据
            class_plans_data.append({
                "id": class_plan.id,
                "class_id": class_id,
                "class_name": class_name,
                "plan_time": class_plan.start_time,
                "order_student_id": order_student.id,
                "order_state": order_state,
                "stu_name": stu_name,
                "unit_price": float(unit_price)
            })
    
    return {
        "cost_type": cost_type_info,
        "total_amount": float(total_amount),
        "class_plans": class_plans_data
    }

async def get_special_cost_type_33_detail(db: AsyncSession, month: int, yyyy: int):
    """
    获取费用类型33(实际课程收入)的特殊处理明细数据
    
    Args:
        db: 数据库会话
        month: 月份(1-12)
        yyyy: 查询的年份
        
    Returns:
        dict: 费用类型33的明细数据
    """
    from datetime import datetime
    from models.m_class import ErpClassPlan, ErpClass, ErpClassChecking
    from models.m_order import ErpOrderStudent, ErpOrder
    from models.m_finance import ErpFinanceCostType
    from decimal import Decimal, ROUND_HALF_UP
    
    # 查询费用类型信息
    cost_type = await db.execute(
        select(ErpFinanceCostType).where(ErpFinanceCostType.id == 33)
    )
    cost_type = cost_type.scalar_one_or_none()
    
    cost_type_info = {
        "id": cost_type.id if cost_type else 33,
        "name": cost_type.name if cost_type else "实际课程收入",
        "parent_id": cost_type.parent_id if cost_type else 0
    }
    
    # 准备查询月份的数据
    start_date = datetime(yyyy, month, 1)
    if month == 12:
        end_date = datetime(yyyy + 1, 1, 1)
    else:
        end_date = datetime(yyyy, month + 1, 1)
    
    # 查询当月所有的签到记录(不包括缺勤)
    class_checkings_query = (
        select(
            ErpClassChecking,
            ErpClass.class_name,
            ErpClassPlan.start_time,
            ErpStudent.stu_name,
            ErpOrder.lesson_price,
            ErpOrderStudent.total_hours,
            ErpOrder.total_income,
            ErpOrder.order_state
        )
        .join(ErpClassPlan, ErpClassChecking.class_plan_id == ErpClassPlan.id)
        .join(ErpClass, ErpClassPlan.class_id == ErpClass.id)
        .join(ErpOrderStudent, ErpClassChecking.order_student_id == ErpOrderStudent.id)
        .join(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
        .join(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
        .where(
            and_(
                ErpClassPlan.start_time >= start_date,
                ErpClassPlan.start_time < end_date,
                ErpClassChecking.check_status != 2,  # 不为缺勤
                ErpClassChecking.disable == 0
            )
        )
    )
    
    class_checkings_result = await db.execute(class_checkings_query)
    class_checkings_raw = class_checkings_result.all()
    
    class_checkings_data = []
    total_amount = Decimal('0.00')
    
    # 处理签到记录数据
    for row in class_checkings_raw:
        checking = row[0]
        class_name = row[1]
        plan_time = row[2]
        stu_name = row[3]
        lesson_price = row[4]
        total_hours = row[5]
        total_income = row[6]
        order_state = row[7]
        
        # 计算单价
        price = Decimal('0.00')
        if lesson_price:
            price = Decimal(str(lesson_price))
        elif total_hours and total_hours > 0 and total_income:
            price = Decimal(str(total_income)) / Decimal(str(total_hours))
        
        price = price.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        total_amount += price
        
        # 构建签到记录明细数据
        class_checkings_data.append({
            "id": checking.id,
            "class_id": checking.class_id,
            "class_name": class_name,
            "plan_time": plan_time,
            "check_time": checking.create_time,
            "order_student_id": checking.order_student_id,
            "order_state": order_state,
            "stu_name": stu_name,
            "check_status": checking.check_status,  # 1:正常，3:补签，4:请假
            "unit_price": float(price)
        })
    
    return {
        "cost_type": cost_type_info,
        "total_amount": float(total_amount),
        "class_checkings": class_checkings_data
    }

async def get_performance_records(db, account_id=None, yyyy=None, count=False):
    """获取绩效评级记录"""
    from models.m_finance import ErpPerformanceRecords
    from models.models import ErpAccount, ErpDepartment, ErpAccountDepartment

    selects = [
        ErpPerformanceRecords.id,
        ErpPerformanceRecords.account_id,
        ErpPerformanceRecords.yyyy,
        ErpPerformanceRecords.quarter,
        ErpPerformanceRecords.rating,
        ErpPerformanceRecords.create_time,
        ErpPerformanceRecords.update_time,
        ErpAccount.employee_name,
        ErpAccount.employee_number,
        ErpAccount.employee_status,
        ErpAccount.employee_type,
        ErpAccount.qy_wechat_position,
        ErpAccount.avatar,
        ErpAccount.qy_wechat_department,
    ]
    
    conditions = [
        ErpPerformanceRecords.disable == 0,
    ]
    
    if account_id:
        conditions.append(ErpPerformanceRecords.account_id == account_id)
    
    if yyyy:
        conditions.append(ErpPerformanceRecords.yyyy == yyyy)
    
    if count:
        count_stmt = (
            select(func.count(ErpPerformanceRecords.id))
            .select_from(ErpPerformanceRecords)
            .outerjoin(ErpAccount, ErpAccount.id == ErpPerformanceRecords.account_id)
            .where(and_(*conditions))
        )
        result = await db.execute(count_stmt)
        return result.scalar()
    
    stmt = (
        select(*selects)
        .select_from(ErpPerformanceRecords)
        .outerjoin(ErpAccount, ErpAccount.id == ErpPerformanceRecords.account_id)
        .where(and_(*conditions))
        .order_by(ErpPerformanceRecords.yyyy.desc(), ErpPerformanceRecords.quarter.desc())
    )
    
    result = await db.execute(stmt)
    return result.fetchall()



# 教师课时费基本表
async def get_teacher_class_fee_module(db: AsyncSession, page=None, page_size=None, keyword=None, count=False):
    """
    获取教师课时费基本表
    """
    selects = [
        ErpAccountTeacher.id,
        ErpAccountTeacher.account_id,
        ErpAccountTeacher.class_fee_type,
        ErpAccountTeacher.class_fee,
        ErpAccount.employee_name.label("teacher_name"),
        ErpAccount.employee_number,
        ErpAccount.avatar,
        ErpAccount.employee_status,
        ErpAccount.employee_type,
        ErpAccount.qy_wechat_position,
        ErpAccountTeacher.teacher_avatar,
        ErpJobLevel.level_name,
    ]
    
    conditions = [
        ErpAccountTeacher.disable == 0,
        ErpAccount.disable == 0,
    ]
    
    if keyword:
        conditions.append(ErpAccount.employee_name.like(f"%{keyword}%"))
    
    if count:
        count_stmt = (select(func.count(ErpAccountTeacher.id))
                      .select_from(ErpAccountTeacher)
                      .outerjoin(ErpAccount, ErpAccount.id == ErpAccountTeacher.account_id)
                      .where(and_(*conditions)))
        result = await db.execute(count_stmt)
        return result.scalar()
    
    stmt = (select(*selects)
            .select_from(ErpAccountTeacher)
            .outerjoin(ErpAccount, ErpAccount.id == ErpAccountTeacher.account_id)
            .outerjoin(ErpJobLevel, ErpJobLevel.id == ErpAccount.level_id)
            .where(and_(*conditions)))
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    
    result = await db.execute(stmt)
    return result.fetchall()


async def adjust_teacher_class_fee_crud(db: AsyncSession, teacher_id: int, class_fee: float, 
                                        class_fee_type: int, user_id: int):
    """
    调整教师课时费
    """
    # 获取教师信息
    teacher = await erp_account_teacher.get_by_id(db, teacher_id)
    if not teacher:
        return None, None, None, "教师信息不存在"
    
    # 获取旧的课时费信息
    old_class_fee_type = teacher.class_fee_type
    old_class_fee = teacher.class_fee
    
    # 更新教师课时费
    teacher.class_fee = class_fee
    teacher.class_fee_type = class_fee_type
    teacher.update_time = datetime.now(settings.TIME_ZONE)
    
    # 返回成功的教师信息和日志内容
    return teacher, old_class_fee_type, old_class_fee, None


async def get_teacher_class_fee_log_crud(db: AsyncSession, teacher_id: int, page=None, page_size=None):
    """
    获取教师课时费日志
    """
    # 查询条件
    conditions = {
        "teacher_id": teacher_id,
        "disable": 0
    }
    
    # 获取日志总数
    log_count = await erp_account_teacher_log.count(db, conditions)
    
    # 获取日志列表
    if page and page_size:
        logs = await erp_account_teacher_log.get_many_with_pagination(db, page, page_size, conditions, reverse=True)
    else:
        logs = await erp_account_teacher_log.get_many(db, conditions, reverse=True)
    
    # 获取创建人和更新人信息
    for log in logs:
        if log.create_by:
            creator = await erp_account.get_by_id(db, log.create_by)
            if creator:
                log.creator_name = creator.employee_name
            else:
                log.creator_name = "未知"
        else:
            log.creator_name = "未知"
            
        if log.update_by:
            updater = await erp_account.get_by_id(db, log.update_by)
            if updater:
                log.updater_name = updater.employee_name
            else:
                log.updater_name = "未知"
        else:
            log.updater_name = "未知"
    
    return {
        "count": log_count,
        "data": logs
    }

