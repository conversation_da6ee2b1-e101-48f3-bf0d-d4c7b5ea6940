from typing import Optional
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app_finance.crud import crud_bill_account_log, stu_e_wallet_with_page, get_e_wallet_total
from app_finance.modules import bill_account_change, stu_ewallet_change
from app_finance.serializer import BankAccountTypeCreate, FinanceRoleCreate, StudentWalletAdjustment, BankAccountCreate
from models.m_finance import ErpBankAccountLog, ErpBankAccount, ErpBankAccountType, ErpFinanceRole, ErpStudentEwalletLog
from models.m_mall import MallMerchantConfig
from models.m_student import ErpStudent
from models.models import ErpAccount
from settings import logger, CF
from utils.db.account_handler import get_current_active_user, UserDict
from utils.db.db_handler import get_default_db
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

erp_bank_account_log = CF.get_crud(ErpBankAccountLog)
erp_bank_account = CF.get_crud(ErpBankAccount)
ERP_STUDENT = CF.get_crud(ErpStudent)
erp_account = CF.get_crud(ErpAccount)
erp_finance_role = CF.get_crud(ErpFinanceRole)
mall_merchant_config = CF.get_crud(MallMerchantConfig)
erp_student_ewallet_log = CF.get_crud(ErpStudentEwalletLog)
erp_bank_account_type = CF.get_crud(ErpBankAccountType)


router = APIRouter(prefix="/finance_account", tags=["财务账户管理"])


# 查询银行账户类型
@router.get(f"/bank_account_type")
async def get_bank_account_type(
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 银行账户类型
    """
    objs = await erp_bank_account_type.get_many(db)
    return await ApiSuccessResponse(objs)

# 新增银行账户类型
@router.post(f"/bank_account_type")
async def create_bank_account_type(
        entity: BankAccountTypeCreate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 新增 银行账户类型
    """
    obj = await erp_bank_account_type.create(db, commit=True, **{
        "account_type_name": entity.account_type_name,
        "is_public": entity.is_public,
        "is_virtual": entity.is_virtual,
        "create_by": user.uid,
        "update_by": user.uid,
    })
    return await ApiSuccessResponse(obj)


# 更新银行账户类型
@router.put("/bank_account_type/{bank_account_type_id}")
async def update_bank_account_type(
        bank_account_type_id: int,
        entity: BankAccountTypeCreate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 更新 银行账户类型
    """
    exist_obj = await erp_bank_account_type.get_by_id(db, bank_account_type_id)
    if not exist_obj:
        return await ApiFailedResponse("银行账户类型不存在")
    if entity.account_type_name and entity.account_type_name != exist_obj.account_type_name:
        exist_obj.account_type_name = entity.account_type_name
    if entity.is_public != exist_obj.is_public:
        exist_obj.is_public = entity.is_public
    if entity.is_virtual != exist_obj.is_virtual:
        exist_obj.is_virtual = entity.is_virtual
    exist_obj.update_by = user.uid
    await db.commit()
    return await ApiSuccessResponse(True)

# 删除银行账户类型
@router.delete("/bank_account_type/{bank_account_type_id}")
async def delete_bank_account_type(
        bank_account_type_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除 银行账户类型
    """
    obj = await erp_bank_account_type.delete_one(db, bank_account_type_id)
    return await ApiSuccessResponse(obj)



# 创建账户
@router.post(f"/bill_account")
async def create_bill_account(
        entity: BankAccountCreate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 新增 账户信息
    - campus_id: 校区id
    - account_alias: 账户别名
    - account_type: /bank_account_type接口的id
    - default_account_type: 默认账户类型，1 公账 2 现金 3 虚拟账户
    - account_number: 账户号
    - bank_name: 银行
    - bank_sub_name: 开户支行
    - account_holder: 持有人
    - balance: 余额， 虚拟账户初始默认为0， 只能划转进去
    - currency_type: 1 人民币
    - is_active: 是否启用收款
    - cmb_merchant_id: 招行商户号
    """
    balance = entity.balance
    if entity.account_type == 3:
        balance = 0

    obj = await erp_bank_account.create(db, commit=True, **{
        "campus_id": entity.campus_id,
        "account_alias": entity.account_alias,
        "account_type": entity.account_type,
        "default_account_type": entity.default_account_type,
        "account_number": entity.account_number,
        "bank_name": entity.bank_name,
        "bank_sub_name": entity.bank_sub_name,
        "account_holder": entity.account_holder,
        "balance": balance,
        "currency_type": entity.currency_type,
        "is_active": entity.is_active,
        "cmb_merchant_id": entity.cmb_merchant_id,
    })
    return await ApiSuccessResponse(obj)

# 启用收款商户
@router.post("/choice_cmb_merchant_id")
async def enable_cmb_merchant_id(
        cmb_merchant_id: str,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    # 查询是否存在
    obj = await erp_bank_account.get_one(db, cmb_merchant_id=cmb_merchant_id)
    if not obj:
        return await ApiFailedResponse("账户不存在")
    # 更新账户表
    bank_acccounts = await erp_bank_account.get_many(db)
    for bank_account in bank_acccounts:
        if bank_account.cmb_merchant_id == cmb_merchant_id:
            bank_account.is_active = 1
        else:
            bank_account.is_active = 0
    # 更新商户表
    mall_merchant_configs = await mall_merchant_config.get_many(db)
    for mc in mall_merchant_configs:
        if mc.CmbMerId == cmb_merchant_id:
            mc.Enable = 1
        else:
            mc.Enable = 0
    await db.commit()
    return await ApiSuccessResponse(True)



# 根据cmb_merchant_id查询账户
@router.get("/cmb_merchant_id/{cmb_merchant_id}")
async def get_cmb_merchant_id( 
        cmb_merchant_id: str,
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 账户信息
    """
    mall_merchant_configs = await mall_merchant_config.get_many(db, {"CmbMerId": cmb_merchant_id})
    if not mall_merchant_configs:
        return await ApiFailedResponse("商户不存在")
    
    return await ApiSuccessResponse(mall_merchant_configs)



# 删除账户
@router.delete(f"/bill_account")
async def delete_bill_account(
        bill_account_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除 账户信息
    """
    obj = await erp_bank_account.delete_one(db, bill_account_id)
    return await ApiSuccessResponse(obj)




# 分页查询银行账户
@router.get(f"/bill_account")
async def get_bill_account(
        page: int = None,
        page_size: int = None,
        default_account_type: Optional[int] = None,
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 账户信息
    """
    data = await erp_bank_account.get_many_with_pagination(db, page, page_size, {
        "default_account_type": default_account_type
    })
    count_data = await erp_bank_account.get_many(db, {
        "default_account_type": default_account_type
    })
    # 附加账户类型
    account_type_ids = [obj.account_type for obj in data]
    account_types = await erp_bank_account_type.get_many(db, raw=[
        ErpBankAccountType.id.in_(account_type_ids)
    ])
    account_type_map = {item.id: item for item in account_types}
    for obj in data:
        obj.account_type_name = account_type_map.get(obj.account_type).account_type_name
    return await ApiSuccessResponse({
        "data": data,
        "total": len(count_data)
    })


# 分页查询学员电子钱包
@router.get(f"/student_wallet")
async def get_student_wallet(
        page: int = None,
        page_size: int = None,
        keyword: Optional[str] = None,
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 学员电子钱包
    """
    data = await stu_e_wallet_with_page(db, page, page_size, keyword=keyword)
    count = await stu_e_wallet_with_page(db, keyword=keyword, count=True)
    return await ApiSuccessResponse({
        "data": data,
        "total": count
    })


# 学员电子钱包调整
@router.put("/student_wallet/{stu_id}")
async def student_wallet_adjustment(
        stu_id: int,
        entity: StudentWalletAdjustment,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 学员电子钱包调整
    - bill_account_id: 账户id
    - change_type: 1 增加 2 减少
    - amount: 数量
    - desc: 描述
    """
    change_type = entity.change_type
    amount = entity.amount
    desc = entity.desc

    return await stu_ewallet_change(db, stu_id, change_type, amount, user.uid, desc, commit=True)



# 查询学员电子钱包变动记录
@router.get("/student_wallet_log")
async def get_student_wallet_log(
        stu_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询学员电子钱包变动记录
    """
    data = await erp_student_ewallet_log.get_many(db, {"stu_id": stu_id})
    # 附加创建人
    account_ids = [obj.create_by for obj in data]
    accounts = await erp_account.get_many(db, raw=[
        ErpAccount.id.in_(account_ids)
    ])
    account_map = {item.id: item for item in accounts}
    for obj in data:
        obj.create_by_name = account_map.get(obj.create_by).employee_name if account_map.get(obj.create_by) else ""
    return await ApiSuccessResponse(data)

# 分页查询账户详情，包含账户变动记录
@router.get(f"/bill_account_detail")
async def get_bill_account_detail(
        bill_account_id: int,
        # account_type: int = 1,
        page: int = None,
        page_size: int = None,
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 账户分页详情
    """
    account = await erp_bank_account.get_by_id(db, bill_account_id)
    if not account:
        return await ApiFailedResponse("账户不存在")
    conditions = {"bill_account_id": bill_account_id}
    data = await erp_bank_account_log.get_many_with_pagination(db, page, page_size, condition=conditions)
    # if account_type == 1:
    accounts = await erp_account.get_many(db)
    account_dict = {item.id: item for item in accounts}
    for i in data:
        i.create_by_name = account_dict.get(i.create_by).employee_name if account_dict.get(i.create_by) else ""
    data.sort(key=lambda x: x.create_time, reverse=True)
    count_data = await erp_bank_account_log.get_many(db, conditions)
    return await ApiSuccessResponse({
        "account": account,
        "data": data,
        "total": len(count_data)
    })


# 查询电子钱包总额
@router.get(f"/wallet_total")
async def get_wallet_total(
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 电子钱包总额
    """
    total = await get_e_wallet_total(db, campus_id=1)
    return await ApiSuccessResponse(total)


# 学员电子钱包提现
@router.put(f"/student_wallet_withdraw")
async def student_wallet_withdraw(
        bill_account_id: int,
        amount: float,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 学员电子钱包提现
    ## 暂未加入流程控制
    - bill_account_id: 账户id
    - amount: 提现金额
    """
    desc = "学员电子钱包提现"
    change_type = 2
    return await bill_account_change(db, bill_account_id, change_type, amount, desc, user)


# 查询账户明细
@router.get(f"/bill_account_log")
async def query_bill_account_log(
        page: int = 1,
        page_size: int = 10,
        default_account_type: int = 1,
        bank_accout_id: int = None,
        begin_time: str = None,
        end_time: str = None,
        cost_type_ids: str = None,
        receipt_id: int = None,
        audit_state: int = None,
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 账户明细
    - default_account_type: 1 银行 2 现金 3 虚拟账户
    - cost_type_ids: 费用类型id，多个用逗号分隔 1,2,3
    - change_type: 1 增加 2 减少
    """
    cost_type_ids = cost_type_ids.split(",") if cost_type_ids else None
    data = await crud_bill_account_log(db, default_account_type,bank_accout_id,receipt_id,audit_state, begin_time, end_time, cost_type_ids,
                                       page=page, page_size=page_size, count=False)
    count = await crud_bill_account_log(db, default_account_type,bank_accout_id,receipt_id,audit_state, begin_time, end_time, cost_type_ids, count=True)
    return await ApiSuccessResponse({
        "data": data,
        "total": count
    })


# 增删改查财务角色的人员
@router.post(f"/finance_role")
async def create_finance_role(
        entity: FinanceRoleCreate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 新增 财务角色
    - type: 1 财务管理 2 出纳管理 
    - name: 角色名称
    - account_id: 人员id
    """
    obj = await erp_finance_role.create(db, commit=True, **entity.dict())
    return await ApiSuccessResponse(obj)


# 删除财务角色
@router.delete(f"/finance_role")
async def delete_finance_role(
        id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除 财务角色
    """
    obj = await erp_finance_role.delete_one(db, id)
    return await ApiSuccessResponse(obj)


# 查询财务角色
@router.get(f"/finance_role")
async def query_finance_role(
        type: int = None,
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 财务角色
    """
    conditions = {}
    if type:
        conditions["type"] = type
    objs = await erp_finance_role.get_many(db, conditions)
    # 附加账户名
    account_ids = [obj.account_id for obj in objs]
    accounts = await erp_account.get_many(db, raw=[
        ErpAccount.id.in_(account_ids)
    ])
    account_map = {item.id: item for item in accounts}
    for obj in objs:
        # obj.employee_name = account_map.get(obj.account_id).employee_name
        account = account_map.get(obj.account_id)
        obj.employee_name = account.employee_name if account else "未知用户"
    return await ApiSuccessResponse(objs)


# 更新财务角色
@router.put(f"/finance_role")
async def update_finance_role(
        id: int,
        entity: FinanceRoleCreate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 更新 财务角色
    """
    obj = await erp_finance_role.update_one(db, id, entity.dict())
    return await ApiSuccessResponse(obj)