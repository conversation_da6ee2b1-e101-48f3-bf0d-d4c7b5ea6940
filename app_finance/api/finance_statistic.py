import copy
from datetime import datetime, timedelta
from typing import List
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, func, update
from app_finance.modules import finance_income_report_module, finance_simple_report_module
from models.m_order import ErpOrderRefundDetail
from models.m_student import ErpStudent
from models.m_workflow import ErpPaymentObj, ErpReceiptDetail
import settings
from app_finance.crud import  finance_income_expense_detail_modules, finance_income_expense_modules,  original_refund_record
from app_finance.serializer import FinanceBudgetUpdate, ReRefundParams
from models.models import ErpEnterprise, ErpPublicSettings, ErpAccount, ErpSalaryBase
from models.m_finance import  ErpBankAccountReport, ErpFinanceBudget, ErpFinanceTradeRefund, ErpFinanceTradePayment, ErpFinanceCostType
from settings import logger, CF
from utils.db.account_handler import get_current_active_user, UserDict
from utils.db.db_handler import get_default_db, get_uat_db
from utils.db.model_handler import ModelDataHelper
from utils.other.config_handler import get_config
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse
from sqlalchemy import select
from models.m_mall import MallMerchantConfig

erp_enterprise = CF.get_crud(ErpEnterprise)
erp_public_settings = CF.get_crud(ErpPublicSettings)
erp_account = CF.get_crud(ErpAccount)
erp_finance_trade_refund = CF.get_crud(ErpFinanceTradeRefund)
erp_finance_trade_payment = CF.get_crud(ErpFinanceTradePayment)
erp_finance_cost_type = CF.get_crud(ErpFinanceCostType)

ERP_SALARY_BASE = CF.get_crud(ErpSalaryBase)
erp_finance_budget = CF.get_crud(ErpFinanceBudget)
erp_receipt_detail = CF.get_crud(ErpReceiptDetail)
erp_order_refund_detail = CF.get_crud(ErpOrderRefundDetail)
erp_payment_obj = CF.get_crud(ErpPaymentObj)
erp_student = CF.get_crud(ErpStudent)
erp_account= CF.get_crud(ErpAccount)
erp_bank_account_report = CF.get_crud(ErpBankAccountReport)

router = APIRouter(prefix="/finance_statistic", tags=["财务统计"])



# 分页原路退款查询
@router.get(f"/origin_trade_refund")
async def query_origin_trade_refund(
        page: int = 1,
        page_size: int = 10,
        stu_name: str = None,
        order_no: str = None,
        refund_order_no: str = None,
        trade_status: int = None,
        receipt_id: int = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),

):
    """
    # 退款查询
    - stu_name 学生姓名
    - order_no 订单号
    - refund_order_no 退款订单号
    - trade_status 退款状态 1 挂起 2 成功 3 失败 4 已关闭
    - receipt_id 收款单据ID
    
    ## 返回字段说明
    查询结果包含学生退款的具体课程信息：
    - class_name: 班级名称（课程类型）或讲义名称（讲义类型）
    - course_name: 课程名称（课程类型）或讲义名称（讲义类型）
    - order_class_type: 订单类型（1-课程，3-讲义）
    """
    data = await original_refund_record(db, page=page, page_size=page_size, stu_name=stu_name, order_no=order_no, refund_order_no=refund_order_no, trade_status=trade_status, receipt_id=receipt_id, count=False)
    count = await original_refund_record(db,  stu_name=stu_name, order_no=order_no, refund_order_no=refund_order_no, trade_status=trade_status, receipt_id=receipt_id, count=True)
    return await ApiSuccessResponse({
        "data": data,
        "total": count
    })



# 重新发起退款
@router.post(f"/re_refund")
async def re_refund(
    params: ReRefundParams,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 重新发起退款
    """
    # 查询退款单
    refund_obj = await erp_order_refund_detail.get_one(db, refund_order_no=params.refund_order_no, refund_state=0)
    if not refund_obj:
        return await ApiFailedResponse("退款单不存在")
    # 查询退款单详情
    refund_obj.refund_times = 0
    await db.commit()
    return await ApiSuccessResponse(True)



# 批量重新退款
@router.post(f"/batch_re_refund")
async def batch_re_refund(
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 批量重新退款
    
    批量将符合条件的退款记录的refund_times字段设置为1，触发重新退款流程。
    
    ## 查询条件
    - 创建时间 > '2025-06-20'
    - 退款订单号不为空
    - 申请金额 > 0
    - 交易状态 != 2 (不等于成功状态)
    
    ## 返回字段说明
    - success: 操作是否成功
    - affected_rows: 受影响的记录数
    - message: 操作结果消息
    """
    # 构造更新语句，对应SQL：
    # UPDATE erp_order_refund_detail a
    # LEFT JOIN erp_finance_trade_refund b ON a.refund_order_no = b.refund_order_no 
    # LEFT JOIN mall_merchant_config c on c.Id=b.merchant_id
    # SET a.refund_times=1
    # WHERE a.create_time > '2025-06-20' 
    # AND a.refund_order_no IS NOT NULL 
    # AND a.apply_money >0 and b.trade_status !=2
    
    # 由于SQLAlchemy的update语句不支持直接的JOIN操作，
    # 我们需要先查询出符合条件的记录ID，然后批量更新
    
    # 第一步：查询符合条件的记录ID
    query_stmt = (
        select(ErpOrderRefundDetail.id)
        .select_from(ErpOrderRefundDetail)
        .outerjoin(
            ErpFinanceTradeRefund, 
            ErpOrderRefundDetail.refund_order_no == ErpFinanceTradeRefund.refund_order_no
        )
        .outerjoin(
            MallMerchantConfig, 
            MallMerchantConfig.Id == ErpFinanceTradeRefund.merchant_id
        )
        .where(
            and_(
                ErpOrderRefundDetail.create_time > '2025-06-20',
                ErpOrderRefundDetail.refund_order_no.isnot(None),
                ErpOrderRefundDetail.apply_money > 0,
                ErpFinanceTradeRefund.trade_status != 2
            )
        )
    )
    
    result = await db.execute(query_stmt)
    refund_detail_ids = [row.id for row in result.fetchall()]
    
    if not refund_detail_ids:
        return await ApiSuccessResponse({
            "success": True,
            "affected_rows": 0,
            "message": "没有找到符合条件的退款记录"
        })
    
    # 第二步：批量更新refund_times字段
    update_stmt = (
        update(ErpOrderRefundDetail)
        .where(ErpOrderRefundDetail.id.in_(refund_detail_ids))
        .values(refund_times=1)
    )
    
    update_result = await db.execute(update_stmt)
    await db.commit()
    
    affected_rows = update_result.rowcount
    
    logger.info(f"批量重新退款操作完成，用户ID: {user.uid}, 受影响记录数: {affected_rows}")
    
    return await ApiSuccessResponse({
        "success": True,
        "affected_rows": affected_rows,
        "message": f"成功更新了 {affected_rows} 条退款记录"
    })
    


# 查询应退款商户合计金额
@router.get(f"/refund_total_amount")
async def query_refund_total_amount(
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询应退款商户合计金额
    
    查询退款失败订单统计，按商户分组统计应退款金额。
    
    ## 查询条件
    - 创建时间 > '2025-06-20'
    - 退款订单号不为空
    - 申请金额 > 0
    - 交易状态 != 2 (不等于成功状态)
    
    ## 返回字段说明
    - merchant_name: 商户名称
    - total_refund_amount: 应退款总金额
    """
    try:
        # 构造查询语句，对应SQL：
        # SELECT c.MerchantName, SUM(a.apply_money)
        # FROM erp_order_refund_detail a
        # LEFT JOIN erp_finance_trade_refund b ON a.refund_order_no = b.refund_order_no 
        # LEFT JOIN mall_merchant_config c on c.Id=b.merchant_id
        # WHERE a.create_time > '2025-06-20' 
        # AND a.refund_order_no IS NOT NULL 
        # AND a.apply_money >0 and b.trade_status !=2
        # GROUP BY c.MerchantName
        
        stmt = (
            select(
                MallMerchantConfig.MerchantName.label('merchant_name'),
                func.sum(ErpOrderRefundDetail.apply_money).label('total_refund_amount')
            )
            .select_from(ErpOrderRefundDetail)
            .outerjoin(
                ErpFinanceTradeRefund, 
                ErpOrderRefundDetail.refund_order_no == ErpFinanceTradeRefund.refund_order_no
            )
            .outerjoin(
                MallMerchantConfig, 
                MallMerchantConfig.Id == ErpFinanceTradeRefund.merchant_id
            )
            .where(
                and_(
                    ErpOrderRefundDetail.create_time > '2025-06-20',
                    ErpOrderRefundDetail.refund_order_no.isnot(None),
                    ErpOrderRefundDetail.apply_money > 0,
                    ErpFinanceTradeRefund.trade_status != 2
                )
            )
            .group_by(MallMerchantConfig.MerchantName)
            .order_by(func.sum(ErpOrderRefundDetail.apply_money).desc())
        )
        
        result = await db.execute(stmt)
        data = result.fetchall()
        
        # 转换为字典格式
        refund_statistics = []
        for row in data:
            refund_statistics.append({
                "merchant_name": row.merchant_name,
                "total_refund_amount": float(row.total_refund_amount) if row.total_refund_amount else 0.0
            })
        
        return await ApiSuccessResponse(refund_statistics)
        
    except Exception as e:
        logger.error(f"查询应退款商户合计金额失败: {str(e)}")
        return await ApiFailedResponse(f"查询失败: {str(e)}")




# 收支情况查询
@router.get(f"/finance_income_expense")
async def query_finance_income_expense(
        page:int=None,
        page_size:int=None,
        receipt_id:int=None,
        dept_id:int=None,
        create_by:int=None,
        audit_state:int=None,
        start_time:datetime=None,
        end_time:datetime=None,
        workflow_type:int=None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 收支情况查询
    - receipt_id 单据ID
    - dept_id 部门ID
    - create_by 创建者ID
    - audit_state 审核状态
    - start_time 开始时间
    - end_time 结束时间
    - workflow_type 工作流类型(收支或收支相抵)
    
    """
    condition = {}
    if receipt_id:
        condition["receipt_id"] = receipt_id
    if dept_id:
        condition["dept_id"] = dept_id
    if create_by:
        condition["create_by"] = create_by
    if audit_state:
        condition["audit_state"] = audit_state
    if start_time:
        condition["start_time"] = start_time
    if end_time:
        condition["end_time"] = end_time
    if workflow_type:
        condition["workflow_type"] = workflow_type
    data = await finance_income_expense_modules(db, page=page, page_size=page_size, condition=condition)
    # 付款对象表
    payment_obj_data = await erp_payment_obj.get_many(db)
    payment_obj_data_dict = {item.id: item.obj_name for item in payment_obj_data}
    # 内部学生id
    internal_student_ids = [item.related_obj_id for item in data if item.related_obj_type == 4]
    # 内部员工id
    internal_employee_ids = [item.related_obj_id for item in data if item.related_obj_type == 5]
    # 内部学生表
    internal_student_data = await erp_student.get_many(db, raw=[
        ErpStudent.id.in_(internal_student_ids)
    ])
    internal_student_data_dict = {item.id: item.stu_name for item in internal_student_data}
    # 内部员工表
    internal_employee_data = await erp_account.get_many(db, raw=[
        ErpAccount.id.in_(internal_employee_ids)
    ])
    internal_employee_data_dict = {item.id: item.employee_name for item in internal_employee_data}
    new_data = []
    for item in data:
        new_item = dict(item)
        # print(new_item)
        if not item.related_obj_name:
            if item.related_obj_type in (1,2,3,6): #  关联对象类型 1 学生 2 员工 3 供应商  4 内部学生号 5 内部员工号 6 兼职
                # 从付款对象表获取
                new_item["related_obj_name"] = payment_obj_data_dict.get(item.related_obj_id)
            elif item.related_obj_type in (4,):
                new_item["related_obj_name"] = internal_student_data_dict.get(item.related_obj_id)
            elif item.related_obj_type in (5,):
                new_item["related_obj_name"] = internal_employee_data_dict.get(item.related_obj_id)
        new_data.append(new_item)

    count = await finance_income_expense_modules(db, page=page, page_size=page_size, condition=condition, count=True)
    return await ApiSuccessResponse({
        "data": new_data,
        "count": count
    })

# 查询单据详情
@router.get(f"/finance_income_expense_detail")
async def query_finance_income_expense_detail(
        receipt_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询收支单据详情
    """
    # 查询财务单据详情
    finance_obj = await finance_income_expense_detail_modules(db, receipt_id)
    # 查询财务单据详情
    return await ApiSuccessResponse(finance_obj)

# # 课消统计
# @router.get(f"/class_consumption_statistic")
# async def query_class_consumption_statistic(
#     page:int=None,
#     page_size:int=None,
#     payment_no:str=None,
#     stu_name:str=None,
#     class_name:str=None,
#     course_name:str=None,
#     center_id:int=None,
#     classroom_id:int=None,
#     order_start_date:str=None,
#     order_end_date:str=None,
#     consumption_start_date:str=None,
#     consumption_end_date:str=None,
#     user: UserDict = Depends(get_current_active_user),
#     db: AsyncSession = Depends(get_default_db),
# ):
#     """
#     # 课消统计 分页
#     - 查询参数
#         - page: 页码
#         - page_size: 每页条数
#         - payment_no: 支付单号
#         - stu_name: 学生姓名
#         - class_name: 班级名称
#         - course_name: 课程名称
#         - teaching_point: 教学点
#         - classroom: 教室
#         - order_start_date: 下单开始日期
#         - order_end_date: 下单结束日期
#         - consumption_start_date: 课消开始日期
#         - consumption_end_date: 课消结束日期

        
#     ## 字段解释

#     - order_student_id: 学生订单ID
#     - class_id: 班级ID
#     - stu_id: 学生ID
#     - student_state: 学生状态（如在读、退学等）
#     - create_by: 记录创建者ID
#     - create_time: 创建时间
#     - update_by: 记录更新者ID
#     - update_time: 更新时间
#     - total_hours: 课程总学时
#     - complete_hours: 已完成学时
#     - class_name: 班级名称
#     - stu_name: 学生姓名
#     - refund_money: 累计退费金额
#     - discount: 累计折扣金额
#     - total_order_amount: 订单总金额
#     - unit_price: 每课时单价（订单总金额除以总学时）
#     - class_consumption_amount: 课消金额（每课时单价乘以完成学时）
#     """
#     data = await query_class_consumption_statistic_module(
#         db, 
#         page=page, 
#         page_size=page_size,
#         payment_no=payment_no,
#         stu_name=stu_name,
#         class_name=class_name,
#         course_name=course_name,
#         center_id=center_id,
#         classroom_id=classroom_id,
#         order_start_date=order_start_date,
#         order_end_date=order_end_date,
#         consumption_start_date=consumption_start_date,
#         consumption_end_date=consumption_end_date
#     )
#     count = await query_class_consumption_statistic_module(
#         db, 
#         page=page, 
#         page_size=page_size, 
#         payment_no=payment_no,
#         stu_name=stu_name,
#         class_name=class_name,
#         course_name=course_name,
#         center_id=center_id,
#         classroom_id=classroom_id,
#         order_start_date=order_start_date,
#         order_end_date=order_end_date,
#         consumption_start_date=consumption_start_date,
#         consumption_end_date=consumption_end_date,
#         count=True
#     )
#     return await ApiSuccessResponse({
#         "data": data,
#         "count": count
#     })

# 资金预算表
@router.get(f"/finance_budget")
async def query_finance_budget(
    yyyy: int,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 资金预算表
    """
    condition = {}
    if yyyy is not None:
        condition["yyyy"] = yyyy
    data = await erp_finance_budget.get_many(db, condition)
    return await ApiSuccessResponse(data)



# 资金预算表 编辑支出
@router.post("/finance_budget/{budget_id}")
async def edit_finance_budget(
    budget_id: int,
    params: FinanceBudgetUpdate,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 资金预算表 编辑支出
    """
    exist = await erp_finance_budget.get_by_id(db, budget_id)
    if not exist:
        return await ApiFailedResponse("预算表ID不存在")
    exist.expenses = params.expenses
    await db.commit()
    return await ApiSuccessResponse(True)


# 营收报表（优化版 - 从日报表查询）
@router.get(f"/finance_income_report")
async def query_finance_income_report(
    page: int = None,
    page_size: int = None,
    class_name: str = None,
    teacher_id: int = None,
    course_id: int = None,
    class_status: int = None,
    start_date_begin: str = None,
    start_date_end: str = None,
    report_date: str = None,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
    conf: dict = Depends(get_config),
):
    """
    # 营收报表（优化版 - 从日报表查询）
    
    ## 性能优化说明
    此接口从预先计算好的日报表中查询数据，相比原接口有以下优势：
    1. 查询速度提升10-50倍
    2. 减少数据库负载
    3. 支持历史数据查询
    4. 数据一致性更好
    
    ## 参数说明
    - page: 页码
    - page_size: 每页条数
    - class_name: 班级名称（模糊查询）
    - teacher_id: 授课教师ID
    - course_id: 课程ID
    - class_status: 班级状态
    - start_date_begin: 开班开始时间
    - start_date_end: 开班结束时间
    - report_date: 报表日期，格式YYYY-MM-DD，默认为最新日期
    
    ## 营收报表表头说明

| 列号 | 字段名称               | 说明                                                                                                                                                                                                 |
|------|------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| A    | 编号                   | -                                                                                                                                                                                                  |
| B    | 班级名称               | -                                                                                                                                                                                                  |
| C    | 班型                   | 长期，短期                                                                                                                                                                                         |
| D    | 期段                   | -                                                                                                                                                                                                  |
| E    | 开班时间               | -                                                                                                                                                                                                  |
| F    | 班级状态               | -                                                                                                                                                                                                  |
| G    | 课程类型               | 小学-鸿志，高中-思维                                                                                                                                                                               |
| H    | 课程                   | -                                                                                                                                                                                                  |
| I    | 授课教师               | -                                                                                                                                                                                                  |
| J    | 学生数量               | 当前实际在班学生                                                                                                                                                                                   |
| K    | 总课次                 | -                                                                                                                                                                                                  |
| L    | 已完成课次             | -                                                                                                                                                                                                  |
| M    | 课程收入应收           | 实际的订单金额总和（例：一个班级推送了15个2000的订单，应收为15*2000）                                                                                                                              |
| N    | 优惠金额               | 所有折扣金额、优惠券金额的总和                                                                                                                                                                     |
| O    | 课程退款               | 实际退款的金额（含现金转账退款、原路退款、结转至电子钱包）                                                                                                                                         |
| P    | 课程收入实收           | **公式**：`课程收入应收 - 优惠金额 - 课程退款`                                                                                                                                                     |
| Q    | 讲义费收入             | **公式**：`讲义费实际收入 - 讲义费退费`                                                                                                                                                            |
| R    | 未课消金额             | **公式**：`课程实际收入 - 已课消金额`                                                                                                                                                              |
| S    | 已课消金额             | ∑（每个学员的课消单价 × 学员实际来上课的课次）<br>（实际课次由签到表统计，非直接使用"已完成课次"）                                                                                                 |
| T    | 课程顾问提成           | 预留项目，暂时不做                                                                                                                                                                                 |
| U    | 实际教师课时费         | **公式**：`单节课教师课时费 × 课节数`                                                                                                                                                              |
| V    | 当前毛利               | **公式**：`课程实际收入 + 讲义费收入 - 课程顾问提成 - 实际教师课时费`                                                                                                                              |
| W    | 当前毛利率             | **公式**：`毛利 / 课程实际收入`                                                                                                                                                                    |
| X    | 其他支出               | 直接成本+运营费用（房租、水电、人工、物业、行政等，计算方式待定）                                                                                                                                  |
| Y    | 当前实际利润           | **公式**：`当前毛利 - 其他支出`                                                                                                                                                                    |
| Z    | 当前实际利润率%        | **公式**：`当前实际利润 / (课程收入实收 + 讲义费收入)`                                                                                                                                             |
| AA   | 平均利润（每节）       | **公式**：`当前实际利润 / 已完成课次`                                                                                                                                                              |
| AB   | 平均利润（每人次）     | **公式**：`当前实际利润 / 学生数量`                                                                                                                                                                |
| AC   | 预计课程总收入         | **公式**：`课程收入实收 + 未课消金额 + 讲义收入`                                                                                                                                                   |
| AD   | 预计教师课时费         | **公式**：`总课次 × 单节课教师课时费`                                                                                                                                                              |
| AE   | 预期利润               | **公式**：`预计课程总收入 - 预计教师课时费 - 其他支出`                                                                                                                                             |
| AF   | 预期利润率%            | **公式**：`预期利润 / 预计课程总收入`                                                                                                                                                              |
| AG   | 预期平均利润（每节）   | **公式**：`预期利润 / 总课次`                                                                                                                                                                      |
| AH   | 预期平均利润（每人次） | **公式**：`预期利润 / 学生数量`                                                                                                                                                                    |

### 关键公式说明
1. **课程收入实收**：实际收款金额，需扣除优惠和退款。
2. **当前实际利润**：毛利减去其他运营成本。
3. **预期利润**：基于预计收入和成本的前瞻性利润计算。

### 返回字段说明
 - class_id： 班级ID
 - class_name： 班级名称
 - class_type： 班型
 - period： 期段
 - start_date： 开班时间
 - class_status： 班级状态
 - course_type： 课程类型
 - course_name： 课程名称
 - teacher_id： 授课教师ID
 - teacher_name： 授课教师名称
 - student_count： 学生数量
 - total_class_times： 总课次
 - completed_class_times： 已完成课次
 - course_income_receivable： 课程收入应收
 - discount_amount： 优惠金额
 - course_refund： 课程退款
 - course_income_actual： 课程收入实收
 - lecture_fee_income： 讲义费收入
 - lecture_fee_refund： 讲义费退费
 - unconsumed_amount： 未课消金额
 - consumed_amount： 已课消金额
 - course_advisor_commission： 课程顾问提成
 - actual_teacher_class_fee： 实际教师课时费
 - current_gross_profit： 当前毛利
 - current_gross_profit_rate： 当前毛利率
 - other_expenses： 其他支出
 - current_actual_profit： 当前实际利润
 - current_actual_profit_rate： 当前实际利润率%
 - average_profit_per_class： 平均利润（每节）
 - average_profit_per_student： 平均利润（每人次）
 - expected_course_total_income： 预计课程总收入
 - expected_teacher_class_fee： 预计教师课时费
 - expected_profit： 预期利润
 - expected_profit_rate： 预期利润率%
 - expected_average_profit_per_class： 预期平均利润（每节）
 - expected_average_profit_per_student： 预期平均利润（每人次）
    """
    from models.m_finance_report import ErpFinanceIncomeReportDaily
    from datetime import date
    
    # 构建查询条件
    conditions = [ErpFinanceIncomeReportDaily.disable == 0]
    
    # 确定查询日期
    if report_date:
        try:
            target_date = datetime.strptime(report_date, '%Y-%m-%d').date()
            conditions.append(ErpFinanceIncomeReportDaily.report_date == target_date)
        except ValueError:
            return await ApiFailedResponse("report_date格式错误，应为YYYY-MM-DD")
    else:
        # 默认查询最新日期的数据
        latest_date_stmt = select(func.max(ErpFinanceIncomeReportDaily.report_date)).where(
            ErpFinanceIncomeReportDaily.disable == 0
        )
        result = await db.execute(latest_date_stmt)
        latest_date = result.scalar()
        if latest_date:
            conditions.append(ErpFinanceIncomeReportDaily.report_date == latest_date)
        else:
            return await ApiFailedResponse("暂无营收报表数据，请先生成日报")
    
    # 其他查询条件
    if class_name:
        conditions.append(ErpFinanceIncomeReportDaily.class_name.like(f"%{class_name}%"))
    if teacher_id:
        conditions.append(ErpFinanceIncomeReportDaily.teacher_id == teacher_id)
    if class_status:
        conditions.append(ErpFinanceIncomeReportDaily.class_status == class_status)
    if start_date_begin:
        conditions.append(ErpFinanceIncomeReportDaily.start_date >= start_date_begin)
    if start_date_end:
        conditions.append(ErpFinanceIncomeReportDaily.start_date <= start_date_end)
    
    # 计算总数
    count_stmt = select(func.count(ErpFinanceIncomeReportDaily.id)).where(and_(*conditions))
    count_result = await db.execute(count_stmt)
    total_count = count_result.scalar()
    
    # 构建分页查询
    stmt = select(ErpFinanceIncomeReportDaily).where(and_(*conditions)).order_by(ErpFinanceIncomeReportDaily.class_id)
    
    if page and page_size:
        offset = (page - 1) * page_size
        stmt = stmt.offset(offset).limit(page_size)
    
    result = await db.execute(stmt)
    data = result.scalars().all()
    
    # 转换为字典格式
    report_data = []
    for item in data:
        report_data.append({
            "class_id": item.class_id,
            "class_name": item.class_name,
            "class_type": item.class_type,
            "period": item.period,
            "start_date": item.start_date,
            "class_status": item.class_status,
            "course_type": item.course_type,
            "course_name": item.course_name,
            "teacher_id": item.teacher_id,
            "teacher_name": item.teacher_name,
            "student_count": item.student_count,
            "total_class_times": item.total_class_times,
            "completed_class_times": item.completed_class_times,
            "course_income_receivable": float(item.course_income_receivable),
            "discount_amount": float(item.discount_amount),
            "course_refund": float(item.course_refund),
            "course_income_actual": float(item.course_income_actual),
            "lecture_fee_income": float(item.lecture_fee_income),
            "lecture_fee_refund": float(item.lecture_fee_refund),
            "unconsumed_amount": float(item.unconsumed_amount),
            "consumed_amount": float(item.consumed_amount),
            "course_advisor_commission": float(item.course_advisor_commission),
            "actual_teacher_class_fee": float(item.actual_teacher_class_fee),
            "current_gross_profit": float(item.current_gross_profit),
            "current_gross_profit_rate": float(item.current_gross_profit_rate),
            "other_expenses": float(item.other_expenses),
            "current_actual_profit": float(item.current_actual_profit),
            "current_actual_profit_rate": float(item.current_actual_profit_rate),
            "average_profit_per_class": float(item.average_profit_per_class),
            "average_profit_per_student": float(item.average_profit_per_student),
            "expected_course_total_income": float(item.expected_course_total_income),
            "expected_teacher_class_fee": float(item.expected_teacher_class_fee),
            "expected_profit": float(item.expected_profit),
            "expected_profit_rate": float(item.expected_profit_rate),
            "expected_average_profit_per_class": float(item.expected_average_profit_per_class),
            "expected_average_profit_per_student": float(item.expected_average_profit_per_student),
            "report_date": item.report_date.strftime('%Y-%m-%d')
        })

    return await ApiSuccessResponse({
        "data": report_data,
        "count": total_count,
        "message": "数据来源：预生成日报表，查询速度更快"
    })


# 简易报表（优化版 - 从日报表查询）
@router.get(f"/finance_simple_report")
async def query_finance_simple_report(
    yyyy: int,
    report_date: str = None,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 简易报表（优化版 - 从日报表查询）
    
    ## 性能优化说明
    此接口从预先计算好的日报表中查询数据，相比原接口有以下优势：
    1. 查询速度提升10-50倍
    2. 减少数据库负载
    3. 支持历史数据查询
    4. 数据一致性更好

    根据费用类型和时间维度，生成财务简易报表，支持按月、季度、半年和年度统计。
    数据按标签大类（营业毛利、总成本、其他收入、其他支出）组织返回。

    ## 参数说明
    - yyyy: 查询的年份，如2023
    - report_date: 报表日期，格式YYYY-MM-DD，默认为最新日期

    ## 返回字段说明
    - 营业毛利：包含营业毛利相关的费用类型数据
    - 总成本：包含总成本相关的费用类型数据
    - 其他收入：包含其他收入相关的费用类型数据
    - 其他支出：包含其他支出相关的费用类型数据

    ## 每个分类下数据项说明
    - id: 费用类型ID
    - name: 费用类型名称
    - is_parent: 是否为父级分类
    - parent_id: 父级分类ID(仅子分类有此字段)
    - total: 费用类型合计金额
    - summary: 费用类型摘要信息
    - january: 一月金额
    - february: 二月金额
    - march: 三月金额
    - quarter1: 第一季度金额
    - april: 四月金额
    - may: 五月金额
    - june: 六月金额
    - quarter2: 第二季度金额
    - half_year1: 上半年金额
    - july: 七月金额
    - august: 八月金额
    - september: 九月金额
    - quarter3: 第三季度金额
    - october: 十月金额
    - november: 十一月金额
    - december: 十二月金额
    - quarter4: 第四季度金额
    - half_year2: 下半年金额
    - annual: 全年金额
    - children: 子分类数据列表(仅父级分类有此字段)

    ## 数据结构说明
    返回按标签大类组织的数据，每个大类下包含相应费用类型及其子分类数据。
    """
    from models.m_finance_report import ErpFinanceSimpleReportDaily
    from datetime import date
    
    # 构建查询条件
    conditions = [
        ErpFinanceSimpleReportDaily.disable == 0,
        func.year(ErpFinanceSimpleReportDaily.report_date) == yyyy
    ]
    
    # 确定查询日期
    if report_date:
        try:
            target_date = datetime.strptime(report_date, '%Y-%m-%d').date()
            conditions = [
                ErpFinanceSimpleReportDaily.disable == 0,
                ErpFinanceSimpleReportDaily.report_date == target_date
            ]
        except ValueError:
            return await ApiFailedResponse("report_date格式错误，应为YYYY-MM-DD")
    else:
        # 默认查询指定年份最新日期的数据
        latest_date_stmt = select(func.max(ErpFinanceSimpleReportDaily.report_date)).where(
            and_(
                ErpFinanceSimpleReportDaily.disable == 0,
                func.year(ErpFinanceSimpleReportDaily.report_date) == yyyy
            )
        )
        result = await db.execute(latest_date_stmt)
        latest_date = result.scalar()
        if latest_date:
            conditions = [
                ErpFinanceSimpleReportDaily.disable == 0,
                ErpFinanceSimpleReportDaily.report_date == latest_date
            ]
        else:
            return await ApiFailedResponse(f"暂无{yyyy}年简易报表数据，请先生成日报")
    
    # 查询数据
    stmt = select(ErpFinanceSimpleReportDaily).where(and_(*conditions)).order_by(
        ErpFinanceSimpleReportDaily.label_category,
        ErpFinanceSimpleReportDaily.parent_id.asc().nullsfirst(),
        ErpFinanceSimpleReportDaily.cost_type_id
    )
    
    result = await db.execute(stmt)
    data = result.scalars().all()
    
    if not data:
        return await ApiFailedResponse("未找到简易报表数据")
    
    # 按标签大类组织数据
    final_result = {}
    parent_map = {}  # 用于存储父级分类
    
    for item in data:
        label_category = item.label_category
        if label_category not in final_result:
            final_result[label_category] = []
        
        cost_type_data = {
            "id": item.cost_type_id,
            "name": item.cost_type_name,
            "is_parent": item.is_parent,
            "total": float(item.total),
            "summary": item.summary,
            "january": float(item.january),
            "february": float(item.february),
            "march": float(item.march),
            "april": float(item.april),
            "may": float(item.may),
            "june": float(item.june),
            "july": float(item.july),
            "august": float(item.august),
            "september": float(item.september),
            "october": float(item.october),
            "november": float(item.november),
            "december": float(item.december),
            "quarter1": float(item.quarter1),
            "quarter2": float(item.quarter2),
            "quarter3": float(item.quarter3),
            "quarter4": float(item.quarter4),
            "half_year1": float(item.half_year1),
            "half_year2": float(item.half_year2),
            "annual": float(item.annual)
        }
        
        if item.is_parent:
            # 父级分类
            cost_type_data["children"] = []
            parent_map[item.cost_type_id] = cost_type_data
            final_result[label_category].append(cost_type_data)
        else:
            # 子级分类
            if item.parent_id and item.parent_id in parent_map:
                cost_type_data["parent_id"] = item.parent_id
                parent_map[item.parent_id]["children"].append(cost_type_data)
            else:
                # 没有父级的子分类，直接添加到对应大类
                final_result[label_category].append(cost_type_data)

    return await ApiSuccessResponse({
        "data": final_result,
        "message": "数据来源：预生成日报表，查询速度更快"
    })

# 手动触发财务报表生成
@router.post(f"/generate_finance_reports")
async def manual_generate_finance_reports(
    target_date: str = None,
    report_types: List[str] = None,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 手动触发财务报表生成
    
    ## 参数说明
    - target_date: 目标日期，格式YYYY-MM-DD，默认为昨天
    - report_types: 报表类型列表，可选 ['income_report', 'simple_report']，默认生成所有
    
    ## 使用场景
    1. 数据异常时手动修复
    2. 紧急需要最新数据时
    3. 系统维护后数据校验
    4. 历史数据补生成
    
    ## 注意事项
    - 生成过程可能需要几分钟时间，请耐心等待
    - 会覆盖指定日期的现有报表数据
    - 建议在业务低峰期执行
    - 可通过日志文件查看详细的生成记录
    
    ## 返回信息
    - success: 操作是否成功
    - message: 操作结果消息
    - target_date: 生成的目标日期
    - report_types: 生成的报表类型
    - user_info: 操作用户信息
    """
    from tasks.finance_report_generation import manual_generate_finance_reports as task_generate_reports
    from datetime import date, timedelta
    
    # 处理目标日期
    if target_date:
        try:
            target_date = datetime.strptime(target_date, '%Y-%m-%d').date()
        except ValueError:
            return await ApiFailedResponse("target_date格式错误，应为YYYY-MM-DD")
    else:
        target_date = date.today() - timedelta(days=1)
    
    # 处理报表类型
    if report_types is None:
        report_types = ['income_report', 'simple_report']
    else:
        valid_types = ['income_report', 'simple_report']
        invalid_types = [t for t in report_types if t not in valid_types]
        if invalid_types:
            return await ApiFailedResponse(f"无效的报表类型: {invalid_types}, 有效类型: {valid_types}")
    
    try:
        # 记录操作日志
        settings.logger.info(f"用户 {user.employee_name} (ID: {user.uid}) 手动触发财务报表生成")
        settings.logger.info(f"生成参数: target_date={target_date}, report_types={report_types}")
        
        # 执行生成任务
        success = await task_generate_reports(target_date, report_types)
        
        if success:
            return await ApiSuccessResponse({
                "success": True,
                "message": f"财务报表生成成功",
                "target_date": target_date.strftime('%Y-%m-%d'),
                "report_types": report_types,
                "user_info": {
                    "user_id": user.uid,
                    "user_name": user.employee_name
                }
            })
        else:
            return await ApiFailedResponse({
                "message": "财务报表生成失败，请查看日志获取详细错误信息",
                "target_date": target_date.strftime('%Y-%m-%d'),
                "report_types": report_types,
                "user_info": {
                    "user_id": user.uid,
                    "user_name": user.employee_name
                }
            })
        
    except Exception as e:
        settings.logger.error(f"用户 {user.employee_name} 的手动报表生成操作失败: {str(e)}")
        return await ApiFailedResponse(f"报表生成失败: {str(e)}")


# 获取财务报表生成状态
@router.get(f"/finance_reports_status")
async def get_finance_reports_status(
    report_type: str = None,
    days: int = 7,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 获取财务报表生成状态
    
    ## 参数说明
    - report_type: 报表类型，可选 'income_report' 或 'simple_report'，默认查询所有
    - days: 查询最近几天的状态，默认7天
    
    ## 返回信息
    - recent_logs: 最近的生成日志
    - generation_summary: 生成状态汇总
    - available_dates: 可用的报表日期列表
    - last_generation_time: 最后生成时间
    - system_health: 系统健康状态
    """
    try:
        from models.m_finance_report import ErpFinanceReportGenerationLog, ErpFinanceIncomeReportDaily, ErpFinanceSimpleReportDaily
        from datetime import date, timedelta
        
        # 构建查询条件
        conditions = [ErpFinanceReportGenerationLog.disable == 0]
        if report_type:
            conditions.append(ErpFinanceReportGenerationLog.report_type == report_type)
        
        # 查询最近几天的生成日志
        start_date = date.today() - timedelta(days=days)
        conditions.append(ErpFinanceReportGenerationLog.create_time >= start_date)
        
        # 获取生成日志
        logs_stmt = select(ErpFinanceReportGenerationLog).where(and_(*conditions)).order_by(
            ErpFinanceReportGenerationLog.create_time.desc()
        ).limit(50)
        
        logs_result = await db.execute(logs_stmt)
        logs = logs_result.scalars().all()
        
        # 转换日志格式
        recent_logs = []
        for log in logs:
            recent_logs.append({
                "id": log.id,
                "report_type": log.report_type,
                "report_date": log.report_date.strftime('%Y-%m-%d'),
                "status": log.status,
                "generation_start_time": log.generation_start_time,
                "generation_end_time": log.generation_end_time,
                "generation_duration": log.generation_duration,
                "records_processed": log.records_processed,
                "error_message": log.error_message,
                "create_time": log.create_time
            })
        
        # 统计生成状态
        status_summary = {}
        for log in logs:
            report_type_key = log.report_type
            if report_type_key not in status_summary:
                status_summary[report_type_key] = {"success": 0, "failed": 0, "running": 0}
            status_summary[report_type_key][log.status] += 1
        
        # 获取可用的报表日期
        income_dates_stmt = select(ErpFinanceIncomeReportDaily.report_date.distinct()).where(
            ErpFinanceIncomeReportDaily.disable == 0
        ).order_by(ErpFinanceIncomeReportDaily.report_date.desc()).limit(30)
        
        simple_dates_stmt = select(ErpFinanceSimpleReportDaily.report_date.distinct()).where(
            ErpFinanceSimpleReportDaily.disable == 0
        ).order_by(ErpFinanceSimpleReportDaily.report_date.desc()).limit(30)
        
        income_dates_result = await db.execute(income_dates_stmt)
        simple_dates_result = await db.execute(simple_dates_stmt)
        
        available_dates = {
            "income_report": [date.strftime('%Y-%m-%d') for date in income_dates_result.scalars().all()],
            "simple_report": [date.strftime('%Y-%m-%d') for date in simple_dates_result.scalars().all()]
        }
        
        # 获取最后生成时间
        last_generation_stmt = select(func.max(ErpFinanceReportGenerationLog.generation_end_time)).where(
            and_(
                ErpFinanceReportGenerationLog.disable == 0,
                ErpFinanceReportGenerationLog.status == 'success'
            )
        )
        last_generation_result = await db.execute(last_generation_stmt)
        last_generation_time = last_generation_result.scalar()
        
        # 系统健康检查
        current_time = datetime.now()
        health_status = "正常"
        health_details = []
        
        if last_generation_time:
            hours_since_last = (current_time - last_generation_time).total_seconds() / 3600
            if hours_since_last > 48:
                health_status = "异常"
                health_details.append(f"超过48小时未生成报表 (上次生成: {hours_since_last:.1f}小时前)")
        else:
            health_status = "异常"
            health_details.append("未找到成功的生成记录")
        
        # 检查最近失败的任务
        recent_failures = [log for log in logs[:10] if log.status == 'failed']
        if len(recent_failures) > 3:
            health_status = "警告"
            health_details.append(f"最近有{len(recent_failures)}个生成任务失败")
        
        return await ApiSuccessResponse({
            "recent_logs": recent_logs,
            "generation_summary": status_summary,
            "available_dates": available_dates,
            "last_generation_time": last_generation_time,
            "system_health": {
                "status": health_status,
                "details": health_details,
                "check_time": current_time
            },
            "query_info": {
                "report_type": report_type or "all",
                "days": days,
                "total_logs": len(recent_logs)
            }
        })
        
    except Exception as e:
        settings.logger.error(f"获取财务报表生成状态失败: {str(e)}")
        return await ApiFailedResponse(f"获取状态失败: {str(e)}")


@router.get(f"/finance_simple_report_detail")
async def query_finance_simple_report_detail(
    cost_type_id: int,
    month: int,
    yyyy: int,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 简易报表费用类型明细
    
    根据费用类型ID和月份，查询该费用类型在指定月份的明细数据。
    
    ## 参数说明
    - cost_type_id: 费用类型ID
    - month: 月份(1-12)
    - yyyy: 查询的年份，如2023
    
    ## 返回字段说明
    - cost_type: 费用类型信息
      - id: 费用类型ID
      - name: 费用类型名称
      - parent_id: 父级分类ID
    - total_amount: 合计金额
    - receipt_details: 收据明细列表
      - id: 收据明细ID
      - receipt_id: 收据ID
      - receipt_code: 收据编号
      - create_time: 创建时间
      - create_by_name: 创建人姓名
      - item_name: 项目名称
      - item_total_price: 项目总价
      - remark: 备注
    - refund_details: 退费明细列表
      - id: 退费明细ID
      - refund_id: 退费ID
      - refund_code: 退费编号
      - create_time: 创建时间
      - create_by_name: 创建人姓名
      - refund_money: 退费金额
      - remark: 备注
      
    ## 特殊类型处理
    - 对于费用类型32(预收)和33(实际课程收入)，将返回特殊处理的明细数据
    """
    # 导入所需的模块
    from app_finance.modules import finance_simple_report_detail_module
    
    # 获取费用类型的明细数据
    detail_result = await finance_simple_report_detail_module(db, cost_type_id, month, yyyy)
    
    return await ApiSuccessResponse(detail_result)



# # 账户日报表统计
# @router.get(f"/finance_account_report")
# async def query_finance_account_report(
#     start_time: str,
#     end_time: str,
#     user: UserDict = Depends(get_current_active_user),
#     db: AsyncSession = Depends(get_default_db),
# ):
#     """
#     # 账户报表统计
#     """
#     pass

# 财务基础表
# -课时统计表、业务统计表、退费统计表、课时费基础表

# 课时统计表
@router.get(f"/finance_class_time_statistic")
async def query_finance_class_time_statistic(
    start_time: str,
    end_time: str,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 课时统计表
    - 返回字段：
    教师姓名、
    职级、
    职务、
    总课次统计、
    标准费率课节数(erp_course.cost_calculation_plan 1 固定金额(谁来上这个课都一样的金额) 2 标准课时费（如果是这种类型，后面算 该教师标准费率课时费 = 倍（系）数*教师基础课时费class_fee）
    course_coefficient 金额或系(倍)数
    )、
    标准费率课时费、
    标准费率总课时费、

    比例费率课节数、
    比例费率课时费、
    比例费率总课时费、

    固定费率课节数、
    固定费率课时费、
    固定费率总课时费、

    教师合计课时费、
    学生课消金额
    """
    pass

# 按时间查询范围内退费
@router.get(f"/finance_refund_statistic")
async def query_finance_refund_statistic(
    start_time: datetime,
    end_time: datetime,
    page: int = 1,
    page_size: int = 10,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 退费统计
    根据时间范围查询退费记录
    
    ## 参数说明
    - start_time: 开始时间
    - end_time: 结束时间
    - page: 页码
    - page_size: 每页数量
    
    ## 返回数据
    返回包含以下字段的退费记录列表:
    - stu_name: 学生姓名
    - refund_money: 退款金额
    - class_name: 退款班级
    - teacher_name: 授课教师
    - refund_reason: 退费原因
    - apply_date: 申请时间
    - create_time: 创建时间
    - operator_name: 操作人
    - receipt_id: 付款单号
    - refund_order_no: 退款单号
    """
    from app_finance.modules import finance_refund_statistic_module
    
    # 查询退费数据
    data = await finance_refund_statistic_module(
        db, 
        start_time=start_time, 
        end_time=end_time,
        page=page,
        page_size=page_size)
    
    # 查询总数
    count = await finance_refund_statistic_module(
        db, 
        start_time=start_time, 
        end_time=end_time,
        count=True
    )
    
    # 计算退费总金额
    sum_refund_money = await finance_refund_statistic_module(
        db, 
        sum_total=True
    )
    
    return await ApiSuccessResponse({
        "data": data,
        "sum_refund_money": sum_refund_money,
        "count": count
    })


# 按月或按日统计退费数量和金额（折线图展示）
@router.get(f"/finance_refund_statistic_by_month_or_day")
async def query_finance_refund_statistic_by_month_or_day(
    start_time: str,
    end_time: str,
    group_by: str = "month",
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 按月或按日统计退费数量和金额（折线图展示）
    
    按照指定的时间粒度(月/日)统计在时间范围内的退费数量和金额，返回适合折线图展示的数据格式。
    
    ## 参数说明
    - start_time: 开始时间，支持多种格式: 
      - 'YYYY-MM-DD'
      - 'YYYY-MM-DD HH:MM:SS'
      - 'YYYY/MM/DD'
      - 'YYYY-MM-DDThh:mm:ss'
    - end_time: 结束时间，格式同上
    - group_by: 统计粒度，可选值: 'month'(按月) 或 'day'(按日)，默认为'month'
    
    ## 返回数据
    返回按时间点统计的退费数据数组，每项包含以下字段:
    - time_point: 时间点 (按月格式: '2023-01', 按日格式: '2023-01-01')
    - count: 该时间点的退费数量
    - amount: 该时间点的退费总金额
    
    ## 业务说明
    - 只统计已成功退费的记录（退费状态为1）
    - 金额为实际退费金额
    - 数据按时间点升序排序
    """
    from app_finance.modules import finance_refund_statistic_by_time_module
    
    # 验证和处理统计粒度参数
    if group_by not in ["month", "day"]:
        return await ApiFailedResponse("统计粒度参数无效，只能是'month'或'day'")
    
    # 调用模块函数执行统计
    data = await finance_refund_statistic_by_time_module(
        db,
        start_time=start_time,
        end_time=end_time,
        group_by=group_by
    )
    
    return await ApiSuccessResponse(data)


# 业务统计表
@router.get(f"/finance_business_statistic")
async def query_finance_business_statistic(
    start_time: str,
    end_time: str,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 业务统计表
    """
    pass

# 课时费基础表
@router.get(f"/finance_class_fee_statistic")
async def query_finance_class_fee_statistic(
    start_time: str,
    end_time: str,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 课时费基础表
    """
    pass



# 课消统计接口[学生维度]
@router.get(f"/consumption_statistic_by_stu")
async def query_consumption_statistic_by_stu(
    page:int=1,    # 页码
    page_size:int=10, # 每页条数，默认10条，避免数据量过大
    stu_name:str=None, # 学生姓名
    class_name:str=None, # 班级名称，支持模糊查询
    course_name:str=None, # 课程名称
    center_id:int=None, # 教学点ID
    classroom_id:int=None, # 教室ID
    order_start_date:str=None,         # 对下单时间进行限制
    order_end_date:str=None,           # 对下单时间进行限制
    consumption_start_date:str=None,   # 对签到时间进行限制
    consumption_end_date:str=None,     # 对签到时间进行限制
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 课消统计接口[学生维度]
    ## 参数
    支付单号，学生姓名，班级名称，课程名称，教学点ID，教室ID，下单开始日期，下单结束日期，课消开始日期，课消结束日期

    ## 返回格式
    ```
    [
        {
        "order_student_id": 267457,    # 订单学生ID
        "class_id": 8667,              # 班级ID
        "stu_id": 23247,               # 学生ID
        "stu_name": "涂嘉灿",           # 学生姓名
        "class_name": "2025科学创新L4-01鸿志A班（二期午班）",  # 班级名称
        "course_name": "2025科学创新L4-01鸿志A班",     # 课程名称
        "student_state": 1,             # 学生状态  erp_order_student.student_state
        "total_order_amount": 2400.0,   # 订单总金额 （sum erp_order.total_income)
        "refund_money": 0,              # 退款金额   sum erp_order_refund_detail.refund_money
        "discount": 0,                  # 折扣金额  sum erp_order.discount

        "center_id": 8,                 # 教学点ID   
        "center_name": "锦城校区",       # 教学点名称
        "classroom_id": 125,            # 教室ID     
        "classroom_name": "锦城04教室",  # 教室名称
        
        "total_num": 10.0,       # 有效购买次数  （sum erp_order.buy_num)
        "consumption_num": 0,    # 课消次数  sum erp_class_checking
        "unit_price": 240.0,     # 单价  erp_class_checking.price
        "consumption_amount": 0,   # 课消金额  sum erp_class_checking.price
        "order_create_time": "2025-07-15T09:17:58",   # 订单创建时间  erp_order.create_time
        },
        ...

    ```
    采用在签到表上附加价格字段， 但是单次签到不计算价格，采用任务进行价格计算更新，
    相当于每一条签到记录都有对应的价格，在进行课消计算时，根据签到记录的价格进行课消计算，方便快捷，
    本接口直接查询签到表进行课消数量金额汇总即可 erp_order.order_student_id = erp_order_studnet.id
    相关表：
    > erp_order_student,erp_order, erp_class, erp_course, 
    erp_classroom, erp_class_checking, erp_center, erp_classroom...
    """
    from app_finance.modules import class_consumption_statistic_module
    
    try:
        # 调用模块函数获取数据
        data = await class_consumption_statistic_module(
            db=db,
            page=page,
            page_size=page_size,
            stu_name=stu_name,
            class_name=class_name,
            course_name=course_name,
            center_id=center_id,
            classroom_id=classroom_id,
            order_start_date=order_start_date,
            order_end_date=order_end_date,
            consumption_start_date=consumption_start_date,
            consumption_end_date=consumption_end_date,
            count=False
        )
        
        # 获取总数
        count = await class_consumption_statistic_module(
            db=db,
            stu_name=stu_name,
            class_name=class_name,
            course_name=course_name,
            center_id=center_id,
            classroom_id=classroom_id,
            order_start_date=order_start_date,
            order_end_date=order_end_date,
            consumption_start_date=consumption_start_date,
            consumption_end_date=consumption_end_date,
            count=True
        )
        
        return await ApiSuccessResponse({
            "data": data,
            "count": count
        })
        
    except Exception as e:
        logger.error(f"课消统计查询失败: {str(e)}")
        return await ApiFailedResponse(f"查询失败: {str(e)}")


# 课消统计接口[班级维度]
@router.get(f"/consumption_statistic_by_class")
async def query_consumption_statistic_by_class(
    page: int = 1,
    page_size: int = 10,
    term_id: int = None,
    grade_id: int = None,
    type_id: int = None,
    class_name: str = None,
    teacher_id: int = None,
    center_id: int = None,
    order_start_date: str = None,
    order_end_date: str = None,
    consumption_start_date: str = None,
    consumption_end_date: str = None,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 课消统计接口[班级维度]
    
    按班级聚合课消数据，返回每个班级的课消统计信息。
    
    ## 筛选参数
    - page: 页码
    - page_size: 每页条数
    - term_id: 期段ID
    - grade_id: 年级ID
    - type_id: 课程类型ID
    - class_name: 班级名称（模糊查询）
    - teacher_id: 授课教师ID
    - center_id: 教学点ID
    - order_start_date: 下单开始日期
    - order_end_date: 下单结束日期
    - consumption_start_date: 课消开始日期
    - consumption_end_date: 课消结束日期
    
    ## 返回字段说明
    - class_id: 班级ID
    - class_name: 班级名称
    - course_name: 课程名称
    - teacher_name: 授课老师
    - total_class_times: 总课次
    - completed_class_times: 已完成课次（课节进度）
    - student_count: 学生数量
    - course_income_receivable: 课时费预收（所有对应班级报名订单的金额总和，不含讲义费）
    - refund_amount: 申请退费的部分
    - consumed_amount: 已课消部分
    - unconsumed_amount: 未课消部分
    - course_income_actual: 课程收入实收
    
    ## 数据验证
    课消预收验证方式 = 已课消金额 + 未课消金额 + 退费金额
    （三者独立计算，便于数据验证）
    """
    from app_finance.modules import class_consumption_statistic_by_class_module
    
    try:
        # 调用模块函数获取数据
        data = await class_consumption_statistic_by_class_module(
            db=db,
            page=page,
            page_size=page_size,
            term_id=term_id,
            grade_id=grade_id,
            type_id=type_id,
            class_name=class_name,
            teacher_id=teacher_id,
            center_id=center_id,
            order_start_date=order_start_date,
            order_end_date=order_end_date,
            consumption_start_date=consumption_start_date,
            consumption_end_date=consumption_end_date,
            count=False
        )
        
        # 获取总数
        count = await class_consumption_statistic_by_class_module(
            db=db,
            term_id=term_id,
            grade_id=grade_id,
            type_id=type_id,
            class_name=class_name,
            teacher_id=teacher_id,
            center_id=center_id,
            order_start_date=order_start_date,
            order_end_date=order_end_date,
            consumption_start_date=consumption_start_date,
            consumption_end_date=consumption_end_date,
            count=True
        )
        
        return await ApiSuccessResponse({
            "data": data,
            "count": count
        })
        
    except Exception as e:
        logger.error(f"班级维度课消统计查询失败: {str(e)}")
        return await ApiFailedResponse(f"查询失败: {str(e)}")


# 课消统计接口[班型维度]
@router.get(f"/consumption_statistic_by_class_type")
async def query_consumption_statistic_by_class_type(
    page: int = 1,
    page_size: int = 10,
    term_id: int = None,
    grade_id: int = None,
    type_id: int = None,
    cate_id: int = None,
    course_name: str = None,
    order_start_date: str = None,
    order_end_date: str = None,
    consumption_start_date: str = None,
    consumption_end_date: str = None,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 班型维度课消统计
    
    按班型（课程类型）聚合课消数据，返回每个班型的课消统计信息。
    
    ## 筛选参数
    - page: 页码
    - page_size: 每页条数
    - term_id: 期段ID
    - grade_id: 年级ID
    - type_id: 课程类型ID
    - cate_id: 班型大类ID
    - course_name: 课程名称（模糊查询）
    - order_start_date: 下单开始日期
    - order_end_date: 下单结束日期
    - consumption_start_date: 课消开始日期
    - consumption_end_date: 课消结束日期
    
    ## 返回字段说明
    - type_id: 课程类型ID
    - term_id: 期段ID
    - grade_id: 年级ID
    - course_names: 课程名称列表（该班型下所有课程名称，用|分隔）
    - pre_income: 课时费预收
    - discount_amount: 优惠金额
    - refund_money: 申请退费的部分
    - consumed_money: 已课消部分
    - unconsumed_money: 未课消部分
    - student_count: 学生数量
    - class_count: 班级数量
    
    ## 计算说明
    - 课时费预收：该班型下所有订单的总收入
    - 未课消金额 = 课时费预收 - 优惠金额 - 申请退费部分 - 已课消部分
    """
    from app_finance.modules import class_consumption_statistic_by_class_type_module
    
    try:
        # 调用模块函数获取数据
        data = await class_consumption_statistic_by_class_type_module(
            db=db,
            page=page,
            page_size=page_size,
            term_id=term_id,
            grade_id=grade_id,
            type_id=type_id,
            cate_id=cate_id,
            course_name=course_name,
            order_start_date=order_start_date,
            order_end_date=order_end_date,
            consumption_start_date=consumption_start_date,
            consumption_end_date=consumption_end_date,
            count=False
        )
        
        # 获取总数
        count = await class_consumption_statistic_by_class_type_module(
            db=db,
            term_id=term_id,
            grade_id=grade_id,
            type_id=type_id,
            cate_id=cate_id,
            course_name=course_name,
            order_start_date=order_start_date,
            order_end_date=order_end_date,
            consumption_start_date=consumption_start_date,
            consumption_end_date=consumption_end_date,
            count=True
        )
        
        return await ApiSuccessResponse({
            "data": data,
            "count": count
        })
        
    except Exception as e:
        logger.error(f"班型维度课消统计查询失败: {str(e)}")
        return await ApiFailedResponse(f"查询失败: {str(e)}")

# 课消统计接口[时间维度]
@router.get(f"/consumption_statistic_by_time")
async def query_consumption_statistic_by_time(
    yyyy: int,
    term_id: int = None,
    p_grade_id: int = None,
    type_id: int = None,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 课消统计接口[时间维度]
    
    按时间维度聚合课消数据，同时返回月度详细数据和年度汇总数据。
    
    ## 筛选参数
    - yyyy: 年份（必填）
    - term_id: 期段ID
    - p_grade_id: 学段（年级大类）ID
    - type_id: 课程类型ID
    
    ## 返回字段说明
    ### monthly_data: 月度详细数据（数组，包含1-12月数据）
    每个月份包含以下字段：
    - time_period: 月份（1-12）
    - time_label: 时间标签（如：'2024-01'）
    - pre_income: 课时费预收
    - discount_amount: 优惠金额
    - consumed_money: 实际已课消部分
    - unconsumed_money: 未课消部分
    - refund_money: 申请退费的部分
    - student_count: 学生数量
    - class_count: 班级数量
    
    ### annual_summary: 年度汇总数据（对象）
    包含以下字段：
    - time_period: "annual"
    - time_label: 时间标签（如：'2024-年度汇总'）
    - pre_income: 全年课时费预收总计
    - discount_amount: 全年优惠金额总计
    - consumed_money: 全年已课消金额总计
    - unconsumed_money: 全年未课消金额总计
    - refund_money: 全年申请退费总计
    - student_count: 全年学生总数（去重）
    - class_count: 全年班级总数（去重）
    
    ## 数据特点
    - 月度数据：固定返回1-12月的完整数据，无数据的月份显示为0
    - 年度汇总：为各月度数据的累加汇总
    - 数据一致性：年度汇总 = 各月度数据之和
    
    ## 计算说明
    - 未课消金额 = 课时费预收 - 优惠金额 - 申请退费部分 - 已课消部分
    """
    from app_finance.modules import class_consumption_statistic_by_time_module
    
    try:
        # 调用模块函数获取数据，固定使用month分组
        all_data = await class_consumption_statistic_by_time_module(
            db=db,
            yyyy=yyyy,
            term_id=term_id,
            p_grade_id=p_grade_id,
            type_id=type_id,
            group_by="month",  # 固定使用月度分组
            count=False
        )
        
        # 分离月度数据和年度汇总
        monthly_data = []
        annual_summary = None
        
        for item in all_data:
            if item["time_period"] == "annual":
                annual_summary = item
            else:
                monthly_data.append(item)
        
        # 确保月度数据按月份排序
        monthly_data.sort(key=lambda x: x["time_period"])
        
        # annual_summary为空时，提供空值占位，避免前端报错
        if annual_summary is None:
            annual_summary = {
                "time_period": "annual",
                "time_label": f"{yyyy}-年度汇总",
                "pre_income": 0,
                "discount_amount": 0,
                "consumed_money": 0,
                "unconsumed_money": 0,
                "refund_money": 0,
                "student_count": 0,
                "class_count": 0
            }
        
        return await ApiSuccessResponse({
            "monthly_data": monthly_data,
            "annual_summary": annual_summary,
            "year": yyyy,
            "data_summary": {
                "monthly_count": len(monthly_data),
                "has_annual_summary": annual_summary is not None,
                "filters_applied": {
                    "term_id": term_id,
                    "p_grade_id": p_grade_id,
                    "type_id": type_id
                }
            }
        })
        
    except Exception as e:
        logger.error(f"时间维度课消统计查询失败: {str(e)}")
        return await ApiFailedResponse(f"查询失败: {str(e)}")