import base64
import copy
from collections import defaultdict
from datetime import datetime
from io import Bytes<PERSON>
from typing import List
from urllib.parse import quote
from fastapi import APIRouter, Depends, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
import pandas as pd
from starlette.responses import StreamingResponse
from models.m_finance import ErpPerformanceRecords
import settings
from app_finance.crud import get_salary_bank, get_teacher_class_fee_module, salary_detail_by_salary_id, get_performance_records
from app_finance.private_dict import manual_field, salary_columns_rename, columns_to_color, columns_header
from app_finance.serializer import PerformanceRecordEntity, PerformanceRecordUpdate, SalaryBaseEntity, SalaryEntity, TeacherClassFeeAdjustment
from app_human_resources.crud import get_salary_check_records, get_salary_base
from app_finance.modules import calculate_deductions_and_salary, add_excel_style, send_salary_email
from app_human_resources.private_dict import check_report_default_data, checking_in_fields
from models.models import ErpAccount, ErpCheckingRecords, ErpDepartment, ErpSalaryBase, ErpSalaryChangeLog, ErpPublicSettings, \
    ErpSalaryDetail, ErpSalary
from modules.qy_wechat.base import QWechatBase
from settings import logger, CF
from utils.db.account_handler import get_current_active_user, UserDict
from utils.db.db_handler import get_default_db
from utils.db.model_handler import ModelDataHelper
from utils.enum.enum_account import SalaryChangeType, SalaryStatus, EmployeeType, EmployeeStatus
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse
from app_finance.modules import process_performance_rating_records

erp_account = CF.get_crud(ErpAccount)
erp_checking_records = CF.get_crud(ErpCheckingRecords)
erp_salary_base = CF.get_crud(ErpSalaryBase)
erp_salary_change_log = CF.get_crud(ErpSalaryChangeLog)
erp_public_settings = CF.get_crud(ErpPublicSettings)
erp_salary_detail = CF.get_crud(ErpSalaryDetail)
erp_salary = CF.get_crud(ErpSalary)
erp_performance_records = CF.get_crud(ErpPerformanceRecords)
erp_department = CF.get_crud(ErpDepartment)

router = APIRouter(prefix="/salary", tags=["薪资"])


@router.get(f"/download_salary_template")
async def download_salary_template(
        ym: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 下载薪资填写报告
    """
    data = await get_salary_check_records(db, ym)
    if not data:
        return await ApiFailedResponse(f'{ym}没有数据,请联系管理员')
    data = [dict(i) for i in data]
    checking_data = defaultdict(lambda: check_report_default_data.copy())
    salary_account_ids = []

    for row in data:
        account_id = row['account_id']
        if account_id not in salary_account_ids:
            salary_account_ids.append(account_id)
        v = row['check_value']
        if not checking_data[account_id]['account_id']:
            checking_data[account_id]['account_id'] = row['account_id']
            checking_data[account_id]['employee_name'] = row['employee_name']
            checking_data[account_id]['employee_number'] = row['employee_number']
            checking_data[account_id]['salary_base'] = row['salary_base']
            checking_data[account_id]['salary_performance'] = row['salary_performance']
            checking_data[account_id]['enterprise_name'] = row['enterprise_name']
            checking_data[account_id]['ym'] = row['ym']
        if v == '正常':
            continue

        checking_data[account_id][checking_in_fields[v]] += 1
    checking_data_list = [v for k, v in checking_data.items()]
    checking_data_list.sort(key=lambda x: x['account_id'])
    # 处理其他情况
    statistic_data = []
    for checking_row in checking_data_list:
        new_row = calculate_deductions_and_salary(checking_row)
        statistic_data.append(new_row)
    company_five_insurances = await erp_public_settings.get_one(db, dict_key='company_five_insurances')
    exclude_company_five_insurances = await erp_public_settings.get_one(db, dict_key='exclude_company_five_insurances')
    # 查询员工基本信息表用于组装
    accounts = await erp_account.get_many(db, raw=[
        ErpAccount.id.in_(salary_account_ids)
    ])
    accounts_map = {str(i.id): i for i in accounts}
    # 组装填报表字段
    collect_data = []
    for line in statistic_data:
        item = {
            # 基本信息
            "account_id": line['account_id'],
            "employee_name": line['employee_name'],
            "employee_number": line['employee_number'],
            "ym": line['ym'],
            "enterprise_name": line['enterprise_name'],
            "salary_base": line['salary_base'],
            "salary_performance": line['salary_performance'],
            "actual_attendance_days": line['actual_attendance_days'],
            # 扣除或补贴
            "forgot_to_check_in_deduction": line['forgot_to_check_in_deduction'],
            "late_deduction": line['late_deduction'],
            "early_leave_deduction": line['early_leave_deduction'],
            "absent_without_notice_deduction": line['absent_without_notice_deduction'],
            "personal_leave_deduction": line['personal_leave_deduction'],
            "sick_leave_deduction": line['sick_leave_deduction'],
            "sick_leave_addition": line['sick_leave_addition'],
            "actual_salary": line['actual_salary'],

        }
        if accounts_map.get(str(line['account_id'])) is None:
            return await ApiFailedResponse(f'员工{line["employee_name"]}不存在')
        if (line['account_id'] in exclude_company_five_insurances.dict_value or
                (accounts_map.get(str(line['account_id'])).employee_type in [EmployeeType.PartTimeJob.value,
                                                                             EmployeeType.Internship.value])):  # 排除列表的账号公司不承担

            empty_fill = copy.deepcopy(company_five_insurances.dict_value)
            empty_fill = {k: 0 for k, _ in empty_fill.items()}
            item.update(empty_fill)
            item.update({"social_security_housing_fund_total_company": 0})
        else:
            item.update(company_five_insurances.dict_value)  # 提取公司五险一金数值
            item.update({
                "social_security_housing_fund_total_company": sum(
                    [float(v) for v in company_five_insurances.dict_value.values()])
            })  # 计算五险一金合并值
        item.update(manual_field)  # 需要财务手动添加的部分
        collect_data.append(item)
    df = pd.DataFrame(collect_data)
    df.rename(columns=salary_columns_rename, inplace=True)
    if df.empty:
        return await ApiFailedResponse('没有数据,请联系管理员')
    output = BytesIO()
    output = add_excel_style(df, output, columns_to_color)
    output.seek(0)
    filename = f'员工工资表模版_{ym}'
    filename = quote(filename)
    headers = {
        'Content-Disposition': f'attachment; filename="{filename}"'
    }
    # 返回StreamingResponse
    return StreamingResponse(output, media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                             headers=headers)


@router.get(f"/salary_base")
async def salary_base(
        page: int = None,
        page_size: int = None,
        keyword: str = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 工资基本表
    """
    data = await get_salary_base(db, keyword, page, page_size, )
    count = await get_salary_base(db, keyword, count=True)
    return await ApiSuccessResponse({
        "count": count,
        "data": data,
    })


# 修改基本薪资表
@router.put("/salary_base/{salary_base_id}")
async def modify_salary_base(
        salary_base_id: int,
        params: SalaryBaseEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 薪资基本表调整
    - 这里只支持修改enterprise_id，bank_sub_name，bank_card_number，bank_city
    """
    update_item = {
        "update_time": datetime.now(settings.TIME_ZONE)
    }
    if params.enterprise_id:
        update_item.update({"enterprise_id": params.enterprise_id})
    if params.bank_sub_name:
        update_item.update({"bank_sub_name": params.bank_sub_name})
    if params.bank_card_number:
        update_item.update({"bank_card_number": params.bank_card_number})
    if params.bank_city:
        update_item.update({"bank_city": params.bank_city})
    exist_base = await erp_salary_base.get_by_id(db, salary_base_id)
    if not exist_base:
        return await ApiFailedResponse('未查询到要修改的工资基本表')
    await erp_salary_base.update_one(db, salary_base_id, update_item)


@router.post(f"/modify_salary")
async def modify_salary(
        salary_item: SalaryBaseEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 工资调整
    """
    # data = await get_salary_base(db)
    new_val = {}
    if salary_item.to_salary and salary_item.to_salary > 0:
        if salary_item.salary_change_type == SalaryChangeType.BASE_SALARY.value:
            new_val = {
                "salary_base": salary_item.to_salary
            }

        else:
            new_val = {
                "salary_performance": salary_item.to_salary
            }
    if salary_item.enterprise_id:
        new_val.update({
            "enterprise_id": salary_item.enterprise_id
        })
    exist = await erp_salary_base.get_one(db, account_id=salary_item.account_id)
    if exist:
        current_salary = 0
        if salary_item.to_salary and salary_item.to_salary > 0:  # 如果是调整薪资
            current_salary = exist.salary_base \
                if salary_item.salary_change_type == SalaryChangeType.BASE_SALARY.value \
                else exist.salary_performance
        await erp_salary_base.update_one(db, obj_id=exist.id, new_values=new_val)
    else:
        current_salary = 0
        item = {
            "account_id": salary_item.account_id,
        }
        item.update(new_val)
        await erp_salary_base.create(db, commit=True, **item)
    # 调薪日志
    if salary_item.to_salary and salary_item.to_salary > 0 and (salary_item.to_salary != current_salary):  # 如果是调整薪资
        await erp_salary_change_log.create(db, commit=True, **{
            "account_id": salary_item.account_id,
            "change_type": salary_item.salary_change_type,
            "salary_type": salary_item.salary_type,
            "prev_salary": current_salary,
            "current_salary": salary_item.to_salary,
            "create_by": user.uid,
            "update_by": user.uid,
        })
    return await ApiSuccessResponse(True)


@router.get(f"/salary_log")
async def query_salary_log(
        account_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 获取账户的工资调整记录
    """
    data = await erp_salary_change_log.get_many(db, {
        "account_id": account_id
    })
    for row in data:
        create_by = await erp_account.get_by_id(db, row.create_by)
        row.create_name = create_by.employee_name
        update_by = await erp_account.get_by_id(db, row.update_by)
        row.update_name = update_by.employee_name
    return await ApiSuccessResponse(data)


@router.post(f"/salary")
async def create_salary(
        salary: SalaryEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 创建工资表
    """
    obj = await erp_salary.create(db, commit=True, **{
        "title": salary.title,
        "ym": salary.ym,
        "salary_status": SalaryStatus.WAITING_UPLOAD.value,
        "comments": salary.comments,
    })
    return await ApiSuccessResponse(obj)


@router.get(f"/salary")
async def query_salary(
        page: int = None,
        page_size: int = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查看工资表
    """
    if page:
        data = await erp_salary.get_many_with_pagination(db, page, page_size, reverse=True)
    else:
        data = await erp_salary.get_many(db, reverse=True)
    count = await erp_salary.count(db)
    return await ApiSuccessResponse({
        "count": count,
        "data": data,
    })


@router.delete(f"/salary")
async def delete_salary(
        salary_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除工资表
    """
    delete_salary_entity = await erp_salary.get_by_id(db, salary_id)
    if not delete_salary_entity:
        return await ApiFailedResponse('未查询到要删除的工资条')
    old_salary = copy.deepcopy(delete_salary_entity)
    await erp_salary.delete_one(db, obj_id=salary_id, commit=True)
    # 删除以后重置工资条
    await erp_salary_detail.delete_many(db, {"salary_id": salary_id}, commit=True)
    await erp_salary.create(db, commit=True, **{
        "title": old_salary.title,
        "ym": old_salary.ym,
        "salary_status": SalaryStatus.WAITING_UPLOAD.value,
        "comments": '删除后程序自动生成',
    })
    return await ApiSuccessResponse(True)


@router.post(f"/upload_whole_salary")
async def upload_whole_salary(
        salary_id: int,
        file: UploadFile,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 上传完整工资表

    """
    contents = await file.read()
    df = pd.read_excel(BytesIO(contents))
    data = df.to_dict('records')
    for row in data:
        # company_social_insurance_and_housing_fund = row["社保及公积金合计(公司)"]
        personal_social_insurance_and_housing_fund = (row["养老保险扣款(个人)"] + row["医疗保险扣款(个人)"] +
                                                      row["失业保险扣款(个人)"] + row["住房公积金扣款(个人)"]
                                                      )
        # 到手工资
        issued_salary = (row["应发合计"]
                         # - company_social_insurance_and_housing_fund
                         - personal_social_insurance_and_housing_fund
                         - row["个人所得税"]
                         - row["本月额外扣款"]
                         + row["本月额外补款"]
                         + row["奖金和津贴"]
                         )
        await erp_salary_detail.create(db, commit=False, **{
            "salary_id": salary_id,
            "account_id": row["账号ID"],
            "enterprise_name": row["企业名称"],
            "actual_salary": row["应发合计"],
            "issued_salary": issued_salary,
            "detail": row,
            "is_send": 0,
            "is_check": 0,
        })
    await erp_salary.update_one(db, salary_id, {
        "salary_status": SalaryStatus.WAITING_SEND.value,
        "update_time": datetime.now(settings.TIME_ZONE)
    }, commit=False)

    try:
        await db.commit()
    except Exception as e:
        logger.info(e)
        await db.rollback()
    return await ApiSuccessResponse(True)


@router.get(f"/salary_detail")
async def query_salary_detail(
        salary_id: int = None,
        detail_id: int = None,
        page: int = None,
        page_size: int = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查看工资条详情
     - salary_id 和detail_id 必传一个; 传salary_id时需传递分页数据
    """
    if salary_id and salary_id > 0:
        if page:
            data = await erp_salary_detail.get_many_with_pagination(db, page, page_size, {"salary_id": salary_id})
        else:
            data = await erp_salary_detail.get_many(db, {"salary_id": salary_id})
        account_list = await erp_account.get_many(db, )
        account_dict = {i.id: {
            "employee_name": i.employee_name,
            "employee_number": i.employee_number,
            "employee_type": i.employee_type,
            "employee_status": i.employee_status,
            "qy_wechat_position": i.qy_wechat_position,
        } for i in account_list}
        count = await erp_salary_detail.count(db, {"salary_id": salary_id})
        for row in data:
            row.account_info = account_dict[row.account_id]
        return await ApiSuccessResponse({
            "count": count,
            "data": data,
        })
    if detail_id and detail_id > 0:
        salary_detail_entity = await erp_salary_detail.get_by_id(db, detail_id)
        if not salary_detail_entity:
            return await ApiFailedResponse('指定工资条不存在或已删除')
        account_id = salary_detail_entity.account_id
        if user.outside and account_id != user.uid:
            return await ApiFailedResponse('查询的账户不合法')
        salary_obj = await erp_salary.get_by_id(db, salary_detail_entity.salary_id)
        account = await erp_account.get_by_id(db, account_id)
        return_data = {
            "ym": salary_obj.ym,
            "account": {
                "employee_name": account.employee_name,
                "employee_number": account.employee_number,
            },
            "salary": salary_detail_entity,
        }
        return await ApiSuccessResponse(return_data)


@router.get(f"/download_salary_detail")
async def download_salary_detail(
        salary_id: int,
        inner_callback: bool = False,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 下载完整工资表
    """
    data = await erp_salary_detail.get_many(db, {"salary_id": salary_id})
    if not data:
        return await ApiFailedResponse('不存在该工资条')
    salary_obj = await erp_salary.get_by_id(db, salary_id)
    return_data = []
    for i in data:
        row = ModelDataHelper.model_to_dict(i)
        new_row = row['detail']
        new_row['实发工资'] = row['issued_salary']
        return_data.append(new_row)
    df = pd.DataFrame(return_data)
    # 按照columns_header顺序进行数据位置调整
    df = df[columns_header]
    output = BytesIO()
    output = add_excel_style(df, output, {k: "f5f6f9" for k in df.columns})
    output.seek(0)
    filename = f'员工最终明细工资表_{salary_obj.ym}'
    filename = quote(filename)
    headers = {
        'Content-Disposition': f'attachment; filename="{filename}"'
    }
    if inner_callback:
        return output.getvalue()
    # 返回StreamingResponse
    return StreamingResponse(output, media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                             headers=headers)


@router.post(f"/archive_salary")
async def archive_salary(
        salary_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 归档 报送
     调用该接口， 会将最终工资统计和银行报送单邮件发送给指定管理员

    """
    salary_obj = await erp_salary.get_by_id(db, salary_id)
    if not salary_obj:
        return await ApiFailedResponse('工资条不存在')
    attachment_list = []  # 附件
    # 发送银行的表格附件处理
    bank_info = await get_salary_bank(db, salary_id)
    data = [dict(i) for i in bank_info]
    bank_df = pd.DataFrame(data)
    bank_df = bank_df.fillna('')
    bank_df.rename(columns={
        "enterprise_name": "绑定主体",
        "bank_card_number": "账号",
        "employee_name": "户名",
        "bank_sub_name": "开户行",
        "bank_city": "开户地",
        "issued_salary": "金额",
        "comments": "注释",
    }, inplace=True)
    bank_output = BytesIO()
    blank_row = bank_df[bank_df['绑定主体'] == '']
    if not blank_row.empty:  # 正确的判断DataFrame是否为空的方式
        return await ApiFailedResponse(f'有员工主体信息不完整：{blank_row["户名"].tolist()}')
    bank_output = add_excel_style(bank_df, bank_output, {k: "f5f6f9" for k in bank_df.columns})
    bank_output.seek(0)
    bank_content = bank_output.getvalue()
    attachment_list.append({
        "file_name": f"报送银行工资条_{salary_obj.ym}.xlsx",
        "content": base64.b64encode(bank_content).decode('utf-8'),
    })
    # 工资统计表
    file_content = await download_salary_detail(salary_id=salary_id, inner_callback=True, db=db)
    encoded_content = base64.b64encode(file_content).decode('utf-8')
    attachment_list.append({
        "file_name": f"工资统计表_{salary_obj.ym}.xlsx",
        "content": encoded_content,
    })
    # 查找收件人
    setting = await erp_public_settings.get_one(db, dict_key='salary_statistic_sender')
    sender_account_ids = setting.dict_value['receiver'].split(',')
    receiver = await erp_account.get_many(db, raw=[
        ErpAccount.id.in_(sender_account_ids)
    ])
    userids = [i.qy_wechat_userid for i in receiver]
    # 邮件发送
    qw_base = QWechatBase()
    access_token = await qw_base.get_access_token()
    subject = f'工资条生成提醒_{salary_obj.ym}'
    content = f'本邮件是由ERP2.0自动发放，详见附件'

    await qw_base.send_email(access_token, userids, subject, content, attachment_list)
    await erp_salary.update_one(db, salary_id, {
        "admin_certified": 1,
        "salary_status": SalaryStatus.ADMIN_SEND.value,
        "update_time": datetime.now(settings.TIME_ZONE)
    })
    return await ApiSuccessResponse(True)


@router.post("/salary_email_manual_all")
async def salary_email_manual_all(
        salary_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 一键发放所有人工资条
    """
    receivers = await salary_detail_by_salary_id(db, salary_id)
    not_send_list, send_list = await send_salary_email(db, salary_id, receivers)
    await erp_salary.update_one(db, salary_id, {
        "salary_status": SalaryStatus.EMPLOYEE_SEND.value,
        "update_time": datetime.now(settings.TIME_ZONE)
    })
    return await ApiSuccessResponse({
        "not_send": not_send_list,
        "waiting_send": send_list,
    })


@router.post("/salary_email_manual")
async def salary_email_manual(
        salary_id: int,
        account_ids: List[int],
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 手动发放指定员工工资条
    """
    # print(salary_id, account_ids)
    receivers = await erp_account.get_many(db, raw=[
        ErpAccount.id.in_(account_ids)
    ])
    if not receivers:
        return await ApiFailedResponse('收件人不存在')
    salary_obj = await erp_salary.get_by_id(db, salary_id)
    not_send_list, send_list = await send_salary_email(db, salary_id, receivers, salary_obj)
    return await ApiSuccessResponse({
        "not_send": not_send_list,
        "waiting_send": send_list,
    })


@router.post("/performance_rating_record")
async def create_performance_rating_record(
        performance: PerformanceRecordEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 创建绩效评级记录
    """
    
    # 检查员工是否存在
    account = await erp_account.get_by_id(db, performance.account_id)
    if not account:
        return await ApiFailedResponse("员工不存在")
    
    # 检查是否已存在相同年份和季度的记录
    exist_record = await erp_performance_records.get_one(db, 
                                                         account_id=performance.account_id, 
                                                         yyyy=performance.yyyy, 
                                                         quarter=performance.quarter, 
                                                         disable=0)
    
    if exist_record:
        return await ApiFailedResponse(f"{performance.yyyy}年第{performance.quarter}季度的绩效评级记录已存在")
    
    # 创建绩效评级记录
    record = await erp_performance_records.create(db, commit=True, **{
        "account_id": performance.account_id,
        "yyyy": performance.yyyy,
        "quarter": performance.quarter,
        "rating": performance.rating,
        "create_by": user.uid,
        "update_by": user.uid,
        "create_time": datetime.now(settings.TIME_ZONE),
        "update_time": datetime.now(settings.TIME_ZONE),
        "disable": 0
    })
    
    return await ApiSuccessResponse(record)


@router.put("/performance_rating_record/{rating_record_id}")
async def update_performance_rating_record(
        rating_record_id: int,
        performance: PerformanceRecordUpdate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 更新评级
    """
    
    # 检查记录是否存在
    record = await erp_performance_records.get_by_id(db, rating_record_id)
    if not record:
        return await ApiFailedResponse("绩效评级记录不存在")
    
    # 更新记录
    record.rating = performance.rating
    record.update_by = user.uid
    record.update_time = datetime.now(settings.TIME_ZONE)
    await db.commit()
    
    return await ApiSuccessResponse(True)


@router.delete("/performance_rating_record/{rating_record_id}")
async def delete_performance_rating_record(
        rating_record_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除绩效评级记录
    """
    
    # 检查记录是否存在
    record = await erp_performance_records.get_by_id(db, rating_record_id)
    if not record:
        return await ApiFailedResponse("绩效评级记录不存在")
    
    # 软删除记录
    record.disable = 1
    record.update_by = user.uid
    record.update_time = datetime.now(settings.TIME_ZONE)
    await db.commit()
    
    return await ApiSuccessResponse(True)


@router.get("/performance_rating_records")
async def get_performance_rating_records(
        page: int = 1,
        page_size: int = 20,
        keyword: str = None,
        yyyy: int = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 获取所有绩效评级记录
    - 支持分页和关键词搜索
    - 支持按年份筛选
    """
    result = await process_performance_rating_records(db, page, page_size, keyword, yyyy)
    return await ApiSuccessResponse(result)
    

# 教师课时费基本表
@router.get("/teacher_class_fee")
async def get_teacher_class_fee(
    page: int = 1,
    page_size: int = 20,
    keyword: str = None,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 教师课时费基本表 erp_account_teacher.class_fee, class_fee_type
    - class_fee_type 1 标准课时费 2 比例课时费
    - class_fee： 对应的课时费或比例
    """
    result = await get_teacher_class_fee_module(db, page, page_size, keyword,)
    count = await get_teacher_class_fee_module(db, page, page_size, keyword, count=True)
    return await ApiSuccessResponse({
        "count": count,
        "data": result,
    })
    
# 调整教师课时费
@router.post("/adjust_teacher_class_fee")
async def query_adjust_teacher_class_fee(
    parms: TeacherClassFeeAdjustment,
    # teacher_id: int,
    # class_fee: float,
    # class_fee_type: int,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 调整教师课时费
    - 如何调整的日志存储在ErpAccountTeacherLog
    - 日志格式：由标准/比例课时费调整为标准/比例课时费，调整前比例/标准课时费为：100%(数据库存储格式为0.0000)/3000，调整后比例/标准课时费为：100%/3000
    - 若无原课时费类型或金额，将使用默认值：标准课时费(1)和0.0
    """
    from app_finance.modules import adjust_teacher_class_fee_module
    
    # 检查课时费类型是否有效
    if parms.class_fee_type not in [1, 2]:
        return await ApiFailedResponse("课时费类型无效，必须是1(标准课时费)或2(比例课时费)")
    
    # 检查课时费数值
    if parms.class_fee <= 0:
        return await ApiFailedResponse("课时费必须大于0")
    
    # 如果是比例课时费，确保值小于等于1
    if parms.class_fee_type == 2 and parms.class_fee > 1:
        return await ApiFailedResponse("比例课时费必须小于等于100%（输入值应小于等于1）")
    
    # 调用模块函数进行调整
    success, message = await adjust_teacher_class_fee_module(
        db, parms.teacher_id, parms.class_fee, parms.class_fee_type, user.uid
    )
    
    if not success:
        return await ApiFailedResponse(message)
    
    return await ApiSuccessResponse(message)


# 教师课时费日志
@router.get("/teacher_class_fee_log")
async def get_teacher_class_fee_log(
    teacher_id: int,
    page: int = 1,
    page_size: int = 20,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 教师课时费日志
    """
    from app_finance.crud import get_teacher_class_fee_log_crud
    
    # 调用crud函数获取日志
    logs = await get_teacher_class_fee_log_crud(db, teacher_id, page, page_size)
    
    return await ApiSuccessResponse(logs)
