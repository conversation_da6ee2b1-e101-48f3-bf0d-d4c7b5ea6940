import copy
from datetime import datetime
from typing import List
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
import settings
from app_finance.crud import  finance_income_expense_detail_modules, finance_income_expense_modules, get_payment_account
from app_finance.serializer import SalaryEntity, UpdateDict, CostTypeEntity, ReceiptType, PaymentAccountEntity
from models.models import ErpEnterprise, ErpPublicSettings, ErpAccount, ErpSalaryBase
from models.m_finance import  ErpFinanceTradeRefund, ErpFinanceTradePayment, ErpFinanceCostType
from settings import logger, CF
from utils.db.account_handler import get_current_active_user, UserDict
from utils.db.db_handler import get_default_db, get_uat_db
from utils.db.model_handler import ModelDataHelper
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

erp_enterprise = CF.get_crud(ErpEnterprise)
erp_public_settings = CF.get_crud(ErpPublicSettings)
erp_account = CF.get_crud(ErpAccount)
erp_finance_trade_refund = CF.get_crud(ErpFinanceTradeRefund)
erp_finance_trade_payment = CF.get_crud(ErpFinanceTradePayment)
erp_finance_cost_type = CF.get_crud(ErpFinanceCostType)
# erp_finance = CF.get_crud(ErpFinance)
# erp_finance_detail = CF.get_crud(ErpFinanceDetail)

ERP_SALARY_BASE = CF.get_crud(ErpSalaryBase)

router = APIRouter(prefix="/finance", tags=["财务"])


@router.get(f"/salary_entity")
async def query_salary_entity(
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 公司主体信息
    """
    data = await erp_enterprise.get_many(db, reverse=True)
    return await ApiSuccessResponse(data)


@router.post(f"/salary_entity")
async def create_salary_entity(
        entity: SalaryEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 新增 公司主体信息
    """
    obj = await erp_enterprise.create(db, commit=True, **{
        "enterprise_name": entity.enterprise_name,
        "enterprise_short_name": entity.enterprise_short_name,
        "enterprise_bank_account": entity.enterprise_bank_account,
    })
    return await ApiSuccessResponse(obj)


@router.delete(f"/salary_entity")
async def delete_salary_entity(
        enterprise_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除 公司主体信息
    """
    obj = await erp_enterprise.delete_one(db, enterprise_id)
    return await ApiSuccessResponse(obj)


@router.get(f"/fiof_company")
async def query_five_insurances_one_fund(
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 五险一金（公司）
    """
    company_five_insurances = await erp_public_settings.get_one(db, dict_key='company_five_insurances')
    # print(company_five_insurances.dict_value)
    return await ApiSuccessResponse(company_five_insurances.dict_value)


@router.post(f"/fiof_company")
async def update_five_insurances_one_fund(
        update_data: UpdateDict,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 修改 五险一金（公司）-其中一项
    """
    company_five_insurances = await erp_public_settings.get_one(db, dict_key='company_five_insurances')
    old = company_five_insurances.dict_value
    current = copy.deepcopy(old)
    current[update_data.dict_key] = float(update_data.dict_value)
    new = await erp_public_settings.update_one(db, company_five_insurances.id,
                                               {"dict_value": current,
                                                "update_time": datetime.now(settings.TIME_ZONE)})
    return await ApiSuccessResponse(new)


@router.get(f"/fiof_exclude_account")
async def query_fiof_exclude_account(
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 五险一金（公司）排除账户
    """
    exclude_company_five_insurances = await erp_public_settings.get_one(db, dict_key='exclude_company_five_insurances')
    accounts = exclude_company_five_insurances.dict_value
    account_detail = await erp_account.get_many(db, raw=[
        ErpAccount.id.in_(accounts)
    ])
    return await ApiSuccessResponse(account_detail)


@router.delete(f"/fiof_exclude_account")
async def delete_fiof_exclude_account(
        account_ids: List[int],
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除 五险一金（公司）排除账户
    """
    exclude_company_five_insurances = await erp_public_settings.get_one(db, dict_key='exclude_company_five_insurances')
    old = exclude_company_five_insurances.dict_value
    new = [i for i in old if i not in account_ids]
    new = await erp_public_settings.update_one(db, exclude_company_five_insurances.id,
                                               {"dict_value": new,
                                                "update_time": datetime.now(settings.TIME_ZONE)})
    return await ApiSuccessResponse(new.dict_value)


@router.post(f"/fiof_exclude_account")
async def add_fiof_exclude_account(
        account_ids: List[int],
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 新增 五险一金（公司）排除账户
    [388,389,390,391,392]
    """
    exclude_company_five_insurances = await erp_public_settings.get_one(db, dict_key='exclude_company_five_insurances')
    old = exclude_company_five_insurances.dict_value
    new = list(set(old) | set(account_ids))
    new = await erp_public_settings.update_one(db, exclude_company_five_insurances.id,
                                               {"dict_value": new,
                                                "update_time": datetime.now(settings.TIME_ZONE)})
    return await ApiSuccessResponse(new.dict_value)


# 新增公司付款账户
@router.post(f"/payment_account")
async def create_payment_account(
        payment_account: PaymentAccountEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 新增 公司付款账户
    - to_type 1 员工 2 供应商 3 兼职 4 学生
    - type 1私账 2 公账
    - bank_card_number 银行卡号
    - bank_sub_name 开户行支行名称
    """
    if payment_account.to_type == 1:
        return await ApiFailedResponse("不支持在此创建员工类型的付款账户")
    await ERP_SALARY_BASE.create(db, commit=True, **{
        "to_name": payment_account.to_name,
        "to_type": payment_account.to_type,
        "type": payment_account.type,
        "bank_card_number": payment_account.bank_card_number,
        "bank_sub_name": payment_account.bank_sub_name,
        "bank_city": payment_account.bank_city,
        "account_id": 0,
        "enterprise_id": 0,
        "salary_base": 0,
        "salary_performance": 0,
    })
    return await ApiSuccessResponse()


# 查询公司付款账户
@router.get(f"/payment_account")
async def query_payment_account(
        type: int = None,
        to_type: int = None,
        keyword: str = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 公司付款账户
    - type 1私账 2 公账
    - to_type 1 员工 2 供应商 3 兼职 4 学生
    """
    data = await get_payment_account(db, type, to_type, keyword)
    return await ApiSuccessResponse(data)


# 删除公司付款账户
@router.delete(f"/payment_account")
async def delete_payment_account(
        payment_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除 公司付款账户
    """
    # 先查询是否存在且不是员工类型才能删除
    exist = await ERP_SALARY_BASE.get_by_id(db, payment_id)
    if not exist:
        return await ApiFailedResponse("删除的付款账户不存在")
    if exist.to_type == 1:
        return await ApiFailedResponse("员工类型的付款账户不支持删除")
    obj = await ERP_SALARY_BASE.delete_one(db, payment_id)
    return await ApiSuccessResponse(obj)


# 修改公司付款账户
@router.put(f"/payment_account")
async def update_payment_account(
        payment_id: int,
        payment_account: PaymentAccountEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 修改 公司付款账户
    """
    # 先查询是否存在且不是员工类型才能修改
    exist = await ERP_SALARY_BASE.get_by_id(db, payment_id)
    if not exist:
        return await ApiFailedResponse("修改的付款账户不存在")
    if exist.to_type == 1:
        return await ApiFailedResponse("员工类型的付款账户不支持修改")
    update_item = {
        "update_time": datetime.now(settings.TIME_ZONE)
    }
    if payment_account.to_name:
        update_item["to_name"] = payment_account.to_name
    if payment_account.to_type:
        update_item["to_type"] = payment_account.to_type
    if payment_account.type:
        update_item["type"] = payment_account.type
    if payment_account.bank_card_number:
        update_item["bank_card_number"] = payment_account.bank_card_number
    if payment_account.bank_sub_name:
        update_item["bank_sub_name"] = payment_account.bank_sub_name
    if payment_account.bank_city:
        update_item["bank_city"] = payment_account.bank_city

    obj = await ERP_SALARY_BASE.update_one(db, payment_id, new_values=update_item, commit=True)
    return await ApiSuccessResponse(obj)


# 查询费用类型
@router.get(f"/cost_type")
async def query_cost_type(
    tag: int = None,
    type: int = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询 费用类型
    """
    conditions = {}
    if tag:
        conditions["tag"] = tag
    if type:
        conditions["type"] = type
    data = await erp_finance_cost_type.get_many(db, conditions)
    tree_data = ModelDataHelper.list_to_tree(0, data, child_field="children", parent_field="parent_id", id_field="id")
    return await ApiSuccessResponse(tree_data)


# 新增费用类型
@router.post(f"/cost_type")
async def create_cost_type(
        cost_type: CostTypeEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 新增 费用类型
    - tag 标签大类，例如1 营业毛利 2 总成本 3 其他收入 4 其他支出
    - type 1 收入 2 支出 3 收支相抵 4 其他
    """
    obj = await erp_finance_cost_type.create(db, commit=True, **{
        "name": cost_type.name,
        "type": cost_type.type,
        "parent_id": cost_type.parent_id,
        "is_admin": cost_type.is_admin,
        "add_finance": cost_type.add_finance,
        "tag": cost_type.tag,
    })
    return await ApiSuccessResponse(obj)


# 删除费用类型
@router.delete(f"/cost_type")
async def delete_cost_type(
        cost_type_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除 费用类型
    """
    obj = await erp_finance_cost_type.delete_one(db, cost_type_id)
    return await ApiSuccessResponse(obj)


# 修改费用类型
@router.put(f"/cost_type")
async def update_cost_type(
        cost_type_id: int,
        cost_type: CostTypeEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 修改 费用类型
    """
    # 进行是否是管理员的判断， 如果parent_id是0， 则is_admin必须是1, 且role_id必须是管理员账户
    # 如果不是管理员，都必须是财务人员才能操作
    # print(user)
    exist = await erp_finance_cost_type.get_by_id(db, cost_type_id)
    if not exist:
        return await ApiFailedResponse("修改的费用类型不存在")
    
    if cost_type.name:
        exist.name = cost_type.name
    if cost_type.type:
        exist.type = cost_type.type
    if cost_type.parent_id:
        exist.parent_id = cost_type.parent_id
    if cost_type.is_admin:
        exist.is_admin = cost_type.is_admin
    if cost_type.add_finance:
        exist.add_finance = cost_type.add_finance
    if cost_type.tag:
        exist.tag = cost_type.tag
    exist.update_time = datetime.now(settings.TIME_ZONE)
    await db.commit()
    return await ApiSuccessResponse(True)


# 通过费用类型id列表查询费用类型
@router.get(f"/cost_type_by_ids")
async def query_cost_type_by_ids(
        cost_type_ids: str,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 通过费用类型id列表查询费用类型
    - cost_type_ids 费用类型id列表, 逗号分隔
    """

    cost_type_ids = [int(i) for i in cost_type_ids.split(",")]
    data = await erp_finance_cost_type.get_many(db, raw=[
        ErpFinanceCostType.id.in_(cost_type_ids)
    ])
    return await ApiSuccessResponse(data)

