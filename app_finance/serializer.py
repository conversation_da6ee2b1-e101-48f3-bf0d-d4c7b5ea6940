from datetime import datetime, date
from typing import Union, List, Optional
from pydantic import BaseModel


class SalaryEntity(BaseModel):
    enterprise_name: str = None
    enterprise_short_name: str = None
    enterprise_bank_account: str = None


class UpdateDict(BaseModel):
    dict_key: str
    dict_value: str


class SalaryBaseEntity(BaseModel):
    account_id: int
    salary_change_type: Optional[int]
    salary_type: Optional[int]
    to_salary: Optional[float]
    enterprise_id: Optional[int]

    bank_sub_name: Optional[str]
    bank_card_number: Optional[str]
    bank_city: Optional[str]


class SalaryEntity(BaseModel):
    title: str
    salary_status: int
    ym: int
    comments: str


class CostTypeEntity(BaseModel):
    name: str
    type: int
    parent_id: int
    is_admin: Optional[int] = 0
    add_finance: Optional[int] = 0
    tag: Optional[int] = 0


class ReceiptType(BaseModel):
    name: str
    type: int
    cost_type_id: int


# 学员电子钱包调整
class StudentWalletAdjustment(BaseModel):
    # 均为必选参数
    change_type: int  # 1 增加 2 减少
    amount: float
    desc: str


class PaymentAccountEntity(BaseModel):
    to_name: str
    to_type: int
    type: int
    bank_card_number: str
    bank_sub_name: str
    bank_city: Optional[str] = ""


# 账户
class BankAccountCreate(BaseModel):
    # 均为可选参数
    campus_id: Optional[int] = None
    account_alias: Optional[str] = None
    default_account_type: Optional[int] = None
    account_type: Optional[int] = None
    account_number: Optional[str] = None
    bank_name: Optional[str] = None
    bank_sub_name: Optional[str] = None
    account_holder: Optional[str] = None
    balance: Optional[float] = None
    currency_type: Optional[int] = None
    is_active: Optional[int] = None
    cmb_merchant_id: Optional[str] = None


# 财务角色
class FinanceRoleCreate(BaseModel):
    type: int
    name: str
    account_id: int


# 资金预算表
class FinanceBudgetUpdate(BaseModel):
    expenses: float


# 绩效评级记录
class PerformanceRecordEntity(BaseModel):
    account_id: int
    yyyy: int
    quarter: int
    rating: str

# 绩效评级记录
class PerformanceRecordUpdate(BaseModel):
    rating: str


# 银行账户类型
class BankAccountTypeCreate(BaseModel):
    account_type_name: str
    is_public: int
    is_virtual: int


# 教师课时费调整
class TeacherClassFeeAdjustment(BaseModel):
    teacher_id: int
    class_fee: float
    class_fee_type: int
    comments: Optional[str] = None


# 重新发起退款
class ReRefundParams(BaseModel):
    refund_order_no: str
