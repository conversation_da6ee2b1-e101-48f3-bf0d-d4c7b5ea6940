from importlib import import_module

# 定义模块路径的列表
from fastapi import APIRouter

modules = [
    "app_finance.api.salary",
    "app_finance.api.finance",
    "app_finance.api.finance_account",
    "app_finance.api.finance_statistic",

    # 可以继续添加更多模块
]
router = APIRouter(prefix='/app_finance')

for module_path in modules:
    module = import_module(module_path)
    router.include_router(module.router)
