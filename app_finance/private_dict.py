# 需要财务手动添加的部分
manual_field = {
    "pension_insurance_deduction": 0,
    "medical_insurance_deduction": 0,
    "unemployment_insurance_deduction": 0,
    "housing_fund_deduction": 0,
    "personal_income_tax": 0,
    "current_month_additional_deduction": 0,
    "current_month_additional_payment": 0,
    "bonus_and_allowance": 0,
}

columns_to_color = {
    '账号ID': 'f5f6f9',
    '员工姓名': 'f5f6f9',
    '员工编号': 'f5f6f9',
    '年月': 'f5f6f9',
    '企业名称': 'f5f6f9',
    '基本工资': 'f5f6f9',
    '绩效工资': 'f5f6f9',
    '计薪天数': 'f5f6f9',
    '忘记打卡扣款': 'f5f6f9',
    '迟到扣款': 'f5f6f9',
    '早退扣款': 'f5f6f9',
    '旷工扣款': 'f5f6f9',
    '事假扣款': 'f5f6f9',
    '病假扣款': 'f5f6f9',
    '病假补助': 'f5f6f9',
    '应发合计': 'f5f6f9',
    '医疗保险(公司)': 'f5f6f9',
    '养老保险(公司)': 'f5f6f9',
    '生育保险(公司)': 'f5f6f9',
    '工伤保险(公司)': 'f5f6f9',
    '住房公积金(公司)': 'f5f6f9',
    '失业保险(公司)': 'f5f6f9',
    '大病保险(公司)': 'f5f6f9',
    '社保及公积金合计(公司)': 'f5f6f9'
}

salary_columns_rename = {
    "account_id": "账号ID",
    "employee_name": "员工姓名",
    "employee_number": "员工编号",
    "ym": "年月",
    "enterprise_name": "企业名称",
    "salary_base": "基本工资",
    "salary_performance": "绩效工资",
    "actual_attendance_days": "计薪天数",
    "forgot_to_check_in_deduction": "忘记打卡扣款",
    "late_deduction": "迟到扣款",
    "early_leave_deduction": "早退扣款",
    "absent_without_notice_deduction": "旷工扣款",
    "personal_leave_deduction": "事假扣款",
    "sick_leave_deduction": "病假扣款",
    "sick_leave_addition": "病假补助",
    "actual_salary": "应发合计",
    "medical_insurance_company": "医疗保险(公司)",
    "pension_insurance_company": "养老保险(公司)",
    "maternity_insurance_company": "生育保险(公司)",
    "work_injury_insurance_company": "工伤保险(公司)",
    "housing_provident_fund_company": "住房公积金(公司)",
    "unemployment_insurance_company": "失业保险(公司)",
    "critical_illness_insurance_company": "大病保险(公司)",
    "social_security_housing_fund_total_company": "社保及公积金合计(公司)",
    "pension_insurance_deduction": "养老保险扣款(个人)",
    "medical_insurance_deduction": "医疗保险扣款(个人)",
    "unemployment_insurance_deduction": "失业保险扣款(个人)",
    "housing_fund_deduction": "住房公积金扣款(个人)",
    "personal_income_tax": "个人所得税",
    "current_month_additional_deduction": "本月额外扣款",
    "current_month_additional_payment": "本月额外补款",
    "bonus_and_allowance": "奖金和津贴"
}

columns_header = [
    "账号ID", "员工姓名", "员工编号", "年月", "企业名称", "基本工资", "绩效工资", "计薪天数",
    "忘记打卡扣款", "迟到扣款", "早退扣款", "旷工扣款", "事假扣款", "病假扣款", "病假补助",
    "应发合计", "医疗保险(公司)", "养老保险(公司)", "生育保险(公司)", "工伤保险(公司)",
    "住房公积金(公司)", "失业保险(公司)", "大病保险(公司)", "社保及公积金合计(公司)",
    "养老保险扣款(个人)", "医疗保险扣款(个人)", "失业保险扣款(个人)", "住房公积金扣款(个人)",
    "个人所得税", "本月额外扣款", "本月额外补款", "奖金和津贴", "实发工资"
]
