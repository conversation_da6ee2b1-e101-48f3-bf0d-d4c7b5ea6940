from datetime import timedelta, datetime
from typing import List, Union
from sqlalchemy import select, and_, func, case, literal_column, or_
from sqlalchemy.orm import aliased
from sqlalchemy.ext.asyncio import AsyncSession
from models.m_class import ErpClass, ErpCourse, ErpCourseCategory, ErpCourseTerm, ErpClassChecking, ErpClassPlan
from models.m_finance import ErpBankAccount, ErpBankAccountLog, ErpStudentEwalletLog
from models.m_order import ErpOrderOffer, ErpOrder, ErpOrderStudent, ErpOrderRefund, ErpOrderRefundDetail
from models.m_teacher import ErpAccountTeacher, ErpAccountTeacherLog
from models.m_student import ErpStudent
from models.m_office import ErpOfficeCenter, ErpOfficeClassroom
from settings import CF, DAYS_IN_MONTH, SICK_LEAVE_SUBSIDY, logger, FRONTEND_BASE_URL
from models.models import ErpAccount, ErpSalaryDetail
import settings
from utils.enum.enum_account import BillType
from utils.enum.enum_order import StudentState
from utils.response.response_handler import ApiFailedResponse, ApiSuccessResponse
from app_finance.crud import (
    get_class_student_count, get_class_flash_times, get_class_order_stats,
    get_class_lecture_fees, get_class_consumption_stats, get_class_other_expenses
)
from utils.db.account_handler import AccountHandler
from utils.other.markdown import convert_to_html
from modules.queue.redis_queue import RedisQueue
from openpyxl.styles import PatternFill, Border, Side
from sqlalchemy.ext.asyncio import AsyncSession
import pandas as pd
import decimal
from app_order.crud import CreateBy
from decimal import Decimal, ROUND_HALF_UP

erp_student = CF.get_crud(ErpStudent)
erp_bank_account = CF.get_crud(ErpBankAccount)
erp_bank_account_log = CF.get_crud(ErpBankAccountLog)
erp_student_ewallet_log = CF.get_crud(ErpStudentEwalletLog)


def calculate_deductions_and_salary(checking_data):
    """
    # 考勤工资计算规则
    忘打卡扣款：忘打卡次数小于等于3次为0，大于3次为 次数*20元
    迟到扣款：1、迟到10分钟以内次数小于等于3次为0，大于3次为 次数*20元
             2、迟到10-30分钟为 次数*50元
                将1和2求和得到本月迟到扣款
    早退扣款：次数 * 100元
    旷工扣款：0.5天扣除0.5天实际工资，旷工1天扣除1天实际工资，即（固定工资+固定绩效）/21.75  *  对应的旷工天数
    实际计薪天数=21.75-病假-事假-旷工-其他全薪假
    事假扣除=（固定工资+固定绩效）/21.75  *  对应的事假天数
    病假扣除=（固定工资+固定绩效）/21.75  *  对应的病假天数  + 2100/21.75*0.8*对应的病假天数
    实发工资= （固定工资+固定绩效）/21.75 *（实际计薪天数+其他全薪假） - 忘打卡扣款-迟到扣款-早退扣款 +2100*0.8*对应的病假天数
    迟到、早退、忘打卡在表格中出现一次记为1次，病假、事假、旷工、其他全薪假在表格中出现一次记为0.5
    """

    fixed_salary = checking_data['salary_base'] or 0
    fixed_performance = checking_data['salary_performance'] or 0
    # 忘打卡扣款
    # forgot_to_check_in_deduction = checking_data['forgot_to_check_in'] * 20 \
    #     if checking_data['forgot_to_check_in'] > 3 else 0
    forgot_to_check_in_deduction = (decimal.Decimal(checking_data['forgot_to_check_in']) + 3) * 20 \
        if decimal.Decimal(checking_data['forgot_to_check_in']) > 0 else 0
    # 迟到扣款
    late_within_10_min_deduction = checking_data['late_within_10_min'] * 20 \
        if checking_data['late_within_10_min'] > 3 else 0  # 次数超出需补前面次数扣款
    late_10_to_30_min_deduction = checking_data['late_10_to_30_min'] * 50
    late_deduction = late_within_10_min_deduction + late_10_to_30_min_deduction
    # 早退扣款
    early_leave_deduction = checking_data['early_leave'] * 100
    # 旷工扣款
    absent_without_notice_deduction = 0.5 * checking_data['absent_without_notice'] * (
            fixed_salary + fixed_performance) / DAYS_IN_MONTH
    # 实际出勤天数
    actual_attendance_days = (DAYS_IN_MONTH
                              - checking_data['sick_leave'] * 0.5
                              - checking_data['personal_leave'] * 0.5
                              - checking_data['absent_without_notice'] * 0.5
                              # - checking_data['other_paid_leave'] * 0.5
                              )
    # 事假扣除
    personal_leave_deduction = (0.5 * checking_data['personal_leave']
                                * (fixed_salary + fixed_performance) / DAYS_IN_MONTH)
    # 病假扣
    sick_leave_deduction = (0.5 * checking_data['sick_leave']
                            * (fixed_salary + fixed_performance) / DAYS_IN_MONTH)
    sick_leave_addition = SICK_LEAVE_SUBSIDY * checking_data['sick_leave'] / DAYS_IN_MONTH * 0.5
    # 实发工资
    actual_salary = (
            (
                    (fixed_salary + fixed_performance)
                    / DAYS_IN_MONTH) * (
                actual_attendance_days
                # + checking_data['other_paid_leave'] * 0.5  # 加上全薪
            )
            - forgot_to_check_in_deduction  # 忘记打卡扣款
            - late_deduction  # 迟到扣款
            - early_leave_deduction  # 早退扣款
            - absent_without_notice_deduction  # 旷工惩罚性扣款
            + sick_leave_addition  # 病假补贴
    )

    # 添加字段到原数据
    checking_data['forgot_to_check_in_deduction'] = round(forgot_to_check_in_deduction, 2)
    checking_data['late_deduction'] = round(late_deduction, 2)
    checking_data['early_leave_deduction'] = round(early_leave_deduction, 2)
    checking_data['absent_without_notice_deduction'] = round(absent_without_notice_deduction, 2)
    checking_data['personal_leave_deduction'] = round(personal_leave_deduction, 2)
    checking_data['sick_leave_deduction'] = round(sick_leave_deduction, 2)
    checking_data['sick_leave_addition'] = round(sick_leave_addition, 2)
    checking_data['actual_attendance_days'] = actual_attendance_days
    checking_data['actual_salary'] = round(actual_salary, 2)
    checking_data['salary_base'] = fixed_salary
    checking_data['salary_performance'] = fixed_performance

    return checking_data


def add_excel_style(df, output, columns_to_color):
    if '绑定主体' not in df.columns:
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Sheet1')
            # 获取创建的 workbook 和 worksheet
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            thin_border = Border(left=Side(style='thin'),
                                 right=Side(style='thin'),
                                 top=Side(style='thin'),
                                 bottom=Side(style='thin'))

            # 为指定列添加背景色
            for column, color in columns_to_color.items():
                # 获取列的索引
                col_idx = df.columns.get_loc(column) + 1  # 因为 Excel 的列索引从 1 开始，而不是从 0 开始

                # 创建填充样式
                fill = PatternFill(start_color=color, end_color=color, fill_type='solid')

                # 应用填充样式到列中的所有单元格
                for row in range(2, len(df) + 2):  # 从第二行开始，因为第一行是标题行
                    cell = worksheet.cell(row=row, column=col_idx)
                    cell.fill = fill
                    cell.border = thin_border
    else:

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            grouped = df.groupby('绑定主体')

            for name, group in grouped:
                group = group.drop(columns=['绑定主体'])
                group.to_excel(writer, index=False, sheet_name=str(name))
                worksheet = writer.sheets[str(name)]
                thin_border = Border(left=Side(style='thin'),
                                     right=Side(style='thin'),
                                     top=Side(style='thin'),
                                     bottom=Side(style='thin'))

                for column, color in columns_to_color.items():
                    if column in group.columns:
                        col_idx = group.columns.get_loc(column) + 1
                        fill = PatternFill(start_color=color, end_color=color, fill_type='solid')

                        for row in range(2, len(group) + 2):
                            cell = worksheet.cell(row=row, column=col_idx)
                            cell.fill = fill
                            cell.border = thin_border
    return output


async def send_salary_email(db: AsyncSession, salary_id: int, receivers: List, salary_obj=None):
    redis_queue = RedisQueue()
    not_send_list = []
    send_list = []
    ERP_SALARY_DETAIL = CF.get_crud(ErpSalaryDetail)

    for receiver in receivers:
        # salary_detail_id = None
        if salary_obj:
            salary_detail_entity = await ERP_SALARY_DETAIL.get_one(db, salary_id=salary_id, account_id=receiver.id)
            if not salary_detail_entity:
                not_send_list.append(receiver.id)
                continue
            salary_detail_id = salary_detail_entity.id
        else:
            salary_detail_id = receiver.salary_detail_id

        userinfo = {
            "uid": receiver.id,
            "username": receiver.username,
            "outside": True,
        }
        token = AccountHandler.create_access_token(userinfo, expires_delta=timedelta(days=2))
        link = f'{FRONTEND_BASE_URL}/#/financial/payslipDetails?detail_id={salary_detail_id}&token={token}'
        content = (f"{receiver.employee_name},\n\n"
                   f"进阶为您生成了一份{receiver.ym if not salary_obj else salary_obj.ym} **薪资报告**, "
                   f"请及时[点击查看]({link})， "
                   f"本链接将于两天后过期,到期后请前往个人中心查看。\n\n"
                   f"如有任何问题，请及时联系")
        html_content = convert_to_html(content)
        email_item = {
            "salary_detail_id": salary_detail_id,
            "userids": [receiver.qy_wechat_userid],
            "subject": f'进阶思维{receiver.ym if not salary_obj else salary_obj.ym}薪资报告',
            "content": html_content,
            "attachment_list": [],
        }
        await redis_queue.produce_message(queue_name='erp_salary_email', message=email_item)
        send_list.append(receiver.id)
    logger.info({
        'keyword': '薪资发放',
        'salary_id': salary_id,
        'send': send_list,
        'not_send': not_send_list,
    })
    return not_send_list, send_list


async def refund_pages(db, erp_db, uat_db, page, page_size, **kwargs):
    """
    # 查询 退款列表
    """
    selects = []
    conditions = []
    stmt = (
        select(*selects)
        .select_from()
        .outerjoin()
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()


async def finance_refund_statistic_module(db, start_time=None, end_time=None, page=None, page_size=None, count=False, sum_total=False):
    """
    # 退费统计模块
    
    根据时间范围查询退费记录，包含学生姓名、退款金额、退款班级、授课教师、退费原因、申请时间、操作人、付款单号、退款单号等信息
    
    ## 参数说明
    - start_time: 开始时间
    - end_time: 结束时间
    - page: 页码
    - page_size: 每页数量
    - count: 是否只返回总数
    - sum_total: 是否只返回退费总金额
    
    ## 返回数据
    返回学生退费相关信息
    """
    CreateBy = aliased(ErpAccount, name='create_by')
    conditions = []
    
    # 添加时间范围条件
    if start_time:
        conditions.append(ErpOrderRefund.create_time >= start_time)
    if end_time:
        conditions.append(ErpOrderRefund.create_time <= end_time)
    
    # 计算退费总金额
    if sum_total:
        sum_stmt = (
            select(func.sum(ErpOrderRefundDetail.refund_money))
            .select_from(ErpOrderRefundDetail)
            .outerjoin(ErpOrderRefund, ErpOrderRefund.id == ErpOrderRefundDetail.refund_id)
            .where(and_(*conditions))
        )
        result = await db.execute(sum_stmt)
        return result.scalar() or 0
        
    # 只统计总数
    if count:
        count_stmt = (
            select(func.count(ErpOrderRefundDetail.id))
            .select_from(ErpOrderRefundDetail)
            .outerjoin(ErpOrder, and_(ErpOrder.id == ErpOrderRefundDetail.order_id, ErpOrder.order_class_type == 1))
            .outerjoin(ErpOrderStudent, ErpOrderStudent.id == ErpOrderRefundDetail.order_student_id)
            .outerjoin(ErpStudent, ErpStudent.id == ErpOrderStudent.stu_id)
            .outerjoin(ErpClass, ErpClass.id == ErpOrderStudent.class_id)
            .outerjoin(ErpAccountTeacher, ErpAccountTeacher.id == ErpClass.teacher_id)
            .outerjoin(ErpAccount, ErpAccount.id == ErpAccountTeacher.account_id)
            .outerjoin(CreateBy, ErpOrderRefundDetail.create_by == CreateBy.id)
            .outerjoin(ErpOrderRefund, ErpOrderRefund.id == ErpOrderRefundDetail.refund_id)
            .where(and_(*conditions))
        )
        result = await db.execute(count_stmt)
        return result.scalar()
    
    # 查询字段
    selects = [
        ErpOrderRefundDetail.id,
        ErpStudent.stu_name,
        ErpOrderRefundDetail.refund_money,
        ErpClass.class_name,
        ErpClass.teacher_id,
        ErpAccount.employee_name.label('teacher_name'),
        ErpOrderRefund.refund_reason,
        ErpOrderRefund.apply_date,
        ErpOrderRefund.create_time,
        ErpOrderRefund.create_by,
        CreateBy.employee_name.label('create_by_name'),
        ErpOrderRefund.receipt_id,
        ErpOrderRefundDetail.order_no,
        ErpOrderRefundDetail.refund_order_no,
        
    ]
    
    # 构建查询
    stmt = (
        select(*selects)
        .select_from(ErpOrderRefundDetail)
        .outerjoin(ErpOrderStudent, ErpOrderStudent.id == ErpOrderRefundDetail.order_student_id)
        .outerjoin(ErpStudent, ErpStudent.id == ErpOrderStudent.stu_id)
        .outerjoin(ErpClass, and_(ErpClass.id == ErpOrderStudent.class_id, ErpOrderStudent.class_id > 0))
        .outerjoin(ErpAccountTeacher, ErpAccountTeacher.id == ErpClass.teacher_id)
        .outerjoin(ErpAccount, ErpAccount.id == ErpAccountTeacher.account_id)
        .outerjoin(CreateBy, ErpOrderRefundDetail.create_by == CreateBy.id)
        .outerjoin(ErpOrderRefund, ErpOrderRefund.id == ErpOrderRefundDetail.refund_id)
        .where(and_(*conditions))
    )
    if page and page_size:
        stmt = stmt.limit(page_size).offset((page - 1) * page_size)
    result = await db.execute(stmt)
    return result.fetchall()



# 创建学员电子钱包的通用方法
async def create_student_wallet(
        db: AsyncSession,
        campus_id: int,
        stu_id: int,
        amount: decimal.Decimal,
        uid
):
    """
    # 创建 学员电子钱包
    """
    stu_obj = await erp_student.get_by_id(db, stu_id)
    if not stu_obj:
        return False
    stu_name = stu_obj.stu_name
    obj = await erp_bank_account.create(db, commit=False, **{
        "campus_id": campus_id,
        "account_alias": f"{stu_name}",
        "account_type": BillType.StuAccount.value,
        "account_number": stu_id,
        "balance": amount,
        "currency_type": 1,
        "is_active": 1,
    })
    await db.commit()
    return obj

async def stu_ewallet_change(db: AsyncSession, 
                             stu_id: int, 
                             change_type: int, 
                             amount: decimal.Decimal, 
                             uid: int = None, 
                             desc: str = None,
                             receipt_id: int = None,
                             from_order_id: int = None,
                             commit: bool = True):
    """
    学员电子钱包变动
    """
    # 金额验证
    if amount <= 0:
        return await ApiFailedResponse("金额必须大于0")
    
    # 转换为Decimal以提高精度
    amount_decimal = Decimal(str(amount))
    
    stu_obj = await erp_student.get_by_id(db, stu_id)
    if not stu_obj:
        return await ApiFailedResponse("学员不存在")
    
    balance = Decimal(str(stu_obj.stu_wallet_amount or 0))
    
    if change_type == 1:
        # 增加金额
        balance += amount_decimal
    elif change_type == 2:
        # 减少金额，需要验证余额是否足够
        if balance < amount_decimal:
            return await ApiFailedResponse("电子钱包余额不足")
        balance -= amount_decimal
    else:
        return await ApiFailedResponse("调整类型错误")

    # 更新学员电子钱包余额
    stu_obj.stu_wallet_amount = decimal.Decimal(balance)

    # 记录账户变动
    await erp_student_ewallet_log.create(db, commit=False, **{
        "stu_id": stu_id,
        "change_type": change_type,
        "amount": decimal.Decimal(amount_decimal),
        "rest_balance": decimal.Decimal(balance),
        "desc": desc,
        "receipt_id": receipt_id or 0,
        "from_order_id": from_order_id or 0,
        "create_by": uid or 0,
    })
    settings.logger.info(f'记录学员电子钱包变动:单据{receipt_id}， 学员id:{stu_id}， 变动金额:{amount}， 变动类型:{change_type}， 变动描述:{desc}')
    
    if commit:
        await db.commit()
    return await ApiSuccessResponse(True)

# 账户变动的公共方法
async def bill_account_change(
        db: AsyncSession,
        bill_account_id: int,
        change_type: int,
        amount: Union[float, Decimal],
        desc: str,
        uid: int,
        commit: bool = True,
        receipt_id: int = None
):
    """
    账户变动
    - change_type: 1 增加 2 减少
    - amount: 变动金额
    - desc: 变动描述
    - uid: 变动人ID
    - commit: 是否提交事务
    - receipt_id: 单据ID
    """
    # 查询账户
    account = await erp_bank_account.get_by_id(db, bill_account_id)
    if not account:
        return await ApiFailedResponse("账户不存在")
    
    # 转换为Decimal以提高精度
    balance = Decimal(str(account.balance or 0))
    amount = Decimal(str(amount))
    
    if change_type == 2 and balance < amount:
        return await ApiFailedResponse("余额不足")

    # 更新账户余额
    if change_type == 1:
        account.balance = balance + amount
    elif change_type == 2:
        account.balance = balance - amount
    else:
        return await ApiFailedResponse("调整类型错误")

    # 记录账户变动
    await erp_bank_account_log.create(db, commit=False, **{
        "bill_account_id": bill_account_id,
        "change_type": change_type,
        "amount": decimal.Decimal(amount), 
        "rest_balance": decimal.Decimal(account.balance),
        "desc": desc,
        "create_by": uid,
        "receipt_id": receipt_id if receipt_id and receipt_id > 0 else 0,
    })
    settings.logger.info(f'记录账户变动:单据{receipt_id}， 账户id:{bill_account_id}， 变动金额:{amount}， 变动类型:{"增加" if change_type==1 else "减少"}， 变动描述:{desc}')
    if commit:
        await db.commit()
    return await ApiSuccessResponse(True)



async def finance_income_report_module(db, page, page_size, condition, count=False):
    """
    返回示例:
    {
        "class_id": 7696,
        "class_name": "2025科学创新L2-03鸿志S班（零期早班）",
        "course_term_name": "寒假班",
        "start_date": "2025-01-12T00:00:00",
        "class_status": null,
        "course_category_name": "02-鸿志班",
        "course_name": "2025科学创新L2-03鸿志班",
        "teacher_name": null,
        "student_count": 0,
        "total_class_times": 7,
        "flash_class_times": 0,
        "accounts_receivable": 0,
        "discount": 0,
        "refund_money": 0,
        "course_income_receipt": 0,
        "lecture_fee_income": 0,
        "un_consumption_amount": 0,
        "class_consumption_amount": 0,
        "course_consultant_commission": 0,
        "actual_teacher_hours": 0,
        "current_gross_profit": 0,
        "current_gross_profit_rate": 0,
        "other_expenses": 1000,
        "current_actual_profit": -1000,
        "current_actual_profit_rate": 0,
        "average_profit_per_class": 0,
        "average_profit_per_student": 0,
        "expected_course_total_income": 0,
        "expected_teacher_hours": 0,
        "expected_profit": -1000,
        "expected_profit_rate": 0,
        "expected_average_profit_per_class": -142.86,
        "expected_average_profit_per_student": 0
    }
    """
    selects = [
        ErpClass.id.label("class_id"),
        ErpClass.class_name,
        ErpClass.start_date,
        ErpClass.class_status,   # 班级状态
        # 总课次
        ErpClass.planning_class_times,

        ErpCourse.course_name,
        ErpCourse.cost_calculation_plan,   # 1 固定金额 2 标准课时费（倍数*教师课时费）
        ErpCourse.course_coefficient,   # 金额或倍数
        
        ErpCourseCategory.category_name.label("course_category_name"),
        ErpCourseTerm.term_name.label("course_term_name"),
        ErpAccount.employee_name.label("teacher_name"),
        ErpAccountTeacher.class_fee,   # 教师课时费 金额或比例值（0.x）
        ErpAccountTeacher.class_fee_type,   # 教师课时费类型 1 固定金额 2 比例值
    ]
    if count:
        selects = [func.count(ErpClass.id)]
    conditions = []
    
    # 添加过滤条件
    if condition:
        if 'class_name' in condition and condition['class_name']:
            conditions.append(ErpClass.class_name.like(f"%{condition['class_name']}%"))
        if 'teacher_id' in condition and condition['teacher_id']:
            conditions.append(ErpClass.teacher_id == condition['teacher_id'])
        if 'course_id' in condition and condition['course_id']:
            conditions.append(ErpClass.course_id == condition['course_id'])
        if 'class_status' in condition and condition['class_status']:
            conditions.append(ErpClass.class_status == condition['class_status'])
        if 'start_date_begin' in condition and condition['start_date_begin']:
            conditions.append(ErpClass.start_date >= condition['start_date_begin'])
        if 'start_date_end' in condition and condition['start_date_end']:
            conditions.append(ErpClass.start_date <= condition['start_date_end'])
        
    stmt = (
        select(*selects)
        .select_from(ErpClass)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .outerjoin(ErpCourseCategory, ErpCourse.category_id == ErpCourseCategory.id)
        .outerjoin(ErpCourseTerm, ErpCourse.term_id == ErpCourseTerm.id)
        .where(and_(*conditions))
    )
    if count:
        result = await db.execute(stmt)
        return result.scalar()
    else:
        if page and page_size:
            stmt = stmt.offset((page - 1) * page_size).limit(page_size)
        result = await db.execute(stmt)
        classes = result.fetchall()
        
        # 获取班级ID列表
        class_ids = [item.class_id for item in classes]
        
        if not class_ids:
            return []
        
        # 统计数据，一次查询多个班级的数据
        stats = await finance_income_report_statistics(db, class_ids)
        
        # 组合结果
        result_data = []
        for class_item in classes:
            class_id = class_item.class_id
            class_stats = stats.get(class_id, {})
            
            class_dict = {
                "class_id": class_id,
                "class_name": class_item.class_name,
                # "class_type": class_item.class_type,
                "course_term_name": class_item.course_term_name,
                "start_date": class_item.start_date,
                "class_status": class_item.class_status, 
                "course_category_name": class_item.course_category_name,
                "course_name": class_item.course_name,
                "teacher_name": class_item.teacher_name,
                "student_count": class_stats.get("student_count", 0),  # 学生数量
                "total_class_times": class_item.planning_class_times,  # 总课次
                "flash_class_times": class_stats.get("flash_class_times", 0),  # 已完成课次
                
                # 财务数据
                "accounts_receivable": class_stats.get("accounts_receivable", 0),  # 课程收入应收
                "discount": class_stats.get("discount", 0),  # 优惠金额
                "refund_money": class_stats.get("refund_money", 0),  # 课程退款
                "course_income_receipt": class_stats.get("course_income_receipt", 0),  # 课程收入实收
                "lecture_fee_income": class_stats.get("lecture_fee_income", 0),  # 讲义费收入
                "un_consumption_amount": class_stats.get("un_consumption_amount", 0),  # 未课消金额
                "class_consumption_amount": class_stats.get("class_consumption_amount", 0),  # 已课消金额
                "course_consultant_commission": 0,  # 课程顾问提成（预留）
            }
            
            # 计算教师课时费
            actual_teacher_hours = 0
            if class_item.class_fee_type == 2:  # 比例
                actual_teacher_hours = decimal.Decimal(class_dict["course_income_receipt"]) * decimal.Decimal(str(class_item.class_fee or 0))
            elif class_item.class_fee_type == 1:  # 固定金额
                if class_item.cost_calculation_plan == 1:  # 固定金额
                    actual_teacher_hours = decimal.Decimal(str(class_item.course_coefficient or 0)) * decimal.Decimal(class_dict["flash_class_times"])
                elif class_item.cost_calculation_plan == 2:  # 标准课时费
                    actual_teacher_hours = decimal.Decimal(str(class_item.course_coefficient or 0)) * decimal.Decimal(str(class_item.class_fee or 0)) * decimal.Decimal(class_dict["flash_class_times"])
            
            class_dict["actual_teacher_hours"] = round(actual_teacher_hours, 2)
            
            # 计算其他指标
            other_expenses = class_stats.get("other_expenses", 0)  # 其他支出（待定）
            
            # 计算当前毛利
            current_gross_profit = decimal.Decimal(class_dict["course_income_receipt"]) + decimal.Decimal(class_dict["lecture_fee_income"]) - decimal.Decimal(class_dict["course_consultant_commission"]) - decimal.Decimal(class_dict["actual_teacher_hours"])
            class_dict["current_gross_profit"] = round(current_gross_profit, 2)
            
            # 计算当前毛利率
            if class_dict["course_income_receipt"] > 0:
                class_dict["current_gross_profit_rate"] = round(decimal.Decimal(current_gross_profit) / decimal.Decimal(class_dict["course_income_receipt"]), 4)
            else:
                class_dict["current_gross_profit_rate"] = 0
            
            # 计算当前实际利润
            current_actual_profit = current_gross_profit - other_expenses
            class_dict["other_expenses"] = other_expenses
            class_dict["current_actual_profit"] = round(current_actual_profit, 2)
            
            # 计算当前实际利润率
            total_income = class_dict["course_income_receipt"] + class_dict["lecture_fee_income"]
            if total_income > 0:
                class_dict["current_actual_profit_rate"] = round(decimal.Decimal(current_actual_profit) / decimal.Decimal(total_income), 4)
            else:
                class_dict["current_actual_profit_rate"] = 0
                
            # 平均利润（每节）
            if class_dict["flash_class_times"] > 0:
                class_dict["average_profit_per_class"] = round(decimal.Decimal(current_actual_profit) / decimal.Decimal(class_dict["flash_class_times"]), 2)
            else:
                class_dict["average_profit_per_class"] = 0
                
            # 平均利润（每人次）
            if class_dict["student_count"] > 0:
                class_dict["average_profit_per_student"] = round(decimal.Decimal(current_actual_profit) / decimal.Decimal(class_dict["student_count"]), 2)
            else:
                class_dict["average_profit_per_student"] = 0
                
            # 预计课程总收入
            expected_course_total_income = decimal.Decimal(class_dict["course_income_receipt"]) + decimal.Decimal(class_dict["un_consumption_amount"]) + decimal.Decimal(class_dict["lecture_fee_income"])
            class_dict["expected_course_total_income"] = round(expected_course_total_income, 2)
            
            # 预计教师课时费
            expected_teacher_hours = 0
            if class_item.class_fee_type == 2:  # 比例
                expected_teacher_hours = expected_course_total_income * decimal.Decimal(str(class_item.class_fee or 0))
            elif class_item.class_fee_type == 1:  # 固定金额
                if class_item.cost_calculation_plan == 1:  # 固定金额
                    expected_teacher_hours = decimal.Decimal(str(class_item.course_coefficient or 0)) * decimal.Decimal(str(class_item.planning_class_times or 0))
                elif class_item.cost_calculation_plan == 2:  # 标准课时费
                    expected_teacher_hours = decimal.Decimal(str(class_item.course_coefficient or 0)) * decimal.Decimal(str(class_item.class_fee or 0)) * decimal.Decimal(str(class_item.planning_class_times or 0))
                    
            class_dict["expected_teacher_hours"] = round(expected_teacher_hours, 2)
            
            # 预期利润
            expected_profit = decimal.Decimal(expected_course_total_income) - decimal.Decimal(expected_teacher_hours) - decimal.Decimal(other_expenses)
            class_dict["expected_profit"] = round(expected_profit, 2)
            
            # 预期利润率
            if expected_course_total_income > 0:
                class_dict["expected_profit_rate"] = round(decimal.Decimal(expected_profit) / decimal.Decimal(expected_course_total_income), 4)
            else:
                class_dict["expected_profit_rate"] = 0
                
            # 预期平均利润（每节）
            if class_item.planning_class_times > 0:
                class_dict["expected_average_profit_per_class"] = round(decimal.Decimal(expected_profit) / decimal.Decimal(class_item.planning_class_times), 2)
            else:
                class_dict["expected_average_profit_per_class"] = 0
                
            # 预期平均利润（每人次）
            if class_dict["student_count"] > 0:
                class_dict["expected_average_profit_per_student"] = round(decimal.Decimal(expected_profit) / decimal.Decimal(class_dict["student_count"]), 2)
            else:
                class_dict["expected_average_profit_per_student"] = 0
                
            result_data.append(class_dict)
            
        return result_data

async def finance_income_report_statistics(db, class_ids):
    """获取班级的收入统计数据"""
    if not class_ids:
        return {}
    
    # 调用CRUD函数获取各种统计数据
    stats = {}
    
    # 1. 获取学生数量
    student_counts = await get_class_student_count(db, class_ids)
    
    # 2. 获取已完成课次
    flash_class_times = await get_class_flash_times(db, class_ids)
    
    # 3. 获取课程订单数据：应收、优惠、退款、实收
    order_stats = await get_class_order_stats(db, class_ids)
    
    # 4. 获取讲义费收入
    lecture_fees = await get_class_lecture_fees(db, class_ids)
    
    # 5. 获取已课消和未课消金额
    consumption_stats = await get_class_consumption_stats(db, class_ids)
    
    # 6. 计算其他支出（示例数据，需根据实际情况修改）
    other_expenses = await get_class_other_expenses(db, class_ids)
    
    # 合并所有统计数据
    for class_id in class_ids:
        stats[class_id] = {
            "student_count": student_counts.get(class_id, 0),
            "flash_class_times": flash_class_times.get(class_id, 0),
            "accounts_receivable": order_stats.get(class_id, {}).get("accounts_receivable", 0),
            "discount": order_stats.get(class_id, {}).get("discount", 0),
            "refund_money": order_stats.get(class_id, {}).get("refund_money", 0),
            "course_income_receipt": order_stats.get(class_id, {}).get("course_income_receipt", 0),
            "lecture_fee_income": lecture_fees.get(class_id, 0),
            "class_consumption_amount": consumption_stats.get(class_id, {}).get("class_consumption_amount", 0),
            "un_consumption_amount": consumption_stats.get(class_id, {}).get("un_consumption_amount", 0),
            "other_expenses": other_expenses.get(class_id, 0),
        }
    
    return stats

async def finance_simple_report_module(db: AsyncSession, yyyy: int):
    """
    按标签大类处理和组织财务简易报表数据
    
    Args:
        db: 数据库会话
        yyyy: 查询的年份，如2023
        
    Returns:
        dict: 按标签大类组织的财务报表数据
    """
    from app_finance.crud import finance_simple_report_crud, get_special_cost_type_32_data, get_special_cost_type_33_data
    from utils.enum.enum_finance import TagType
    from models.m_finance import ErpFinanceCostType
    from sqlalchemy import select
    
    def to_decimal(value):
        """将任意值转换为Decimal并保留2位小数"""
        if value is None:
            return decimal.Decimal('0.00')
        return decimal.Decimal(str(decimal.Decimal(value))).quantize(decimal.Decimal('0.00'), rounding=decimal.ROUND_HALF_UP)
    
    # 从数据库获取原始数据
    cost_type_objs, receipt_details, refund_details = await finance_simple_report_crud(db, yyyy)
    
    # 获取特殊处理数据
    special_cost_type_32_data = await get_special_cost_type_32_data(db, yyyy)
    special_cost_type_33_data = await get_special_cost_type_33_data(db, yyyy)
    
    # 检查费用类型32是否存在于cost_type_objs中，如果不存在，则单独查询
    cost_type_32_exists = False
    for ct in cost_type_objs:
        if ct.id == 32:
            cost_type_32_exists = True
            break
    
    # 如果不存在，单独查询费用类型32
    cost_type_32 = None
    cost_type_32_parent = 0
    if not cost_type_32_exists:
        cost_type_32_query = await db.execute(select(ErpFinanceCostType).where(ErpFinanceCostType.id == 32))
        cost_type_32 = cost_type_32_query.scalar_one_or_none()
        if cost_type_32:
            cost_type_32_parent = cost_type_32.parent_id
    
    # 按父ID分组子费用类型
    cost_type_maps = {ct.id: ct for ct in cost_type_objs}
    if cost_type_32 and not cost_type_32_exists:
        cost_type_maps[32] = cost_type_32
    
    cost_type_children = {}
    parent_cost_types = {}
    
    for ct in cost_type_objs:
        if ct.parent_id == 0:  # 是顶级分类
            parent_cost_types[ct.id] = ct
        else:  # 是子分类
            if ct.parent_id not in cost_type_children:
                cost_type_children[ct.parent_id] = []
            cost_type_children[ct.parent_id].append(ct)
    
    # 如果费用类型32是子分类，需要添加到对应父分类的子分类列表中
    if cost_type_32 and not cost_type_32_exists and cost_type_32_parent != 0:
        if cost_type_32_parent not in cost_type_children:
            cost_type_children[cost_type_32_parent] = []
        # 检查是否已经添加
        already_added = False
        for ct in cost_type_children[cost_type_32_parent]:
            if ct.id == 32:
                already_added = True
                break
        if not already_added:
            cost_type_children[cost_type_32_parent].append(cost_type_32)
    
    # 按标签大类分组费用类型
    tag_groups = {
        TagType.BUSINESS_MARGIN.value: {"name": "营业毛利", "items": []},
        TagType.TOTAL_COST.value: {"name": "总成本", "items": []},
        TagType.OTHER_INCOME.value: {"name": "其他收入", "items": []},
        TagType.OTHER_EXPENSE.value: {"name": "其他支出", "items": []}
    }
    
    # 将费用类型按标签分组
    for ct_id, ct in parent_cost_types.items():
        tag = ct.tag or 0
        if tag in tag_groups:
            tag_groups[tag]["items"].append(ct_id)
    
    # 按月、季度、半年度、全年汇总的数据结构
    time_dimensions = {
        "total": {},
        "summary": {},
        "january": {},
        "february": {},
        "march": {},
        "quarter1": {},
        "april": {},
        "may": {},
        "june": {},
        "quarter2": {},
        "half_year1": {},
        "july": {},
        "august": {},
        "september": {},
        "quarter3": {},
        "october": {},
        "november": {},
        "december": {},
        "quarter4": {},
        "half_year2": {},
        "annual": {}
    }
    
    # 初始化所有费用类型的数据结构
    for ct_id, ct in cost_type_maps.items():
        # 初始化所有时间维度的汇总数据
        for time_dimension in time_dimensions.keys():
            if time_dimension == "summary":
                time_dimensions[time_dimension][ct_id] = ct.name
            else:
                time_dimensions[time_dimension][ct_id] = Decimal('0.00')
                
                # 对于特殊类型32和33，使用特殊数据
                if ct_id == 32 and time_dimension in special_cost_type_32_data:
                    time_dimensions[time_dimension][ct_id] = to_decimal(special_cost_type_32_data[time_dimension])
                elif ct_id == 33 and time_dimension in special_cost_type_33_data:
                    time_dimensions[time_dimension][ct_id] = to_decimal(special_cost_type_33_data[time_dimension])
    
    # 处理收据明细
    for detail in receipt_details:
        cost_type_id = detail.cost_type_id
        amount = to_decimal(detail.item_total_price)
        
        # 对于特殊类型32和33，跳过常规处理
        if cost_type_id in [32, 33]:
            continue
            
        if cost_type_id not in cost_type_maps:
            continue
        
        # 找到所属的月份和对应的季度、半年度
        month = detail.create_time.month
        month_mapping = {
            1: "january", 2: "february", 3: "march", 
            4: "april", 5: "may", 6: "june",
            7: "july", 8: "august", 9: "september", 
            10: "october", 11: "november", 12: "december"
        }
        month_name = month_mapping[month]
        
        quarter_mapping = {
            1: "quarter1", 2: "quarter1", 3: "quarter1", 
            4: "quarter2", 5: "quarter2", 6: "quarter2",
            7: "quarter3", 8: "quarter3", 9: "quarter3", 
            10: "quarter4", 11: "quarter4", 12: "quarter4"
        }
        quarter_name = quarter_mapping[month]
        
        half_year = "half_year1" if month <= 6 else "half_year2"
        
        # 确定这个费用类型是否为子类型，如果是，找到其父类型
        parent_id = None
        ct = cost_type_maps[cost_type_id]
        if ct.parent_id == 0:  # 是顶级分类
            parent_id = ct.id
        else:  # 是子分类
            parent_id = ct.parent_id
        
        # 累加到月度数据
        time_dimensions[month_name][cost_type_id] += amount
        time_dimensions[month_name][cost_type_id] = time_dimensions[month_name][cost_type_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        
        # 累加到季度数据
        time_dimensions[quarter_name][cost_type_id] += amount
        time_dimensions[quarter_name][cost_type_id] = time_dimensions[quarter_name][cost_type_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        
        # 累加到半年度数据
        time_dimensions[half_year][cost_type_id] += amount
        time_dimensions[half_year][cost_type_id] = time_dimensions[half_year][cost_type_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        
        # 累加到年度数据
        time_dimensions["annual"][cost_type_id] += amount
        time_dimensions["annual"][cost_type_id] = time_dimensions["annual"][cost_type_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        
        # 累加到合计数据
        time_dimensions["total"][cost_type_id] += amount
        time_dimensions["total"][cost_type_id] = time_dimensions["total"][cost_type_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        
        # 如果是子分类，也要累加到父分类
        if parent_id != cost_type_id:
            time_dimensions[month_name][parent_id] += amount
            time_dimensions[month_name][parent_id] = time_dimensions[month_name][parent_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
            
            time_dimensions[quarter_name][parent_id] += amount
            time_dimensions[quarter_name][parent_id] = time_dimensions[quarter_name][parent_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
            
            time_dimensions[half_year][parent_id] += amount
            time_dimensions[half_year][parent_id] = time_dimensions[half_year][parent_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
            
            time_dimensions["annual"][parent_id] += amount
            time_dimensions["annual"][parent_id] = time_dimensions["annual"][parent_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
            
            time_dimensions["total"][parent_id] += amount
            time_dimensions["total"][parent_id] = time_dimensions["total"][parent_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
    
    # 处理退费明细
    for refund in refund_details:
        cost_type_id = refund.cost_type_id
        refund_amount = to_decimal(refund.refund_money)
        
        # 对于特殊类型32和33，跳过常规处理
        if cost_type_id in [32, 33]:
            continue
            
        if cost_type_id not in cost_type_maps:
            continue
        
        # 找到所属的月份和对应的季度、半年度
        month = refund.create_time.month
        month_mapping = {
            1: "january", 2: "february", 3: "march", 
            4: "april", 5: "may", 6: "june",
            7: "july", 8: "august", 9: "september", 
            10: "october", 11: "november", 12: "december"
        }
        month_name = month_mapping[month]
        
        quarter_mapping = {
            1: "quarter1", 2: "quarter1", 3: "quarter1", 
            4: "quarter2", 5: "quarter2", 6: "quarter2",
            7: "quarter3", 8: "quarter3", 9: "quarter3", 
            10: "quarter4", 11: "quarter4", 12: "quarter4"
        }
        quarter_name = quarter_mapping[month]
        
        half_year = "half_year1" if month <= 6 else "half_year2"
        
        # 确定这个费用类型是否为子类型，如果是，找到其父类型
        parent_id = None
        ct = cost_type_maps[cost_type_id]
        if ct.parent_id == 0:  # 是顶级分类
            parent_id = ct.id
        else:  # 是子分类
            parent_id = ct.parent_id
        
        # 从各时间维度中减去退费金额
        time_dimensions[month_name][cost_type_id] -= refund_amount
        time_dimensions[month_name][cost_type_id] = time_dimensions[month_name][cost_type_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        
        time_dimensions[quarter_name][cost_type_id] -= refund_amount
        time_dimensions[quarter_name][cost_type_id] = time_dimensions[quarter_name][cost_type_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        
        time_dimensions[half_year][cost_type_id] -= refund_amount
        time_dimensions[half_year][cost_type_id] = time_dimensions[half_year][cost_type_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        
        time_dimensions["annual"][cost_type_id] -= refund_amount
        time_dimensions["annual"][cost_type_id] = time_dimensions["annual"][cost_type_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        
        time_dimensions["total"][cost_type_id] -= refund_amount
        time_dimensions["total"][cost_type_id] = time_dimensions["total"][cost_type_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
        
        # 如果是子分类，也要从父分类中减去
        if parent_id != cost_type_id:
            time_dimensions[month_name][parent_id] -= refund_amount
            time_dimensions[month_name][parent_id] = time_dimensions[month_name][parent_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
            
            time_dimensions[quarter_name][parent_id] -= refund_amount
            time_dimensions[quarter_name][parent_id] = time_dimensions[quarter_name][parent_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
            
            time_dimensions[half_year][parent_id] -= refund_amount
            time_dimensions[half_year][parent_id] = time_dimensions[half_year][parent_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
            
            time_dimensions["annual"][parent_id] -= refund_amount
            time_dimensions["annual"][parent_id] = time_dimensions["annual"][parent_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
            
            time_dimensions["total"][parent_id] -= refund_amount
            time_dimensions["total"][parent_id] = time_dimensions["total"][parent_id].quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
    
    # 构建按标签大类组织的结果数据
    final_result = {}
    
    # 填充标签大类的数据
    for tag_id, tag_info in tag_groups.items():
        tag_name = tag_info["name"]
        final_result[tag_name] = []
        processed_parents = set()  # 用于跟踪已处理的父级费用类型
        
        # 特殊处理：如果是营业毛利标签，先添加费用类型32（预收）到第一个位置
        if tag_id == TagType.BUSINESS_MARGIN.value:
            # 查询费用类型32信息
            cost_type_32_query = await db.execute(select(ErpFinanceCostType).where(ErpFinanceCostType.id == 32))
            cost_type_32 = cost_type_32_query.scalar_one_or_none()
            
            if cost_type_32:
                parent_data = {
                    "id": 32,
                    "name": cost_type_32.name,
                    "is_parent": True,
                    "total": decimal.Decimal('0.00'),
                    "summary": time_dimensions["summary"].get(32, ""),
                    "january": decimal.Decimal('0.00'),
                    "february": decimal.Decimal('0.00'),
                    "march": decimal.Decimal('0.00'),
                    "quarter1": decimal.Decimal('0.00'),
                    "april": decimal.Decimal('0.00'),
                    "may": decimal.Decimal('0.00'),
                    "june": decimal.Decimal('0.00'),
                    "quarter2": decimal.Decimal('0.00'),
                    "half_year1": decimal.Decimal('0.00'),
                    "july": decimal.Decimal('0.00'),
                    "august": decimal.Decimal('0.00'),
                    "september": decimal.Decimal('0.00'),
                    "quarter3": decimal.Decimal('0.00'),
                    "october": decimal.Decimal('0.00'),
                    "november": decimal.Decimal('0.00'),
                    "december": decimal.Decimal('0.00'),
                    "quarter4": decimal.Decimal('0.00'),
                    "half_year2": decimal.Decimal('0.00'),
                    "annual": decimal.Decimal('0.00'),
                    "children": []
                }
                
                # 填充所有时间维度的金额
                for key in special_cost_type_32_data:
                    if key in parent_data:
                        parent_data[key] = to_decimal(special_cost_type_32_data[key])
                
                parent_item = {k: decimal.Decimal(v) if isinstance(v, decimal.Decimal) else v for k, v in parent_data.items()}
                final_result[tag_name].append(parent_item)
                processed_parents.add(32)  # 标记为已处理
        
        # 遍历该标签下的所有一级费用类型
        for parent_id in tag_info["items"]:
            if parent_id not in parent_cost_types or parent_id in processed_parents:
                continue
                
            processed_parents.add(parent_id)  # 标记该父级费用类型已处理
            parent_type = parent_cost_types[parent_id]
            
            # 初始化父级数据结构 - 全部重新计算，不使用累计好的值
            parent_data = {
                "id": parent_id,
                "name": parent_type.name,
                "is_parent": True,
                "total": decimal.Decimal('0.00'),
                "summary": time_dimensions["summary"].get(parent_id, ""),
                "january": decimal.Decimal('0.00'),
                "february": decimal.Decimal('0.00'),
                "march": decimal.Decimal('0.00'),
                "quarter1": decimal.Decimal('0.00'),
                "april": decimal.Decimal('0.00'),
                "may": decimal.Decimal('0.00'),
                "june": decimal.Decimal('0.00'),
                "quarter2": decimal.Decimal('0.00'),
                "half_year1": decimal.Decimal('0.00'),
                "july": decimal.Decimal('0.00'),
                "august": decimal.Decimal('0.00'),
                "september": decimal.Decimal('0.00'),
                "quarter3": decimal.Decimal('0.00'),
                "october": decimal.Decimal('0.00'),
                "november": decimal.Decimal('0.00'),
                "december": decimal.Decimal('0.00'),
                "quarter4": decimal.Decimal('0.00'),
                "half_year2": decimal.Decimal('0.00'),
                "annual": decimal.Decimal('0.00'),
                "children": []
            }
            
            # 添加子分类数据并累加到父级
            if parent_id in cost_type_children:
                for child_type in cost_type_children[parent_id]:
                    # 对于特殊类型32和33，使用特殊处理后的数据
                    if child_type.id == 32:
                        child_data = {
                            "id": child_type.id,
                            "name": child_type.name,
                            "is_parent": False,
                            "parent_id": parent_id,
                            "total": to_decimal(special_cost_type_32_data.get("total", 0)),
                            "summary": time_dimensions["summary"].get(child_type.id, ""),
                            "january": to_decimal(special_cost_type_32_data.get("january", 0)),
                            "february": to_decimal(special_cost_type_32_data.get("february", 0)),
                            "march": to_decimal(special_cost_type_32_data.get("march", 0)),
                            "quarter1": to_decimal(special_cost_type_32_data.get("quarter1", 0)),
                            "april": to_decimal(special_cost_type_32_data.get("april", 0)),
                            "may": to_decimal(special_cost_type_32_data.get("may", 0)),
                            "june": to_decimal(special_cost_type_32_data.get("june", 0)),
                            "quarter2": to_decimal(special_cost_type_32_data.get("quarter2", 0)),
                            "half_year1": to_decimal(special_cost_type_32_data.get("half_year1", 0)),
                            "july": to_decimal(special_cost_type_32_data.get("july", 0)),
                            "august": to_decimal(special_cost_type_32_data.get("august", 0)),
                            "september": to_decimal(special_cost_type_32_data.get("september", 0)),
                            "quarter3": to_decimal(special_cost_type_32_data.get("quarter3", 0)),
                            "october": to_decimal(special_cost_type_32_data.get("october", 0)),
                            "november": to_decimal(special_cost_type_32_data.get("november", 0)),
                            "december": to_decimal(special_cost_type_32_data.get("december", 0)),
                            "quarter4": to_decimal(special_cost_type_32_data.get("quarter4", 0)),
                            "half_year2": to_decimal(special_cost_type_32_data.get("half_year2", 0)),
                            "annual": to_decimal(special_cost_type_32_data.get("annual", 0)),
                        }
                    elif child_type.id == 33:
                        child_data = {
                            "id": child_type.id,
                            "name": child_type.name,
                            "is_parent": False,
                            "parent_id": parent_id,
                            "total": to_decimal(special_cost_type_33_data.get("total", 0)),
                            "summary": time_dimensions["summary"].get(child_type.id, ""),
                            "january": to_decimal(special_cost_type_33_data.get("january", 0)),
                            "february": to_decimal(special_cost_type_33_data.get("february", 0)),
                            "march": to_decimal(special_cost_type_33_data.get("march", 0)),
                            "quarter1": to_decimal(special_cost_type_33_data.get("quarter1", 0)),
                            "april": to_decimal(special_cost_type_33_data.get("april", 0)),
                            "may": to_decimal(special_cost_type_33_data.get("may", 0)),
                            "june": to_decimal(special_cost_type_33_data.get("june", 0)),
                            "quarter2": to_decimal(special_cost_type_33_data.get("quarter2", 0)),
                            "half_year1": to_decimal(special_cost_type_33_data.get("half_year1", 0)),
                            "july": to_decimal(special_cost_type_33_data.get("july", 0)),
                            "august": to_decimal(special_cost_type_33_data.get("august", 0)),
                            "september": to_decimal(special_cost_type_33_data.get("september", 0)),
                            "quarter3": to_decimal(special_cost_type_33_data.get("quarter3", 0)),
                            "october": to_decimal(special_cost_type_33_data.get("october", 0)),
                            "november": to_decimal(special_cost_type_33_data.get("november", 0)),
                            "december": to_decimal(special_cost_type_33_data.get("december", 0)),
                            "quarter4": to_decimal(special_cost_type_33_data.get("quarter4", 0)),
                            "half_year2": to_decimal(special_cost_type_33_data.get("half_year2", 0)),
                            "annual": to_decimal(special_cost_type_33_data.get("annual", 0)),
                        }
                    else:
                        child_data = {
                            "id": child_type.id,
                            "name": child_type.name,
                            "is_parent": False,
                            "parent_id": parent_id,
                            "total": time_dimensions["total"].get(child_type.id, Decimal('0.00')),
                            "summary": time_dimensions["summary"].get(child_type.id, ""),
                            "january": time_dimensions["january"].get(child_type.id, Decimal('0.00')),
                            "february": time_dimensions["february"].get(child_type.id, Decimal('0.00')),
                            "march": time_dimensions["march"].get(child_type.id, Decimal('0.00')),
                            "quarter1": time_dimensions["quarter1"].get(child_type.id, Decimal('0.00')),
                            "april": time_dimensions["april"].get(child_type.id, Decimal('0.00')),
                            "may": time_dimensions["may"].get(child_type.id, Decimal('0.00')),
                            "june": time_dimensions["june"].get(child_type.id, Decimal('0.00')),
                            "quarter2": time_dimensions["quarter2"].get(child_type.id, Decimal('0.00')),
                            "half_year1": time_dimensions["half_year1"].get(child_type.id, Decimal('0.00')),
                            "july": time_dimensions["july"].get(child_type.id, Decimal('0.00')),
                            "august": time_dimensions["august"].get(child_type.id, Decimal('0.00')),
                            "september": time_dimensions["september"].get(child_type.id, Decimal('0.00')),
                            "quarter3": time_dimensions["quarter3"].get(child_type.id, Decimal('0.00')),
                            "october": time_dimensions["october"].get(child_type.id, Decimal('0.00')),
                            "november": time_dimensions["november"].get(child_type.id, Decimal('0.00')),
                            "december": time_dimensions["december"].get(child_type.id, Decimal('0.00')),
                            "quarter4": time_dimensions["quarter4"].get(child_type.id, Decimal('0.00')),
                            "half_year2": time_dimensions["half_year2"].get(child_type.id, Decimal('0.00')),
                            "annual": time_dimensions["annual"].get(child_type.id, Decimal('0.00')),
                        }
                    
                    # 累加子分类数据到父级
                    for key, value in child_data.items():
                        # 对于营业毛利标签，不累加费用类型32的数据到父级
                        if key not in ["id", "name", "is_parent", "parent_id", "summary"]:
                            # 如果是营业毛利标签且当前子分类是费用类型32，则不累加到父级
                            if not (tag_id == TagType.BUSINESS_MARGIN.value and child_type.id == 32):
                                parent_data[key] += value
                                parent_data[key] = parent_data[key].quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            
                    child_item = {k: decimal.Decimal(v) if isinstance(v, Decimal) else v for k, v in child_data.items()}
                    parent_data["children"].append(child_item)

            parent_item = {k: decimal.Decimal(v) if isinstance(v, Decimal) else v for k, v in parent_data.items()}
            final_result[tag_name].append(parent_item)
    
    return final_result

async def finance_simple_report_detail_module(db: AsyncSession, cost_type_id: int, month: int, yyyy: int):
    """
    获取指定费用类型和月份的明细数据
    
    Args:
        db: 数据库会话
        cost_type_id: 费用类型ID
        month: 月份(1-12)
        yyyy: 查询的年份，如2023
        
    Returns:
        dict: 费用类型明细数据，包含收据明细和退费明细
    """
    from datetime import datetime
    from models.m_workflow import ErpReceiptDetail, ErpReceipt
    from models.m_order import ErpOrderRefundDetail, ErpOrderRefund
    from models.m_finance import ErpFinanceCostType
    from models.models import ErpAccount
    from decimal import Decimal, ROUND_HALF_UP
    from app_finance.crud import get_special_cost_type_32_detail, get_special_cost_type_33_detail
    
    # 验证月份参数
    if month < 1 or month > 12:
        raise ValueError("月份参数必须在1-12之间")
    
    # 查询费用类型信息
    cost_type = await db.execute(
        select(ErpFinanceCostType).where(ErpFinanceCostType.id == cost_type_id)
    )
    cost_type = cost_type.scalar_one_or_none()
    
    if not cost_type:
        return {
            "cost_type": None,
            "total_amount": 0,
            "receipt_details": [],
            "refund_details": []
        }
    
    cost_type_info = {
        "id": cost_type.id,
        "name": cost_type.name,
        "parent_id": cost_type.parent_id
    }
    
    # 对于特殊类型32(预收)和33(实际课程收入)，调用特殊处理函数
    if cost_type_id == 32:
        return await get_special_cost_type_32_detail(db, month, yyyy)
    elif cost_type_id == 33:
        return await get_special_cost_type_33_detail(db, month, yyyy)
    
    # 准备查询月份的数据
    start_date = datetime(yyyy, month, 1)
    if month == 12:
        end_date = datetime(yyyy + 1, 1, 1)
    else:
        end_date = datetime(yyyy, month + 1, 1)
    
    # 查询收据明细
    receipt_details_query = (
        select(
            ErpReceiptDetail,
            ErpReceipt.apply_reason,
            ErpAccount.employee_name.label("create_by_name")
        )
        .join(ErpReceipt, ErpReceiptDetail.receipt_id == ErpReceipt.id)
        .outerjoin(ErpAccount, ErpReceiptDetail.create_by == ErpAccount.id)
        .where(
            and_(
                ErpReceiptDetail.create_time >= start_date,
                ErpReceiptDetail.create_time < end_date,
                ErpReceiptDetail.cost_type_id == cost_type_id,
                ErpReceiptDetail.item_total_price != None
            )
        )
    )
    receipt_details_result = await db.execute(receipt_details_query)
    receipt_details_raw = receipt_details_result.all()
    
    receipt_details = []
    total_receipt_amount = Decimal('0.00')
    
    for row in receipt_details_raw:
        receipt_detail = row[0]
        apply_reason = row[1]
        create_by_name = row[2]
        
        amount = Decimal(str(receipt_detail.item_total_price)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        total_receipt_amount += amount
        
        receipt_details.append({
            "id": receipt_detail.id,
            "receipt_id": receipt_detail.receipt_id,
            "apply_reason": apply_reason,
            "create_time": receipt_detail.create_time,
            "create_by_name": create_by_name,
            "item_name": receipt_detail.item_name,
            "item_total_price": decimal.Decimal(amount),
            "remark": receipt_detail.remark or ""
        })
    
    # 查询退费明细
    refund_details_query = (
        select(
            ErpOrderRefundDetail,
            ErpOrderRefund.refund_reason,
            ErpOrderRefund.refund_remark,
            ErpAccount.employee_name.label("create_by_name")
        )
        .join(ErpOrderRefund, ErpOrderRefundDetail.refund_id == ErpOrderRefund.id)
        .outerjoin(ErpAccount, ErpOrderRefundDetail.create_by == ErpAccount.id)
        .where(
            and_(
                ErpOrderRefundDetail.create_time >= start_date,
                ErpOrderRefundDetail.create_time < end_date,
                ErpOrderRefundDetail.cost_type_id == cost_type_id,
                ErpOrderRefundDetail.refund_money != None
            )
        )
    )
    refund_details_result = await db.execute(refund_details_query)
    refund_details_raw = refund_details_result.all()
    
    refund_details = []
    total_refund_amount = Decimal('0.00')
    
    for row in refund_details_raw:
        refund_detail = row[0]
        refund_reason = row[1]
        refund_remark = row[2]
        create_by_name = row[3]
        
        amount = Decimal(str(refund_detail.refund_money)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        total_refund_amount += amount
        
        refund_details.append({
            "id": refund_detail.id,
            "refund_id": refund_detail.refund_id,
            "refund_reason": refund_reason,
            "create_time": refund_detail.create_time,
            "create_by_name": create_by_name,
            "refund_money": decimal.Decimal(amount),
            "refund_remark": refund_remark
        })
    
    # 计算总金额（收入减去退款）
    total_amount = total_receipt_amount - total_refund_amount
    
    return {
        "cost_type": cost_type_info,
        "total_amount": decimal.Decimal(total_amount),
        "receipt_details": receipt_details,
        "refund_details": refund_details,
        # "refund_remark": refund_remark
    }

async def process_performance_rating_records(db, page, page_size, keyword, yyyy):
    """
    处理绩效评级记录的业务逻辑
    
    Args:
        db: 数据库会话
        page: 页码
        page_size: 每页大小
        keyword: 搜索关键词
        yyyy: 年份筛选
        
    Returns:
        dict: 包含总记录数和处理后的绩效评级记录列表
    """
    from sqlalchemy import and_, or_
    from sqlalchemy.sql import select
    from app_user.crud import get_departments_by_account
    from models.models import ErpAccount, ErpDepartment
    from utils.db.model_handler import ModelDataHelper
    from settings import CF
    from app_finance.crud import get_performance_records
    
    erp_account = CF.get_crud(ErpAccount)
    erp_department = CF.get_crud(ErpDepartment)
    
    # 首先获取所有绩效记录数据
    raw_records = await get_performance_records(db, yyyy=yyyy)
    if not raw_records:
        return {
            "count": 0,
            "data": []
        }
    
    # 提取所有账号ID
    account_ids = set([record.account_id for record in raw_records])
    
    # 如果有关键词，筛选匹配的账号
    if keyword:
        accounts = await erp_account.get_many(db, raw=[
            and_(
                ErpAccount.id.in_(list(account_ids)),
                or_(
                    ErpAccount.employee_name.like(f"%{keyword}%"),
                    ErpAccount.employee_number.like(f"%{keyword}%"),
                    ErpAccount.qy_wechat_position.like(f"%{keyword}%")
                )
            )
        ])
        # 更新账号ID集合
        account_ids = set([account.id for account in accounts])
    
    # 重新筛选绩效记录
    filtered_records = [record for record in raw_records if record.account_id in account_ids]
    
    # 获取总记录数
    total_count = len(filtered_records)
    
    # 分页
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    paged_records = filtered_records[start_idx:end_idx]
    
    # 收集所有账号ID
    paged_account_ids = set([record.account_id for record in paged_records])
    
    # 获取账号信息
    accounts = await erp_account.get_many(db, raw=[
        ErpAccount.id.in_(list(paged_account_ids))
    ])
    account_map = {account.id: account for account in accounts}
    
    # 获取所有部门信息，用于构建部门树
    all_departments = await erp_department.get_many(db, {"disable": 0})
    
    # 构建部门ID到部门信息的映射
    dept_map = {dept.id: {"id": dept.id, "name": dept.dept_name, "parent_id": dept.parent_id} for dept in all_departments}
    
    # 获取用户与部门的关联信息
    departments_data = {}
    for account_id in paged_account_ids:
        account_depts = await get_departments_by_account(db, account_id)
        if account_depts:
            dept_ids = [dept.dept_id for dept in account_depts]
            # 为每个部门ID构建完整的层级路径
            dept_paths = []
            for dept_id in dept_ids:
                path = []
                current_id = dept_id
                # 向上遍历构建路径
                while current_id and current_id in dept_map:
                    dept_info = dept_map[current_id]
                    path.insert(0, dept_info)
                    current_id = dept_info["parent_id"]
                if path:
                    dept_paths.append(path)
            departments_data[account_id] = dept_paths
        else:
            departments_data[account_id] = []
    
    # 组织数据结构
    # 按照账号ID分组，包含所有季度的评级
    result_data = {}
    for record in paged_records:
        account_id = record.account_id
        if account_id not in result_data:
            account = account_map.get(account_id)
            if not account:
                continue
            
            # 格式化部门层级数据
            formatted_depts = []
            for dept_path in departments_data.get(account_id, []):
                # 为每个部门路径创建层级显示
                levels = {}
                for i, dept in enumerate(dept_path, 1):
                    level_key = f"level{i}"
                    levels[level_key] = dept["name"]
                formatted_depts.append(levels)
                
            result_data[account_id] = {
                "account_id": account_id,
                "avatar": account.avatar,
                "employee_name": account.employee_name,
                "departments": formatted_depts,
                "position": account.qy_wechat_position,
                "employee_status": account.employee_status,
                "employee_type": account.employee_type,
                "ratings": {}
            }
        
        # 添加季度评级
        quarter_key = f"{record.yyyy}年{record.quarter}季度"
        result_data[account_id]["ratings"][quarter_key] = {
            "rating_id": record.id,
            "rating": record.rating
        }
    
    # 将字典转换为列表
    result_list = list(result_data.values())
    
    return {
        "count": total_count,
        "data": result_list
    }

async def adjust_teacher_class_fee_module(db, teacher_id, class_fee, class_fee_type, user_id):
    """
    调整教师课时费的模块函数
    """
    from app_finance.crud import adjust_teacher_class_fee_crud
    
    # 调用crud函数更新课时费
    teacher, old_class_fee_type, old_class_fee, error = await adjust_teacher_class_fee_crud(
        db, teacher_id, class_fee, class_fee_type, user_id
    )
    
    if error:
        return False, error
    
    if not teacher:
        return False, "教师信息不存在"
    
    # 处理可能为None的情况
    old_class_fee_type = old_class_fee_type or 1  # 默认为标准课时费
    old_class_fee = old_class_fee or 0.0  # 默认为0
    
    # 生成日志内容
    old_fee_type_text = "标准课时费" if old_class_fee_type == 1 else "比例课时费"
    new_fee_type_text = "标准课时费" if class_fee_type == 1 else "比例课时费"
    
    # 格式化课时费/比例展示
    old_fee_display = f"{old_class_fee:.2f}" if old_class_fee_type == 1 else f"{old_class_fee*100:.2f}%"
    new_fee_display = f"{class_fee:.2f}" if class_fee_type == 1 else f"{class_fee*100:.2f}%"
    
    # 生成日志内容
    if old_class_fee_type == class_fee_type:
        log_content = f"调整{old_fee_type_text}，原{old_fee_type_text}为：{old_fee_display}，新{new_fee_type_text}为：{new_fee_display}"
    else:
        log_content = f"由{old_fee_type_text}调整为{new_fee_type_text}，调整前{old_fee_type_text}为：{old_fee_display}，调整后{new_fee_type_text}为：{new_fee_display}"
    
    # 创建日志记录
    erp_account_teacher_log = CF.get_crud(ErpAccountTeacherLog)
    await erp_account_teacher_log.create(db, commit=False, **{
        "teacher_id": teacher_id,
        "content": log_content,
        "create_by": user_id,
        "update_by": user_id,
        "create_time": datetime.now(settings.TIME_ZONE),
        "update_time": datetime.now(settings.TIME_ZONE),
        "disable": 0
    })
    
    # 提交事务
    await db.commit()
    
    return True, "课时费调整成功"

async def finance_refund_statistic_by_time_module(db, start_time, end_time, group_by='month'):
    """
    # 按月或按日统计退费数量和金额
    
    根据指定的时间范围和统计粒度(月/日)，统计退费数量和金额
    
    ## 参数说明
    - start_time: 开始时间字符串 (格式: 'YYYY-MM-DD' 或 'YYYY-MM-DD HH:MM:SS')
    - end_time: 结束时间字符串 (格式: 'YYYY-MM-DD' 或 'YYYY-MM-DD HH:MM:SS')
    - group_by: 统计粒度 'month'(按月) 或 'day'(按日)
    
    ## 返回数据
    返回包含时间点、退费数量和退费金额的数组，适用于折线图展示:
    [
        {
            "time_point": "2023-01" 或 "2023-01-01" (取决于group_by),
            "count": 退费数量,
            "amount": 退费金额
        },
        ...
    ]
    """
    from datetime import datetime
    from sqlalchemy import func, extract
    
    # 将字符串转换为日期时间对象，兼容不同格式
    try:
        # 尝试解析完整日期时间格式 'YYYY-MM-DD HH:MM:SS'
        start_datetime = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        try:
            # 尝试解析仅日期格式 'YYYY-MM-DD'
            start_datetime = datetime.strptime(start_time, '%Y-%m-%d')
        except ValueError:
            # 如果都失败，则尝试其他可能的格式
            date_formats = ['%Y/%m/%d', '%Y-%m-%dT%H:%M:%S', '%Y/%m/%d %H:%M:%S']
            for date_format in date_formats:
                try:
                    start_datetime = datetime.strptime(start_time, date_format)
                    break
                except ValueError:
                    continue
            else:
                raise ValueError(f"无法解析开始时间格式: {start_time}")
    
    try:
        # 尝试解析完整日期时间格式 'YYYY-MM-DD HH:MM:SS'
        end_datetime = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        try:
            # 尝试解析仅日期格式 'YYYY-MM-DD'，并设置为当天结束时间
            end_datetime = datetime.strptime(end_time, '%Y-%m-%d').replace(hour=23, minute=59, second=59)
        except ValueError:
            # 如果都失败，则尝试其他可能的格式
            date_formats = ['%Y/%m/%d', '%Y-%m-%dT%H:%M:%S', '%Y/%m/%d %H:%M:%S']
            for date_format in date_formats:
                try:
                    end_datetime = datetime.strptime(end_time, date_format)
                    # 如果没有时间部分，设置为当天结束时间
                    if date_format == '%Y/%m/%d':
                        end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                    break
                except ValueError:
                    continue
            else:
                raise ValueError(f"无法解析结束时间格式: {end_time}")
    
    # 基本查询条件
    conditions = [
        ErpOrderRefundDetail.create_time >= start_datetime,
        ErpOrderRefundDetail.create_time <= end_datetime,
        ErpOrderRefundDetail.disable == 0,
        ErpOrderRefundDetail.refund_state == 1,  # 只统计已成功退费的记录
        ErpOrderRefundDetail.refund_money.isnot(None)  # 确保金额不为空
    ]
    
    # 根据统计粒度选择分组和格式
    if group_by == 'month':
        # 按月分组
        time_extract = func.date_format(ErpOrderRefundDetail.create_time, '%Y-%m')
        time_format = '%Y-%m'
    else:
        # 按日分组
        time_extract = func.date_format(ErpOrderRefundDetail.create_time, '%Y-%m-%d')
        time_format = '%Y-%m-%d'
    
    # 构建统计查询
    query = (
        select(
            time_extract.label('time_point'),
            func.count(ErpOrderRefundDetail.id).label('count'),
            func.sum(ErpOrderRefundDetail.refund_money).label('amount')
        )
        .where(and_(*conditions))
        .group_by(time_extract)
        .order_by(time_extract)
    )
    
    # 执行查询
    result = await db.execute(query)
    rows = result.fetchall()
    
    # 格式化结果
    data = []
    for row in rows:
        data.append({
            "time_point": row.time_point,
            "count": row.count,
            "amount": decimal.Decimal(row.amount) if row.amount else 0
        })
    
    return data

async def class_consumption_statistic_module(
    db: AsyncSession,
    page: int = 1,
    page_size: int = 10,
    stu_name: str = None,
    class_name: str = None,
    course_name: str = None,
    center_id: int = None,
    classroom_id: int = None,
    order_start_date: str = None,
    order_end_date: str = None,
    consumption_start_date: str = None,
    consumption_end_date: str = None,
    count: bool = False
):
    """
    课消统计模块函数（性能优化版）
    基于签到表进行课消数量和金额的统计
    
    性能优化措施：
    1. 分离主查询和退款查询，减少JOIN复杂度
    2. 添加查询提示和索引优化
    3. 优化WHERE条件顺序，按选择性排序
    4. 使用子查询替代部分复杂JOIN
    """
    from models.m_class import ErpClass, ErpCourse, ErpClassChecking
    from models.m_office import ErpOfficeCenter, ErpOfficeClassroom
    from models.m_order import ErpOrder, ErpOrderStudent, ErpOrderRefundDetail
    from models.m_student import ErpStudent
    from sqlalchemy.orm import aliased
    from sqlalchemy import text
    
    # 为多次使用同一表的情况创建别名
    Center = aliased(ErpOfficeCenter)
    Classroom = aliased(ErpOfficeClassroom)
    
    # 构建筛选条件 - 按选择性排序，高选择性的条件放前面
    conditions = []
    
    # 高选择性条件
    if center_id:
        conditions.append(Center.id == center_id)
    if classroom_id:
        conditions.append(Classroom.id == classroom_id)
    
    # 中等选择性条件
    if stu_name:
        conditions.append(ErpStudent.stu_name.like(f"%{stu_name}%"))
    if class_name:
        conditions.append(ErpClass.class_name.like(f"%{class_name}%"))
    if course_name:
        conditions.append(ErpCourse.course_name.like(f"%{course_name}%"))
    
    # 时间范围条件
    if order_start_date:
        try:
            start_date = datetime.strptime(order_start_date, '%Y-%m-%d')
            conditions.append(ErpOrder.create_time >= start_date)
        except ValueError:
            pass
    
    if order_end_date:
        try:
            end_date = datetime.strptime(order_end_date, '%Y-%m-%d') + timedelta(days=1)
            conditions.append(ErpOrder.create_time < end_date)
        except ValueError:
            pass
    
    # 处理签到时间条件 - 如果有签到时间限制，需要确保订单学生在指定时间内有签到记录
    checking_time_conditions = []
    if consumption_start_date:
        try:
            start_date = datetime.strptime(consumption_start_date, '%Y-%m-%d')
            checking_time_conditions.append(ErpClassChecking.create_time >= start_date)
        except ValueError:
            pass
    
    if consumption_end_date:
        try:
            end_date = datetime.strptime(consumption_end_date, '%Y-%m-%d') + timedelta(days=1)
            checking_time_conditions.append(ErpClassChecking.create_time < end_date)
        except ValueError:
            pass
    
    # 基础条件（低选择性，放最后）
    base_conditions = [
        ErpOrderStudent.disable == 0,
        ErpOrder.disable == 0,
        ErpOrder.order_class_type == 1,  # 只统计课程类型订单
        ErpStudent.disable == 0,
        ErpClass.disable == 0,
        ErpCourse.disable == 0,
        ErpOrder.total_income > 0,
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value, StudentState.GRADUATED.value])
    ]
    
    all_conditions = conditions + base_conditions
    
    # 如果有签到时间条件，需要通过子查询筛选有符合条件签到记录的订单学生
    if checking_time_conditions:
        # 创建子查询，查找在指定时间范围内有签到记录的订单学生ID
        checking_subquery = (
            select(ErpClassChecking.order_student_id.distinct())
            .where(
                and_(
                    ErpClassChecking.disable == 0,
                    ErpClassChecking.check_status != 2,  # 排除缺勤状态
                    *checking_time_conditions
                )
            )
        )
        
        # 将子查询结果作为条件添加到主查询
        all_conditions.append(ErpOrderStudent.id.in_(checking_subquery))
    
    if count:
        # 优化的计数查询
        count_query = (
            select(func.count(func.distinct(ErpOrderStudent.id)))
            .select_from(ErpOrderStudent)
            .join(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
            .join(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
            .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
            .join(ErpCourse, ErpClass.course_id == ErpCourse.id)
            .join(Classroom, ErpClass.classroom_id == Classroom.id)
            .join(Center, Classroom.center_id == Center.id)
            .where(and_(*all_conditions))
        )
        
        # 添加查询提示以优化执行计划
        count_query = count_query.execution_options(
            compile_state_render_options={"literal_binds": True}
        )
        
        result = await db.execute(count_query)
        return result.scalar()
    
    # 主查询 - 分步获取数据以提高性能
    # 第一步：获取基础的订单学生信息
    base_query = (
        select(
            ErpOrderStudent.id.label("order_student_id"),
            ErpOrderStudent.class_id,
            ErpOrderStudent.stu_id,
            ErpOrderStudent.student_state,
            ErpStudent.stu_name,
            ErpClass.class_name,
            ErpCourse.course_name,
            ErpClass.classroom_id,
            Classroom.room_name.label("classroom_name"),
            Classroom.center_id,
            Center.center_name,
            # 订单相关汇总数据
            func.sum(ErpOrder.total_income).label("total_order_amount"),
            func.sum(ErpOrder.discount).label("discount"),
            func.sum(ErpOrder.buy_num).label("total_num"),
            func.min(ErpOrder.create_time).label("order_create_time")
        )
        .select_from(ErpOrderStudent)
        .join(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
        .join(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
        .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .join(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .join(Classroom, ErpClass.classroom_id == Classroom.id)
        .join(Center, Classroom.center_id == Center.id)
        .where(and_(*all_conditions))
        .group_by(
            ErpOrderStudent.id,
            ErpOrderStudent.class_id,
            ErpOrderStudent.stu_id,
            ErpOrderStudent.student_state,
            ErpStudent.stu_name,
            ErpClass.class_name,
            ErpCourse.course_name,
            ErpClass.classroom_id,
            Classroom.room_name,
            Classroom.center_id,
            Center.center_name
        )
    )
    
    # 分页查询
    if page and page_size:
        offset = (page - 1) * page_size
        base_query = base_query.offset(offset).limit(page_size)
    
    # 添加ORDER BY提高查询稳定性
    base_query = base_query.order_by(ErpOrderStudent.id.desc())
    
    # 添加查询提示
    base_query = base_query.execution_options(
        compile_state_render_options={"literal_binds": True}
    )
    
    result = await db.execute(base_query)
    rows = result.fetchall()
    
    if not rows:
        return []
    
    # 获取订单学生ID列表
    order_student_ids = [row.order_student_id for row in rows]
    
    # 第二步：批量查询签到数据
    checking_query_conditions = [
        ErpClassChecking.order_student_id.in_(order_student_ids),
        ErpClassChecking.check_status != 2,  # 排除缺勤状态
        ErpClassChecking.disable == 0
    ]
    
    # 如果有签到时间筛选条件，添加到签到查询中
    if checking_time_conditions:
        checking_query_conditions.extend(checking_time_conditions)
    
    checking_query = (
        select(
            ErpClassChecking.order_student_id,
            func.count(ErpClassChecking.id).label("consumption_num"),
            func.sum(ErpClassChecking.price).label("consumption_amount"),
            func.avg(ErpClassChecking.price).label("unit_price")
        )
        .where(and_(*checking_query_conditions))
        .group_by(ErpClassChecking.order_student_id)
    )
    
    checking_result = await db.execute(checking_query)
    checking_rows = checking_result.fetchall()
    checking_data = {
        row.order_student_id: {
            "consumption_num": row.consumption_num or 0,
            "consumption_amount": float(row.consumption_amount) if row.consumption_amount else 0.0,
            "unit_price": float(row.unit_price) if row.unit_price else 0.0
        } 
        for row in checking_rows
    }
    
    # 第三步：批量查询退款数据
    refund_query = (
        select(
            ErpOrderRefundDetail.order_student_id,
            func.sum(ErpOrderRefundDetail.refund_money).label("total_refund")
        )
        .where(
            and_(
                ErpOrderRefundDetail.order_student_id.in_(order_student_ids),
                ErpOrderRefundDetail.disable == 0,
                ErpOrderRefundDetail.refund_state == 1  # 已退款状态
            )
        )
        .group_by(ErpOrderRefundDetail.order_student_id)
    )
    
    refund_result = await db.execute(refund_query)
    refund_rows = refund_result.fetchall()
    refund_data = {row.order_student_id: float(row.total_refund) for row in refund_rows}
    
    # 第四步：组装最终数据
    data = []
    for row in rows:
        order_student_id = row.order_student_id
        checking_info = checking_data.get(order_student_id, {
            "consumption_num": 0,
            "consumption_amount": 0.0,
            "unit_price": 0.0
        })
        
        data.append({
            "order_student_id": order_student_id,
            "class_id": row.class_id,
            "stu_id": row.stu_id,
            "stu_name": row.stu_name,
            "class_name": row.class_name,
            "course_name": row.course_name,
            "student_state": row.student_state,
            "total_order_amount": float(row.total_order_amount) if row.total_order_amount else 0.0,
            "refund_money": refund_data.get(order_student_id, 0.0),
            "discount": float(row.discount) if row.discount else 0.0,
            "center_id": row.center_id,
            "center_name": row.center_name,
            "classroom_id": row.classroom_id,
            "classroom_name": row.classroom_name,
            "total_num": float(row.total_num) if row.total_num else 0.0,
            "consumption_num": checking_info["consumption_num"],
            "unit_price": checking_info["unit_price"],
            "consumption_amount": checking_info["consumption_amount"],
            "order_create_time": row.order_create_time
        })
    
    return data

async def class_consumption_statistic_by_class_module(
    db: AsyncSession,
    page: int = 1,
    page_size: int = 10,
    term_id: int = None,
    grade_id: int = None,
    type_id: int = None,
    class_name: str = None,
    teacher_id: int = None,
    center_id: int = None,
    order_start_date: str = None,
    order_end_date: str = None,
    consumption_start_date: str = None,
    consumption_end_date: str = None,
    count: bool = False
):
    """
    课消统计模块函数（班级维度）
    按班级聚合课消数据，返回每个班级的课消统计信息
    """
    from models.m_class import ErpClass, ErpCourse, ErpClassChecking, ErpClassPlan
    from models.m_office import ErpOfficeCenter, ErpOfficeClassroom
    from models.m_order import ErpOrder, ErpOrderStudent, ErpOrderRefundDetail
    from models.m_student import ErpStudent
    from models.m_teacher import ErpAccountTeacher
    from models.models import ErpAccount
    from utils.enum.enum_order import StudentState
    from sqlalchemy.orm import aliased
    
    # 创建别名
    Center = aliased(ErpOfficeCenter)
    Classroom = aliased(ErpOfficeClassroom)
    Teacher = aliased(ErpAccount)
    
    # 构建筛选条件
    conditions = []
    
    # 班级和课程筛选条件
    if term_id:
        conditions.append(ErpCourse.term_id == term_id)
    if grade_id:
        conditions.append(ErpCourse.grade_id == grade_id)
    if type_id:
        conditions.append(ErpCourse.type_id == type_id)
    if class_name:
        conditions.append(ErpClass.class_name.like(f"%{class_name}%"))
    if teacher_id:
        conditions.append(ErpClass.teacher_id == teacher_id)
    if center_id:
        conditions.append(Center.id == center_id)
    
    # 时间范围条件
    if order_start_date:
        try:
            start_date = datetime.strptime(order_start_date, '%Y-%m-%d')
            conditions.append(ErpOrder.create_time >= start_date)
        except ValueError:
            pass
    
    if order_end_date:
        try:
            end_date = datetime.strptime(order_end_date, '%Y-%m-%d') + timedelta(days=1)
            conditions.append(ErpOrder.create_time < end_date)
        except ValueError:
            pass
    
    # 签到时间条件处理
    checking_time_conditions = []
    if consumption_start_date:
        try:
            start_date = datetime.strptime(consumption_start_date, '%Y-%m-%d')
            checking_time_conditions.append(ErpClassChecking.create_time >= start_date)
        except ValueError:
            pass
    
    if consumption_end_date:
        try:
            end_date = datetime.strptime(consumption_end_date, '%Y-%m-%d') + timedelta(days=1)
            checking_time_conditions.append(ErpClassChecking.create_time < end_date)
        except ValueError:
            pass
    
    # 基础条件
    base_conditions = [
        ErpOrderStudent.disable == 0,
        ErpOrder.disable == 0,
        ErpOrder.order_class_type == 1,  # 只统计课程类型订单
        ErpClass.disable == 0,
        ErpCourse.disable == 0,
        ErpOrder.total_income > 0,
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value, StudentState.GRADUATED.value])
    ]
    
    all_conditions = conditions + base_conditions
    
    # 如果有签到时间条件，需要通过子查询筛选
    if checking_time_conditions:
        checking_subquery = (
            select(ErpClassChecking.order_student_id.distinct())
            .where(
                and_(
                    ErpClassChecking.disable == 0,
                    ErpClassChecking.check_status != 2,  # 排除缺勤状态
                    *checking_time_conditions
                )
            )
        )
        all_conditions.append(ErpOrderStudent.id.in_(checking_subquery))
    
    if count:
        # 计数查询
        count_query = (
            select(func.count(func.distinct(ErpClass.id)))
            .select_from(ErpOrderStudent)
            .join(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
            .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
            .join(ErpCourse, ErpClass.course_id == ErpCourse.id)
            .join(Classroom, ErpClass.classroom_id == Classroom.id)
            .join(Center, Classroom.center_id == Center.id)
            .join(Teacher, ErpClass.teacher_id == Teacher.id)
            .where(and_(*all_conditions))
        )
        
        result = await db.execute(count_query)
        return result.scalar()
    
    # 主查询 - 按班级聚合
    base_query = (
        select(
            ErpClass.id.label("class_id"),
            ErpClass.class_name,
            ErpCourse.course_name,
            ErpCourse.term_id,
            ErpCourse.grade_id,
            ErpCourse.type_id,
            ErpClass.teacher_id,
            Teacher.employee_name.label("teacher_name"),
            ErpClass.planning_class_times.label("total_class_times"),
            Center.id.label("center_id"),
            Center.center_name,
            Classroom.id.label("classroom_id"),
            Classroom.room_name.label("classroom_name"),
            # 订单汇总数据
            func.sum(ErpOrder.total_income).label("course_income_receivable"),  # 课程收入应收
            func.sum(ErpOrder.discount).label("discount_amount"),  # 优惠金额
            func.sum(ErpOrder.buy_num).label("total_course_num"),  # 总购买课次
            func.count(func.distinct(ErpOrderStudent.id)).label("student_count")  # 学生数量
        )
        .select_from(ErpOrderStudent)
        .join(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
        .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .join(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .join(Classroom, ErpClass.classroom_id == Classroom.id)
        .join(Center, Classroom.center_id == Center.id)
        .join(Teacher, ErpClass.teacher_id == Teacher.id)
        .where(and_(*all_conditions))
        .group_by(
            ErpClass.id,
            ErpClass.class_name,
            ErpCourse.course_name,
            ErpCourse.term_id,
            ErpCourse.grade_id,
            ErpCourse.type_id,
            ErpClass.teacher_id,
            Teacher.employee_name,
            ErpClass.planning_class_times,
            Center.id,
            Center.center_name,
            Classroom.id,
            Classroom.room_name
        )
    )
    
    # 分页
    if page and page_size:
        offset = (page - 1) * page_size
        base_query = base_query.offset(offset).limit(page_size)
    
    base_query = base_query.order_by(ErpClass.id.desc())
    
    result = await db.execute(base_query)
    rows = result.fetchall()
    
    if not rows:
        return []
    
    # 获取班级ID列表
    class_ids = [row.class_id for row in rows]
    
    # 查询已完成课次（通过班级计划表）
    completed_class_query = (
        select(
            ErpClassPlan.class_id,
            func.count(ErpClassPlan.id).label("completed_class_times")
        )
        .where(
            and_(
                ErpClassPlan.class_id.in_(class_ids),
                ErpClassPlan.finish == 1,
                ErpClassPlan.disable == 0
            )
        )
        .group_by(ErpClassPlan.class_id)
    )
    
    completed_result = await db.execute(completed_class_query)
    completed_data = {row.class_id: row.completed_class_times for row in completed_result.fetchall()}
    
    # 批量查询退款数据（按班级聚合）
    refund_query = (
        select(
            ErpOrderStudent.class_id,
            func.sum(ErpOrderRefundDetail.refund_money).label("total_refund")
        )
        .select_from(ErpOrderRefundDetail)
        .join(ErpOrderStudent, ErpOrderRefundDetail.order_student_id == ErpOrderStudent.id)
        .where(
            and_(
                ErpOrderStudent.class_id.in_(class_ids),
                ErpOrderRefundDetail.disable == 0,
                ErpOrderRefundDetail.refund_state == 1  # 已退款状态
            )
        )
        .group_by(ErpOrderStudent.class_id)
    )
    
    refund_result = await db.execute(refund_query)
    refund_data = {row.class_id: float(row.total_refund) for row in refund_result.fetchall()}
    
    # 批量查询课消数据（按班级聚合）
    consumption_query_conditions = [
        ErpClassChecking.disable == 0,
        ErpClassChecking.check_status != 2  # 排除缺勤状态
    ]
    
    # 如果有签到时间筛选条件，添加到课消查询中
    if checking_time_conditions:
        consumption_query_conditions.extend(checking_time_conditions)
    
    consumption_query = (
        select(
            ErpOrderStudent.class_id,
            func.count(ErpClassChecking.id).label("consumption_num"),
            func.sum(ErpClassChecking.price).label("consumed_amount")
        )
        .select_from(ErpClassChecking)
        .join(ErpOrderStudent, ErpClassChecking.order_student_id == ErpOrderStudent.id)
        .where(
            and_(
                ErpOrderStudent.class_id.in_(class_ids),
                *consumption_query_conditions
            )
        )
        .group_by(ErpOrderStudent.class_id)
    )
    
    consumption_result = await db.execute(consumption_query)
    consumption_data = {
        row.class_id: {
            "consumption_num": row.consumption_num or 0,
            "consumed_amount": float(row.consumed_amount) if row.consumed_amount else 0.0
        }
        for row in consumption_result.fetchall()
    }
    
    # 组装最终数据
    data = []
    for row in rows:
        class_id = row.class_id
        consumption_info = consumption_data.get(class_id, {"consumption_num": 0, "consumed_amount": 0.0})
        refund_amount = refund_data.get(class_id, 0.0)
        course_income_receivable = float(row.course_income_receivable) if row.course_income_receivable else 0.0
        discount_amount = float(row.discount_amount) if row.discount_amount else 0.0
        consumed_amount = consumption_info["consumed_amount"]
        
        # 计算课程收入实收 = 课程收入应收 - 优惠金额 - 退款金额
        course_income_actual = course_income_receivable - discount_amount - refund_amount
        
        # 计算未课消金额 = 课程收入实收 - 已课消金额
        unconsumed_amount = course_income_actual - consumed_amount
        
        data.append({
            "class_id": class_id,
            "class_name": row.class_name,
            "course_name": row.course_name,
            "term_id": row.term_id,
            "grade_id": row.grade_id,
            "type_id": row.type_id,
            "teacher_id": row.teacher_id,
            "teacher_name": row.teacher_name,
            "total_class_times": row.total_class_times,
            "completed_class_times": completed_data.get(class_id, 0),
            "student_count": row.student_count,
            "center_id": row.center_id,
            "center_name": row.center_name,
            "classroom_id": row.classroom_id,
            "classroom_name": row.classroom_name,
            "course_income_receivable": course_income_receivable,  # 课时费预收
            "discount_amount": discount_amount,
            "refund_amount": refund_amount,  # 申请退费的部分
            "course_income_actual": course_income_actual,  # 课程收入实收
            "consumed_amount": consumed_amount,  # 已课消部分
            "unconsumed_amount": unconsumed_amount,  # 未课消部分
            "consumption_num": consumption_info["consumption_num"],
            "total_course_num": float(row.total_course_num) if row.total_course_num else 0.0
        })
    
    return data


async def class_consumption_statistic_by_class_type_module(
    db: AsyncSession,
    page: int = 1,
    page_size: int = 10,
    term_id: int = None,
    grade_id: int = None,
    type_id: int = None,
    cate_id: int = None,
    course_name: str = None,
    order_start_date: str = None,
    order_end_date: str = None,
    consumption_start_date: str = None,
    consumption_end_date: str = None,
    count: bool = False
):
    """
    课消统计模块函数（班型维度）
    按班型（课程类型）聚合课消数据
    """
    from models.m_class import ErpClass, ErpCourse, ErpClassChecking
    from models.m_order import ErpOrder, ErpOrderStudent, ErpOrderRefundDetail
    from utils.enum.enum_order import StudentState
    
    # 构建筛选条件
    conditions = []
    
    if term_id:
        conditions.append(ErpCourse.term_id == term_id)
    if grade_id:
        conditions.append(ErpCourse.grade_id == grade_id)
    if type_id:
        conditions.append(ErpCourse.type_id == type_id)
    if cate_id:
        conditions.append(ErpCourse.category_id == cate_id)
    if course_name:
        conditions.append(ErpCourse.course_name.like(f"%{course_name}%"))
    
    # 时间范围条件
    if order_start_date:
        try:
            start_date = datetime.strptime(order_start_date, '%Y-%m-%d')
            conditions.append(ErpOrder.create_time >= start_date)
        except ValueError:
            pass
    
    if order_end_date:
        try:
            end_date = datetime.strptime(order_end_date, '%Y-%m-%d') + timedelta(days=1)
            conditions.append(ErpOrder.create_time < end_date)
        except ValueError:
            pass
    
    # 签到时间条件处理
    checking_time_conditions = []
    if consumption_start_date:
        try:
            start_date = datetime.strptime(consumption_start_date, '%Y-%m-%d')
            checking_time_conditions.append(ErpClassChecking.create_time >= start_date)
        except ValueError:
            pass
    
    if consumption_end_date:
        try:
            end_date = datetime.strptime(consumption_end_date, '%Y-%m-%d') + timedelta(days=1)
            checking_time_conditions.append(ErpClassChecking.create_time < end_date)
        except ValueError:
            pass
    
    # 基础条件
    base_conditions = [
        ErpOrderStudent.disable == 0,
        ErpOrder.disable == 0,
        ErpOrder.order_class_type == 1,
        ErpClass.disable == 0,
        ErpCourse.disable == 0,
        ErpOrder.total_income > 0,
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value, StudentState.GRADUATED.value])
    ]
    
    all_conditions = conditions + base_conditions
    
    # 如果有签到时间条件，需要通过子查询筛选
    if checking_time_conditions:
        checking_subquery = (
            select(ErpClassChecking.order_student_id.distinct())
            .where(
                and_(
                    ErpClassChecking.disable == 0,
                    ErpClassChecking.check_status != 2,
                    *checking_time_conditions
                )
            )
        )
        all_conditions.append(ErpOrderStudent.id.in_(checking_subquery))
    
    if count:
        # 计数查询 - 按课程类型分组的数量
        count_query = (
            select(func.count(func.distinct(ErpCourse.type_id)))
            .select_from(ErpOrderStudent)
            .join(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
            .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
            .join(ErpCourse, ErpClass.course_id == ErpCourse.id)
            .where(and_(*all_conditions))
        )
        
        result = await db.execute(count_query)
        return result.scalar()
    
    # 主查询 - 按课程类型聚合
    base_query = (
        select(
            ErpCourse.type_id,
            ErpCourse.term_id,
            ErpCourse.grade_id,
            func.group_concat(func.distinct(ErpCourse.course_name).op('SEPARATOR')(' | ')).label("course_names"),
            # 订单汇总数据
            func.sum(ErpOrder.total_income).label("pre_income"),  # 课时费预收
            func.sum(ErpOrder.discount).label("discount_amount"),  # 优惠金额
            func.count(func.distinct(ErpOrderStudent.id)).label("student_count"),  # 学生数量
            func.count(func.distinct(ErpClass.id)).label("class_count")  # 班级数量
        )
        .select_from(ErpOrderStudent)
        .join(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
        .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .join(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(and_(*all_conditions))
        .group_by(
            ErpCourse.type_id,
            ErpCourse.term_id,
            ErpCourse.grade_id
        )
    )
    
    # 分页
    if page and page_size:
        offset = (page - 1) * page_size
        base_query = base_query.offset(offset).limit(page_size)
    
    base_query = base_query.order_by(ErpCourse.type_id, ErpCourse.term_id, ErpCourse.grade_id)
    
    result = await db.execute(base_query)
    rows = result.fetchall()
    
    if not rows:
        return []
    
    # 获取类型条件用于后续查询
    type_conditions = [(row.type_id, row.term_id, row.grade_id) for row in rows]
    
    # 批量查询退款数据（按课程类型聚合）
    refund_conditions = []
    for type_id, term_id, grade_id in type_conditions:
        refund_conditions.append(
            and_(
                ErpCourse.type_id == type_id,
                ErpCourse.term_id == term_id,
                ErpCourse.grade_id == grade_id
            )
        )
    
    refund_query = (
        select(
            ErpCourse.type_id,
            ErpCourse.term_id,
            ErpCourse.grade_id,
            func.sum(ErpOrderRefundDetail.refund_money).label("total_refund")
        )
        .select_from(ErpOrderRefundDetail)
        .join(ErpOrderStudent, ErpOrderRefundDetail.order_student_id == ErpOrderStudent.id)
        .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .join(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(
            and_(
                ErpOrderRefundDetail.disable == 0,
                ErpOrderRefundDetail.refund_state == 1,
                or_(*refund_conditions)
            )
        )
        .group_by(ErpCourse.type_id, ErpCourse.term_id, ErpCourse.grade_id)
    )
    
    refund_result = await db.execute(refund_query)
    refund_data = {(row.type_id, row.term_id, row.grade_id): float(row.total_refund) for row in refund_result.fetchall()}
    
    # 批量查询课消数据（按课程类型聚合）
    consumption_query_conditions = [
        ErpClassChecking.disable == 0,
        ErpClassChecking.check_status != 2,
        or_(*[
            and_(
                ErpCourse.type_id == type_id,
                ErpCourse.term_id == term_id,
                ErpCourse.grade_id == grade_id
            )
            for type_id, term_id, grade_id in type_conditions
        ])
    ]
    
    if checking_time_conditions:
        consumption_query_conditions.extend(checking_time_conditions)
    
    consumption_query = (
        select(
            ErpCourse.type_id,
            ErpCourse.term_id,
            ErpCourse.grade_id,
            func.sum(ErpClassChecking.price).label("consumed_money")
        )
        .select_from(ErpClassChecking)
        .join(ErpOrderStudent, ErpClassChecking.order_student_id == ErpOrderStudent.id)
        .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .join(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(and_(*consumption_query_conditions))
        .group_by(ErpCourse.type_id, ErpCourse.term_id, ErpCourse.grade_id)
    )
    
    consumption_result = await db.execute(consumption_query)
    consumption_data = {
        (row.type_id, row.term_id, row.grade_id): float(row.consumed_money) if row.consumed_money else 0.0
        for row in consumption_result.fetchall()
    }
    
    # 组装最终数据
    data = []
    for row in rows:
        key = (row.type_id, row.term_id, row.grade_id)
        pre_income = float(row.pre_income) if row.pre_income else 0.0
        discount_amount = float(row.discount_amount) if row.discount_amount else 0.0
        refund_money = refund_data.get(key, 0.0)
        consumed_money = consumption_data.get(key, 0.0)
        
        # 计算未课消金额
        unconsumed_money = pre_income - discount_amount - refund_money - consumed_money
        
        data.append({
            "type_id": row.type_id,
            "term_id": row.term_id,
            "grade_id": row.grade_id,
            "course_names": row.course_names,
            "pre_income": pre_income,  # 课时费预收
            "discount_amount": discount_amount,
            "refund_money": refund_money,  # 申请退费的部分
            "consumed_money": consumed_money,  # 已课消部分
            "unconsumed_money": unconsumed_money,  # 未课消部分
            "student_count": row.student_count,
            "class_count": row.class_count
        })
    
    return data


async def class_consumption_statistic_by_time_module(
    db: AsyncSession,
    yyyy: int,
    term_id: int = None,
    p_grade_id: int = None,
    type_id: int = None,
    group_by: str = "month",  # month 或 quarter
    count: bool = False
):
    """
    课消统计模块函数（时间维度）
    按时间维度（月份/季度）聚合课消数据
    """
    from models.m_class import ErpClass, ErpCourse, ErpClassChecking
    from models.m_order import ErpOrder, ErpOrderStudent, ErpOrderRefundDetail
    from utils.enum.enum_order import StudentState
    
    # 构建筛选条件
    conditions = []
    
    if term_id:
        conditions.append(ErpCourse.term_id == term_id)
    if p_grade_id:
        conditions.append(ErpCourse.p_grade_id == p_grade_id)
    if type_id:
        conditions.append(ErpCourse.type_id == type_id)
    
    # 年份条件
    conditions.append(func.year(ErpOrder.create_time) == yyyy)
    
    # 基础条件
    base_conditions = [
        ErpOrderStudent.disable == 0,
        ErpOrder.disable == 0,
        ErpOrder.order_class_type == 1,
        ErpClass.disable == 0,
        ErpCourse.disable == 0,
        ErpOrder.total_income > 0,
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value, StudentState.GRADUATED.value])
    ]
    
    all_conditions = conditions + base_conditions
    
    if count:
        # 对于时间维度，返回时间点的数量（12个月或4个季度）
        if group_by == "month":
            return 12
        else:  # quarter
            return 4
    
    # 根据分组方式确定时间字段
    if group_by == "month":
        time_field = func.month(ErpOrder.create_time)
        time_label = "month"
    else:  # quarter
        time_field = func.quarter(ErpOrder.create_time)
        time_label = "quarter"
    
    # 主查询 - 按时间聚合
    base_query = (
        select(
            time_field.label(time_label),
            func.sum(ErpOrder.total_income).label("pre_income"),  # 课时费预收
            func.sum(ErpOrder.discount).label("discount_amount"),  # 优惠金额
            func.count(func.distinct(ErpOrderStudent.id)).label("student_count"),  # 学生数量
            func.count(func.distinct(ErpClass.id)).label("class_count")  # 班级数量
        )
        .select_from(ErpOrderStudent)
        .join(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
        .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .join(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(and_(*all_conditions))
        .group_by(time_field)
        .order_by(time_field)
    )
    
    result = await db.execute(base_query)
    rows = result.fetchall()
    
    if not rows:
        # 返回空的时间序列数据
        empty_data = []
        if group_by == "month":
            for month in range(1, 13):
                empty_data.append({
                    "time_period": month,
                    "time_label": f"{yyyy}-{month:02d}",
                    "pre_income": 0.0,
                    "discount_amount": 0.0,
                    "consumed_money": 0.0,
                    "refund_money": 0.0,
                    "unconsumed_money": 0.0,
                    "student_count": 0,
                    "class_count": 0
                })
        else:  # quarter
            for quarter in range(1, 5):
                empty_data.append({
                    "time_period": quarter,
                    "time_label": f"{yyyy}-Q{quarter}",
                    "pre_income": 0.0,
                    "discount_amount": 0.0,
                    "consumed_money": 0.0,
                    "refund_money": 0.0,
                    "unconsumed_money": 0.0,
                    "student_count": 0,
                    "class_count": 0
                })
        return empty_data
    
    # 获取时间点列表
    time_periods = [getattr(row, time_label) for row in rows]
    
    # 查询退款数据（按时间聚合）
    refund_query = (
        select(
            time_field.label(time_label),
            func.sum(ErpOrderRefundDetail.refund_money).label("total_refund")
        )
        .select_from(ErpOrderRefundDetail)
        .join(ErpOrderStudent, ErpOrderRefundDetail.order_student_id == ErpOrderStudent.id)
        .join(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
        .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .join(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(
            and_(
                ErpOrderRefundDetail.disable == 0,
                ErpOrderRefundDetail.refund_state == 1,
                func.year(ErpOrder.create_time) == yyyy,
                *([ErpCourse.term_id == term_id] if term_id else []),
                *([ErpCourse.p_grade_id == p_grade_id] if p_grade_id else []),
                *([ErpCourse.type_id == type_id] if type_id else [])
            )
        )
        .group_by(time_field)
    )
    
    refund_result = await db.execute(refund_query)
    refund_data = {getattr(row, time_label): float(row.total_refund) for row in refund_result.fetchall()}
    
    # 查询课消数据（按时间聚合）
    consumption_query = (
        select(
            time_field.label(time_label),
            func.sum(ErpClassChecking.price).label("consumed_money")
        )
        .select_from(ErpClassChecking)
        .join(ErpOrderStudent, ErpClassChecking.order_student_id == ErpOrderStudent.id)
        .join(ErpOrder, ErpOrder.order_student_id == ErpOrderStudent.id)
        .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .join(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(
            and_(
                ErpClassChecking.disable == 0,
                ErpClassChecking.check_status != 2,
                func.year(ErpOrder.create_time) == yyyy,
                *([ErpCourse.term_id == term_id] if term_id else []),
                *([ErpCourse.p_grade_id == p_grade_id] if p_grade_id else []),
                *([ErpCourse.type_id == type_id] if type_id else [])
            )
        )
        .group_by(time_field)
    )
    
    consumption_result = await db.execute(consumption_query)
    consumption_data = {getattr(row, time_label): float(row.consumed_money) if row.consumed_money else 0.0 for row in consumption_result.fetchall()}
    
    # 组装完整的时间序列数据
    data = []
    if group_by == "month":
        for month in range(1, 13):
            row_data = next((row for row in rows if getattr(row, time_label) == month), None)
            if row_data:
                pre_income = float(row_data.pre_income) if row_data.pre_income else 0.0
                discount_amount = float(row_data.discount_amount) if row_data.discount_amount else 0.0
                student_count = row_data.student_count
                class_count = row_data.class_count
            else:
                pre_income = discount_amount = student_count = class_count = 0
            
            refund_money = refund_data.get(month, 0.0)
            consumed_money = consumption_data.get(month, 0.0)
            unconsumed_money = pre_income - discount_amount - refund_money - consumed_money
            
            data.append({
                "time_period": month,
                "time_label": f"{yyyy}-{month:02d}",
                "pre_income": pre_income,
                "discount_amount": discount_amount,
                "consumed_money": consumed_money,
                "refund_money": refund_money,
                "unconsumed_money": unconsumed_money,
                "student_count": student_count,
                "class_count": class_count
            })
    else:  # quarter
        for quarter in range(1, 5):
            row_data = next((row for row in rows if getattr(row, time_label) == quarter), None)
            if row_data:
                pre_income = float(row_data.pre_income) if row_data.pre_income else 0.0
                discount_amount = float(row_data.discount_amount) if row_data.discount_amount else 0.0
                student_count = row_data.student_count
                class_count = row_data.class_count
            else:
                pre_income = discount_amount = student_count = class_count = 0
            
            refund_money = refund_data.get(quarter, 0.0)
            consumed_money = consumption_data.get(quarter, 0.0)
            unconsumed_money = pre_income - discount_amount - refund_money - consumed_money
            
            data.append({
                "time_period": quarter,
                "time_label": f"{yyyy}-Q{quarter}",
                "pre_income": pre_income,
                "discount_amount": discount_amount,
                "consumed_money": consumed_money,
                "refund_money": refund_money,
                "unconsumed_money": unconsumed_money,
                "student_count": student_count,
                "class_count": class_count
            })
    
    # 添加年度汇总
    annual_data = {
        "time_period": "annual",
        "time_label": f"{yyyy}-年度汇总",
        "pre_income": sum(item["pre_income"] for item in data),
        "discount_amount": sum(item["discount_amount"] for item in data),
        "consumed_money": sum(item["consumed_money"] for item in data),
        "refund_money": sum(item["refund_money"] for item in data),
        "unconsumed_money": sum(item["unconsumed_money"] for item in data),
        "student_count": sum(item["student_count"] for item in data),
        "class_count": sum(item["class_count"] for item in data)
    }
    data.append(annual_data)
    
    return data
