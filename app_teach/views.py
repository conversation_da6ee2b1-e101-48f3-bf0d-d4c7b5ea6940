from importlib import import_module

# 定义模块路径的列表
from fastapi import APIRouter

prefix = 'app_teach'
apps = [
    "exam_question",
    'exam_paper',
    'online_exam',
    'question_bank',
    'student',
    'class_report',
    'class',
    'class_wechat',
    'materials',
    'checking',
    'class_transfor',
    'class_wechat',
]

router = APIRouter(prefix=f'/{prefix}')

for app in apps:
    module = import_module(f"{prefix}.api.{app}")
    router.include_router(module.router)
