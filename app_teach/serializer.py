from datetime import datetime
from typing import Optional, Union, List, Dict

from pydantic import BaseModel, Field, Json


class QuestionOption(BaseModel):
    option_content: Union[str] = None
    correct: Union[int] = None


class QuestionBlank(BaseModel):
    option_content: Union[str] = None
    answer: Union[str] = None
    option_score: Union[str] = None


class QuestionDetail(BaseModel):
    question_id: int
    option: Optional[List[QuestionOption]] = None
    blank: Optional[List[QuestionBlank]] = None


class QuestionOptionBase(BaseModel):
    id: Optional[int] = None
    option_content: Optional[str] = None
    correct: Optional[int] = None
    answer: Optional[str] = None
    option_score: Optional[float] = None
    old_correct_id: Optional[int] = None


class ModifyQuestionOption(BaseModel):
    id: Union[int] = None
    option_content: Union[str] = None
    correct: Union[int] = None


class CreateQuestion(BaseModel):
    paper_id: Union[int] = None
    sort: Union[int] = None
    content: Union[str] = None
    difficulty_level: Union[int] = None
    score: Union[float] = None
    knowledge_point: Union[str] = None
    analysis: Union[str] = None
    question_type: Union[int] = None
    options: Union[List[QuestionOption]] = None
    blanks: Union[List[QuestionBlank]] = None


class UpdateQuestion(BaseModel):
    content: Union[str, None] = None
    difficulty_level: Union[int, None] = None
    score: Union[float, None] = None
    knowledge_point: Union[str, None] = None
    options: Union[List[ModifyQuestionOption], None] = None


class PaperCreate(BaseModel):
    paper_name: str
    is_active: Union[int, None] = None
    grade_id: Union[int, None] = None
    p_grade_id: Union[int, None] = None
    subject_id: Union[int, None] = None
    is_lock: Union[int, None] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    passing_score: Optional[float] = None
    total_score: Optional[float] = None

    class Config:
        orm_mode = True


class PaperUpdate(BaseModel):
    paper_name: str
    is_active: Union[int, None] = None
    grade_id: Union[int, None] = None
    subject_id: Union[int, None] = None
    is_lock: Union[int, None] = None
    p_grade_id: Union[int, None] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    passing_score: Optional[float] = None
    total_score: Optional[float] = None

    class Config:
        orm_mode = True


class PaperQuestionCreate(BaseModel):
    paper_id: int
    question: CreateQuestion
    sort: Union[int, None] = None

    class Config:
        orm_mode = True


class QuestionBlankAnswer(BaseModel):
    option_id: Union[str, None] = None
    answer: Union[str, None] = None


class AnswerQuestionCreate(BaseModel):
    paper_stu_id: int
    paper_question_id: int
    choice_option_id: int
    question_type: int
    answers: Optional[List[QuestionBlankAnswer]] = None

    class Config:
        orm_mode = True


class RandomChoicePaperOption(BaseModel):
    grade_id: Optional[int]
    subject_id: Optional[int]


class ErpOnlinePaperCourseBase(BaseModel):
    paper_id: int
    min_score: int
    max_score: int
    course_name: str
    teacher_msg: Optional[str] = None

    class Config:
        orm_mode = True


class ErpOnlinePaperCourseDelete(ErpOnlinePaperCourseBase):
    id: int


class JobLevelBase(BaseModel):
    id: int
    level_name: str
    level_desc: str
    level_type: str
    level_item: str


class CreateErpGuest(BaseModel):
    guest_name: str
    username: str
    password: str


class CreateQbPermission(BaseModel):
    account_id: Optional[int] = None
    account_type: int
    subject_ids: List[int]
    grade_ids: List[int]
    permission_ids: List[int]
    erp_guest: Optional[CreateErpGuest] = None


class UpdateQbPermission(BaseModel):
    subject_ids: List[int]
    grade_ids: List[int]
    permission_ids: List[int]


class ErpStudentEntity(BaseModel):
    stu_username: str
    stu_name: str
    stu_birth: str
    stu_gender: int
    stu_avatar: Optional[str] = None
    stu_area: Optional[str] = None
    stu_address: Optional[str] = None
    stu_grade: int
    stu_idcard: Optional[str] = None
    stu_school_name: Optional[str] = None


class StudyReport(BaseModel):
    report_name: str
    desc: str


class StudyClassReport(BaseModel):
    study_report_id: int
    class_ids: list[int]


class ClassBase(BaseModel):
    id: Optional[int] = None
    course_id: int = Field(..., alias='course_id')
    class_name: str = Field(..., alias='class_name')
    class_capacity: int = Field(..., alias='class_capacity')
    pre_enrollment: int = Field(..., alias='pre_enrollment')
    teacher_id: int = Field(..., alias='teacher_id')
    start_date: str = Field(..., alias='start_date')
    classroom_id: int = Field(..., alias='classroom_id')
    use_standard_full_rate: int = Field(default=0, alias='use_standard_full_rate')
    planning_class_times: int = Field(..., alias='planning_class_times')
    scheduling_method: int = Field(..., alias='scheduling_method')
    scheduling_json: list[Dict] = Field(..., alias='scheduling_json')
    is_shelf_miniprogram: int = Field(..., alias='is_shelf_miniprogram')
    miniprogram_start_enrollment_time: str = Field(..., alias='miniprogram_start_enrollment_time')
    miniprogram_end_enrollment_time: str = Field(..., alias='miniprogram_end_enrollment_time')
    enrollment_conditions: Optional[str] = Field(default=None, alias='enrollment_conditions')
    sync_tutor: int = Field(default=0, alias='sync_tutor')
    hourly_tuition_ratio: float = Field(default=0.00, alias='hourly_tuition_ratio')


# excel上传班级单个验证
class ExcelClassCreate(BaseModel):
    class_name: str
    course_name: str
    teacher_name: str
    room_name: str
    start_date: str
    use_standard_full_rate: int   # 0不使用 >0则为实际值
    class_capacity:int
    planning_class_times: int
    scheduling_method: int
    scheduling_json: list[Dict]
    miniprogram_start_enrollment_time: str
    miniprogram_end_enrollment_time: str
    is_shelf_miniprogram: int



class ClassModify(BaseModel):
    # 修改班级，均为可选字段
    course_id: Optional[int] = Field(default=None, alias='course_id')
    class_name: Optional[str] = Field(default=None, alias='class_name')
    class_capacity: Optional[int] = Field(default=None, alias='class_capacity')
    pre_enrollment: Optional[int] = Field(default=None, alias='pre_enrollment')
    teacher_id: Optional[int] = Field(default=None, alias='teacher_id')
    assistant_teacher_id: Optional[int] = Field(default=None, alias='assistant_teacher_id')
    start_date: str = Field(..., alias='start_date')
    classroom_id: Optional[int] = Field(default=None, alias='classroom_id')
    use_standard_full_rate: Optional[int] = Field(default=None, alias='use_standard_full_rate')
    planning_class_times: Optional[int] = Field(default=None, alias='planning_class_times')
    scheduling_method: Optional[int] = Field(default=None, alias='scheduling_method')
    # scheduling_json: Optional[str] = Field(default=None, alias='scheduling_json')
    is_shelf_miniprogram: Optional[bool] = Field(default=None, alias='is_shelf_miniprogram')
    miniprogram_start_enrollment_time: str = Field(..., alias='miniprogram_start_enrollment_time')
    miniprogram_end_enrollment_time: str = Field(..., alias='miniprogram_end_enrollment_time')
    # qwechat_id: Optional[str] = Field(default=None, alias='qwechat_id')
    # classin_id: Optional[str] = Field(default=None, alias='classin_id')
    enrollment_conditions: Optional[str] = Field(default=None, alias='enrollment_conditions')
    sync_tutor: Optional[int] = Field(default=None, alias='sync_tutor')
    hourly_tuition_ratio: Optional[float] = Field(default=None, alias='hourly_tuition_ratio')


class OutlineBase(BaseModel):
    course_id:Optional[int] = None
    outline_name: str
    outline_desc: Optional[str] = None


class OutlineList(BaseModel):
    outlines: List[OutlineBase]

class CourseBase(BaseModel):
    id: Optional[int] = None
    course_name: str = Field(..., alias='course_name')
    year: int = Field(..., alias='year')
    term_id: int = Field(..., alias='term_id')
    subject_id: int = Field(..., alias='subject_id')
    grade_id: int = Field(..., alias='grade_id')
    p_grade_id: int = Field(..., alias='p_grade_id')
    type_id: int = Field(..., alias='type_id')
    category_id: int = Field(..., alias='category_id')
    course_cover: Optional[str] = Field(default=None, alias='course_cover')
    course_introduction_page: Optional[str] = Field(default=None, alias='course_introduction_page')
    allow_refund: int = Field(default=0, alias='allow_refund')
    allow_repeated_purchase: int = Field(default=0, alias='allow_repeated_purchase')
    cost_calculation_plan: int = Field(..., alias='cost_calculation_plan')
    course_coefficient: Optional[float] = Field(default=None, alias='course_coefficient')
    pricing_plan: int = Field(..., alias='pricing_plan')
    original_price: Optional[float] = Field(default=None, alias='original_price')
    sale_price: Optional[float] = Field(default=None, alias='sale_price')
    number_of_lessons: int = Field(..., alias='number_of_lessons')
    bound_textbook_id: Optional[int] = Field(default=None, alias='bound_textbook_id')
    outlines: Optional[list[OutlineBase]] = None
    is_term_plan: int = Field(..., alias='is_term_plan')
    is_enable: Optional[int] = Field(default=None, alias='is_enable')


class CourseTermBase(BaseModel):
    term_name: str
    term_type: int
    year: int


class CourseCategoryBase(BaseModel):
    category_name: str
    category_desc: Optional[str] = None
    category_objective: Optional[str] = None
    stu_type_desc: Optional[str] = None
    grade_id: Optional[int] = None
    is_enable: Optional[int] = None


class CourseTextbook(BaseModel):
    name: str
    unit: str
    origin_price: float
    sale_price: float


class TeacherBase(BaseModel):
    username: Optional[str] = None
    employee_name: Optional[str] = None
    # employee_number: Optional[str] = None
    # employee_birth: Optional[str] = None
    # employee_hire_date: Optional[str] = None
    qy_wechat_position: Optional[str] = None
    # employee_status: int = None
    # employee_type: Optional[int] = None
    # employee_major: Optional[str] = None
    # employee_education: Optional[str] = None
    # teacher_id: Optional[int] = None
    teacher_grade: Optional[int] = None
    teacher_subject: Optional[int] = None
    # teacher_certification: Optional[str] = None
    # teacher_fee: Optional[float] = None
    teacher_avatar: Optional[str] = None
    teacher_image: Optional[str] = None
    teacher_qr_img: Optional[str] = None
    teacher_desc: Optional[str] = None
    teacher_tag: Optional[list] = None
    dept_ids: Optional[List[int]] = None
    is_show: Optional[int] = None


class ClassPlanBase(BaseModel):
    class_id: int = None
    start_time: str
    end_time: str
    teacher_id: int
    room_id: int


class ClassPlanBatchUpdate(BaseModel):
    plan_id: int = Field(..., description="班级计划ID")
    class_id: Optional[int] = Field(default=None, description="班级ID，可选")
    start_time: Optional[str] = Field(default=None, description="开始时间")
    end_time: Optional[str] = Field(default=None, description="结束时间")
    teacher_id: Optional[int] = Field(default=None, description="教师ID")
    room_id: Optional[int] = Field(default=None, description="教室ID")


class StudentCheckingParams(BaseModel):
    class_id: int
    class_plan_id: int
    check_status: int
    order_student_id: int
    class_room_id: int
    stu_type: int
    is_online: int
    time_duration: float


class StudentCheckingUpdateParams(BaseModel):
    class_plan_id: int
    check_status: int
    order_student_id: int

class CancelAllCheckingParams(BaseModel):
    class_plan_id: int
    order_student_ids: List[int]

class ClassTransferRulePaidBase(BaseModel):
    p_grade_id: int
    grade_id: Optional[int] = None
    subject_id: Optional[int] = None
    cate_id: Optional[int] = None
    target_cate_ids: Optional[str] = None
    target_subject_ids: Optional[str] = None
    target_grade_ids: Optional[str] = None
    description: Optional[str] = None
    is_enabled: int

class ClassRenewRuleBase(BaseModel):
    current_class_id: int
    current_teacher_id: int
    term_id: int
    next_class_id: int
    next2_class_id: int
    start_time: str
    end_time: str
    signup_start: str



class PaidTransferClassParams(BaseModel):
    old_order_student_id: int
    new_class_id: int
    transfor_reason: str = "教务转班"
    transfor_num: int
    is_inner: int = 1
    stu_id: int



class RescheduleClassParams(BaseModel):
    order_student_id: int
    old_plan_id: int
    new_plan_id: int
    reschedule_reason: Optional[str] = None
    is_inner: int


class CancelRescheduleClassParams(BaseModel):
    reschedule_id: int
    stu_id:int


class ModifyClassCompleteBase(BaseModel):
    modify_type: int # 1 增加 2 减少
    modify_num: int
    order_student_id: int