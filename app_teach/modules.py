from io import BytesIO
import json
from typing import Any
from datetime import datetime
import openpyxl
from sqlalchemy.orm import aliased
from sqlalchemy import select, and_, or_, func
from app_teach.crud import class_waiting_approval_receipts, get_valid_class, query_class_info
from models.m_class import ErpClassChangeRulesPaid, ErpClassChecking, ErpClassLog, ErpClassPlan, ErpClassRescheduleRules, ErpCourse, ErpCourseOutline, ErpCourseTerm, ErpCourseCategory, ErpCourseLog, ErpClass
from models.m_office import ErpOfficeClassroom
from models.m_online_exam import ErpOnlineQuestion, ErpOnlinePaper, ErpOnlinePaperQuestion, ErpOnlineQuestionOption, \
    ErpOnlineStuPaper, ErpOnlineStuScore
from models.m_order import ErpOrderStudent, ErpOrderRefundDetail
from models.m_student import ErpStudent
from models.m_teacher import ErpAccountTeacher
from models.m_wechat import ErpWechatExternaluser
from models.models import ErpAccount
from modules.qy_wechat.qy_wechat_relate import QyWechatRelate
from public_api.crud import get_user_dept
import settings
from utils.db.crud_handler import CRUD
from models.m_workflow import ErpCostTypeBind, ErpReceipt, ErpWorkflowCostType
from utils.db.db_handler import get_default_db
from utils.enum.enum_approval import AuditState, ClassAuditStatus, CostTypeBind
from utils.enum.enum_class import ClassLogType, CourseLogType, ClassStatus
from sqlalchemy.ext.asyncio import AsyncSession

from utils.enum.enum_order import StudentState
from utils.response.response_handler import ApiFailedResponse, ApiSuccessResponse

erp_online_question = CRUD(ErpOnlineQuestion)
erp_online_paper = CRUD(ErpOnlinePaper)
erp_online_paper_question = CRUD(ErpOnlinePaperQuestion)
erp_online_question_option = CRUD(ErpOnlineQuestionOption)
erp_online_stu_paper = CRUD(ErpOnlineStuPaper)
erp_online_stu_score = CRUD(ErpOnlineStuScore)
erp_receipt = CRUD(ErpReceipt)
erp_course_log = CRUD(ErpCourseLog)
erp_class_log = CRUD(ErpClassLog)
erp_workflow_cost_type = CRUD(ErpWorkflowCostType)
erp_cost_type_bind = CRUD(ErpCostTypeBind)
erp_order_student = CRUD(ErpOrderStudent)
erp_class = CRUD(ErpClass)
erp_class_plan = CRUD(ErpClassPlan)
erp_course = CRUD(ErpCourse)
erp_account = CRUD(ErpAccount)
erp_office_classroom = CRUD(ErpOfficeClassroom)
erp_class_change_rules_paid = CRUD(ErpClassChangeRulesPaid)



def generate_serial_number(db, MyModel: Any) -> str:
    """
    # 查询数据库中当前最大的序列号
    """
    last_entry = db.query(MyModel).order_by(MyModel.id.desc()).first()

    if last_entry and last_entry.stu_serial:
        # 从序列号中提取数字部分，递增1
        last_number = int(last_entry.stu_serial[1:])
        new_number = last_number + 1
    else:
        # 如果没有找到任何记录，从1开始
        new_number = 1

    # 将数字部分格式化为固定长度（例如5位），并在前面加上"S"
    new_serial = f"S{str(new_number).zfill(5)}"

    return new_serial


async def get_course_config(db, conf):
    # 调取课程配置信息

    erp_course_term = CRUD(ErpCourseTerm)
    erp_course_category = CRUD(ErpCourseCategory)

    grade_map = conf.get('grade_map')
    p_grade_map = conf.get('p_grade_map')
    subject_map = conf.get('subject_map')
    course_type_map = conf.get('course_type_map')
    term_data = await erp_course_term.get_many(db)
    category_data = await erp_course_category.get_many(db)
    term_map = {str(i.id): i for i in term_data}
    category_map = {str(i.id): i.category_name for i in category_data}

    return {
        "grade_map": grade_map,
        "p_grade_map": p_grade_map,
        "subject_map": subject_map,
        "term_map": term_map,
        "category_map": category_map,
        "course_type_map": course_type_map,
    }


async def add_course_log(db, objective_id, log_name, log_type, log_content):
    # 添加课程日志
    await erp_course_log.create(db, commit=False, **{
        "log_type": log_type,
        "log_content": log_content,
        "log_name": log_name,
        "objective_id": objective_id
    })

async def add_class_log(db, class_id, log_name, log_type, log_content):
    # 添加班级日志
    await erp_class_log.create(db, commit=False, **{
        "log_type": log_type,
        "log_content": log_content,
        "log_name": log_name,
        "objective_id": class_id
    })

async def get_paper_question(db, paper_id):
    paper = await erp_online_paper.get_by_id(db, paper_id)
    paper_question = await erp_online_paper_question.get_many(db, {"paper_id": paper_id})

    # 创建question_id到sort的映射
    question_sort_map = {q.question_id: q for q in paper_question}
    question_ids = list(question_sort_map.keys())

    questions = await erp_online_question.get_many(db, raw=[
        ErpOnlineQuestion.id.in_(question_ids)
    ])

    # 将sort字段添加到questions对象中
    for question in questions:
        question.sort = question_sort_map[question.id].sort
        question.paper_question_id = question_sort_map[question.id].id

    # 根据sort字段排序
    paper.questions = sorted(questions, key=lambda x: x.sort)

    return paper


async def check_time_conflicts(db, classroom_id, teacher_id, scheduling_json, exclude_plan_id=None):
    """
    检查教室和教师时间冲突
    
    参数:
        db: 数据库会话
        classroom_id: 教室ID
        teacher_id: 教师ID
        scheduling_json: 时间安排列表 [{"start_time":"2024-10-09 14:04:15","end_time":"2024-10-09 16:04:15"}, ...]
        exclude_plan_id: 排除的班级计划ID(用于修改班级计划时排除自身)
        
    返回:
        conflict_info: 冲突信息列表
    """
    from models.m_class import ErpClassPlan, ErpClass
    
    # 添加班级和班级计划CRUD
    erp_class = CRUD(ErpClass)
    erp_class_plan = CRUD(ErpClassPlan)
    
    conflict_info = []
    
    if not scheduling_json or len(scheduling_json) == 0:
        return conflict_info
    
    for schedule in scheduling_json:
        start_time = schedule.get('start_time')
        end_time = schedule.get('end_time')
        
        # 基础查询条件
        base_conditions = [
            ErpClassPlan.room_id == classroom_id, 
            ErpClassPlan.disable == 0
        ]
        
        # 如果有排除ID，添加到条件中
        if exclude_plan_id:
            base_conditions.append(ErpClassPlan.id != exclude_plan_id)
        
        # 检查教室冲突 - 需要关联班级表检查班级状态
        room_conflicts_query = (
            select(ErpClassPlan)
            .join(ErpClass, ErpClass.id == ErpClassPlan.class_id)
            .where(*base_conditions)
            .where(and_(ErpClass.disable == 0, ErpClass.id>0))  # 只检查未删除的班级
            .where(
                or_(
                    # 新课程开始时间在已有课程时间段内
                    and_(
                        ErpClassPlan.start_time <= start_time,
                        ErpClassPlan.end_time > start_time
                    ),
                    # 新课程结束时间在已有课程时间段内
                    and_(
                        ErpClassPlan.start_time < end_time,
                        ErpClassPlan.end_time >= end_time
                    ),
                    # 新课程时间段完全包含已有课程
                    and_(
                        ErpClassPlan.start_time >= start_time,
                        ErpClassPlan.end_time <= end_time
                    ),
                    # 已有课程时间段完全包含新课程
                    and_(
                        ErpClassPlan.start_time <= start_time,
                        ErpClassPlan.end_time >= end_time
                    )
                )
            )
        )
        result = await db.execute(room_conflicts_query)
        room_conflicts = result.scalars().all()
        
        # 基础查询条件 - 教师
        teacher_base_conditions = [
            ErpClassPlan.teacher_id == teacher_id, 
            ErpClassPlan.disable == 0
        ]
        
        # 如果有排除ID，添加到条件中
        if exclude_plan_id:
            teacher_base_conditions.append(ErpClassPlan.id != exclude_plan_id)
        
        # 检查教师冲突 - 需要关联班级表检查班级状态
        teacher_conflicts_query = (
            select(ErpClassPlan)
            .join(ErpClass, ErpClass.id == ErpClassPlan.class_id)
            .where(*teacher_base_conditions)
            .where(and_(ErpClass.disable == 0, ErpClass.id>0))  # 只检查未删除的班级
            .where(
                or_(
                    # 新课程开始时间在已有课程时间段内
                    and_(
                        ErpClassPlan.start_time <= start_time,
                        ErpClassPlan.end_time > start_time
                    ),
                    # 新课程结束时间在已有课程时间段内
                    and_(
                        ErpClassPlan.start_time < end_time,
                        ErpClassPlan.end_time >= end_time
                    ),
                    # 新课程时间段完全包含已有课程
                    and_(
                        ErpClassPlan.start_time >= start_time,
                        ErpClassPlan.end_time <= end_time
                    ),
                    # 已有课程时间段完全包含新课程
                    and_(
                        ErpClassPlan.start_time <= start_time,
                        ErpClassPlan.end_time >= end_time
                    )
                )
            )
        )
        result = await db.execute(teacher_conflicts_query)
        teacher_conflicts = result.scalars().all()
        
        # 处理教室冲突信息
        if room_conflicts:
            for conflict in room_conflicts:
                class_obj = await erp_class.get_one(db, id=conflict.class_id)
                conflict_info.append({
                    "type": "教室冲突",
                    "time": f"{start_time} - {end_time}",
                    "room_id": classroom_id,
                    "conflict_class": class_obj.class_name if class_obj else f"班级ID:{conflict.class_id}"
                })
        
        # 处理教师冲突信息
        if teacher_conflicts:
            for conflict in teacher_conflicts:
                class_obj = await erp_class.get_one(db, id=conflict.class_id)
                conflict_info.append({
                    "type": "教师冲突",
                    "time": f"{start_time} - {end_time}",
                    "teacher_id": teacher_id,
                    "conflict_class": class_obj.class_name if class_obj else f"班级ID:{conflict.class_id}"
                })
    
    return conflict_info



async def create_class_workflow(class_obj: ErpClass,  user_id: int):
    """
    为新创建的班级创建审核流程
    
    Args:
        db: 数据库会话
        class_obj: 班级对象
        workflow_id: 工作流ID
        user_id: 创建人ID
        
    Returns:
        receipt_obj: 创建的单据对象
    """
    from public_api.modules import start_workflow

    async for db in get_default_db():   

        # 发起开班审核流程
        cost_type_bind = await erp_cost_type_bind.get_one(db, id=CostTypeBind.OpenClass.value)
        if not cost_type_bind:
            settings.logger.error(f"找不到开班审核工作流配置, 班级信息: {class_obj}")
            return
        workflow_id = cost_type_bind.workflow_id
        # 创建开班审核工作流
    
        # 获取用户部门ID
        dept_obj = await get_user_dept(db, user_id)
        
        # 创建审核单据
        receipt_data = {
            "apply_reason": f"开班申请: {class_obj.class_name}",
            "related_obj_id": 0,    # 不需关联对象
            "related_obj_type": 0,  # 不需关联对象
            "apply_remark": f"申请开设班级: {class_obj.class_name}",
            "audit_state": AuditState.AUDITING.value,  # 待审
            "workflow_id": workflow_id,
            "create_by": user_id,
            "update_by": user_id,
            "dept_id": dept_obj.id,  # 用户部门ID
            "related_obj_name": class_obj.class_name,
            "class_id": class_obj.id
        }
        
        # 创建单据记录
        receipt_obj = await erp_receipt.create(db, commit=False, **receipt_data)
        
        # 添加课程日志
        await add_class_log(
            db, 
            class_obj.id, 
            "班级创建审核", 
            ClassLogType.CreateClassLog.value, 
            f"用户: {user_id} 创建了班级: {class_obj.class_name}，进入审核流程"
        )
        
        # 启动工作流
        await start_workflow(
            db=db,
            workflow_id=workflow_id,
            business_id=receipt_obj.id,
            user_id=user_id,
            business_type="receipt"
        )
        
        await db.commit()


async def handle_class_approval(db, receipt_id, status):
    """
    处理班级审核通过后的逻辑
    
    Args:
        db: 数据库会话
        receipt_id: 审核单据ID
        status: 审核状态 (2: 通过, 3: 驳回, 4: 取消)
        
    Returns:
        bool: 处理是否成功
    """
    from models.m_class import ErpClass
    from utils.enum.enum_class import ClassStatus, CourseLogType
    
    erp_class = CRUD(ErpClass)
    
    try:
        # 获取单据信息
        receipt = await erp_receipt.get_one(db, id=receipt_id)
        if not receipt:
            print(f"找不到审核单据: {receipt_id}")
            return False
        
        # 检查单据状态与传入状态是否一致
        if receipt.audit_state != status:
            print(f"单据状态不匹配: 传入={status}, 实际={receipt.audit_state}")
            return False
            
        # 获取班级信息
        class_obj = await erp_class.get_one(db, id=receipt.class_id)
        if not class_obj:
            print(f"找不到班级信息或已删除: {receipt.class_id}")
            return False
        
        if status == 2:  # 审核通过
            # 更新班级状态为已激活
            class_obj.audit_status = ClassAuditStatus.PASS.value
            class_obj.class_status = ClassStatus.NotStart.value
            class_obj.update_time = datetime.now()
            class_obj.update_by = receipt.update_by
            
            # 记录审核通过日志
            await add_class_log(
                db, 
                class_obj.id, 
                "班级审核通过", 
                ClassLogType.CreateClassLog.value, 
                f"班级: {class_obj.class_name} 审核通过，已激活"
            )
            
            print(f"班级审核通过处理成功: {class_obj.class_name}")
            
        elif status == 3 or status == 4:  # 审核驳回或取消
            # 记录审核驳回日志
            status_text = "驳回" if status == 3 else "取消"
            await add_course_log(
                db, 
                class_obj.id, 
                f"班级审核{status_text}", 
                CourseLogType.RejectClassLog.value, 
                f"班级: {class_obj.class_name} 审核{status_text}"
            )
            # 更新班级状态为已驳回
            class_obj.audit_status = ClassAuditStatus.REJECT.value
            class_obj.update_time = datetime.now()
            class_obj.update_by = receipt.update_by
            
            print(f"班级审核{status_text}处理成功: {class_obj.class_name}")
        
        await db.commit()
        return True
        
    except Exception as e:
        print(f"处理班级审核出错1: {str(e)}")
        return False




async def start_class_approval_monitor():
    """
    启动班级审核监控任务, 保底检查， 处理工作流未正常处理的开班单
    每60秒检查一次工作流状态，处理已完成的开班审核流程
    """
    import asyncio
    from utils.db.db_handler import get_default_db
    # print("正在启动班级审核保底检查任务...")
    
    while True:
        # try:
            # 获取数据库会话
            async for db in get_default_db():
                # try:
                    # 查询状态为已完成且未开班的开班审核单据
                receipts = await class_waiting_approval_receipts(db)
                # print(f"待审核的开班审核单据: {len(receipts)}")
                processed_count = 0
                # 处理每个符合条件的审核
                for receipt in receipts:
                    # 处理班级审核结果
                    success = await handle_class_approval(
                        db=db,
                        receipt_id=receipt.id,
                        status=receipt.audit_state
                    )
                    if success:
                        processed_count += 1
                
                if processed_count > 0:
                    print(f"班级审核监控任务: 成功处理 {processed_count} 个班级审核")
                    
                # except Exception as e:
                #     print(f"班级审核监控任务出错: {str(e)}")
                # finally:
                #     await db.close()
                    
        # except Exception as e:
        #     print(f"班级审核监控任务异常: {str(e)}")
        
        # 等待60秒再次检查
            await asyncio.sleep(60)


async def calculate_classroom_utilization(db, year, center_id=None):
    """
    计算教室月利用率 - 优化版本
    
    参数:
        db: 数据库会话
        year: 年份
        center_id: 校区ID，可选参数，不传则查询所有校区教室
        
    返回:
        results: 计算结果列表
    """
    import calendar
    from sqlalchemy import extract, func, select, and_
    from datetime import datetime
    from models.m_office import ErpOfficeClassroom
    from models.m_class import ErpClassPlan
    from settings import CLASS_ROOM_UTILIZATION_RATE_PARAMS
    from collections import defaultdict
    
    # 获取配置参数
    special_months = CLASS_ROOM_UTILIZATION_RATE_PARAMS.get("special_months", [1, 2, 7, 8])
    special_month_daily_hours = CLASS_ROOM_UTILIZATION_RATE_PARAMS.get("special_month_daily_hours", 8.33)
    weekday_hours = CLASS_ROOM_UTILIZATION_RATE_PARAMS.get("weekday_hours", 0)
    friday_hours = CLASS_ROOM_UTILIZATION_RATE_PARAMS.get("friday_hours", 2.5)
    weekend_hours = CLASS_ROOM_UTILIZATION_RATE_PARAMS.get("weekend_hours", 10)
    
    # 查询教室信息
    room_query = select(ErpOfficeClassroom.id, ErpOfficeClassroom.room_name, ErpOfficeClassroom.center_id).where(ErpOfficeClassroom.disable == 0)
    
    # 如果指定了校区ID，则按校区筛选
    if center_id is not None:
        room_query = room_query.where(ErpOfficeClassroom.center_id == center_id)
        
    rooms_result = await db.execute(room_query)
    rooms = rooms_result.all()
    
    if not rooms:
        return []
    
    # 获取所有教室ID
    room_ids = [room.id for room in rooms]
    
    # 一次性查询所有教室整年的数据，按教室和月份分组
    plan_query = select(
        ErpClassPlan.room_id,
        extract('month', ErpClassPlan.start_time).label('month'),
        func.sum(ErpClassPlan.time_duration).label('total_duration')
    ).where(
        and_(
            ErpClassPlan.room_id.in_(room_ids),
            ErpClassPlan.disable == 0,
            extract('year', ErpClassPlan.start_time) == year
        )
    ).group_by(
        ErpClassPlan.room_id,
        extract('month', ErpClassPlan.start_time)
    )
    
    plan_result = await db.execute(plan_query)
    plan_data = plan_result.all()
    
    # 将查询结果组织成字典，便于快速查找
    room_month_data = defaultdict(dict)
    for row in plan_data:
        room_month_data[row.room_id][row.month] = float(row.total_duration or 0)
    
    # 每月基础课时计算函数
    def get_base_hours_for_month(year, month):
        total_days = calendar.monthrange(year, month)[1]
        total_base_hours = 0
        
        # 特殊月份每日基础课时固定
        if month in special_months:
            return total_days * special_month_daily_hours
        
        # 其他月份根据周几计算基础课时
        for day in range(1, total_days + 1):
            weekday = calendar.weekday(year, month, day)
            if weekday == 4:  # 周五
                total_base_hours += friday_hours
            elif weekday in [5, 6]:  # 周六、周日
                total_base_hours += weekend_hours
            else:  # 周一至周四
                total_base_hours += weekday_hours
        
        return total_base_hours
    
    # 计算结果列表
    results = []
    
    # 为每个教室计算月利用率
    for room in rooms:
        room_id = room.id
        room_name = room.room_name
        room_center_id = room.center_id
        
        # 创建结果字典
        result = {
            "room_id": room_id,
            "room_name": room_name,
            "center_id": room_center_id,
            "yyyy": year,
        }
        
        # 计算全年总时长
        annual_total_duration = 0
        annual_base_hours = 0
        
        # 初始化详细过程记录
        process_detail = {}
        
        # 获取该教室的月度数据
        room_data = room_month_data.get(room_id, {})
        
        # 逐月计算数据
        for month in range(1, 13):
            # 从预查询的数据中获取当月时长
            month_duration = room_data.get(month, 0)
            
            # 计算当月基础课时
            base_hours = get_base_hours_for_month(year, month)
            
            # 计算利用率
            utilization_rate = round(month_duration / base_hours, 2) if base_hours > 0 else 0
            
            # 添加到结果
            result[f"m{month}"] = utilization_rate
            
            # 记录每月的详细计算过程
            process_detail[f"m{month}"] = {
                "month_duration": round(month_duration, 2),  # 当月总课时时长
                "base_hours": round(base_hours, 2),  # 当月基础课时
                "utilization_rate": utilization_rate,  # 利用率
                "calculation": f"{round(month_duration, 2)} / {round(base_hours, 2)} = {utilization_rate}",  # 计算公式
                "month": month,  # 月份
                "is_special_month": month in special_months  # 是否为特殊月份
            }
            
            # 累加年度数据
            annual_total_duration += month_duration
            annual_base_hours += base_hours
        
        # 添加详细过程到结果中
        result['process_detail'] = process_detail
        
        # 计算年度利用率
        result["y"] = round(annual_total_duration / annual_base_hours, 2) if annual_base_hours > 0 else 0
        
        results.append(result)
    
    return results


async def calculate_classroom_utilization_week(db, year, center_id=None):
    """
    计算周中教室利用率 - 优化版本
    
    参数:
        db: 数据库会话
        year: 年份
        center_id: 校区ID，可选参数，不传则查询所有校区教室
        
    返回:
        results: 计算结果列表
    """
    import calendar
    from sqlalchemy import extract, func, select, and_
    from datetime import datetime
    from models.m_office import ErpOfficeClassroom
    from models.m_class import ErpClassPlan
    from settings import CLASS_ROOM_UTILIZATION_WEEKDAY_RATE_PARAMS
    from collections import defaultdict
    
    # 获取配置参数
    valid_months = CLASS_ROOM_UTILIZATION_WEEKDAY_RATE_PARAMS.get("valid_months", [3, 4, 5, 6, 9, 10, 11, 12])
    valid_weekdays = CLASS_ROOM_UTILIZATION_WEEKDAY_RATE_PARAMS.get("valid_weekdays", [1, 2, 3, 4])  # 1=周一，2=周二，...
    per_hours = CLASS_ROOM_UTILIZATION_WEEKDAY_RATE_PARAMS.get("per_hours", 2.5)
    
    # 配置中 1=周一, 2=周二, ..., 7=周日
    # Python calendar.weekday() 返回 0=周一, 1=周二, ..., 6=周日
    # MySQL中DAYOFWEEK()返回1=周日,2=周一,...,7=周六
    
    # 将配置中的星期几(1-7)转换为Python calendar中的表示(0-6)
    py_weekdays = [wd - 1 for wd in valid_weekdays]
    
    # 查询教室信息
    room_query = select(ErpOfficeClassroom.id, ErpOfficeClassroom.room_name, ErpOfficeClassroom.center_id).where(ErpOfficeClassroom.disable == 0)
    
    # 如果指定了校区ID，则按校区筛选
    if center_id is not None:
        room_query = room_query.where(ErpOfficeClassroom.center_id == center_id)
        
    rooms_result = await db.execute(room_query)
    rooms = rooms_result.all()
    
    if not rooms:
        return []
    
    # 获取所有教室ID
    room_ids = [room.id for room in rooms]
    
    # 一次性查询所有教室有效月份的周中数据，按教室和月份分组
    plan_query = select(
        ErpClassPlan.room_id,
        extract('month', ErpClassPlan.start_time).label('month'),
        func.sum(ErpClassPlan.time_duration).label('total_duration')
    ).where(
        and_(
            ErpClassPlan.room_id.in_(room_ids),
            ErpClassPlan.disable == 0,
            extract('year', ErpClassPlan.start_time) == year,
            extract('month', ErpClassPlan.start_time).in_(valid_months),
            # MySQL中DAYOFWEEK()返回1=周日,2=周一,...,7=周六
            # 需要将我们的配置(1=周一,...,4=周四)转换为MySQL的范围(2=周一,...,5=周四)
            func.DAYOFWEEK(ErpClassPlan.start_time).in_([wd + 1 for wd in valid_weekdays])
        )
    ).group_by(
        ErpClassPlan.room_id,
        extract('month', ErpClassPlan.start_time)
    )
    
    plan_result = await db.execute(plan_query)
    plan_data = plan_result.all()
    
    # 将查询结果组织成字典，便于快速查找
    room_month_data = defaultdict(dict)
    for row in plan_data:
        room_month_data[row.room_id][row.month] = float(row.total_duration or 0)
    
    # 计算每月周中基础课时
    def get_weekday_base_hours_for_month(year, month):
        if month not in valid_months:
            return 0
            
        total_days = calendar.monthrange(year, month)[1]
        total_base_hours = 0
        
        # 统计当月符合条件的工作日数量
        for day in range(1, total_days + 1):
            weekday = calendar.weekday(year, month, day)  # 0=周一, 1=周二, ..., 6=周日
            if weekday in py_weekdays:  # 只计算周一至周四
                total_base_hours += per_hours
        
        return total_base_hours
    
    # 计算结果列表
    results = []
    
    # 为每个教室计算月利用率
    for room in rooms:
        room_id = room.id
        room_name = room.room_name
        room_center_id = room.center_id
        
        # 创建结果字典
        result = {
            "room_id": room_id,
            "room_name": room_name,
            "center_id": room_center_id,
            "yyyy": year,
        }
        
        # 计算全年总时长
        annual_total_duration = 0
        annual_base_hours = 0
        
        # 获取该教室的月度数据
        room_data = room_month_data.get(room_id, {})
        
        # 逐月计算数据
        for month in range(1, 13):
            # 只处理有效月份
            if month not in valid_months:
                continue
                
            # 从预查询的数据中获取当月周中时长
            month_duration = room_data.get(month, 0)
            
            # 计算当月基础课时
            base_hours = get_weekday_base_hours_for_month(year, month)
            
            # 计算利用率
            utilization_rate = round(month_duration / base_hours, 2) if base_hours > 0 else 0
            
            # 添加到结果
            result[f"m{month}"] = utilization_rate
            
            # 累加年度数据
            annual_total_duration += month_duration
            annual_base_hours += base_hours
        
        # 计算年度利用率
        result["y"] = round(annual_total_duration / annual_base_hours, 2) if annual_base_hours > 0 else 0
        
        results.append(result)
    
    return results


async def process_teacher_class_statistics(db, start_time=None, end_time=None, term_ids=None, conf=None):
    """
    处理教师课时统计数据
    
    参数:
        db: 数据库会话
        start_time: 开始时间, 可选
        end_time: 结束时间, 可选
        term_ids: 学期ID列表, 可选
        conf: 配置信息, 可选
    返回:
        处理后的教师课时统计数据
    """
    from app_teach.crud import query_teacher_class_times_statistics
    
    # 调用查询函数获取原始数据
    data = await query_teacher_class_times_statistics(
        db, 
        start_time=start_time, 
        end_time=end_time, 
        term_ids=term_ids
    )
    
    # 如果没有配置信息，或数据为空，直接返回原始数据
    if not conf or not data:
        return data
    
    # 获取课程类型的映射
    course_type_map = conf.get('course_type_map', {})
    
    # 处理结果数据，添加课程类型名称
    for teacher in data:
        # 计算总课时持续时间
        total_duration = teacher.get("total_duration", 0)
        if total_duration:
            # 保留两位小数
            teacher["total_duration"] = round(float(total_duration), 2)
        
        # 处理各班型统计
        course_types = teacher.get("course_types", {})
        course_types_with_name = {}
        
        for type_id, stats in course_types.items():
            # 获取班型名称
            type_name = course_type_map.get(str(type_id), f"未知类型{type_id}")
            
            # 处理课时持续时间
            type_duration = stats.get("duration", 0)
            if type_duration:
                stats["duration"] = round(float(type_duration), 2)
            
            course_types_with_name[type_name] = stats
        
        teacher["course_types_with_name"] = course_types_with_name
        
        # 处理详细记录
        detail_records = teacher.get("detail_records", [])
        for record in detail_records:
            # 添加课程类型名称
            course_type_id = record.get("course_type_id")
            if course_type_id:
                type_name = course_type_map.get(str(course_type_id), f"未知类型{course_type_id}")
                record["course_type_name"] = type_name
            
            # 格式化时间
            if "start_time" in record and record["start_time"]:
                if isinstance(record["start_time"], datetime):
                    record["start_time"] = record["start_time"].strftime('%Y-%m-%d %H:%M:%S')
            
            if "end_time" in record and record["end_time"]:
                if isinstance(record["end_time"], datetime):
                    record["end_time"] = record["end_time"].strftime('%Y-%m-%d %H:%M:%S')
            
            # 格式化持续时间
            if "time_duration" in record and record["time_duration"]:
                record["time_duration"] = round(float(record["time_duration"]), 2)
    
    return data


async def process_class_data(class_info, class_plan_data, student_counts, checking_durations):
    """
    处理班级数据，计算各项指标
    
    参数:
        class_info: 班级基本信息
        class_plan_data: 班级计划数据
        student_counts: 班级学生数量
        checking_durations: 班级签到数据（包含次数和时长）
        
    返回:
        class_data: 处理后的班级数据
    """
    class_id = class_info.class_id
    
    # 计算已上课节数
    finished_class_count = class_plan_data.get(class_id, 0)
    
    # 获取签到数据
    checking_data = checking_durations.get(class_id, {"count": 0, "duration": 0})
    checking_count = checking_data["count"]
    total_duration = checking_data["duration"]
    
    # 计算平均课消（使用签到次数）
    average_consumption = round(checking_count / finished_class_count, 2) if finished_class_count > 0 else 0
    
    # 计算满班系数
    student_count = student_counts.get(class_id, 0)
    use_standard_full_rate = class_info.use_standard_full_rate or 0
    
    if use_standard_full_rate > 0:
        full_rate_coefficient = round(student_count / use_standard_full_rate, 2)
    else:
        full_rate_coefficient = "不计算"
    
    # 返回处理后的班级数据
    return {
        "class_id": class_id,
        "class_name": class_info.class_name,
        "use_standard_full_rate": use_standard_full_rate,
        "average_consumption": average_consumption,  # 签到次数/已上课节数
        "finished_class_count": finished_class_count,  # 已上课节数
        "full_rate_coefficient": full_rate_coefficient,  # 满班系数
        "student_count": student_count  # 额外添加学生数量，便于查看
    }


async def group_data_by_teacher(classes, class_plan_data, student_counts, checking_durations):
    """
    按教师分组整理数据
    
    参数:
        classes: 班级信息列表
        class_plan_data: 班级计划数据
        student_counts: 班级学生数量
        checking_durations: 班级签到数据（包含次数和时长）
        
    返回:
        teachers_data_list: 按教师分组的数据列表，其中班级数据包含：
        - class_id: 班级ID
        - class_name: 班级名称
        - use_standard_full_rate: 满班基数
        - average_consumption: 平均课消（签到次数/已上课节数）
        - finished_class_count: 已上课节数
        - full_rate_coefficient: 满班系数
        - student_count: 学生数量
    """
    teachers_data = {}
    
    for cls in classes:
        teacher_id = cls.teacher_id
        
        # 处理班级数据
        class_data = await process_class_data(cls, class_plan_data, student_counts, checking_durations)
        
        # 添加到教师分组
        if teacher_id not in teachers_data:
            teachers_data[teacher_id] = {
                "teacher_id": teacher_id,
                "teacher_name": cls.teacher_name,
                "teacher_avatar": cls.teacher_avatar,
                "class_data": []
            }
        
        teachers_data[teacher_id]["class_data"].append(class_data)
    
    # 转换为列表返回
    return list(teachers_data.values())


async def class_full_data_module(db, term_id, grade_id, type_id, subject_id, category_id, start_time, teacher_id=None):
    """
    满班数据统计模块，分组每个老师的数据
    表头：
        - 教师名称
        - 班级名称
        - 满班基数 <erp_class.use_standard_full_rate>
        - 平均课消<总签到记录 总课消/已上课节数 相关表erp_class_checking/erp_class_plan >
        - 已上课节数 <班级已上课节数>
        - 满班系数 <招生人数/满班基数>

    return 
    [
        {
            "teacher_id": teacher_id1,
            "teacher_name": teacher_name1,
            "class_data": [
                {
                    "class_id": class_id,
                    "class_name": class_name,
                    "use_standard_full_rate": <erp_class.use_standard_full_rate>,  # 满班基数
                    "average_consumption": 0,  # 平均课消
                    "finished_class_count": 0,  # 已上课节数
                    "full_rate_coefficient": 0,  # 满班系数=招生人数/满班基数 | 满班基数为0时，满班系数为"不计算"
                }
                ...
            ]
        },
        {
            "teacher_id": teacher_id2,
            "teacher_name": teacher_name2,
            "class_data": [
                ...
            ]
        }
    ]
    """
    from app_teach.crud import (
        get_class_info_by_conditions, 
        get_class_plan_data, 
        get_student_counts, 
        get_checking_durations
    )
    
    # 1. 获取符合条件的班级信息
    classes = await get_class_info_by_conditions(
        db, 
        term_id=term_id, 
        grade_id=grade_id, 
        type_id=type_id, 
        subject_id=subject_id, 
        category_id=category_id, 
        start_time=start_time,
        teacher_id=teacher_id
    )
    
    # 如果没有数据，直接返回空列表
    if not classes:
        return []
    
    # 收集所有班级ID
    class_ids = [c.class_id for c in classes]
    
    # 2. 查询已上课节数（已结束的课程计划数量）
    class_plan_data = await get_class_plan_data(db, class_ids)
    
    # 3. 查询班级的学生数量（用于计算满班系数）
    student_counts = await get_student_counts(db, class_ids)
    
    # 4. 查询平均课消数据
    checking_durations = await get_checking_durations(db, class_ids)
    
    # 5. 按教师分组整理数据
    return await group_data_by_teacher(classes, class_plan_data, student_counts, checking_durations)

async def class_renew_report_module(
                    db: AsyncSession,
                    start_teacher_id=None,
                    start_term_id=None,
                    start_grade_id=None,
                    start_p_grade_id=None,
                    start_type_id=None,
                    start_subject_id=None,
                    start_category_id=None,
                    end_term_id=None,
                    # end_grade_id=None,
                    # end_p_grade_id=None,
                    end_type_id=None,
                    # end_subject_id=None,
                    # end_category_id=None,
                ):
    """
    续报数据统计模块
    
    核心目的：统计 start_* 条件下的学生是否在 end_* 条件下进行过报名
    
    强一致性匹配：
    - 学科必须强一致性匹配（start_subject_ids=3,4的班，续报数据中3只能和subject_id=3的班比对，4只能和subject_id=4的班比对）
    
    参数说明：
        - start_teacher_id: 开始教师ID，支持单个ID或ID列表
        - start_term_id: 开始学期ID，支持单个ID或ID列表
        - start_grade_id: 开始年级ID，支持单个ID或ID列表
        - start_p_grade_id: 开始预科年级ID，支持单个ID或ID列表
        - start_type_id: 开始课程类型ID，支持单个ID或ID列表
        - start_subject_id: 开始科目ID，支持单个ID或ID列表（强一致性匹配关键字段）
        - start_category_id: 开始分类ID，支持单个ID或ID列表
        - end_term_id: 结束学期ID，支持单个ID或ID列表
        - end_type_id: 结束课程类型ID，支持单个ID或ID列表
        
    统计数据：
        - 招生数据（基于start_*条件统计）
            - 带班数
            - 满班基数
            - 报名人数
            - 报名满班率
        - 续报数据（start_*条件学生在end_*条件下的强一致性匹配续报情况）
            - 本期应续报人数
            - 本期已续报人数
            - 续报率
        - 退费数据（基于start_*条件统计）
            - 课前退费
            - 课中退费
            - 课中退费率
        - 班级招生一览表（基于start_*条件统计）
            - 表头: 班级名称、报名人数/满班基数
        - 未续报名单（start_*条件应续报学生中未在end_*条件下报名的学生）
            - 表头: 学生名称、联系电话、上期在读、授课教师
    
    返回严格按照以下格式：
    {
        "enrollment_data": {
            "class_count": start_class_count,
            "full_class_base": full_class_base_count,
            "enrollment_number": enrollment_count,
            "enrollment_full_rate": full_class_rate,
        },
        "renewal_data": {
            "expected_renewal_count": 0,
            "actual_renewal_count": 0,
            "renewal_rate": 0,
        },
        "refund_data": {
            "pre_course_refund": 0,
            "mid_course_refund": 0,
            "mid_course_refund_rate": 0,
        },
        "class_full_rate": [
            # {"class_name": "班级名称", "enrollment_count": "报名人数", "use_standard_full_rate": "满班基数"}
        ],
        "non_renewal_list": [
            # {"stu_name": "学生名称", "stu_username": "联系电话", "last_class_name": "上期在读", "last_teacher_name": "授课教师"}
        ]
    }
    """
    from app_teach.crud import get_non_renewal_student_info, get_non_renewal_student_detail_info
    from utils.db.default_crud import CRUD
    from models.m_student import ErpStudent
    from models.m_teacher import ErpAccountTeacher
    from models.models import ErpAccount
    from models.m_order import ErpOrderRefundDetail

    # === 招生数据统计逻辑 ===
    # 查询start_*条件下班级信息
    # 这是所有统计的基础数据源，所有数据都基于这些班级进行统计
    start_class_objs = await query_class_info(db, term_id=start_term_id, grade_id=start_grade_id, p_grade_id=start_p_grade_id, type_id=start_type_id, subject_id=start_subject_id, category_id=start_category_id)
    
    # 如果指定了教师ID，需要进一步过滤
    # 支持单个教师ID或教师ID列表
    # 注意：需要将字符串类型的ID转换为整数类型进行匹配
    if start_teacher_id:
        teacher_ids = start_teacher_id if isinstance(start_teacher_id, list) else [start_teacher_id]
        
        # 数据类型转换：将字符串ID转换为整数ID
        try:
            teacher_ids = [int(tid) for tid in teacher_ids if tid]  # 过滤空字符串并转换为整数
            # 过滤前的班级数量
            before_filter_count = len(start_class_objs)
            start_class_objs = [obj for obj in start_class_objs if obj.teacher_id in teacher_ids]
            after_filter_count = len(start_class_objs)
            
        except ValueError as e:
            # 如果ID转换失败，记录错误并不进行教师过滤
            pass
    
    # 统计带班数（符合start_*条件的班级总数）
    start_class_count = len(start_class_objs)
    
    # 统计满班基数（所有班级的满班基数之和）
    # 只统计use_standard_full_rate > 0的班级，避免包含无效数据
    full_class_base_count = sum(obj.use_standard_full_rate for obj in start_class_objs if obj.use_standard_full_rate > 0)
    
    # 获取所有start_*班级的ID列表，用于后续查询
    start_class_ids = [obj.id for obj in start_class_objs]
    
    if not start_class_ids:
        # 如果没有符合条件的班级，返回空数据
        # 避免后续查询报错，直接返回零值统计结果
        return {
            "enrollment_data": {
                "class_count": 0,
                "full_class_base": 0,
                "enrollment_number": 0,
                "enrollment_full_rate": 0,
            },
            "renewal_data": {
                "expected_renewal_count": 0,
                "actual_renewal_count": 0,
                "renewal_rate": 0,
            },
            "refund_data": {
                "pre_course_refund": 0,
                "mid_course_refund": 0,
                "mid_course_refund_rate": 0,
                "pre_course_refund_details": [],  # 课前退费详情
                "mid_course_refund_details": [],  # 课中退费详情
            },
            "class_full_rate": [],
            "non_renewal_list": []
        }
    
    # 统计start_*班级的报名人数
    # 只统计正常学生和转入学生，不包括退费、毕业等其他状态学生
    start_order_student_objs = await erp_order_student.get_many(db, raw=[
        ErpOrderStudent.class_id.in_(start_class_ids),
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value])
    ])
    
    # 按班级统计学生人数，建立班级ID到学生数量的映射
    # 用于后续计算各班级的报名情况和满班率
    class_enrollment_count = {}
    for order_student in start_order_student_objs:
        class_id = order_student.class_id
        if class_id not in class_enrollment_count:
            class_enrollment_count[class_id] = 0
        class_enrollment_count[class_id] += 1
    
    # 统计总报名人数（所有班级学生数量之和）
    enrollment_count = sum(class_enrollment_count.values())
    
    # 计算报名满班率 = 总报名人数 / 总满班基数
    # 如果满班基数为0，满班率为0，避免除零错误
    full_class_rate = round(enrollment_count / full_class_base_count, 2) if full_class_base_count > 0 else 0
    
    # === [重构] 续报数据与未续报名单逻辑 ===
    # 1. 计算应续报人次
    expected_renewal_count = len(start_order_student_objs)
    
    all_non_renewal_order_students = []
    
    if expected_renewal_count > 0:
        # 2. 按学科对 start_* 周期的学生进行分组
        start_students_by_subject = {}
        students_without_subject = []
        start_class_subject_map = {obj.id: obj.subject_id for obj in start_class_objs}
        
        for order_student in start_order_student_objs:
            subject_id = start_class_subject_map.get(order_student.class_id)
            if subject_id:
                if subject_id not in start_students_by_subject:
                    start_students_by_subject[subject_id] = []
                start_students_by_subject[subject_id].append(order_student)
            else:
                students_without_subject.append(order_student)

        # 3. 对每个学科，进行续报匹配
        for subject_id, subject_order_students in start_students_by_subject.items():
            # 查询该学科在 end_* 周期的班级
            end_class_objs_for_subject = await query_class_info(
                db, term_id=end_term_id, type_id=end_type_id, subject_id=[subject_id]
            )
            end_class_ids_for_subject = [obj.id for obj in end_class_objs_for_subject]
            
            # 获取该学科 start_* 周期学生ID列表
            subject_student_ids = [os.stu_id for os in subject_order_students]
            
            subject_renewed_order_students = []
            if end_class_ids_for_subject:
                # 查询这些学生在 end_* 周期班级中的报名记录 (续报记录)
                subject_renewed_order_students = await erp_order_student.get_many(db, raw=[
                    ErpOrderStudent.class_id.in_(end_class_ids_for_subject),
                    ErpOrderStudent.stu_id.in_(subject_student_ids),
                    ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value])
                ])

            # 4. 人次匹配：为每个 start_* 人次寻找一个 end_* 人次
            renewed_count_by_student = {}
            for ros in subject_renewed_order_students:
                renewed_count_by_student[ros.stu_id] = renewed_count_by_student.get(ros.stu_id, 0) + 1

            for order_student in subject_order_students:
                student_id = order_student.stu_id
                if renewed_count_by_student.get(student_id, 0) > 0:
                    # 匹配成功，此 start_* 人次已续报，消耗一个 end_* 人次
                    renewed_count_by_student[student_id] -= 1
                else:
                    # 匹配失败，此 start_* 人次未续报
                    all_non_renewal_order_students.append(order_student)
        
        # 5. 没有学科ID的学生全部视为未续报
        all_non_renewal_order_students.extend(students_without_subject)

    # 6. 计算实际续报人次和未续报人次
    non_renewal_count = len(all_non_renewal_order_students)
    actual_renewal_count = expected_renewal_count - non_renewal_count
    
    # 7. 计算续报率
    renewal_rate = round(actual_renewal_count / expected_renewal_count, 2) if expected_renewal_count > 0 else 0
    
    # === 退费数据统计逻辑 ===
    refund_student_objs = await erp_order_student.get_many(db, raw=[
        ErpOrderStudent.class_id.in_(start_class_ids),
        ErpOrderStudent.student_state == StudentState.REFUND.value
    ])
    
    pre_course_refund = 0
    mid_course_refund = 0
    pre_course_refund_details = []
    mid_course_refund_details = []
    
    if refund_student_objs:
        refund_order_student_ids = [student.id for student in refund_student_objs]
        erp_class_checking = CRUD(ErpClassChecking)
        refund_checking_records = await erp_class_checking.get_many(db, raw=[
            ErpClassChecking.order_student_id.in_(refund_order_student_ids),
            ErpClassChecking.disable == 0
        ])
        
        has_checking_student_ids = set(record.order_student_id for record in refund_checking_records)
        
        refund_student_ids = [student.stu_id for student in refund_student_objs]
        erp_student = CRUD(ErpStudent)
        refund_student_info_objs = await erp_student.get_many(db, raw=[
            ErpStudent.id.in_(refund_student_ids),
            ErpStudent.disable == 0
        ])
        student_info_map = {student.id: student for student in refund_student_info_objs}
        
        erp_order_refund_detail = CRUD(ErpOrderRefundDetail)
        refund_detail_objs = await erp_order_refund_detail.get_many(db, raw=[
            ErpOrderRefundDetail.order_student_id.in_(refund_order_student_ids),
            ErpOrderRefundDetail.disable == 0
        ])
        refund_detail_map = {}
        for detail in refund_detail_objs:
            if detail.order_student_id not in refund_detail_map:
                refund_detail_map[detail.order_student_id] = []
            refund_detail_map[detail.order_student_id].append(detail)
            
        class_info_map = {c.id: c for c in start_class_objs}
        
        teacher_ids = [c.teacher_id for c in start_class_objs]
        erp_account_teacher = CRUD(ErpAccountTeacher)
        erp_account = CRUD(ErpAccount)
        teacher_objs = await erp_account_teacher.get_many(db, raw=[
            ErpAccountTeacher.id.in_(teacher_ids), ErpAccountTeacher.disable == 0
        ])
        account_ids = [teacher.account_id for teacher in teacher_objs]
        teacher_info_objs = await erp_account.get_many(db, raw=[
            ErpAccount.id.in_(account_ids), ErpAccount.disable == 0
        ])
        teacher_account_map = {teacher.id: teacher for teacher in teacher_info_objs}
        teacher_info_map = {}
        for teacher in teacher_objs:
            account_info = teacher_account_map.get(teacher.account_id)
            if account_info:
                teacher_info_map[teacher.id] = account_info

        for refund_student in refund_student_objs:
            student_info = student_info_map.get(refund_student.stu_id)
            if not student_info: continue
            class_info = class_info_map.get(refund_student.class_id)
            if not class_info: continue
            teacher_info = teacher_info_map.get(class_info.teacher_id)
            teacher_name = teacher_info.employee_name if teacher_info else "未知老师"
            refund_details = refund_detail_map.get(refund_student.id, [])
            
            refund_detail_data = {
                "stu_name": student_info.stu_name,
                "stu_phone": student_info.stu_username,
                "teacher_name": teacher_name,
                "class_name": class_info.class_name,
                "refund_orders": []
            }
            
            if refund_details:
                for detail in refund_details:
                    refund_detail_data["refund_orders"].append({
                        "refund_order_no": detail.refund_order_no or "无",
                        "refund_time": detail.pay_time.strftime("%Y-%m-%d %H:%M:%S") if detail.pay_time else "未知",
                        "refund_amount": float(detail.refund_money) if detail.refund_money else 0.0,
                        "refund_quantity": detail.refund_num or 0
                    })
            else:
                refund_detail_data["refund_orders"].append({
                    "refund_order_no": "无退费单", "refund_time": "未知", "refund_amount": 0.0, "refund_quantity": 0
                })

            if refund_student.id in has_checking_student_ids:
                mid_course_refund += 1
                mid_course_refund_details.append(refund_detail_data)
            else:
                pre_course_refund += 1
                pre_course_refund_details.append(refund_detail_data)

    total_students_and_mid_refund = enrollment_count + mid_course_refund
    mid_course_refund_rate = round(mid_course_refund / total_students_and_mid_refund, 4) if total_students_and_mid_refund > 0 else 0
    
    # === 班级招生一览表生成逻辑 ===
    class_full_rate_list = []
    for class_obj in start_class_objs:
        class_full_rate_list.append({
            "class_name": class_obj.class_name,
            "enrollment_count": class_enrollment_count.get(class_obj.id, 0),
            "use_standard_full_rate": class_obj.use_standard_full_rate,
            "teacher_name": class_obj.teacher_name,
        })

    # === [重构] 未续报名单生成逻辑 ===
    non_renewal_list = []
    if all_non_renewal_order_students:
        class_info_map = {c.id: c for c in start_class_objs}
        
        non_renewal_student_ids = list(set([os.stu_id for os in all_non_renewal_order_students]))
        erp_student = CRUD(ErpStudent)
        student_info_objs = await erp_student.get_many(db, raw=[
            ErpStudent.id.in_(non_renewal_student_ids), ErpStudent.disable == 0
        ])
        student_info_map = {student.id: student for student in student_info_objs}
        
        teacher_ids = list(set([class_info_map[os.class_id].teacher_id for os in all_non_renewal_order_students if os.class_id in class_info_map]))
        erp_account_teacher = CRUD(ErpAccountTeacher)
        teacher_objs = await erp_account_teacher.get_many(db, raw=[
            ErpAccountTeacher.id.in_(teacher_ids), ErpAccountTeacher.disable == 0
        ])
        account_ids = [teacher.account_id for teacher in teacher_objs]
        erp_account = CRUD(ErpAccount)
        teacher_info_objs = await erp_account.get_many(db, raw=[
            ErpAccount.id.in_(account_ids), ErpAccount.disable == 0
        ])
        teacher_account_map = {teacher.id: teacher for teacher in teacher_info_objs}
        teacher_info_map = {}
        for teacher in teacher_objs:
            account_info = teacher_account_map.get(teacher.account_id)
            if account_info:
                teacher_info_map[teacher.id] = account_info
        
        for order_student in all_non_renewal_order_students:
            student_info = student_info_map.get(order_student.stu_id)
            class_info = class_info_map.get(order_student.class_id)
            
            if student_info and class_info:
                teacher_info = teacher_info_map.get(class_info.teacher_id)
                teacher_name = teacher_info.employee_name if teacher_info else "未分配"
                
                non_renewal_list.append({
                    "stu_name": student_info.stu_name,
                    "stu_username": student_info.stu_username,
                    "last_class_name": class_info.class_name,
                    "last_teacher_name": teacher_name
                })

    # === 数据一致性验证 (可选, 调试用) ===
    # calculated_total = actual_renewal_count + len(non_renewal_list)
    # if calculated_total != expected_renewal_count:
    #     print(f"数据一致性警告: 续报人次({actual_renewal_count}) + 未续报人次({len(non_renewal_list)}) = {calculated_total} != 应续报人次({expected_renewal_count})")
    
    # === 组装最终返回结果 ===
    result = {
        "enrollment_data": {
            "class_count": start_class_count,
            "full_class_base": full_class_base_count,
            "enrollment_number": enrollment_count,
            "enrollment_full_rate": full_class_rate,
        },
        "renewal_data": {
            "expected_renewal_count": expected_renewal_count,
            "actual_renewal_count": actual_renewal_count,
            "renewal_rate": renewal_rate,
        },
        "refund_data": {
            "pre_course_refund": pre_course_refund,
            "mid_course_refund": mid_course_refund,
            "mid_course_refund_rate": mid_course_refund_rate,
            "pre_course_refund_details": pre_course_refund_details,
            "mid_course_refund_details": mid_course_refund_details,
        },
        "class_full_rate": class_full_rate_list,
        "non_renewal_list": non_renewal_list
    }
    return result


async def class_full_data_detail_module(db, class_id):
    """
    班级满班数据表
    return 
    [
        {
            "class_id": class_id,
            "class_name": class_name,
            "data": [
                {
                    "date": "2024-01-01",    # 课程计划日期
                    "use_standard_full_rate": 100,  # 满班基数
                    "finished_stu_count": 100,   # 课消人数
                    "student_count": 100,   # 班级学生总数
                    "finished_stu_detail": [
                        {
                            "stu_id": 1,
                            "stu_name": "学生名称",
                        }
                        ...
                    ]
                }
                ...
            ]
        }
    ]
    """
    
    # 获取班级基本信息
    erp_class = CRUD(ErpClass)
    class_info = await erp_class.get_one(db, id=class_id)
    if not class_info:
        return []
    
    # 获取班级课程计划
    erp_class_plan = CRUD(ErpClassPlan)
    class_plans = await erp_class_plan.get_many(db, {"class_id": class_id})
    
    # 如果没有课程计划，直接返回简单数据
    if not class_plans:
        return [{
            "class_id": class_info.id,
            "class_name": class_info.class_name,
            "data": []
        }]
    
    # 获取班级的满班基数
    use_standard_full_rate = class_info.use_standard_full_rate or 0
    
    # 获取班级所有历史学生（包括退费学生），用于展示历史课消记录
    erp_order_student = CRUD(ErpOrderStudent)
    all_students = await erp_order_student.get_many(db, {"class_id": class_id})
    
    # 获取班级当前在读学生（不包括退费学生），用于计算当前班级人数
    current_students = await erp_order_student.get_many(db, raw=[
        ErpOrderStudent.class_id == class_id,
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value])
    ])
    
    # 当前在班学生数
    current_student_count = len(current_students)
    
    # 创建order_student_id与学生信息的映射
    order_student_map = {s.id: s for s in all_students}
    
    # 获取所有学生ID并创建映射
    student_ids = [s.stu_id for s in all_students]
    erp_student = CRUD(ErpStudent)
    student_objs = await erp_student.get_many(db, raw=[ErpStudent.id.in_(student_ids)])
    student_map = {s.id: s for s in student_objs}
    
    # 获取所有课程计划的签到记录
    erp_class_checking = CRUD(ErpClassChecking)
    plan_ids = [plan.id for plan in class_plans]
    checking_records = await erp_class_checking.get_many(db, raw=[
        ErpClassChecking.class_plan_id.in_(plan_ids)
    ])
    
    # 按课程计划分组签到记录
    plan_checking_map = {}
    for record in checking_records:
        if record.class_plan_id not in plan_checking_map:
            plan_checking_map[record.class_plan_id] = []
        plan_checking_map[record.class_plan_id].append(record)
    
    # 获取当前时间，用于区分历史课程和未来课程
    now = datetime.now()
    
    # 组织数据
    result_data = []
    for plan in class_plans:
        # 确定是历史课程还是未来课程
        is_past_class = plan.start_time < now if isinstance(plan.start_time, datetime) else False
        
        # 获取当前计划的签到记录
        plan_checking = plan_checking_map.get(plan.id, [])
        finished_stu_count = len(plan_checking)
        
        # 处理学生详情
        finished_stu_detail = []
        for check in plan_checking:
            # 通过order_student_id获取order_student记录
            order_student = order_student_map.get(check.order_student_id)
            if order_student:
                # 再通过order_student.stu_id获取学生信息
                student = student_map.get(order_student.stu_id)
                if student:
                    finished_stu_detail.append({
                        "stu_id": student.id,
                        "order_student_id": order_student.id,
                        "class_plan_id": plan.id,
                        "check_id": check.id,
                        "check_status": check.check_status,
                        "stu_name": student.stu_name
                    })
        
        # 格式化日期
        plan_date = datetime.strftime(plan.start_time, "%Y-%m-%d") if isinstance(plan.start_time, datetime) else str(plan.start_time).split(" ")[0]
        
        # 如果是过去的课程，显示实际签到人数；如果是未来课程，显示当前在读学生数
        # 但student_count总是显示当前在读学生数
        result_data.append({
            "date": plan_date,
            "use_standard_full_rate": use_standard_full_rate,
            "finished_stu_count": finished_stu_count,
            "student_count": current_student_count,  # 只显示当前在班学生数
            "finished_stu_detail": finished_stu_detail,
            "is_past_class": is_past_class  # 添加标记，前端可以据此区分显示
        })
    
    # 按日期排序
    result_data.sort(key=lambda x: x["date"])
    
    return [{
        "class_id": class_info.id,
        "class_name": class_info.class_name,
        "data": result_data
    }]



async def single_class_verify_module(db, class_params):
    """
    单个班级验证
    """
    # 检查课程名称是否存在且唯一
    course_obj = await erp_course.get_many(db, {"course_name": class_params.course_name.strip()})
    if not course_obj:
        return False, "课程名称不存在"
    if len(course_obj) > 1:
        return False, "课程名称不唯一"
    
    # 检查教师名称是否存在且唯一
    teacher_obj = await erp_account.get_many(db, {"employee_name": class_params.teacher_name.strip(), "is_teacher": 1})
    if not teacher_obj:
        return False, "教师名称不存在"
    if len(teacher_obj) > 1:
        return False, "教师名称不唯一"
    
    # 检查教室名称是否存在且唯一
    room_obj = await erp_office_classroom.get_many(db, {"room_name": class_params.room_name.strip()})
    if not room_obj:
        return False, "教室名称不存在"
    if len(room_obj) > 1:
        return False, "教室名称不唯一"
    course_id = course_obj[0].id
    teacher_id = teacher_obj[0].id
    classroom_id = room_obj[0].id

    # 检测排课数量是否与大纲数量一致
    erp_course_outline = CRUD(ErpCourseOutline)
    outline_obj = await erp_course_outline.get_many(db, {"course_id": course_id})
    if len(outline_obj) != len(class_params.scheduling_json):
        return False, "排课数量与大纲数量不一致"
    
    # 检查教室时间和教师时间冲突
    conflict_info = await check_time_conflicts(
        db,
        classroom_id,
        teacher_id,
        class_params.scheduling_json
    )
    
    # 如果存在冲突，返回冲突信息
    if conflict_info:
        return False, conflict_info
    
    return True, '验证通过'

async def class_transfer_rule_paid_module(db):
    """
    查询已付费转班规则
    """
    # TargetCategory = aliased(ErpCourseCategory, name="TargetCategory")
    UpdateAccount = aliased(ErpAccount, name="UpdateAccount")
    CreateAccount = aliased(ErpAccount, name="CreateAccount")

    selects = [
        ErpClassChangeRulesPaid.id,
        ErpClassChangeRulesPaid.p_grade_id,
        ErpClassChangeRulesPaid.grade_id,
        ErpClassChangeRulesPaid.subject_id,
        ErpClassChangeRulesPaid.cate_id,
        ErpClassChangeRulesPaid.target_cate_ids,
        ErpClassChangeRulesPaid.target_subject_ids,
        ErpClassChangeRulesPaid.target_grade_ids,
        ErpClassChangeRulesPaid.description,
        ErpClassChangeRulesPaid.is_enabled,
        ErpClassChangeRulesPaid.create_time,
        ErpClassChangeRulesPaid.update_time,
        ErpClassChangeRulesPaid.create_by,
        ErpClassChangeRulesPaid.update_by,
        ErpCourseCategory.category_name.label("source_category_name"),
        CreateAccount.employee_name.label("create_by_name"),
        UpdateAccount.employee_name.label("update_by_name"),
    ]
    conditions = [
        ErpClassChangeRulesPaid.is_enabled == 1,
        ErpClassChangeRulesPaid.disable == 0
    ]
    stmt = (select(selects)
            .select_from(ErpClassChangeRulesPaid)
            .outerjoin(ErpCourseCategory, ErpCourseCategory.id == ErpClassChangeRulesPaid.cate_id)
            .outerjoin(UpdateAccount, UpdateAccount.id == ErpClassChangeRulesPaid.update_by)
            .outerjoin(CreateAccount, CreateAccount.id == ErpClassChangeRulesPaid.create_by)
            .where(and_(*conditions))
            .order_by(ErpClassChangeRulesPaid.create_time.desc())
            )
    result = await db.execute(stmt)
    return result.fetchall()





async def format_class_info(db, class_obj, conf, include_capacity_info=True):
    """
    统一格式化班级信息，确保转班和调课相关接口返回的班级信息字段一致
    
    Args:
        db: 数据库会话
        class_obj: 班级对象或字典
        conf: 配置字典
        include_capacity_info: 是否包含容量相关信息（剩余名额、未上课节数等）
    
    Returns:
        dict: 格式化后的班级信息字典
    """
    # 转换为字典以便添加属性
    if hasattr(class_obj, '__dict__'):
        class_dict = dict(class_obj)
    else:
        class_dict = dict(class_obj)
    
    # 添加名称映射字段
    class_dict['type_name'] = conf.get('course_type_map', {}).get(str(class_dict.get('type_id')), '')
    class_dict['grade_name'] = conf.get('grade_map', {}).get(str(class_dict.get('grade_id')), '')
    class_dict['subject_name'] = conf.get('subject_map', {}).get(str(class_dict.get('subject_id')), '')
    class_dict['category_name'] = conf.get('course_category_map', {}).get(str(class_dict.get('category_id')), '')
    
    # 如果需要包含容量相关信息
    if include_capacity_info:
        # 获取班级ID（兼容不同的字段名）
        class_id = class_dict.get('class_id') or class_dict.get('id')
        if class_id:
            # 计算当前在班学生数量
            normal_student_num = await get_class_student_study_num(db, class_id)
            # 剩余名额
            class_capacity = class_dict.get('class_capacity', 0)
            class_dict['residue_num'] = max(0, class_capacity - normal_student_num)
            class_dict['sign_num'] = normal_student_num
            
            # 获取未上课节数
            valid_class = await get_valid_class(db, class_id)
            class_dict['valid_class'] = valid_class
    
    return class_dict


async def format_class_list(db, class_objs, conf, include_capacity_info=True):
    """
    批量格式化班级信息列表
    
    Args:
        db: 数据库会话
        class_objs: 班级对象列表
        conf: 配置字典
        include_capacity_info: 是否包含容量相关信息
    
    Returns:
        list: 格式化后的班级信息列表
    """
    formatted_classes = []
    for class_obj in class_objs:
        formatted_class = await format_class_info(db, class_obj, conf, include_capacity_info)
        formatted_classes.append(formatted_class)
    
    return formatted_classes




async def can_change_class(db, grade_id, cate_id, subject_id):
    """
    可以调整的班型：
     年级大类	班型	                   可转班型
        小学  	思维X班	               思维X班
                思维班	               思维班、鸿志班、创新班
                鸿志班	               鸿志班、创新班
                鸿志B班	               鸿志B班、鸿志班、创新班
                创新班	               创新班

        初中 	集训队	               不可转班
                数学 初一 思维班	       同年级同学科同班型
                数学 初一 鸿志A班	       同年级同学科同班型
                数学 初一 鸿志B班	       同年级同学科同班型
                数学 初一 创新班	       同年级同学科同班型
                数学 初二	           不可转班
                初三 集训班	           同年级同学科同班型
                物理 一体化	           不可转班
                物理 初二 鸿志班	       同年级同学科同班型
                英语	                   同年级同学科

        高中 	数学 高一 思维班	       同年级所有学科所有班型
                数学 高一 鸿志班	       同年级所有学科鸿志班、创新班
                数学 高一 创新班	       同年级所有学科创新班
                强基一轮	               不可转班
                强基二轮	               不可转班
                物理 高一 思维班	       同年级物理化学所有班型
                物理 高一 鸿志班	       同年级物理化学鸿志班、创新班
                物理 高一 创新班	       同年级物理化学创新班
                化学 高一 思维班	       同年级化学所有班型
                化学 高一 鸿志班	       同年级化学鸿志班、创新班
                化学 高一 创新班	       同年级化学创新班

    """
    
    # 从数据库获取转班规则
    raw = [
        ErpClassChangeRulesPaid.is_enabled == 1,
        ErpClassChangeRulesPaid.disable == 0
    ]
    if grade_id and grade_id > 0:
        raw.append(ErpClassChangeRulesPaid.grade_id == grade_id)
    if subject_id and subject_id > 0:
        raw.append(ErpClassChangeRulesPaid.subject_id == subject_id)
    if cate_id and cate_id > 0:
        raw.append(ErpClassChangeRulesPaid.cate_id == cate_id)
    rules = await erp_class_change_rules_paid.get_many(db, raw=raw)
    
    
    # 如果找到匹配的规则
    if rules:
        # 获取第一条匹配的规则
        rule = rules[0]
        # 解析目标班型、学科、年级IDs（逗号分隔的字符串转为列表）
        cate_target = [int(x) for x in rule.target_cate_ids.split(',') if x]
        subject_target = [int(x) for x in rule.target_subject_ids.split(',') if x]
        grade_target = [int(x) for x in rule.target_grade_ids.split(',') if x]
        
        return {
            "cate_target": cate_target,
            "subject_target": subject_target,
            "grade_target": grade_target
        }
    
    # 如果没有找到规则，返回False表示不可转班
    return False



async def get_class_student_study_num(db, class_id):
    selects = [
        func.count(1)
    ]
    conditions = [
        ErpOrderStudent.class_id == class_id,
        ErpOrderStudent.disable == 0,
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value, StudentState.WAIT_PAY.value]), # 正常、转班、待支付(名额锁定)
        ErpOrderStudent.total_hours > 0,
        ErpOrderStudent.total_hours > ErpOrderStudent.complete_hours,
    ]
    stmt = (
        select(*selects)
        .select_from(ErpOrderStudent)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.scalar()



async def can_reschedule_class(db,  grade_id, cate_id, subject_id):
    """
    可以调课的班级：
    1. 当前时间在限制日期之间的可以调课，否则均不可调课
    2. 
    """
    # 从数据库获取调课规则
    erp_class_reschedule_rules = CRUD(ErpClassRescheduleRules)
    raw = [
        ErpClassRescheduleRules.is_enabled == 1,
        ErpClassRescheduleRules.disable == 0
    ]
    if grade_id and grade_id > 0:
        raw.append(ErpClassRescheduleRules.grade_id == grade_id)
    if subject_id and subject_id > 0:
        raw.append(ErpClassRescheduleRules.subject_id == subject_id)
    if cate_id and cate_id > 0:
        raw.append(ErpClassRescheduleRules.cate_id == cate_id)
    rules = await erp_class_reschedule_rules.get_many(db, raw=raw)
    
    
    # 如果找到匹配的规则
    if rules:
        # 获取第一条匹配的规则
        rule = rules[0]
        # 解析目标班型、学科、年级IDs（逗号分隔的字符串转为列表）
        cate_target = [int(x) for x in rule.target_cate_ids.split(',') if x]
        subject_target = [int(x) for x in rule.target_subject_ids.split(',') if x]
        grade_target = [int(x) for x in rule.target_grade_ids.split(',') if x]
        
        return {
            "cate_target": cate_target,
            "subject_target": subject_target,
            "grade_target": grade_target
        }
    
    # 如果没有找到规则，返回False表示不可调课
    return False


async def process_student_list_data(student_data, order_financial_data, refund_data, transfer_data, order_data):
    """
    处理学员名单数据，组装最终返回格式
    """
    result_data = []
    for row in student_data:
        # 获取财务信息
        financial_info = order_financial_data.get(row.order_student_id)
        
        # 获取退款信息
        refunds = refund_data.get(row.order_student_id, [])
        
        # 获取转班信息
        transfer_info = transfer_data.get(row.order_student_id)
        
        # 获取订单列表
        orders = order_data.get(row.order_student_id, [])
        
        # 判断类型（转班/正常报入）
        if row.parent_order_id and row.parent_order_id > 0:
            join_type = "转班报入"
        elif transfer_info and transfer_info['type'] == 'transfer_in':
            join_type = "转班报入"
        elif transfer_info and transfer_info['type'] == 'transfer_out':
            join_type = "转班转出"
        else:
            join_type = "正常报入"
        
        student_info = {
            'stu_id': row.stu_id,
            'order_student_id': row.order_student_id,
            'stu_name': row.stu_name,
            'stu_username': row.stu_username,  # 手机号
            'class_id': row.class_id,
            'class_name': row.class_name,
            'course_id': row.course_id,
            'course_name': row.course_name,
            'teacher_name': row.teacher_name,
            'teacher_avatar': row.teacher_avatar,
            'classroom_name': row.classroom_name,
            'center_name': row.center_name,
            'enrollment_time': row.enrollment_time.strftime('%Y-%m-%d %H:%M:%S') if row.enrollment_time else None,
            'total_receivable': float(financial_info.total_receivable) if financial_info and financial_info.total_receivable else 0.0,
            'total_income': float(financial_info.total_income) if financial_info and financial_info.total_income else 0.0,
            'total_refund': float(financial_info.total_refund) if financial_info and financial_info.total_refund else 0.0,
            'refund_details': refunds,
            'order_list': orders,  # 新增订单列表
            'complete_hours': float(row.complete_hours) if row.complete_hours else 0.0,
            'total_hours': float(row.total_hours) if row.total_hours else 0.0,
            'student_state': row.student_state,
            'join_type': join_type,
            'transfer_info': transfer_info
        }
        result_data.append(student_info)
    
    return result_data


def get_time_period(start_time):
    """
    根据开始时间判断时间段
    
    Args:
        start_time: datetime对象
        
    Returns:
        str: 时间段标识
            - morning: 上午（11:00以前）
            - noon: 中午（11:00-13:00）
            - afternoon: 下午（13:00-18:00）
            - evening: 晚上（18:00以后）
    """
    hour = start_time.hour
    if hour < 11:
        return "morning"
    elif hour < 13:
        return "noon"
    elif hour < 18:
        return "afternoon"
    else:
        return "evening"


async def query_classroom_occupancy_data(db, room_ids, start_datetime, end_datetime):
    """
    查询教室占用数据
    
    Args:
        db: 数据库会话
        room_ids: 教室ID列表，为None时查询所有教室
        start_datetime: 开始时间
        end_datetime: 结束时间
        
    Returns:
        tuple: (教室信息映射, 课程计划列表)
    """
    from models.m_office import ErpOfficeClassroom
    from models.m_class import ErpClassPlan, ErpClass
    from models.models import ErpAccount
    from sqlalchemy import select, and_
    from utils.db.crud_handler import CRUD
    
    # 查询教室信息
    erp_office_classroom = CRUD(ErpOfficeClassroom)
    if room_ids is not None:
        # 查询指定的教室
        rooms = await erp_office_classroom.get_many(db, raw=[
            ErpOfficeClassroom.id.in_(room_ids),
            ErpOfficeClassroom.disable == 0
        ])
    else:
        # 查询所有教室
        rooms = await erp_office_classroom.get_many(db, raw=[
            ErpOfficeClassroom.disable == 0
        ])
    
    if not rooms:
        return None, None
    
    # 创建教室ID到教室信息的映射
    room_map = {room.id: room for room in rooms}
    actual_room_ids = list(room_map.keys())
    
    # 查询指定时间范围内的课程计划
    selects = [
        ErpClassPlan.id.label("plan_id"),
        ErpClassPlan.room_id,
        ErpClassPlan.class_id,
        ErpClassPlan.start_time,
        ErpClassPlan.end_time,
        ErpClass.class_name,
        ErpAccount.employee_name.label("teacher_name"),
        ErpOfficeClassroom.room_name
    ]
    
    conditions = [
        ErpClassPlan.room_id.in_(actual_room_ids),
        ErpClassPlan.disable == 0,
        ErpClass.disable == 0,
        ErpClassPlan.start_time < end_datetime,
        ErpClassPlan.end_time > start_datetime
    ]
    
    stmt = (
        select(*selects)
        .select_from(ErpClassPlan)
        .outerjoin(ErpClass, ErpClass.id == ErpClassPlan.class_id)
        .outerjoin(ErpOfficeClassroom, ErpOfficeClassroom.id == ErpClassPlan.room_id)
        .outerjoin(ErpAccountTeacher, ErpAccountTeacher.id == ErpClassPlan.teacher_id)
        .outerjoin(ErpAccount, ErpAccount.id == ErpAccountTeacher.account_id)
        .where(and_(*conditions))
        .order_by(ErpClassPlan.start_time)
    )
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    class_plans = result.fetchall()
    
    return room_map, class_plans


def organize_classroom_occupancy_data(class_plans):
    """
    组织教室占用数据
    
    Args:
        class_plans: 课程计划列表
        
    Returns:
        dict: 按教室和日期分组的数据
    """
    from collections import defaultdict
    
    # 按教室和日期分组数据
    room_data = defaultdict(lambda: defaultdict(lambda: {
        "morning": [],
        "noon": [],
        "afternoon": [],
        "evening": []
    }))
    
    # 处理课程计划数据
    for plan in class_plans:
        room_id = plan.room_id
        date_str = plan.start_time.strftime("%Y-%m-%d")
        time_period = get_time_period(plan.start_time)
        
        plan_info = {
            "plan_id": plan.plan_id,
            "start_time": plan.start_time.strftime("%H:%M"),
            "end_time": plan.end_time.strftime("%H:%M"),
            "class_id": plan.class_id,
            "class_name": plan.class_name,
            "class_room_id": plan.room_id,
            "class_room_name": plan.room_name,
            "teacher_name": plan.teacher_name,
        }
        
        room_data[room_id][date_str][time_period].append(plan_info)
    
    return room_data


def build_classroom_occupancy_result(room_ids, room_map, room_data, start_datetime, end_datetime):
    """
    构建教室占用结果数据
    
    Args:
        room_ids: 教室ID列表
        room_map: 教室信息映射
        room_data: 按教室和日期分组的数据
        start_datetime: 开始时间
        end_datetime: 结束时间
        
    Returns:
        list: 格式化的结果数据
    """
    from datetime import timedelta
    
    result_data = []
    for room_id in room_ids:
        if room_id in room_map:
            room_info = room_map[room_id]
            
            # 生成日期范围内的所有日期
            current_date = start_datetime.date()
            end_date = end_datetime.date()
            detail_list = []
            
            while current_date <= end_date:
                date_str = current_date.strftime("%Y-%m-%d")
                
                # 获取当天的课程安排，如果没有则为空
                day_data = room_data[room_id].get(date_str, {
                    "morning": [],
                    "noon": [],
                    "afternoon": [],
                    "evening": []
                })
                
                detail_list.append({
                    "date": date_str,
                    "value": day_data
                })
                
                current_date += timedelta(days=1)
            
            result_data.append({
                "class_room_id": room_info.id,
                "class_room_name": room_info.room_name,
                "detail": detail_list
            })
    
    return result_data


async def get_classroom_occupancy_module(db, class_room_ids, start_time, end_time):
    """
    查询教室占用情况模块
    
    Args:
        db: 数据库会话
        class_room_ids: 教室ID列表字符串，逗号分隔，为空时查询所有教室
        start_time: 开始时间字符串
        end_time: 结束时间字符串
        
    Returns:
        tuple: (success: bool, data: dict/str)
    """
    from datetime import datetime
    
    # 解析教室ID列表
    room_ids = None
    if class_room_ids:
        try:
            room_ids = [int(id.strip()) for id in class_room_ids.split(',') if id.strip()]
        except ValueError:
            return False, "教室ID格式错误"
        
        if not room_ids:
            return False, "教室ID格式错误"
    
    # 解析时间参数
    try:
        start_datetime = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        end_datetime = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        return False, "时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式"
    
    if start_datetime >= end_datetime:
        return False, "开始时间必须小于结束时间"
    
    # 查询教室占用数据
    room_map, class_plans = await query_classroom_occupancy_data(db, room_ids, start_datetime, end_datetime)
    
    if room_map is None:
        return False, "未找到有效的教室"
    
    # 组织数据
    room_data = organize_classroom_occupancy_data(class_plans)
    
    # 获取实际的教室ID列表（用于构建结果）
    actual_room_ids = list(room_map.keys())
    
    # 构建结果
    result_data = build_classroom_occupancy_result(actual_room_ids, room_map, room_data, start_datetime, end_datetime)
    
    return True, {"data": result_data}


async def get_class_group_user_list(class_id: int,db):
    """
    获取可拉群的用户列表
    获取班级下老师、助教老师和学生的企微UserID
    支持客户端直接创建群聊并关联班级
    限制最少2个学生，最多40个学生
    """
    import settings
    from models.m_teacher import ErpAccountTeacher
    from models.models import ErpAccount
    from models.m_wechat import ErpWechatExternaluser
    from models.m_order import ErpOrderStudent
    from models.m_class import ErpClass
    from modules.qy_wechat.qy_wechat_relate import QyWechatRelate
    from utils.enum.enum_order import StudentState
    from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse
    
    erp_class = settings.CF.get_crud(ErpClass)
    erp_account_teacher = settings.CF.get_crud(ErpAccountTeacher)
    erp_account = settings.CF.get_crud(ErpAccount)
    erp_wechat_externaluser = settings.CF.get_crud(ErpWechatExternaluser)
    erp_order_student = settings.CF.get_crud(ErpOrderStudent)

    # 验证班级
    class_obj = await erp_class.get_by_id(db, class_id)
    if not class_obj:
        return None
    
    # 如果班级已绑定企微群，则获取可添加的新成员
    if class_obj.qwechat_id:
        # 获取群聊当前成员
        qy_wechat_relate = QyWechatRelate()
        chat_detail = await qy_wechat_relate.get_groupchat_detail(class_obj.qwechat_id)
        if chat_detail and chat_detail.get('errcode') == 0:
            group_chat = chat_detail.get('group_chat', {})
            member_list = group_chat.get('member_list', [])
            
            # 提取当前群聊中的外部联系人
            current_external_members = {
                member.get('userid') for member in member_list 
                if member.get('type') == 2  # 外部联系人
            }
        else:
            current_external_members = set()
    
    # 获取主讲老师信息
    teacher_obj = await erp_account_teacher.get_by_id(db, class_obj.teacher_id)
    if not teacher_obj:
        return None
    
    teacher_account = await erp_account.get_by_id(db, teacher_obj.account_id)
    if not teacher_account or not teacher_account.qy_wechat_userid:
        return None
    
    # 准备老师用户列表
    teacher_userids = [teacher_account.qy_wechat_userid]
    
    # 获取助教老师信息（如果设置了助教）
    assistant_teacher_userid = None
    if class_obj.assistant_teacher_id and class_obj.assistant_teacher_id > 0:
        assistant_teacher_obj = await erp_account_teacher.get_by_id(db, class_obj.assistant_teacher_id)
        if assistant_teacher_obj:
            assistant_account = await erp_account.get_by_id(db, assistant_teacher_obj.account_id)
            if assistant_account and assistant_account.qy_wechat_userid:
                assistant_teacher_userid = assistant_account.qy_wechat_userid
                teacher_userids.append(assistant_teacher_userid)
    
    # 查询班级下的学生
    order_students = await erp_order_student.get_many(db, raw=[
        ErpOrderStudent.class_id == class_id,
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value]),  # 正常状态
        ErpOrderStudent.disable == 0
    ])
    if len(order_students) < 2:
        return None

    # 获取学生的企微外部联系人信息
    stu_ids = [os.stu_id for os in order_students]
    contact_list = await erp_wechat_externaluser.get_many(db, raw=[
        ErpWechatExternaluser.teacher_id == teacher_obj.id,
        ErpWechatExternaluser.stu_id.in_(stu_ids),
        ErpWechatExternaluser.disable == 0
    ])

    if len(contact_list) < 2:
        return None
    
    # 限制最多40个学生
    if len(contact_list) > 40:
        contact_list = contact_list[:40]
    
    external_user_ids = [contact.external_user_id for contact in contact_list]
    
    # 如果班级已绑定群聊，则只返回未加入群聊的成员
    if class_obj.qwechat_id:
        external_user_ids = [
            userid for userid in external_user_ids 
            if userid not in current_external_members
        ]
        
        if not external_user_ids:
            return None
    
    result = {
        "teacher_userid": teacher_account.qy_wechat_userid,
        "assistant_teacher_userid": assistant_teacher_userid,
        "teacher_userids": teacher_userids,  # 包含主讲老师和助教老师的列表
        "student_userids": external_user_ids,
        "student_count": len(external_user_ids),
        "teacher_count": len(teacher_userids)
    }

    return result




# 下载自定义考试模版
def download_custom_exam_template_replace(class_name, student_data):
    template_path = 'app_teach/template/upload_template_custom_exam.xlsx'
    wb = openpyxl.load_workbook(template_path)
    ws = wb.worksheets[0]
    row_start = 7  # 数据开始行
    for stu in student_data:
        ws[f"A{row_start}"].value = stu['class_id']
        ws[f"B{row_start}"].value = class_name
        ws[f"C{row_start}"].value = stu['stu_id']
        ws[f"D{row_start}"].value = stu['stu_name']
        row_start += 1
    filename = f"{class_name}_{str(datetime.now().date()).replace('-', '')}.xlsx"
    virtual_workbook = BytesIO()
    wb.save(virtual_workbook)
    return filename, virtual_workbook.getvalue()



async def travel_custom_exam_excel(exam_id, virtual_workbook, exam_type=1):
    """
    处理自定义考试Excel文件的函数
    Excel格式说明：
    - 第1行（索引0）：分组信息（仅当exam_type=2时使用）
    - 第2行（索引1）：题号
    - 第3行（索引2）：难度信息  
    - 第4行（索引3）：知识点
    - 第5行（索引4）：题目分数
    - 第6行（索引5）：平均分
    - 第7行开始（索引6+）：学生数据行，包含学生信息和各题得分
    - 支持动态题目数量，最多100题
    
    参数说明：
    - exam_type=1: 不分组模式，不读取第一行分组信息
    - exam_type=2: 分组模式，读取第一行分组信息
    """
    import pandas as pd
    

    df = pd.read_excel(virtual_workbook)
    data = df.to_dict(orient='records')
    if len(data) < 7:  # 至少需要7行：6行表头+1行学生数据
        return [], []
    
    # 识别题目列（通过题号行的内容）
    question_columns = []
    question_numbers = []
    
    # 寻找题号行 - 尝试找到包含数字题号的行
    title_row = None
    title_row_index = -1
    for i in range(min(6, len(data))):  # 检查前6行
        row = data[i]
        # 检查这一行是否包含数字题号
        has_numbers = False
        numeric_cols = 0
        
        for col in df.columns:
            # 跳过固定的学生信息列
            if col in ["学生号", "学生姓名", "总分", "排名", "百分比排名", "匹配班型", "教师寄语", "体系"]:
                continue
                
            col_value = row.get(col)
            if pd.notna(col_value):
                try:
                    question_num = int(float(col_value))
                    if 1 <= question_num <= 100:  # 题号范围1-100
                        numeric_cols += 1
                        has_numbers = True
                except (ValueError, TypeError):
                    continue
        
        # 如果这一行有足够多的数字题号，认为是题号行
        if has_numbers and numeric_cols >= 1:
            title_row = row
            title_row_index = i
            break
    
    if title_row is None:
        return [], []

    # 识别所有题目列
    for col in df.columns:
        # 跳过固定的学生信息列
        if col in ["学生号", "学生姓名", "总分", "排名", "百分比排名", "匹配班型", "教师寄语", "体系"]:
            continue
            
        col_value = title_row.get(col)
        if pd.notna(col_value):
            # 尝试将值转换为数字（题号）
            try:
                question_num = int(float(col_value))
                if 1 <= question_num <= 100:  # 支持1-100题
                    question_columns.append(col)
                    question_numbers.append(question_num)
            except (ValueError, TypeError):
                continue
    
    # 按题号排序
    if question_columns:
        sorted_pairs = sorted(zip(question_numbers, question_columns))
        question_numbers = [pair[0] for pair in sorted_pairs]
        question_columns = [pair[1] for pair in sorted_pairs]
    
    if not question_columns:
        return [], []
    
    # 处理表头 ####################################################
    headers_data = []
    avg_score_dict = {}
    t_score = 0
    
    # 处理每个题目的信息
    # 根据找到的题号行重新确定各行的位置
    # Excel结构：题号、难度、知识点、分值、平均分
    question_row_index = title_row_index  # 使用找到的题号行
    difficulty_row_index = title_row_index + 1  # 难度行
    knowledge_row_index = title_row_index + 2   # 知识点行
    score_row_index = title_row_index + 3       # 分值行
    avg_row_index = title_row_index + 4         # 平均分行
    
    # 确保所有需要的行都存在
    if avg_row_index >= len(data):
        return [], []
    
    avg_row = data[avg_row_index]
    
    for i, q_col in enumerate(question_columns):
        question_num = question_numbers[i]  # 从识别出的题号列表中获取
        
        # 根据exam_type决定是否读取分组信息
        if exam_type == 2:
            # 分组模式：从列名中获取分组信息
            if str(q_col).strip() != '' and not str(q_col).startswith('Unnamed'):
                # 从列名中获取分组名称
                group_name = str(q_col).strip()
                # 清理分组名称，去掉可能的重复后缀（如.1, .2等）
                if '.' in group_name:
                    # 如果列名包含点号，取点号前的部分作为分组名称
                    base_name = group_name.split('.')[0]
                    group_name = base_name
            else:
                group_name = f"分组{question_num}"
        else:
            # 不分组模式：不使用分组信息
            group_name = ""
        
        # 安全获取各行数据
        difficulty_value = data[difficulty_row_index].get(q_col, '') if difficulty_row_index < len(data) else ''
        knowledge_value = data[knowledge_row_index].get(q_col, '') if knowledge_row_index < len(data) else ''
        score_value = data[score_row_index].get(q_col, 0) if score_row_index < len(data) else 0
        avg_score_value = avg_row.get(q_col, 0)
        
        difficulty = str(difficulty_value) if pd.notna(difficulty_value) else ''
        knowledge = str(knowledge_value) if pd.notna(knowledge_value) else ''
        
        # 验证分值是否有效
        try:
            score = float(score_value) if pd.notna(score_value) else 0
            avg_score = float(avg_score_value) if pd.notna(avg_score_value) else 0
        except (ValueError, TypeError):
            continue  # 跳过无效的分值
        
        if score > 0:  # 只处理有效分数的题目
            row_dict = {
                "sort": question_num,  # 使用实际的题号
                "group_name": group_name,  # 分组字段（根据exam_type决定是否使用）
                "knowledge_point": knowledge,  # 知识点
                "score": round(score, 2),  # 分值
                "exam_id": exam_id,
                "difficulty": difficulty,  # 难度
            }
            avg_score_dict[q_col] = round(avg_score, 2)
            t_score += score
            headers_data.append(row_dict)
    
    # 处理学生数据 ####################################################
    exam_score_data = []
    field_map = {
        "学生号": "stu_id",
        "学生姓名": "stu_name",
        "总分": "t_score",
        "排名": "rank",
        "百分比排名": "rank_rate",
        "匹配班型": "match_class",
        "教师寄语": "teacher_comments",
        "体系": "match_system",
    }
    
    # 处理学生数据（从平均分行之后开始）
    student_start_index = avg_row_index + 1
    for index in range(student_start_index, len(data)):
        row = data[index]
        
        # 检查是否为有效的学生数据行
        stu_id_value = None
        for key in ["学生号", "stu_id", "学号", "编号"]:
            if key in row and pd.notna(row[key]):
                stu_id_value = row[key]
                break
        
        if not stu_id_value:
            continue
        
        # 检查是否有题目分数（至少有一道题有分数）
        has_question_scores = False
        for q_col in question_columns:
            col_value = row.get(q_col)
            if pd.notna(col_value) and col_value != "":
                try:
                    float(col_value)  # 验证是否为有效数字
                    has_question_scores = True
                    break
                except (ValueError, TypeError):
                    continue
        
        if not has_question_scores:
            continue
            
        item = {}
        content = []
        
        # 处理题目分数
        for i, q_col in enumerate(question_columns):
            value = row.get(q_col)
            if pd.notna(value) and value != "":
                try:
                    score_value = float(value)
                    content.append({
                        "sort": question_numbers[i],  # 使用实际的题号
                        "score": round(score_value, 2),
                        "avg_score": avg_score_dict.get(q_col, 0)
                    })
                except (ValueError, TypeError):
                    # 如果无法转换为数字，跳过该题目
                    continue
        
        # 处理其他字段（学生信息等）
        for key, value in row.items():
            if key in field_map and pd.notna(value):
                field_name = field_map[key]
                if field_name in ['t_score', 'rank_rate']:
                    item[field_name] = round(float(value), 2)
                else:
                    item[field_name] = value
        
        # 确保必要字段存在
        item['exam_score'] = round(float(t_score), 2)
        item['content'] = json.dumps(content, ensure_ascii=False)
        item['exam_id'] = exam_id
        
        # 处理学生ID
        try:
            item['stu_id'] = int(float(stu_id_value))
        except (ValueError, TypeError):
            continue
        
        if content:  # 只有有题目分数时才添加
            exam_score_data.append(item)

    return headers_data, exam_score_data

async def auto_push_teacher_card_after_enroll(order_student_id: int, stu_id: int, class_id: int):
    """
    学生入班后自动推送老师名片给家长
    
    Args:
        order_student_id: 学生订单ID 
        stu_id: 学生ID
        class_id: 班级ID
    """
    try:
        import settings
        import asyncio
        from models.m_class import ErpClass
        from models.m_teacher import ErpAccountTeacher
        from models.models import ErpAccount
        from models.m_student import ErpStudent, ErpStudentWechat
        from models.m_wechat import ErpWechatChannel
        from modules.qy_wechat.qy_wechat_relate import QyWechatRelate
        from utils.tencent.wechatTools import WeChatTemplateMessage
        from utils.db.db_handler import get_default_db
        from datetime import datetime
        
        # 创建新的数据库会话
        async for db in get_default_db():
            # 获取必要的CRUD实例
            erp_class = settings.CF.get_crud(ErpClass)
            erp_account_teacher = settings.CF.get_crud(ErpAccountTeacher)
            erp_account = settings.CF.get_crud(ErpAccount)
            erp_student = settings.CF.get_crud(ErpStudent)
            erp_student_wechat = settings.CF.get_crud(ErpStudentWechat)
            erp_wechat_channel = settings.CF.get_crud(ErpWechatChannel)
            
            # 获取班级信息
            class_obj = await erp_class.get_by_id(db, class_id)
        if not class_obj:
            settings.logger.warning(f"班级不存在，无法推送老师名片: class_id={class_id}")
            return False
            
        # 获取老师信息
        teacher_obj = await erp_account_teacher.get_by_id(db, class_obj.teacher_id)
        if not teacher_obj:
            settings.logger.warning(f"老师不存在，无法推送老师名片: teacher_id={class_obj.teacher_id}")
            return False
            
        # 获取老师账号信息
        teacher_account = await erp_account.get_by_id(db, teacher_obj.account_id)
        if not teacher_account:
            settings.logger.warning(f"老师账号不存在，无法推送老师名片: account_id={teacher_obj.account_id}")
            return False
            
        # 获取学生信息
        student_obj = await erp_student.get_by_id(db, stu_id)
        if not student_obj:
            settings.logger.warning(f"学生不存在，无法推送老师名片: stu_id={stu_id}")
            return False
            
        # 检查学生是否有微信绑定
        student_wechat_bindings = await erp_student_wechat.get_many(db, {"stu_id": stu_id})
        if not student_wechat_bindings:
            settings.logger.info(f"学生{student_obj.stu_name}未绑定微信，无法推送老师名片")
            return False
            
        # 检查老师是否绑定企微
        if not teacher_account.qy_wechat_userid:
            settings.logger.warning(f"老师{teacher_account.employee_name}未绑定企微，无法推送老师名片")
            return False
            
        # 创建专门的企微渠道码用于老师名片推送
        qy_wechat = QyWechatRelate()
        
        # 构造state参数，格式：cid=渠道ID|tid=老师ID|sid=学生ID
        state = f"cid=0|tid={teacher_obj.id}|sid={stu_id}"
        
        # 创建联系我方式（企微渠道码）
        contact_way_result = await qy_wechat.add_contact_way(
            type=1,  # 单人
            scene=2,  # 通过二维码联系
            user=[teacher_account.qy_wechat_userid],
            remark=f"{student_obj.stu_name}家长添加{teacher_account.employee_name}老师",
            skip_verify=True,  # 无需验证
            state=state
        )
        
        if not contact_way_result or contact_way_result.get('errcode') != 0:
            settings.logger.error(f"创建企微联系我方式失败: {contact_way_result}")
            return False
            
        config_id = contact_way_result.get('config_id')
        qr_code = contact_way_result.get('qr_code', '')
        
        # 保存渠道码配置到数据库
        channel_data = {
            'wx_config_id': config_id,
            'wx_qrcode': qr_code,
            'name': f"{student_obj.stu_name}家长添加{teacher_account.employee_name}老师",
            'teacher_id': teacher_obj.id,
            'stu_id': stu_id,
            'class_id': class_id,
            'remark': f"学生入班自动创建的老师名片推送渠道码",
            'create_time': datetime.now(),
            'update_time': datetime.now()
        }
        
        await erp_wechat_channel.create(db, commit=False, **channel_data)
        
        # 通过微信公众号模板消息推送老师名片
        template_id = settings.GZH_CONFIG.get('TEACHER_CARD_TEMPLATE_ID', 'PXMDStoX6UF0ghkMP2Nkqb0DpealAReh4FK8RuaEKhs')
        
        # 构建推送消息内容，遵循旧系统SendWeChatGZHAddTeacher的结构
        first_data = f"{student_obj.stu_name}您好，{class_obj.class_name}的讲师'{teacher_account.employee_name}'等待您的添加好友"
        message_data = {
            "first": {
                "value": first_data,
                "color": "#173177"
            },
            "keyword1": {
                "value": "添加老师提醒",
                "color": "#173177"
            },
            "keyword2": {
                "value": datetime.now().strftime('%Y-%m-%d %H:%M'),
                "color": "#173177"
            },
            "remark": {
                "value": "点击可查看详情、长按识别二维码添加老师好友",
                "color": "#173177"
            }
        }
        
        # 小程序跳转配置
        miniprogram = {
            "appid": settings.MINI_PROGRAM_CONFIG['app_id'],
            "pagepath": f"pages/QrCode/TeacherAndClassQrCode?teacherId={teacher_obj.id}&stuId={stu_id}&classId={class_id}"
        }
        
        # 推送给每个绑定的微信
        wechat_tool = WeChatTemplateMessage(
            app_id=settings.GZH_CONFIG['app_id'],
            app_secret=settings.GZH_CONFIG['app_secret']
        )
        
        success_count = 0
        total_count = len(student_wechat_bindings)
        
        for wechat_binding in student_wechat_bindings:
            try:
                openid = wechat_binding.wechat_open_id
                if openid:
                    result = await wechat_tool.send_template_message(
                        openid=openid,
                        template_id=template_id,
                        data=message_data,
                        miniprogram=miniprogram
                    )
                    
                    if result:
                        success_count += 1
                        settings.logger.info(f"老师名片推送成功: 学生{student_obj.stu_name}, 微信{openid}")
                    else:
                        settings.logger.warning(f"老师名片推送失败: 学生{student_obj.stu_name}, 微信{openid}")
                        
            except Exception as push_error:
                settings.logger.error(f"推送老师名片异常: 学生{student_obj.stu_name}, 错误: {str(push_error)}")
        settings.logger.info(f"老师名片推送完成: 学生{student_obj.stu_name}, 成功{success_count}/{total_count}")
        # 提交数据库事务
        await db.commit()
        
        
        return True
        
    except Exception as e:
        settings.logger.error(f"动推送老师名片失败, 错误: {str(e)}")
        return False
