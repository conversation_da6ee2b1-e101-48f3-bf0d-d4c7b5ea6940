from typing import List
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app_teach.crud import get_student_by_osi
from models.m_order import ErpOrderStudent
import settings
from app_teach.serializer import CancelAllCheckingParams, StudentCheckingParams, StudentCheckingUpdateParams
from models.m_class import ErpClass, ErpClassChecking, ErpClassRescheduling, ErpClassTransfor, ErpCourse, ErpCourseTerm, ErpCourseCategory, ErpCourseLog, ErpCourseOutline, \
    ErpClassPlan
from models.m_student import ErpStudent
from settings import CF
from utils.db.account_handler import UserDict, role_required
from utils.db.db_handler import get_default_db
from utils.enum.enum_order import StudentState
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

router = APIRouter(prefix="/checking", tags=["签到"])

erp_class = CF.get_crud(ErpClass)
erp_class_plan = CF.get_crud(ErpClassPlan)
erp_course = CF.get_crud(ErpCourse)
erp_order_student = CF.get_crud(ErpOrderStudent)
erp_class_checking = CF.get_crud(ErpClassChecking)


# 学生批量签到
@router.post("/batch_student_checking")
async def add_batch_student_checking(
        params: List[StudentCheckingParams],
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
        # conf: dict = Depends(get_config),
):
    """
    # 学生批量签到
    - class_id: int
    - class_plan_id: int
    - check_status: int   1 出勤 2 缺勤 3 请假 4 迟到 5 线上
    - order_student_id: int
    - teacher_id: int
    - class_room_id: int
    - stu_type: int  1 正常 2 转班
    - is_online: int  1 线上
    - time_duration: float
    """
    # 签到人
    checking_teacher_id = user.uid
    
    order_student_objs = await erp_order_student.get_many(db, raw=[
        ErpOrderStudent.id.in_([p.order_student_id for p in params])
    ])
    order_student_map = {osi.id: osi for osi in order_student_objs}

    osi_ids = [p.order_student_id for p in params]
    # # 查询学生课程的签到记录
    class_checking_objs = await erp_class_checking.get_many(db, raw=[
        ErpClassChecking.class_plan_id == params[0].class_plan_id,
        ErpClassChecking.order_student_id.in_(osi_ids),
        ErpClassChecking.disable == 0,
    ])
    # 签到失败的order_student_id
    failed_order_student_ids = []
    # 签到成功的order_student_id
    success_order_student_ids = []

    erp_class_plan = CF.get_crud(ErpClassPlan)
    class_plan_obj = await erp_class_plan.get_by_id(db, params[0].class_plan_id)
    if not class_plan_obj:
        return await ApiFailedResponse(message="传入的班级计划不存在")
    

    for param in params:
        # 首先必须先不存在该学生课程的签到记录
        if any(checking.class_plan_id == param.class_plan_id and checking.order_student_id == param.order_student_id for checking in class_checking_objs):
            settings.logger.warning(f"学生{param.order_student_id}课程{param.class_plan_id}已存在签到记录, 跳过")
            failed_order_student_ids.append(param.order_student_id)
            continue

        # 创建签到记录
        await erp_class_checking.create(db, commit=False, **{
            'class_id': param.class_id,
            'class_plan_id': param.class_plan_id,
            'check_status': param.check_status,
            'order_student_id': param.order_student_id,
            'teacher_id': checking_teacher_id,
            'class_room_id': param.class_room_id,
            'stu_type': param.stu_type,
            'is_online': param.is_online,
            'time_duration': class_plan_obj.time_duration,
            'create_by': checking_teacher_id,
            'update_by': checking_teacher_id,
        })
        if param.check_status != 2:   # 不是缺勤则更新课消
            order_student_map[param.order_student_id].complete_hours += 1
        success_order_student_ids.append(param.order_student_id)
    await db.commit()
    return await ApiSuccessResponse(message="批量签到成功", data={
        "success_order_student_ids": success_order_student_ids,
        "failed_order_student_ids": failed_order_student_ids,
    })


# 取消全部签到并返还课消
@router.post("/cancel_all_checking")
async def cancel_all_checking(
        params: CancelAllCheckingParams,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    class_plan_id = params.class_plan_id
    order_student_ids = params.order_student_ids
    # 获取排课信息
    class_plan = await erp_class_plan.get_by_id(db, class_plan_id)
    if not class_plan:
        return await ApiFailedResponse(message="传入的班级计划不存在")
    # 获取所有签到记录
    class_checking_objs = await erp_class_checking.get_many(db, raw=[
        ErpClassChecking.class_plan_id == class_plan_id,
        ErpClassChecking.order_student_id.in_(order_student_ids),
    ])
    # 获取order_student_id对应的order_student_obj
    order_student_objs = await erp_order_student.get_many(db, raw=[
        ErpOrderStudent.id.in_(order_student_ids),
    ])
    order_student_map = {osi.id: osi for osi in order_student_objs}
    # 取消签到
    for checking in class_checking_objs:
        checking.disable = 1
        if checking.check_status != 2:   # 不是缺勤则更新课消
            order_student_map[checking.order_student_id].complete_hours -= 1
        
    await db.commit()
    return await ApiSuccessResponse(message="取消全部签到并返还课消成功")



# 创建单个学生签到
@router.post("/create_student_checking")
async def add_student_checking(
        param: StudentCheckingParams,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 单个学生签到
    - class_id: int
    - class_plan_id: int
    - check_status: int   1 出勤 2 缺勤 3 请假 4 迟到 5 线上
    - order_student_id: int
    - teacher_id: int
    - class_room_id: int
    - stu_type: int  1 正常 2 转班
    - is_online: int  1 线上
    - time_duration: float
    """
    # 签到人
    checking_teacher_id = user.uid
    # 获取排课信息
    class_plan = await erp_class_plan.get_by_id(db, param.class_plan_id)
    if not class_plan:
        return await ApiFailedResponse(message="传入的班级计划不存在")
    # 获取学生信息
    order_student_obj = await erp_order_student.get_by_id(db, param.order_student_id)
    if not order_student_obj:
        return await ApiFailedResponse(message="传入的学生班级信息不存在")
    # 查询学生课程的签到记录
    class_checking_objs = await erp_class_checking.get_many(db, raw=[
        ErpClassChecking.class_plan_id == param.class_plan_id,
        ErpClassChecking.order_student_id == param.order_student_id,
    ])
    if class_checking_objs:
        return await ApiFailedResponse(message="该学生课程的签到记录已存在")
    # 创建签到记录
    checking_record = await erp_class_checking.create(db, commit=False, **{
        'class_id': param.class_id,
        'class_plan_id': param.class_plan_id,
        'check_status': param.check_status,
        'order_student_id': param.order_student_id,
        'teacher_id': checking_teacher_id,
        'class_room_id': param.class_room_id,
        'stu_type': param.stu_type,
        'is_online': param.is_online,
        'time_duration': class_plan.time_duration,
        'create_by': checking_teacher_id,
        'update_by': checking_teacher_id,
    })
    if param.check_status != 2:   # 不是缺勤则更新课消
        order_student_obj.complete_hours += 1
    
    # 异步更新签到价格
    try:
        from tasks.update_checking_price import update_checking_price_for_order_student
        await update_checking_price_for_order_student(db, param.order_student_id)
    except Exception as e:
        settings.logger.error(f"更新签到价格失败: {str(e)}")
        # 价格更新失败不影响签到记录的创建
    
    await db.commit()
    return await ApiSuccessResponse(True)


# 更新单个学生签到
@router.post("/update_student_checking")
async def update_student_checking(
        params: StudentCheckingUpdateParams,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 更新单个学生签到
    - class_plan_id: int
    - order_student_id: int
    - check_status: int     1 出勤 2 缺勤 3 请假 4 迟到 5 线上
    """
    # 获取排课信息
    class_plan = await erp_class_plan.get_by_id(db, params.class_plan_id)
    if not class_plan:
        return await ApiFailedResponse(message="传入的班级计划不存在")
    # 获取学生信息
    # if class_plan.order_student_id != params.order_student_id:
    #     return await ApiFailedResponse(message="传入的学生order_student_id信息与班级计划不匹配")
    order_student_obj = await erp_order_student.get_by_id(db, params.order_student_id)
    if not order_student_obj:
        return await ApiFailedResponse(message="传入的学生班级信息不存在")
    # 获取学生签到记录
    class_checking_obj = await erp_class_checking.get_one(db, class_plan_id=params.class_plan_id, order_student_id=params.order_student_id)
    if not class_checking_obj:
        return await ApiFailedResponse(message="传入的学生课程的签到记录不存在")
    print(order_student_obj.id)
    if class_checking_obj.check_status != 2 and params.check_status == 2:
        print('由计算课消的状态（!=2）更新至不计课消的状态（=2），则需要complete_hours - 1')
        order_student_obj.complete_hours -= 1
    elif class_checking_obj.check_status == 2 and params.check_status != 2:
        print('由不计课消的状态(=2)更新至计算课消的状态（!=2），则需要complete_hours + 1')
        order_student_obj.complete_hours += 1
    class_checking_obj.check_status = params.check_status
    print('class_checking_obj: ', class_checking_obj.id, class_checking_obj.check_status, params.check_status)
    # 由计算课消的状态（!=2）更新至不计课消的状态（=2），则需要complete_hours - 1, 不计课消的状态(=2)更新至计算课消的状态（!=2），则需要complete_hours + 1
    
    await db.commit()
    return await ApiSuccessResponse(message="更新成功")

# 获取签到名单
@router.get("/student_checking_list")
async def get_student_checking_list(
        class_id: int,
        class_plan_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    获取签到名单
    - stu_type: 1 正常单 2 转入单 3 临时插班
    """
    erp_class_temporary = CF.get_crud(ErpClassRescheduling)  # 插班记录
    # 1. 获取排课信息
    class_plan = await erp_class_plan.get_by_id(db, class_plan_id)
    if class_plan.class_id != class_id:
        return await ApiFailedResponse(message="传入的班级计划与班级不匹配")
    # 2. 获取班级正常学生名单, 需包含正常单和转入单
    class_students = await erp_order_student.get_many(db, raw=[
        ErpOrderStudent.class_id == class_id,
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value]),
    ])
    in_class_students_map = {}  # 1 正常单 2 转入单 3 临时插班
    all_order_student_ids = []
    for osi in class_students:
        if osi.student_state == StudentState.NORMAL.value:
            in_class_students_map[osi.id] = 1
        elif osi.student_state == StudentState.TRANSFER_IN.value:
            in_class_students_map[osi.id] = 2
        all_order_student_ids.append(osi.id)
    # 3. 添加 插入本班的学生
    class_temporary = await erp_class_temporary.get_many(db, raw=[
        ErpClassRescheduling.new_plan_id == class_plan_id,
    ])
    for ct in class_temporary:
        in_class_students_map[ct.order_student_id] = 3
        all_order_student_ids.append(ct.order_student_id)
    # 4. 移除 插入其他班的学生
    class_temporary = await erp_class_temporary.get_many(db, raw=[
        ErpClassRescheduling.old_plan_id == class_plan_id,
    ])
    for ct in class_temporary:
        if ct.order_student_id in all_order_student_ids:
            all_order_student_ids.remove(ct.order_student_id)
    # 5. 应签到学生名单：根据all_order_student_ids获取学生信息，结合in_class_students_map并返回
    student_checking_list = await get_student_by_osi(db, all_order_student_ids)
    new_student_checking_list = []
    for osi in student_checking_list:
        item = dict(osi)
        item['stu_type'] = in_class_students_map[osi.order_student_id]
        item['class_plan_id'] = class_plan_id
        new_student_checking_list.append(item)
    return await ApiSuccessResponse(data=new_student_checking_list)


# 获取学生签到记录
@router.get("/get_student_checking_record")
async def get_student_checking_record(
        class_plan_id: int,
        db: AsyncSession = Depends(get_default_db),
):
    """
    获取排课的所有学生签到列表
    - class_plan_id: 班级计划ID
    """
    # 获取排课信息
    class_plan = await erp_class_plan.get_by_id(db, class_plan_id)
    if not class_plan:
        return await ApiFailedResponse(message="传入的班级计划不存在")
    
    # 获取该排课的所有学生签到记录
    checking_records = await erp_class_checking.get_many(db, raw=[
        ErpClassChecking.class_plan_id == class_plan_id,
        ErpClassChecking.disable == 0
    ])
    
    if not checking_records:
        return await ApiSuccessResponse(data=[])
    
    # 获取所有学生订单ID
    order_student_ids = [record.order_student_id for record in checking_records]
    
    # 获取学生订单信息
    order_student_objs = await erp_order_student.get_many(db, raw=[
        ErpOrderStudent.id.in_(order_student_ids)
    ])
    
    # 获取学生ID
    student_ids = [stu.stu_id for stu in order_student_objs if stu.stu_id]
    
    # 获取ErpStudent表中的学生信息
    erp_student = CF.get_crud(ErpStudent)
    student_objs = await erp_student.get_many(db, raw=[
        ErpStudent.id.in_(student_ids)
    ])
    
    # 构建学生信息映射
    student_map = {stu.id: stu for stu in student_objs}
    order_student_map = {osi.id: osi for osi in order_student_objs}
    
    # 构建返回结果
    result = []
    for record in checking_records:
        order_student = order_student_map.get(record.order_student_id)
        if order_student:
            student = student_map.get(order_student.stu_id) if order_student.stu_id else None
            result.append({
                "id": record.id,
                "class_id": record.class_id,
                "class_plan_id": record.class_plan_id,
                "check_status": record.check_status,
                "order_student_id": record.order_student_id,
                "stu_id": order_student.stu_id if order_student else None,
                "stu_name": student.stu_name if student else None,
                "stu_username": student.stu_username if student else None,
                "stu_gender": student.stu_gender  if student else None,
                "teacher_id": record.teacher_id,
                "class_room_id": record.class_room_id,
                "stu_type": record.stu_type,
                "is_online": record.is_online,
                "time_duration": record.time_duration,
                "create_time": record.create_time,
                "update_time": record.update_time
            })
    
    return await ApiSuccessResponse(data=result)