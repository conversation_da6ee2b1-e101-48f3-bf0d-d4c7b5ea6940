from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Depends

from app_teach.crud import get_questions
from app_teach.serializer import CreateQuestion, UpdateQuestion
from models.m_online_exam import ErpOnlineQuestion, ErpOnlinePaper, ErpOnlinePaperQuestion, ErpOnlineQuestionOption, \
    ErpOnlineStuPaper, ErpOnlineStuScore
from utils.db.account_handler import UserDict, role_required
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db
from utils.other.config_handler import get_config
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

router = APIRouter(prefix="/question", tags=["【诊断】题目"])

erp_online_question = CRUD(ErpOnlineQuestion)
erp_online_paper = CRUD(ErpOnlinePaper)
erp_online_paper_question = CRUD(ErpOnlinePaperQuestion)
erp_online_question_option = CRUD(ErpOnlineQuestionOption)
erp_online_stu_paper = CRUD(ErpOnlineStuPaper)
erp_online_stu_score = CRUD(ErpOnlineStuScore)

allow_role = []


@router.get("/question/{question_id}")
async def query_question_detail(question_id: int,
                                db: AsyncSession = Depends(get_default_db),
                                conf: dict = Depends(get_config),
                                user: UserDict = Depends(role_required(allow_role)),
                                ):
    """
    # 获取试题详情
    """
    question_difficulty_level = conf.get('question_difficulty_level')
    detail = await erp_online_question.get_one(db, id=question_id)
    options = await erp_online_question_option.get_many(db, {"question_id": question_id})
    detail.options = options
    detail.difficulty_name = question_difficulty_level[str(detail.difficulty_level)]

    return await ApiSuccessResponse(detail, )


@router.post("/question")
async def creat_question(question: CreateQuestion,
                         db: AsyncSession = Depends(get_default_db),
                         user: UserDict = Depends(role_required(allow_role)),
                         ):
    """
    # 保存题目
    """

    question_obj = await erp_online_question.create(db, commit=False, **{
        "content": question.content,
        "score": question.score,
        "knowledge_point": question.knowledge_point,
        "difficulty_level": question.difficulty_level,
        "creator": user.uid,
    })

    for option in question.options:
        await erp_online_question_option.create(db, commit=False, **{
            "question_id": question_obj.id,
            "option_content": option.option_content,
            "correct": option.correct,
        })
    # 提交事务
    await db.commit()
    return await ApiSuccessResponse(True, )


@router.put("/question")
async def update_question(
        question_id: int,
        update_info: UpdateQuestion,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 更新题目
    """
    item = {}
    if update_info.content:
        item.update({"content": update_info.content})
    if update_info.difficulty_level > 0:
        item.update({"difficulty_level": update_info.difficulty_level})
    if update_info.score > 0:
        item.update({"score": update_info.score})
    if update_info.knowledge_point:
        item.update({"knowledge_point": update_info.knowledge_point})
    item.update({"update_time": datetime.now()})
    await erp_online_question.update_one(db, question_id, item, commit=False)

    if update_info.options:
        option_objs = await erp_online_question_option.get_many(db, {"question_id": question_id})
        new_option_ids = set([i.id for i in update_info.options])
        old_option_ids = set([i.id for i in option_objs])
        # 移除项
        remove_objs = set(old_option_ids) - set(new_option_ids)
        # 增加项
        add_objs = set(new_option_ids) - set(old_option_ids)
        # 更新项
        update_objs = set(new_option_ids) & set(old_option_ids)
        if remove_objs:
            for r_id in remove_objs:
                await erp_online_question_option.delete_one(db, r_id, commit=False)
        if add_objs:
            for option in update_info.options:
                if option.id not in add_objs:
                    continue
                await erp_online_question_option.create(db, commit=False, **{
                    "question_id": question_id,
                    "option_content": option.option_content,
                    "correct": option.correct,
                })
        if update_objs:
            for option in update_info.options:
                if option.id not in update_objs:
                    continue
                await erp_online_question_option.update_one(db, option.id, {
                    "option_content": option.option_content,
                    "correct": option.correct,
                }, commit=False)
    await db.commit()
    return await ApiSuccessResponse(True, )


@router.delete("/question")
async def delete_question(
        question_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 删除题目
    """
    await erp_online_question.delete_one(db, question_id)
    return await ApiSuccessResponse(True, )
