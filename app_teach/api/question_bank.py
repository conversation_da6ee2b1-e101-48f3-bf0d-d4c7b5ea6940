
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Depends

from app_teach.serializer import CreateQbPermission, UpdateQbPermission
from models.m_qb import ErpQbAccountPermission, ErpQbPermission, ErpGuest
from models.models import ErpAccount
from utils.db.account_handler import UserDict, role_required
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db
from utils.other.PassEncrypt import encrypt
from utils.other.config_handler import get_config
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse
import pandas as pd

router = APIRouter(prefix="/qb", tags=["题库"])

ERP_QB_ACCOUNT_PERMISSION = CRUD(ErpQbAccountPermission)
ERP_QB_PERMISSION = CRUD(ErpQbPermission)
erp_account = CRUD(ErpAccount)
ERP_GUEST = CRUD(ErpGuest)


@router.get("/qb_permission")
async def create_qb_permission(
        conf: dict = Depends(get_config),
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询题库权限
    """
    data = await ERP_QB_PERMISSION.get_many(db)
    return await ApiSuccessResponse(data)


@router.post("/qb_account_permission")
async def create_qb_account_permission(
        permission: CreateQbPermission,
        conf: dict = Depends(get_config),
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 创建账户的题库权限
     - account_id: int 内部账号id
     - account_type: int 1: 内部账号， 必传account_id； 2: 外部账号， 必传用户名，电话，用户密码
     - subject_ids: List[int]  学科id
     - grade_ids: List[int] 年级id
     - permission_ids: List[int] 权限id
    """
    account_id = permission.account_id
    if permission.account_type == 2:  # 外部账号
        if permission.erp_guest:
            guest_item = {
                "guest_name": permission.erp_guest.guest_name,
                "username": permission.erp_guest.username,
                "password": encrypt(permission.erp_guest.password),

            }
            guest_obj = await ERP_GUEST.create(db, commit=False, **guest_item)
            account_id = guest_obj.id
        else:
            return await ApiFailedResponse("外部账号信息不能为空")
    else:  # 内部账号
        account = await erp_account.get_one(db, id=account_id)
        if not account:
            return await ApiFailedResponse("账号不存在")
    create_item = {"account_id": account_id, "account_type": permission.account_type,
                   "subject_ids": permission.subject_ids, "grade_ids": permission.grade_ids,
                   "permission_ids": permission.permission_ids, 'update_by': user.uid, 'create_by': user.uid}
    await ERP_QB_ACCOUNT_PERMISSION.create(db, commit=False, **create_item)
    await db.commit()
    return await ApiSuccessResponse(True)


# 分页查询erp_guest账户
@router.get("/erp_guest")
async def query_erp_guest(
        page: int,
        page_size: int,
        conf: dict = Depends(get_config),
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 分页查询erp_guest账户
    """
    data = await ERP_GUEST.get_many_with_pagination(db, page=page, page_size=page_size)
    count_data = await ERP_GUEST.get_many(db)
    return await ApiSuccessResponse({
        "data": data,
        "total": len(count_data)
    })


@router.put("/qb_account_permission/{account_permission_id}")
async def update_qb_account_permission(
        account_permission_id: int,
        permission: UpdateQbPermission,
        conf: dict = Depends(get_config),
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 更新账户的题库权限
     - account_type: int 1: 内部账号 2: 外部账号
     - subject_ids: List[int]  学科id
     - grade_ids: List[int] 年级id
     - permission_ids: List[int] 权限id
    """
    await ERP_QB_ACCOUNT_PERMISSION.update_one(db, account_permission_id, permission.dict())
    return await ApiSuccessResponse(True)


@router.get("/qb_account_permission")
async def query_qb_account_permission(
        page: int,
        page_size: int,
        conf: dict = Depends(get_config),
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 分页查询账户题库权限
    """
    accounts = await erp_account.get_many(db)
    account_map = {i.id: {"account_name": i.employee_name} for i in accounts}
    guests = await ERP_GUEST.get_many(db)
    guest_map = {i.id: {"account_name": i.guest_name} for i in guests}
    data = await ERP_QB_ACCOUNT_PERMISSION.get_many_with_pagination(db, page=page, page_size=page_size)
    for i in data:
        if i.account_type == 1:
            i.account = account_map.get(i.account_id)
        else:
            i.account = guest_map.get(i.account_id)
    count_data = await ERP_QB_ACCOUNT_PERMISSION.get_many(db)
    return await ApiSuccessResponse({
        "data": data,
        "total": len(count_data)
    })


# 删除账户题库权限
@router.delete("/qb_account_permission/{account_permission_id}")
async def delete_qb_account_permission(
        account_permission_id: int,
        conf: dict = Depends(get_config),
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除账户题库权限
    """
    await ERP_QB_ACCOUNT_PERMISSION.delete_one(db, account_permission_id)
    return await ApiSuccessResponse(True)
