import copy
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends
from pydantic import BaseModel
from sqlalchemy import or_
from sqlalchemy.ext.asyncio import AsyncSession

from app_teach.crud import query_class_with_page, get_teacher_module
from app_teach.modules import generate_serial_number, get_course_config, add_course_log
from app_teach.serializer import CourseBase, CourseTermBase, CourseCategoryBase, ClassBase, ClassModify, OutlineBase, \
    CourseTextbook, TeacherBase
from models.m_class import ErpClass, ErpCourse, ErpCourseTerm, ErpCourseCategory, ErpCourseLog, ErpCourseOutline, \
    ErpCourseTextbook
from models.m_student import ErpStudent
from models.m_teacher import ErpAccountTeacher
from models.models import ErpAccount, ErpDepartment, ErpAccountDepartment
from modules.qy_wechat.base import QWechatBase
from settings import CF
from utils.db.account_handler import UserDict, get_current_active_user, role_required
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db
from utils.enum.enum_class import CourseLogType
from utils.other.config_handler import get_config
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

router = APIRouter(prefix="/materials", tags=["教学资源"])

erp_class = CRUD(ErpClass)
erp_course = CRUD(ErpCourse)
erp_account = CF.get_crud(ErpAccount)
erp_account_teacher = CF.get_crud(ErpAccountTeacher)
erp_department = CF.get_crud(ErpDepartment)
erp_account_department = CF.get_crud(ErpAccountDepartment)
erp_course_textbook = CRUD(ErpCourseTextbook)


# 创建textbook的增删改查
@router.post("/textbook")
async def create_textbook(
        params: CourseTextbook,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增随材
    """
    create_item = {
        "name": params.name,
        "unit": params.unit,
        "origin_price": params.origin_price,
        "sale_price": params.sale_price
    }
    await erp_course_textbook.create(db, commit=True, **create_item)
    return await ApiSuccessResponse(True)


# 删除textbook
@router.delete("/textbook/{textbook_id}")
async def delete_textbook(
        textbook_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除随材
    """
    await erp_course_textbook.delete_one(db, textbook_id)
    return await ApiSuccessResponse(True)


# 修改textbook
@router.put("/textbook/{textbook_id}")
async def update_textbook(
        textbook_id: int,
        params: CourseTextbook,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改随材
    """
    update_item = {
        **{k: v for k, v in params.dict().items() if v is not None}
    }
    await erp_course_textbook.update_one(db, textbook_id, update_item)
    return await ApiSuccessResponse(True)


# 查询textbook
@router.get("/textbook")
async def get_textbook(
        page: int = 1,
        page_size: int = 10,
        keyword:str=None,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询随材
    """
    raw = []
    if keyword:
        raw.append(
            ErpCourseTextbook.name.ilike(f"%{keyword}%")
        )
    data = await erp_course_textbook.get_many_with_pagination(db, page, page_size, raw=raw)
    count_data = await erp_course_textbook.get_many(db, raw=raw)
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 查询textbook详情
@router.get("/textbook/{textbook_id}")
async def get_textbook_detail(
        textbook_id: int,
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询随材详情
    """
    data = await erp_course_textbook.get_one(db, id=textbook_id)
    return await ApiSuccessResponse(data)


# 查询教师
@router.get("/teacher")
async def get_teacher(
        page: int = 1,
        page_size: int = 10,
        employee_status: int = None,
        teacher_name: str = None,
        teacher_subject: str = None,
        teacher_grade: str = None,
        teacher_tag: str = None,
        is_show: int = None,
        phone: str = None,
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询教师
    - 如果teacher_id为空， 说明需要去完善教师信息
    """
    data = await get_teacher_module(db, page, page_size, employee_status, teacher_name, teacher_subject, teacher_grade, teacher_tag, is_show, phone)
    count = await get_teacher_module(db, employee_status, teacher_name, teacher_subject, teacher_grade, teacher_tag, is_show, phone, count=True)
    return await ApiSuccessResponse(
        {
            "data": data,
            "count": count
        }
    )


# 查询未同步教师
@router.get("/teacher/sync")
async def get_teacher_sync(
        keyword: Optional[str] = None,
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 同步教师查询接口
    - 如果teacher_id为空， 说明需要去完善教师信息
    """
    data = await get_teacher_module(db, sync=True, keyword=keyword)
    count_data = await get_teacher_module(db, sync=True, keyword=keyword)
    return await ApiSuccessResponse(
        {
            "data": data,
            "count": len(count_data)
        }
    )


# 修改教师
@router.put("/teacher/{teacher_id}")
async def update_teacher(
        teacher_id: int,
        params: TeacherBase,
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 修改/新增教师
    """
    exist_user = await get_teacher_module(db, teacher_id=teacher_id)
    if not exist_user:
        return await ApiFailedResponse('该用户不存在')
    user_id = copy.deepcopy(exist_user.qy_wechat_userid)
    if not user_id:
        return await ApiFailedResponse('未同步企业微信')
    qy_update_item = {"userid": user_id}

    account_item = {
        "username": params.username,
        "employee_name": params.employee_name,
        "qy_wechat_position": params.qy_wechat_position,
        "update_by": user.uid,
        "update_time": datetime.now()
    }

    teacher_item = {
        "teacher_grade": params.teacher_grade,
        "teacher_subject": params.teacher_subject,
        "teacher_avatar": params.teacher_avatar,
        "teacher_image": params.teacher_image,
        "teacher_qr_img": params.teacher_qr_img,
        "teacher_desc": params.teacher_desc,
        "teacher_tag": params.teacher_tag,
        "is_show": params.is_show,
        "update_by": user.uid,
        "update_time": datetime.now()
    }

    if exist_user.employee_name and exist_user.employee_name != params.employee_name:
        qy_update_item.update({"name": params.employee_name})
    if exist_user.qy_wechat_position and exist_user.qy_wechat_position != params.qy_wechat_position:
        qy_update_item.update({"position": params.qy_wechat_position})

    # 更新部门
    if params.dept_ids:
        # 先更新企微dict
        dept_objs = await erp_department.get_many(db, raw=[
            ErpDepartment.id.in_(params.dept_ids)
        ])
        qy_dept_ids = [i.qy_wechat_dept_id for i in dept_objs]
        qy_update_item.update({"department": qy_dept_ids})

        # 更新ERP dict
        old_dept = await erp_account_department.get_many(db, {"account_id": exist_user.id})
        old_dept_ids = [i.dept_id for i in old_dept]
        new_dept_ids = params.dept_ids
        # print(f"new:{new_dept_ids}, old:{old_dept_ids}")
        remove_list = set(old_dept_ids) - set(new_dept_ids)
        add_list = set(new_dept_ids) - set(old_dept_ids)
        for i in add_list:
            await erp_account_department.create(db, commit=False, **{
                "account_id": exist_user.id,
                "dept_id": i,
            })
        for j in remove_list:
            await erp_account_department.delete_many(db, {
                "account_id": exist_user.id,
                "dept_id": j,
            }, commit=False)

    # 更新员工表
    if account_item:
        await erp_account.update_one(db, obj_id=exist_user.id, new_values=account_item, commit=False)
    # 更新教师表
    if teacher_item:
        await erp_account_teacher.update_one(db, obj_id=teacher_id, new_values=teacher_item, commit=False)

    qw_base = QWechatBase()
    address_access_token = await qw_base.get_address_access_token()
    qy_update_resp = await qw_base.update_user(address_access_token, qy_update_item)
    print('qy_update_resp:', qy_update_resp)
    await db.commit()
    return await ApiSuccessResponse(True)


# 查询教师详情
@router.get("/teacher/{teacher_id}")
async def get_teacher_detail(
        teacher_id: int,
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询教师详情
    """
    data = await get_teacher_module(db, teacher_id=teacher_id)
    return await ApiSuccessResponse(data)
