import asyncio
import copy
from datetime import datetime
from typing import List
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app_order.modules import get_offer_count
from models.m_office import ErpOfficeClassroom
from models.m_order import <PERSON>rp<PERSON><PERSON>r<PERSON>ffer, ErpOrderStudent
from models.m_workflow import ErpWorkflowCostType
from models.models import ErpAccount
import settings
from app_teach.crud import class_renew_rule_module, get_class_change_record, get_student_by_class_id, get_transfer_record, query_class_plan, query_class_record, query_class_with_page, get_classplan_by_classid,  query_teacher_class_times, query_open_class_progress, query_class_with_page_optimized, get_child_teacher_id_list, query_class_statistics_summary
from app_teach.modules import add_class_log, class_full_data_detail_module, class_full_data_module, class_renew_report_module, class_transfer_rule_paid_module, generate_serial_number, get_course_config, add_course_log, check_time_conflicts, \
    create_class_workflow, calculate_classroom_utilization, calculate_classroom_utilization_week, single_class_verify_module
from app_teach.serializer import ClassRenewRuleBase, ClassTransferRulePaidBase, CourseBase, CourseTermBase, CourseCategoryBase, ClassBase, ClassModify, ExcelClassCreate, ModifyClassCompleteBase, OutlineBase, \
    ClassPlanBase, ClassPlanBatchUpdate, OutlineList
from models.m_class import ErpClass, ErpClassChangeRulesPaid, ErpClassChecking, ErpClassLog, ErpClassRenewRules, ErpCourse, ErpCourseTerm, ErpCourseCategory, ErpCourseLog, ErpCourseOutline, \
    ErpClassPlan, ErpClassChangeRules, ErpClassRescheduleRules
from models.m_teacher import ErpAccountTeacher
from models.old_models.old_class import RbClassPlan, RbStudentTempinvitation
# from public_api.api.approval import erp_account
from settings import CF, FILE_SERVER
# from tasks.auto_renew import create_renew_order_for_class
from tasks.sync_class import sync_class_data_to_classin
from utils.db.account_handler import UserDict, role_required
from utils.db.db_handler import get_default_db, get_uat_db
from utils.enum.enum_approval import ClassAuditStatus, CostTypeBind
from utils.enum.enum_class import CourseLogType, ClassStatus, RuleType
from utils.enum.enum_order import StudentState
from utils.file.minio_handler import S3Client
from utils.net.http_handler import HttpHandler
from utils.other.config_handler import get_config
from collections import defaultdict
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse
from settings import CLASS_ROOM_UTILIZATION_RATE_PARAMS
from modules.classin.classinApiHandler import ClassInSDK
from enum import Enum
import time

classin_sdk = ClassInSDK()

router = APIRouter(prefix="/class", tags=["班级和课程"])

erp_class = CF.get_crud(ErpClass)
erp_class_plan = CF.get_crud(ErpClassPlan)
erp_course = CF.get_crud(ErpCourse)
erp_course_term = CF.get_crud(ErpCourseTerm)
erp_course_category = CF.get_crud(ErpCourseCategory)
erp_course_outline = CF.get_crud(ErpCourseOutline)
erp_course_log = CF.get_crud(ErpCourseLog)
erp_account = CF.get_crud(ErpAccount)
erp_workflow_cost_type = CF.get_crud(ErpWorkflowCostType)
erp_office_classroom = CF.get_crud(ErpOfficeClassroom)
erp_order_student = CF.get_crud(ErpOrderStudent)
erp_account_teacher = CF.get_crud(ErpAccountTeacher)
erp_class_change_rules_paid = CF.get_crud(ErpClassChangeRulesPaid)
erp_class_change_rules = CF.get_crud(ErpClassChangeRules)
erp_class_reschedule_rules = CF.get_crud(ErpClassRescheduleRules)


# 获取课程配置信息
@router.get("/course_config")
async def get_course_config_info(
        db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
):
    """
    # 获取课程配置信息
    """
    # 调取课程配置信息
    course_config = await get_course_config(db, conf)
    return await ApiSuccessResponse(course_config)


# 创建针对erp_course_term的增删改查
@router.post("/course_term")
async def create_course_term(
        term_params: CourseTermBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增学期
    """
    create_item = {
        "term_name": term_params.term_name,
        # "term_type": term_params.term_type,
        "year": term_params.year,
    }

    await erp_course_term.create(db, commit=True, **create_item)
    return await ApiSuccessResponse(True)


# 删除学期
@router.delete("/course_term/{term_id}")
async def delete_course_term(
        term_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除学期
    """
    await erp_course_term.delete_one(db, term_id)
    return await ApiSuccessResponse(True)


# 修改学期
@router.put("/course_term/{term_id}")
async def update_course_term(
        term_id: int,
        term_params: CourseTermBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改学期
    """
    update_item = {
        **{k: v for k, v in term_params.dict().items() if v not in (None, 0)}
    }
    await erp_course_term.update_one(db, term_id, update_item)
    return await ApiSuccessResponse(True)


# 查询学期
@router.get("/course_term")
async def get_course_term(
        page: int = 1,
        page_size: int = 10,
        keyword: str = None,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询学期
    """
    raw = []
    if keyword:
        raw = [ErpCourseTerm.term_name.like(f'%{keyword}%')]
    data = await erp_course_term.get_many_with_pagination(db, page, page_size, raw=raw)
    count_data = await erp_course_term.get_many(db, raw=raw)
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 针对erp_course_category的增删改查
@router.post("/course_category")
async def create_course_category(
        category_params: CourseCategoryBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增课程分类
    """
    create_item = {
        "category_name": category_params.category_name,
        "category_desc": category_params.category_desc,
        "category_objective": category_params.category_objective,
        "stu_type_desc": category_params.stu_type_desc,
        "grade_id": category_params.grade_id,
        "create_by": user.uid,
        "is_enable": category_params.is_enable
    }
    category_obj = await erp_course_category.create(db, commit=False, **create_item)

    # 变动记录
    log_content = f"用户：{user.uid} - 创建了课程类型:{category_params.category_name}"
    log_type = CourseLogType.ChangeCategoryLog.value
    log_name = "修改课程类型"
    objective_id = category_obj.id
    await add_course_log(db, objective_id, log_name, log_type, log_content)
    await db.commit()
    return await ApiSuccessResponse(True)


# 删除课程分类
@router.delete("/course_category/{category_id}")
async def delete_course_category(
        category_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除课程分类
    """
    await erp_course_category.delete_one(db, category_id)
    # 变动记录
    log_content = f"用户：{user.uid} - 删除了课程类型:{category_id}"
    log_type = CourseLogType.ChangeCategoryLog.value
    log_name = "修改课程类型"
    objective_id = category_id
    await add_course_log(db, objective_id, log_name, log_type, log_content)

    return await ApiSuccessResponse(True)


# 修改课程分类
@router.put("/course_category/{category_id}")
async def update_course_category(
        category_id: int,
        category_params: CourseCategoryBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改课程分类
    """
    update_item = {
        **{k: v for k, v in category_params.dict().items() if v is not None}
    }
    log_content = f"用户：{user.uid} - 修改了课程类型:{update_item}"
    log_type = CourseLogType.ChangeCategoryLog.value
    log_name = "修改课程类型"
    objective_id = category_id
    await add_course_log(db, objective_id, log_name, log_type, log_content)
    await erp_course_category.update_one(db, category_id, update_item)
    return await ApiSuccessResponse(True)


# 查询课程分类
@router.get("/course_category")
async def get_course_category(
        page: int = 1,
        page_size: int = 10,
        keyword: str = None,
        is_enable: int = None,
        grade_id: int = None,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询课程分类
    - enable: 1 启用 0 禁用
    """
    raw = []
    if keyword:
        raw = [ErpCourseCategory.category_name.like(f'%{keyword}%')]
    if is_enable is not None:
        raw.append(ErpCourseCategory.is_enable == is_enable)
    if grade_id:
        raw.append(ErpCourseCategory.grade_id == grade_id)
    account_objs = await erp_account.get_many(db)   
    account_maps = {i.id: i for i in account_objs}
    data = await erp_course_category.get_many_with_pagination(db, page, page_size, raw=raw)
    count_data = await erp_course_category.get_many(db, raw=raw)
    for i in data:
        account = account_maps.get(i.create_by)
        i.create_by_name = account.employee_name if account else "未匹配用户"
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 根据category_id查询课程分类变动记录
@router.get("/course_category_log/{category_id}")
async def get_course_category_log(
        category_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询课程分类变动记录
    """

    data = await erp_course_log.get_one(db, objective_id=category_id)
    return await ApiSuccessResponse(data)


# 针对课程大纲的增删改查
@router.post("/course_outline")
async def create_course_outline(
        outline_list: OutlineList,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增课程大纲
    """
    # 存在大纲则返回报错
    outline_obj = await erp_course_outline.get_one(db, course_id=outline_list.outlines[0].course_id)
    if outline_obj:
        return await ApiFailedResponse({"success": False, "message": "课程大纲已存在"})
    
    for outline_params in outline_list.outlines:
        create_item = {
            "course_id": outline_params.course_id,
            "outline_name": outline_params.outline_name,
            "outline_desc": outline_params.outline_desc,
            "create_by": user.uid
        }
        obj = await erp_course_outline.create(db, commit=False, **create_item)
    await db.commit()
    return await ApiSuccessResponse(obj)

# 修改课程大纲 
@router.put("/course_outline/{outline_id}")
async def update_course_outline(
        outline_id: int,
        outline_params: OutlineBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改课程大纲
    """
    outline_obj = await erp_course_outline.get_by_id(db, outline_id)
    if not outline_obj:
        return await ApiFailedResponse({"success": False, "message": "课程大纲不存在"})
    outline_obj.outline_name = outline_params.outline_name
    outline_obj.outline_desc = outline_params.outline_desc
    await db.commit()
    return await ApiSuccessResponse(True)


# 查询课程大纲
@router.get("/course_outline/{course_id}")
async def get_course_outline(
    course_id: int,
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询课程大纲
    """
    outline_objs = await erp_course_outline.get_many(db, raw=[
        ErpCourseOutline.course_id == course_id
    ])
    return await ApiSuccessResponse(outline_objs)

# 创建课程的增删改查
@router.post("/course")
async def create_course(
        course: CourseBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增课程
    """
    create_item = {
        "course_name": course.course_name,
        "year": course.year,
        "term_id": course.term_id,
        "subject_id": course.subject_id,
        "grade_id": course.grade_id,
        "p_grade_id": course.p_grade_id,
        "type_id": course.type_id,
        "category_id": course.category_id,
        "course_cover": course.course_cover,
        "course_introduction_page": course.course_introduction_page,
        "allow_refund": course.allow_refund,
        "allow_repeated_purchase": course.allow_repeated_purchase,
        "cost_calculation_plan": course.cost_calculation_plan,
        "course_coefficient": course.course_coefficient,
        "pricing_plan": course.pricing_plan,
        "original_price": course.original_price,
        "sale_price": course.sale_price,
        "number_of_lessons": course.number_of_lessons,
        "bound_textbook_id": course.bound_textbook_id,
        # "outline_id": course.outline_id,
        "is_term_plan": course.is_term_plan,
        "is_enable": course.is_enable,
        "create_by": user.uid,
        "update_by": user.uid,
    }

    course_obj = await erp_course.create(db, commit=False, **create_item)

    for i in course.outlines:
        outline_item = {
            "outline_name": i.outline_name,
            "outline_desc": i.outline_desc,
            "course_id": course_obj.id,
            "create_by": user.uid
        }
        await erp_course_outline.create(db, commit=False, **outline_item)
    await db.commit()
    return await ApiSuccessResponse(True)


# 删除课程
@router.delete("/course/{course_id}")
async def delete_course(
        course_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除课程
    """
    await erp_course.delete_one(db, course_id)
    return await ApiSuccessResponse(True)


# 修改课程
@router.put("/course/{course_id}")
async def update_course(
        course_id: int,
        course: CourseBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改课程
    """
    update_item = {
        "update_by": user.uid,
        "update_time": datetime.now(),
        **{k: v for k, v in course.dict().items() if v is not None}
    }
    await erp_course.update_one(db, course_id, update_item)
    return await ApiSuccessResponse(True)


# 查询课程
@router.get("/course")
async def get_course(
        page: int = 1,
        page_size: int = 10,
        course_name: str = None,
        subject_id: int = None,
        category_id: int = None,
        grade_id: int = None,
        p_grade_id: int = None,
        type_id: int = None,
        term_id: int = None,
        is_enable: int = None,
        db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询课程
    """
    raw = []
    if course_name:
        raw.append(ErpCourse.course_name.like(f'%{course_name}%'))
    if subject_id:
        raw.append(ErpCourse.subject_id == subject_id)
    if category_id:
        raw.append(ErpCourse.category_id == category_id)
    if grade_id:
        raw.append(ErpCourse.grade_id == grade_id)
    if p_grade_id:
        raw.append(ErpCourse.p_grade_id == p_grade_id)
    if type_id:
        raw.append(ErpCourse.type_id == type_id)
    if term_id:
        raw.append(ErpCourse.term_id == term_id)
    if is_enable is not None:
        raw.append(ErpCourse.is_enable == is_enable)
    data = await erp_course.get_many_with_pagination(db, page, page_size, raw=raw)
    count_data = await erp_course.get_many(db, raw=raw)
    # 员工信息映射表
    account_objs = await erp_account.get_many(db)
    account_maps = {i.id: i for i in account_objs}

    # 调取课程配置信息
    course_config = await get_course_config(db, conf)
    for i in data:
        i.category_name = course_config.get('category_map').get(str(i.category_id))
        i.term_name = course_config.get('term_map').get(str(i.term_id))
        i.grade_name = course_config.get('grade_map').get(str(i.grade_id))
        i.subject_name = course_config.get('subject_map').get(str(i.subject_id))
        i.course_type_name = course_config.get('course_type_map').get(str(i.type_id))
        i.create_by_name = (account_maps.get(i.create_by) or account_maps.get(4001)).employee_name
        i.update_by_name = (account_maps.get(i.update_by) or account_maps.get(4001)).employee_name

    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 查询课程详情
@router.get("/course/{course_id}")
async def get_course_detail(
        course_id: int,
        db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
):
    """
    # 查询课程详情
    """
    data = await erp_course.get_one(db, id=course_id)
    # 调取课程配置信息
    course_config = await get_course_config(db, conf)
    # 组装中文数据
    data.category_name = course_config.get('category_map').get(str(data.category_id))
    data.term_name = course_config.get('term_map').get(str(data.term_id))
    data.grade_name = course_config.get('grade_map').get(str(data.grade_id))
    data.subject_name = course_config.get('subject_map').get(str(data.subject_id))
    data.course_type_name = course_config.get('course_type_map').get(str(data.type_id))
    course_outline = await erp_course_outline.get_many(db, {"course_id": course_id})
    data.outlines = course_outline
    return await ApiSuccessResponse(data)


# 创建班级的增删改查
@router.post("/class")
async def create_class(
        class_params: ClassBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增班级
    - scheduling_json [{"start_time":"2024-10-09 14:04:15","end_time":"2024-10-09 16:04:15"},...
    """
    # 检查教室和教师时间冲突
    conflict_info = await check_time_conflicts(
        db,
        class_params.classroom_id,
        class_params.teacher_id,
        class_params.scheduling_json
    )
    
    # 如果存在冲突，返回冲突信息
    if conflict_info:
        return await ApiFailedResponse({"message": "时间冲突，无法创建班级", "conflicts": conflict_info})
    
    # 无冲突，继续创建班级
    create_item = {
        "course_id": class_params.course_id,
        "class_name": class_params.class_name,
        "class_capacity": class_params.class_capacity,
        "pre_enrollment": class_params.pre_enrollment,
        "teacher_id": class_params.teacher_id,
        "start_date": class_params.start_date,
        "classroom_id": class_params.classroom_id,
        "use_standard_full_rate": class_params.use_standard_full_rate,
        "planning_class_times": class_params.planning_class_times,
        "scheduling_method": class_params.scheduling_method,
        "is_shelf_miniprogram": class_params.is_shelf_miniprogram,
        "miniprogram_start_enrollment_time": class_params.miniprogram_start_enrollment_time,
        "miniprogram_end_enrollment_time": class_params.miniprogram_end_enrollment_time,
        "classin_sync": 0,   # 默认不同步classin， 该班还未审核通过
        "enrollment_conditions": class_params.enrollment_conditions,
        "hourly_tuition_ratio": class_params.hourly_tuition_ratio,
        "create_by": user.uid,
        "update_by": user.uid,
        "class_status": ClassStatus.NotActive.value
    }
    class_obj = await erp_class.create(db, commit=False, **create_item)

    if class_params.scheduling_json and len(class_params.scheduling_json) > 0:
        for i in class_params.scheduling_json:
            start_datetime = datetime.strptime(i.get('start_time'), '%Y-%m-%d %H:%M:%S')
            end_datetime = datetime.strptime(i.get('end_time'), '%Y-%m-%d %H:%M:%S')
            time_duration = (end_datetime - start_datetime).total_seconds() / 3600
            create_plan_item = {
                "class_id": class_obj.id,
                "room_id": class_params.classroom_id,
                "teacher_id": class_params.teacher_id,
                "start_time": i.get('start_time'),
                "end_time": i.get('end_time'),
                "create_by": user.uid,
                "update_by": user.uid,
                "time_duration": round(time_duration, 2),
            }
            await erp_class_plan.create(db, commit=False, **create_plan_item)
    class_obj = copy.deepcopy(class_obj)
    await db.commit()

    # 发起开班审核流程
    asyncio.create_task(create_class_workflow(class_obj, user.uid))

    return await ApiSuccessResponse(True)



# 批量开班前的 excel上传班级单个验证 class_verify
@router.post("/class_verify")
async def single_class_verify(
        class_params: ExcelClassCreate,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 批量开班前的 excel上传班级单个验证
    - class_name: str 班级名称 不检测
    - course_name: str 课程名称 检测
    - teacher_name: str 教师名称 检测
    - room_name: str 教室名称 检测
    - start_date: str 开班日期
    - use_standard_full_rate: int   # 0不使用 >0则为实际值
    - class_capacity:int 班级容量
    - planning_class_times: int 计划课时
    - scheduling_method: int 排课方式
    - scheduling_json: list[Dict] 具体排课
    - miniprogram_start_enrollment_time: str 小程序开班报名开始时间
    - miniprogram_end_enrollment_time: str 小程序开班报名结束时间
    - is_shelf_miniprogram: int 是否上架小程序

    """
    flag, msg = await single_class_verify_module(db, class_params)
    if not flag:
        return await ApiFailedResponse(msg)
    return await ApiSuccessResponse(True)

    

# 批量开班
@router.post("/class_batch")
async def class_batch_create(
    class_params: List[ExcelClassCreate],
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 批量开班
    """
    # 错误列表
    error_list = []
    # 信息收集预制
    course_names = []
    teacher_names = []
    room_names = []
    for i in class_params:
        flag, msg = await single_class_verify_module(db, i)
        if not flag:
            error_list.append({
                "class_name": i.class_name,
                "course_name": i.course_name,
                "teacher_name": i.teacher_name,
                "room_name": i.room_name,
                "msg": msg
            })
        course_names.append(i.course_name.strip())
        teacher_names.append(i.teacher_name.strip())
        room_names.append(i.room_name.strip())
    if error_list:
        return await ApiFailedResponse(error_list)
    
    # 这里开始正常创建班级
    # 1.先构建预制信息
    # 课程idmap
    course_objs = await erp_course.get_many(db, raw=[
        ErpCourse.course_name.in_(course_names)
    ])
    course_id_map = {i.course_name.strip(): i.id for i in course_objs}
    # 教师idmap
    teacher_objs = await erp_account.get_many(db, raw=[
        ErpAccount.employee_name.in_(teacher_names)
    ])
    # 获取教师账号ID到姓名的映射
    account_id_to_name = {i.id: i.employee_name for i in teacher_objs}
    # 通过账号ID查询对应的教师ID
    teacher_account_ids = list(account_id_to_name.keys())
    teacher_erp_objs = await erp_account_teacher.get_many(db, raw=[
        ErpAccountTeacher.account_id.in_(teacher_account_ids)
    ])
    # 建立从教师姓名到教师ID的映射
    teacher_id_map = {}
    for teacher_obj in teacher_erp_objs:
        teacher_name = account_id_to_name.get(teacher_obj.account_id)
        if teacher_name:
            teacher_id_map[teacher_name] = teacher_obj.id
    # 教室idmap
    room_objs = await erp_office_classroom.get_many(db, raw=[
        ErpOfficeClassroom.room_name.in_(room_names)
    ])
    room_id_map = {i.room_name: i.id for i in room_objs}
    class_objs = []   # 收集，用于发起开班审核流程
    # 2.开始创建班级
    for i in class_params:
        # 无冲突，继续创建班级
        create_item = {
            "course_id": course_id_map[i.course_name.strip()],
            "class_name": i.class_name,
            "class_capacity": i.class_capacity,
            "pre_enrollment": i.class_capacity,
            "teacher_id": teacher_id_map[i.teacher_name.strip()],
            "start_date": i.start_date,
            "classroom_id": room_id_map[i.room_name.strip()],
            "use_standard_full_rate": i.use_standard_full_rate,
            "planning_class_times": i.planning_class_times,
            "scheduling_method": i.scheduling_method,
            "is_shelf_miniprogram": i.is_shelf_miniprogram,
            "miniprogram_start_enrollment_time": i.miniprogram_start_enrollment_time,
            "miniprogram_end_enrollment_time": i.miniprogram_end_enrollment_time,
            "classin_sync": 0,   # 默认不同步classin， 该班还未审核通过
            "enrollment_conditions": 0,
            "hourly_tuition_ratio": 0,
            "create_by": user.uid,
            "update_by": user.uid,
            "class_status": ClassStatus.NotActive.value
        }
        class_obj = await erp_class.create(db, commit=False, **create_item)

        if i.scheduling_json and len(i.scheduling_json) > 0:
            for j in i.scheduling_json:
                start_datetime = datetime.strptime(j.get('start_time'), '%Y-%m-%d %H:%M:%S')
                end_datetime = datetime.strptime(j.get('end_time'), '%Y-%m-%d %H:%M:%S')
                time_duration = (end_datetime - start_datetime).total_seconds() / 3600
                create_plan_item = {
                    "class_id": class_obj.id,
                    "room_id": room_id_map[i.room_name.strip()],
                    "teacher_id": teacher_id_map[i.teacher_name.strip()],
                    "start_time": j.get('start_time'),
                    "end_time": j.get('end_time'),
                    "create_by": user.uid,
                    "update_by": user.uid,
                    "time_duration": round(time_duration, 2),
                }
                await erp_class_plan.create(db, commit=False, **create_plan_item)
        class_objs.append(class_obj)
    class_objs = copy.deepcopy(class_objs)
    await db.commit()
    for i in class_objs:
        # 发起开班审核流程
        asyncio.create_task(create_class_workflow(i, user.uid))
    return await ApiSuccessResponse(True)
        


# 删除班级
@router.delete("/class/{class_id}")
async def delete_class(
        class_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除班级
    """
    # 1. 验证是否存在课节和学生
    class_obj = await erp_class.get_by_id(db, class_id)
    if not class_obj:
        return await ApiFailedResponse("班级不存在")
    # 查询课节
    class_plan_obj = await erp_class_plan.get_many(db, {"class_id": class_id})
    if class_plan_obj:
        return await ApiFailedResponse("班级存在课节，不能删除")
    # 查询学生
    order_student_obj = await erp_order_student.get_many(db, raw=[
        ErpOrderStudent.class_id == class_id,
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value])
    ])
    if order_student_obj:
        return await ApiFailedResponse("班级存在在班学生，不能删除")
    # 2.开始删除
    
    # 2.1 是否存在classin_id
    if class_obj.classin_id is not None and int(class_obj.classin_id) > 0:
        # 删除classin班级
        resp = await classin_sdk.classroom.end_course(class_obj.classin_id)
        settings.logger.info(f"删除classin班级 -- class_id:{class_id}, classin_id:{class_obj.classin_id}, 结果: {resp}")
    # 2.2 删除班级
    class_obj.disable = 1
    await db.commit()
    
    return await ApiSuccessResponse(True)

# 批量结课
@router.post("/class_end")
async def class_end(
    class_ids: List[int],
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    批量结课

    - empty_class_ids: 不存在的班级id
    - non_end_class_ids: 存在未上课节的班级id
    - success_class_ids: 成功结课的班级id
    """
    empty_class_ids = []
    non_end_class_ids = []
    success_class_ids = []
    
    for class_id in class_ids:
        try:
            # 1. 验证是否存在未上课节
            class_obj = await erp_class.get_by_id(db, class_id)
            if not class_obj:
                empty_class_ids.append(class_id)
                continue
            # 查询课节
            class_plan_obj = await erp_class_plan.get_many(db, raw=[
                ErpClassPlan.start_time >= datetime.now(),
                ErpClassPlan.class_id == class_id
            ])
            if class_plan_obj:
                non_end_class_ids.append(class_id)
                continue
            
            # 2. 验证是否存在classin_id
            if class_obj.classin_id is not None and class_obj.classin_id > 0:
                # 删除classin班级
                resp = await classin_sdk.classroom.end_course(class_obj.classin_id)
                settings.logger.info(f"删除classin班级 -- class_id:{class_id}, classin_id:{class_obj.classin_id}, 结果: {resp}")
            # 3. 结课
            class_obj.class_status = ClassStatus.Closed.value
            success_class_ids.append(class_id)
        except Exception as e:
            settings.logger.error(f"结课失败 -- class_id:{class_id}, 错误: {e}")
            continue

    await db.commit()
    
    return await ApiSuccessResponse({
        "empty_class_ids": empty_class_ids,
        "non_end_class_ids": non_end_class_ids,
        "success_class_ids": success_class_ids
    })


# 修改班级
@router.put("/class/{class_id}")
async def update_class(
        class_id: int,
        class_params: ClassModify,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改班级
    """
    # 获取当前班级信息
    class_obj = await erp_class.get_by_id(db, class_id)
    if not class_obj:
        return await ApiFailedResponse("班级不存在")
    
    # 检查助教老师是否发生变化
    old_assistant_teacher_id = class_obj.assistant_teacher_id
    new_assistant_teacher_id = class_params.assistant_teacher_id
    
    # 检查主讲老师是否发生变化
    old_teacher_id = class_obj.teacher_id
    new_teacher_id = class_params.teacher_id
    
    # 检查班级名称是否发生变化
    old_class_name = class_obj.class_name
    new_class_name = class_params.class_name
    
    update_item = {
        **{k: v for k, v in class_params.dict().items() if v is not None}
    }
    await erp_class.update_one(db, class_id, update_item)
    
    # 如果班级已同步到ClassIn，同步相关变更
    if (class_obj.classin_id and int(class_obj.classin_id) > 0 and 
        class_obj.classin_sync == 1):
        
        try:
            from modules.classin.classinApiHandler import ClassInSDK
            classin_sdk = ClassInSDK()
            
            # 同步班级名称变更
            if new_class_name and new_class_name != old_class_name:
                course_response = await classin_sdk.classroom.edit_course(
                    course_id=str(class_obj.classin_id),
                    course_name=new_class_name
                )
                if course_response.get('code') == 1:
                    settings.logger.info(f"同步班级名称到ClassIn成功: 班级ID={class_id}, 新名称={new_class_name}")
                else:
                    settings.logger.error(f"同步班级名称到ClassIn失败: {course_response.get('msg', '未知错误')}")
            
            # 同步主讲老师变更
            if new_teacher_id and new_teacher_id != old_teacher_id:
                new_teacher = await erp_account_teacher.get_by_id(db, new_teacher_id)
                if new_teacher:
                    teacher_account = await erp_account.get_by_id(db, new_teacher.account_id)
                    if teacher_account:
                        # 确保新老师在ClassIn中注册
                        teacher_uid = new_teacher.classin_uid
                        if not teacher_uid:
                            register_response = await classin_sdk.user.register_user(
                                telephone=teacher_account.username,
                                nickname=teacher_account.employee_name,
                                add_to_school_member=2
                            )
                            teacher_uid = register_response.get('data')
                            if teacher_uid:
                                new_teacher.classin_uid = teacher_uid
                                new_teacher.classin_sync = 1
                                new_teacher.update_time = datetime.now()
                                await db.commit()
                        
                        # 更换课程主讲老师
                        if teacher_uid:
                            teacher_response = await classin_sdk.classroom.change_course_teacher(
                                course_id=str(class_obj.classin_id),
                                teacher_id=str(teacher_uid),
                                teacher_name=teacher_account.employee_name
                            )
                            if teacher_response.get('code') == 1:
                                settings.logger.info(f"同步主讲老师到ClassIn成功: 班级ID={class_id}, 新老师ID={new_teacher_id}")
                            else:
                                settings.logger.error(f"同步主讲老师到ClassIn失败: {teacher_response.get('msg', '未知错误')}")
                        else:
                            settings.logger.error(f"主讲老师ClassIn注册失败: 班级ID={class_id}, 老师ID={new_teacher_id}")
                    else:
                        settings.logger.error(f"找不到主讲老师账户信息: 班级ID={class_id}, 老师ID={new_teacher_id}")
                else:
                    settings.logger.error(f"找不到主讲老师信息: 班级ID={class_id}, 老师ID={new_teacher_id}")
            
            # 同步助教老师变更
            if old_assistant_teacher_id != new_assistant_teacher_id:
                # 如果原来有助教，先移除
                if old_assistant_teacher_id and old_assistant_teacher_id > 0:
                    old_assistant_teacher = await erp_account_teacher.get_by_id(db, old_assistant_teacher_id)
                    if old_assistant_teacher and old_assistant_teacher.classin_uid:
                        await classin_sdk.classroom.remove_course_teacher(
                            course_id=str(class_obj.classin_id),
                            teacher_id=str(old_assistant_teacher.classin_uid)
                        )
                        settings.logger.info(f"从ClassIn移除助教老师: 班级ID={class_id}, 助教ID={old_assistant_teacher_id}")
                
                # 如果设置了新助教，添加到ClassIn
                if new_assistant_teacher_id and new_assistant_teacher_id > 0:
                    new_assistant_teacher = await erp_account_teacher.get_by_id(db, new_assistant_teacher_id)
                    if not new_assistant_teacher:
                        return await ApiSuccessResponse(True, "班级更新成功，但助教老师不存在")
                    
                    assistant_account = await erp_account.get_by_id(db, new_assistant_teacher.account_id)
                    if not assistant_account:
                        return await ApiSuccessResponse(True, "班级更新成功，但助教老师账户不存在")
                    
                    # 确保助教老师在ClassIn中注册
                    assistant_uid = new_assistant_teacher.classin_uid
                    if not assistant_uid:
                        register_response = await classin_sdk.user.register_user(
                            telephone=assistant_account.username,
                            nickname=assistant_account.employee_name,
                            add_to_school_member=2
                        )
                        assistant_uid = register_response.get('data')
                        if assistant_uid:
                            new_assistant_teacher.classin_uid = assistant_uid
                            new_assistant_teacher.classin_sync = 1
                            new_assistant_teacher.update_time = datetime.now()
                            await db.commit()
                    
                    # 添加助教到ClassIn课程
                    if assistant_uid:
                        await classin_sdk.classroom.add_course_teacher(
                            course_id=str(class_obj.classin_id),
                            teacher_id=str(assistant_uid),
                            teacher_name=assistant_account.employee_name
                        )
                        settings.logger.info(f"向ClassIn添加助教老师: 班级ID={class_id}, 助教ID={new_assistant_teacher_id}")
                        
        except Exception as e:
            settings.logger.error(f"同步班级信息到ClassIn失败: {str(e)}")
            return await ApiSuccessResponse(True, f"班级更新成功，但同步到ClassIn失败: {str(e)}")
    
    return await ApiSuccessResponse(True)


# 查询班级
@router.get("/class")
async def get_class(
        page: int = 1,
        page_size: int = 10,
        class_name: str = None,
        teacher_id: str = None,
        term_ids: str = None,
        grade_ids: str = None,
        subject_ids: str = None,
        category_ids: str = None,
        course_type_ids:str=None,
        class_status:int=None,
        course_name:str=None,
        class_start_time:str=None,
        class_end_time:str=None,
        audit_status:int=None,
        is_shelf_miniprogram:int=None,
        is_my_class:int=None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
        conf: dict = Depends(get_config),
):
    """
    # 查询班级
    - ids: eg. 1,2,3
    """
    condition = {}
    if class_name:
        condition["class_names"] = class_name.split(' ')    # 这里是空格分隔的str
    if teacher_id is not None:
        condition["teacher_id"] = teacher_id
    if term_ids:
        condition["term_ids"] = term_ids.split(',')
    if grade_ids:
        condition["grade_ids"] = grade_ids.split(',')
    if subject_ids:
        condition["subject_ids"] = subject_ids.split(',')
    if category_ids:
        condition["category_ids"] = category_ids.split(',')
    if course_type_ids:
        condition["course_type_ids"] = course_type_ids.split(',')
    if class_status is not None:
        condition["class_status"] = class_status
    if course_name:
        condition["course_names"] = course_name.split(' ')
    if class_start_time:
        condition["class_start_time"] = class_start_time
    if class_end_time:
        condition["class_end_time"] = class_end_time
    if audit_status in (ClassAuditStatus.PASS.value, ClassAuditStatus.WAITING.value, ClassAuditStatus.REJECT.value):
        condition["audit_status"] = audit_status
    if is_shelf_miniprogram is not None:
        condition["is_shelf_miniprogram"] = is_shelf_miniprogram
    if is_my_class is not None and is_my_class > 0:
        teacher_obj = await erp_account_teacher.get_one(db, account_id=user.uid)
        if not teacher_obj:
            return await ApiFailedResponse(message="未找到教师账号")
        condition["teacher_id"] = teacher_obj.id
    
    # 使用优化后的查询函数
    data = await query_class_with_page_optimized(db, page, page_size, condition=condition)
    count = await query_class_with_page_optimized(db, page=None, page_size=None, condition=condition, count=True)
    
    # 获取统计汇总信息
    statistics_summary = await query_class_statistics_summary(db, condition=condition)
    
    # 预先获取配置映射，避免在循环中重复获取
    subject_map = conf.get('subject_map', {})
    course_type_map = conf.get('course_type_map', {})
    grade_map = conf.get('grade_map', {})
    
    # 优化数据处理逻辑
    new_data = []
    for i in data:
        # 直接使用字典，避免重复转换
        if isinstance(i, dict):
            item = i
        else:
            item = dict(i)
        
        # 批量添加映射信息
        item['subject_name'] = subject_map.get(str(item.get('subject_id', '')), '')
        item['course_type_name'] = course_type_map.get(str(item.get('type_id', '')), '')
        item['grade_name'] = grade_map.get(str(item.get('grade_id', '')), '')
        
        # 处理审核流程信息 - 如果是审核未通过状态，保留workflow_info；否则设为None以减少数据传输
        if item.get('audit_status') in [ClassAuditStatus.WAITING.value, ClassAuditStatus.REJECT.value]:
            # 保留workflow_info，审核中或已驳回时需要显示审核信息
            pass
        else:
            # 审核通过时，可以简化workflow_info或设为None
            item['workflow_info'] = None
            
        new_data.append(item)
    
    return await ApiSuccessResponse({
        "data": new_data,
        "count": count,
        "total_paid_stu_count": statistics_summary["total_paid_stu_count"],
        "total_unpaid_stu_count": statistics_summary["total_unpaid_stu_count"], 
        "total_pre_enrollment": statistics_summary["total_pre_enrollment"]
    })


# 查询我的班级
@router.get("/my_class")
async def get_my_class(
        page: int = 1,
        page_size: int = 10,
        class_name: str = None,
        term_ids: str = None,
        grade_ids: str = None,
        subject_ids: str = None,
        category_ids: str = None,
        course_type_ids:str=None,
        class_status:int=None,
        course_name:str=None,
        class_start_time:str=None,
        class_end_time:str=None,
        audit_status:int=None,
        is_shelf_miniprogram:int=None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 查询班级 【弃用】统一使用/class
    - ids: eg. 1,2,3
    """
    # 查询当前登录用户对应的teacher_id
    teacher_obj = await erp_account_teacher.get_one(db, account_id=user.uid)
    if not teacher_obj:
        return await ApiSuccessResponse(False, "当前用户不是教师")
    

    condition = {
        "teacher_id": teacher_obj.id
    }
    if class_name:
        condition["class_names"] = class_name.split(' ')    # 这里是空格分隔的str
    if term_ids:
        condition["term_ids"] = term_ids.split(',')
    if grade_ids:
        condition["grade_ids"] = grade_ids.split(',')
    if subject_ids:
        condition["subject_ids"] = subject_ids.split(',')
    if category_ids:
        condition["category_ids"] = category_ids.split(',')
    if course_type_ids:
        condition["course_type_ids"] = course_type_ids.split(',')
    if class_status is not None:
        condition["class_status"] = class_status
    if course_name:
        condition["course_names"] = course_name.split(' ')
    if class_start_time:
        condition["class_start_time"] = class_start_time
    if class_end_time:
        condition["class_end_time"] = class_end_time
    
    if audit_status in (ClassAuditStatus.PASS.value, ClassAuditStatus.WAITING.value, ClassAuditStatus.REJECT.value):
        condition["audit_status"] = audit_status
    if is_shelf_miniprogram is not None:
        condition["is_shelf_miniprogram"] = is_shelf_miniprogram
    data = await query_class_with_page(db, page, page_size, condition=condition)
    count = await query_class_with_page(db, page=None, page_size=None, condition=condition, count=True)
    return await ApiSuccessResponse({
        "data": data,
        "count": count
    })


# 查询班级详情
@router.get("/class/{class_id}")
async def get_class_detail(
        class_id: int,
        db: AsyncSession = Depends(get_default_db),
        conf: dict = Depends(get_config),
):
    """
    # 查询班级详情
    """
    data = await erp_class.get_one(db, id=class_id)
    if not data:
        return await ApiSuccessResponse(False, "班级不存在")
    course = await erp_course.get_one(db, id=data.course_id)
    if not course:
        return await ApiSuccessResponse(False, "课程不存在")
    data.course_name = course.course_name
    course_config = await get_course_config(db, conf)
    data.term_name = course_config.get('term_map').get(str(course.term_id))
    data.category_name = course_config.get('category_map').get(str(course.category_id))
    data.grade_name = course_config.get('grade_map').get(str(course.grade_id))
    data.subject_name = course_config.get('subject_map').get(str(course.subject_id))
    data.original_price = course.original_price
    data.sale_price = course.sale_price
    # 查询班级计划
    data.schedule_json = await get_classplan_by_classid(db, class_id)

    return await ApiSuccessResponse(data)


# 针对班级计划的增删改查
@router.post("/class_plan")
async def create_class_plan(
        params: ClassPlanBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增班级计划
    -  finish: 传0
    """

    # 检查教室和教师时间冲突
    conflict_info = await check_time_conflicts(
        db,
        params.room_id,
        params.teacher_id,
        [{"start_time": params.start_time, "end_time": params.end_time}]
    )
    
    # 如果存在冲突，返回冲突信息
    if conflict_info:
        return await ApiFailedResponse({"success": False, "message": "时间冲突，无法创建班级计划", "conflicts": conflict_info})

    start_datetime = datetime.strptime(params.start_time, '%Y-%m-%d %H:%M:%S')
    end_datetime = datetime.strptime(params.end_time, '%Y-%m-%d %H:%M:%S')
    time_duration = (end_datetime - start_datetime).total_seconds() / 3600
    create_item = {
        "class_id": params.class_id,
        "start_time": params.start_time,
        "end_time": params.end_time,
        "teacher_id": params.teacher_id,
        "room_id": params.room_id,
        "create_by": user.uid,
        "update_by": user.uid,
        "time_duration": round(time_duration, 2),
        "classin_id": 0,
        "classin_activity_id": 0,
    }

    plan_obj = await erp_class_plan.create(db, commit=False, **create_item)
    
    # 获取班级信息
    class_obj = await erp_class.get_one(db, id=params.class_id)
    if not class_obj:
        await db.commit()
        return await ApiSuccessResponse(plan_obj, "班级计划创建成功，但无法获取班级信息")
    
    # 获取教师信息
    teacher_obj = await erp_account_teacher.get_one(db, id=params.teacher_id)
    if not teacher_obj:
        return await ApiSuccessResponse(plan_obj, "但无法获取教师class_in信息")
    teacher_uid = teacher_obj.classin_uid
    if not teacher_uid:
        # 调用register_teacher接口注册教师
        teacher_account= await erp_account.get_one(db, id=teacher_obj.account_id)
        if not teacher_account:
            return await ApiSuccessResponse(plan_obj, "但无法获取教师账号信息")
        register_response = await classin_sdk.user.register_user(
            telephone=teacher_account.username,
            nickname=teacher_account.employee_name,
            add_to_school_member=2
        )
        teacher_uid = register_response.get('data')
    # 判断是否需要同步到ClassIn（班级已激活并有classin_id）
    if class_obj.classin_id and int(class_obj.classin_id) > 0 and class_obj.classin_sync == 1:
        try:
            # 获取课程信息，确定座位数量
            course_obj = await erp_course.get_one(db, id=class_obj.course_id)
            seat_num = 2 if course_obj and int(course_obj.grade_id) in (1, ) else 1
            
            # 转换时间戳
            plan_start_timestamp = classin_sdk.get_time_stamp_from_date(start_datetime)
            plan_end_timestamp = classin_sdk.get_time_stamp_from_date(end_datetime)
            
            # 查询该班级所有计划，确定当前计划是第几节课
            all_plans = await erp_class_plan.get_many(db, raw=[
                ErpClassPlan.class_id == params.class_id,
                # ErpClassPlan.disable == 0
            ])
            all_plans.sort(key=lambda x: x.start_time)
            lesson_index = len(all_plans)  # 包括当前新增的计划
            
            # 构建课节名称
            lesson_name = f"{class_obj.class_name}-课节{lesson_index}"
            
            # 查找该班级的单元ID，如果没有则创建默认单元
            # 尝试通过现有课节获取单元ID
            unit_id = 0
            for existing_plan in all_plans:
                if existing_plan.id != plan_obj.id and existing_plan.classin_unit_id:
                    unit_id = existing_plan.classin_unit_id
                    break
            
            # 如果没有找到单元ID，创建新单元
            if not unit_id:
                unit_response = await classin_sdk.lms.create_unit(
                    course_id=class_obj.classin_id,
                    name="课堂实录",
                    publish_flag=2,
                    content="课堂实录存放所有课节的录课视频"
                )
                settings.logger.info(f"创建单元响应: {unit_response}")
                unit_id = unit_response.get('data', {}).get('unitId', 0)
                if not unit_id:
                    settings.logger.error(f"创建单元失败: {unit_response}")
                    await db.commit()
                    return await ApiSuccessResponse(plan_obj, "班级计划创建成功，但ClassIn单元创建失败")
            
            # 创建ClassIn课堂活动
            activity_response = await classin_sdk.lms.create_class_activity(
                course_id=class_obj.classin_id,
                unit_id=unit_id,
                name=lesson_name,
                start_time=plan_start_timestamp,
                end_time=plan_end_timestamp,
                teacher_uid=teacher_uid,
                seat_num=seat_num,
                is_hd=0,
                is_auto_onstage=0,
                live_state=1,
                record_type=0,
                record_state=1,
                open_state=1
            )
            settings.logger.info(f"创建ClassIn课堂活动响应: {activity_response}")
            
            # 检查响应状态
            if activity_response.get('code') == 1:
                # 更新班级计划的ClassIn相关信息
                plan_obj.classin_activity_id = activity_response.get('data', {}).get('activityId', 0)
                plan_obj.classin_id = activity_response.get('data', {}).get('classId', 0)
                plan_obj.classin_name = activity_response.get('data', {}).get('name', '')
                plan_obj.classin_live_url = activity_response.get('data', {}).get('live_url', '')
                plan_obj.classin_live_info = activity_response.get('data', {}).get('live_info', {})
                plan_obj.classin_unit_id = unit_id
                settings.logger.info(f"同步班级计划到ClassIn成功: {plan_obj.classin_id}-{plan_obj.classin_activity_id}")
                await db.commit()
                return await ApiSuccessResponse(plan_obj, "班级计划创建成功并同步到ClassIn")
            else:
                error_msg = activity_response.get('msg', '未知错误')
                settings.logger.error(f"同步班级计划到ClassIn失败: {error_msg}")
                await db.commit()
                return await ApiSuccessResponse(plan_obj, f"班级计划创建成功，但ClassIn同步失败：{error_msg}")
        
        except Exception as e:
            settings.logger.error(f"同步班级计划到ClassIn异常: {str(e)}")
            await db.commit()
            return await ApiSuccessResponse(plan_obj, f"班级计划创建成功，但ClassIn同步出现异常：{str(e)}")
    
    # 如果不需要同步或同步失败，直接提交事务并返回
    await db.commit()
    return await ApiSuccessResponse(plan_obj)


# 删除班级计划
@router.delete("/class_plan/{plan_id}")
async def delete_class_plan(
        plan_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除班级计划
    """
    current_plan = await erp_class_plan.get_one(db, id=plan_id)
    if not current_plan:
        return await ApiFailedResponse("班级计划不存在")
    current_plan.update_time = datetime.now()
    current_plan.disable = 1
    # 获取course_id
    class_obj = await erp_class.get_one(db, id=current_plan.class_id)
    try:
        if current_plan.classin_id and int(current_plan.classin_id) > 0:
            delete_response = await classin_sdk.lms.delete_activity(class_obj.classin_id, current_plan.classin_activity_id)
            settings.logger.info(f"删除班级计划到ClassIn成功: {class_obj.classin_id}-{current_plan.classin_activity_id}, 删除人: {user.uid}, 响应: {delete_response}")
    except Exception as e:
        settings.logger.error(f"删除班级计划到ClassIn异常: {str(e)}")
        return await ApiSuccessResponse(True, f"本地删除成功，但ClassIn同步失败：{str(e)}")
    finally:    
        await db.commit()
    return await ApiSuccessResponse(True)


# 修改班级计划
@router.put("/class_plan/{plan_id}")
async def update_class_plan(
        plan_id: int,
        params: ClassPlanBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改班级计划
    - class_id 可不传
    - 自动同步到ClassIn系统
    """
    print(f"修改班级计划: {params}")
    # 先查询当前计划信息
    current_plan = await erp_class_plan.get_one(db, id=plan_id)
    if not current_plan:
        return await ApiFailedResponse("班级计划不存在")
    
    # 检查是否修改了时间或资源配置
    time_or_resource_changed = False
    if ((params.start_time and params.start_time != current_plan.start_time) or 
        (params.end_time and params.end_time != current_plan.end_time) or 
        (params.teacher_id and params.teacher_id != current_plan.teacher_id) or 
        (params.room_id and params.room_id != current_plan.room_id)):
        time_or_resource_changed = True
    
    # 如果修改了时间或资源，需要进行冲突检查
    if time_or_resource_changed:
        # 确定检查参数
        check_start_time = params.start_time if params.start_time else current_plan.start_time
        check_end_time = params.end_time if params.end_time else current_plan.end_time
        check_teacher_id = params.teacher_id if params.teacher_id else current_plan.teacher_id
        check_room_id = params.room_id if params.room_id else current_plan.room_id
        
        # 检查教室和教师时间冲突，排除当前计划自身
        conflict_info = await check_time_conflicts(
            db,
            check_room_id,
            check_teacher_id,
            [{"start_time": check_start_time, "end_time": check_end_time}],
            exclude_plan_id=plan_id
        )
        
        # 如果存在冲突，返回冲突信息
        if conflict_info:
            return await ApiFailedResponse({"success": False, "message": "时间冲突，无法修改班级计划", "conflicts": conflict_info})
    
    if params.start_time:
        current_plan.start_time = params.start_time
    if params.end_time:
        current_plan.end_time = params.end_time
    if params.teacher_id:
        current_plan.teacher_id = params.teacher_id
    if params.room_id:
        current_plan.room_id = params.room_id

    start_datetime = datetime.strptime(params.start_time, '%Y-%m-%d %H:%M:%S')
    end_datetime = datetime.strptime(params.end_time, '%Y-%m-%d %H:%M:%S')
    time_duration = (end_datetime - start_datetime).total_seconds() / 3600
    current_plan.time_duration = round(time_duration, 2)
    current_plan.update_time = datetime.now()

    teacher_obj = await erp_account_teacher.get_one(db, id=current_plan.teacher_id)
    if not teacher_obj:
        return await ApiFailedResponse("教师不存在")
    
    # 如果该班级计划有ClassIn相关ID，同步到ClassIn
    if current_plan.classin_id and int(current_plan.classin_id) > 0:
        try:
            # 获取班级信息
            class_obj = await erp_class.get_one(db, id=current_plan.class_id)
            if not class_obj or not hasattr(class_obj, 'classin_id') or not class_obj.classin_id:
                return await ApiSuccessResponse(True, "本地更新成功，但ClassIn同步失败：班级ClassIn信息不完整")
            
            # 转换时间戳
            plan_start_timestamp = classin_sdk.get_time_stamp_from_date(start_datetime)
            plan_end_timestamp = classin_sdk.get_time_stamp_from_date(end_datetime)
            
            # 调用ClassIn API编辑课堂活动
            activity_response = await classin_sdk.lms.edit_class_activity(
                # 必传
                course_id=class_obj.classin_id,  
                activity_id=current_plan.classin_activity_id,
                # 选传
                start_time=plan_start_timestamp,
                end_time=plan_end_timestamp,
                teacher_uid=teacher_obj.classin_uid,
            )
            settings.logger.info(f"同步班级计划到ClassIn响应: {activity_response}")
            # 检查响应状态
            if activity_response.get('code') == 1:
                # 更新数据库中的ClassIn相关信息
                return await ApiSuccessResponse(True, "本地更新成功并同步到ClassIn")
            else:
                error_msg = activity_response.get('msg', '未知错误')
                settings.logger.error(f"同步班级计划到ClassIn失败: {error_msg}")
                return await ApiSuccessResponse(True, f"本地更新成功，但ClassIn同步失败：{error_msg}")
        
        except Exception as e:
            settings.logger.error(f"同步班级计划到ClassIn异常: {str(e)}")
            return await ApiSuccessResponse(True, f"本地更新成功，但ClassIn同步出现异常：{str(e)}")

        finally:
            await db.commit()
    else:
        await db.commit()
    return await ApiSuccessResponse(True, "更新成功")



# 班级计划批量同步到ClassIn
@router.post("/class_plan_batch_sync_to_classin")
async def class_plan_batch_sync_to_classin(
        class_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 班级计划批量同步到ClassIn
    - 支持批量同步多个班级计划到ClassIn系统
    - 在清除原有classIn课堂活动后，重新创建新的classIn课堂活动
    """
    # 1. 验证班级是否存在
    class_obj = await erp_class.get_by_id(db, class_id)
    if not class_obj:
        return await ApiFailedResponse("班级不存在")
    
    # 2. 检查班级是否已同步到ClassIn
    if not class_obj.classin_id or int(class_obj.classin_id) <= 0:
        return await ApiFailedResponse("班级尚未同步到ClassIn，无法执行批量同步操作")
    
    if class_obj.classin_sync != 1:
        return await ApiFailedResponse("班级ClassIn同步状态异常，无法执行批量同步操作")
    
    # 3. 查询该班级的所有课程计划
    class_plans = await erp_class_plan.get_many(db, raw=[
        ErpClassPlan.class_id == class_id,
        ErpClassPlan.disable == 0
    ])
    
    if not class_plans:
        return await ApiSuccessResponse({
            "message": "该班级没有需要同步的课程计划",
            "sync_count": 0,
            "failed_count": 0
        })
    
    # 4. 获取班级主讲教师信息
    main_teacher = await erp_account_teacher.get_by_id(db, class_obj.teacher_id)
    if not main_teacher:
        return await ApiFailedResponse("无法获取班级主讲教师信息")
    
    # 确保主讲教师在ClassIn中已注册
    main_teacher_uid = main_teacher.classin_uid
    if not main_teacher_uid:
        teacher_account = await erp_account.get_by_id(db, main_teacher.account_id)
        if not teacher_account:
            return await ApiFailedResponse("无法获取主讲教师账户信息")
        
        # 注册教师到ClassIn
        register_response = await classin_sdk.user.register_user(
            telephone=teacher_account.username,
            nickname=teacher_account.employee_name,
            add_to_school_member=2
        )
        main_teacher_uid = register_response.get('data')
        if main_teacher_uid:
            main_teacher.classin_uid = main_teacher_uid
            main_teacher.classin_sync = 1
            main_teacher.update_time = datetime.now()
            await db.commit()
        else:
            return await ApiFailedResponse("主讲教师ClassIn注册失败")
    
    # 5. 获取课程信息，确定座位数量
    course_obj = await erp_course.get_one(db, id=class_obj.course_id)
    seat_num = 2 if course_obj and int(course_obj.grade_id) in (1, ) else 1
    
    # 6. 清除现有的ClassIn课堂活动
    settings.logger.info(f"开始清除班级{class_id}的现有ClassIn课堂活动")
    clear_success_count = 0
    clear_failed_count = 0
    
    for plan in class_plans:
        if plan.classin_activity_id and int(plan.classin_activity_id) > 0:
            try:
                delete_response = await classin_sdk.lms.delete_activity(
                    class_obj.classin_id, 
                    plan.classin_activity_id
                )
                if delete_response.get('code') == 1:
                    clear_success_count += 1
                    settings.logger.info(f"清除ClassIn活动成功: plan_id={plan.id}, activity_id={plan.classin_activity_id}")
                else:
                    clear_failed_count += 1
                    settings.logger.warning(f"清除ClassIn活动失败: plan_id={plan.id}, activity_id={plan.classin_activity_id}, 响应: {delete_response}")
            except Exception as e:
                clear_failed_count += 1
                settings.logger.error(f"清除ClassIn活动异常: plan_id={plan.id}, activity_id={plan.classin_activity_id}, 错误: {str(e)}")
        
        # 清空本地ClassIn相关字段
        plan.classin_activity_id = 0
        plan.classin_id = 0
        plan.classin_name = ""
        plan.classin_live_url = ""
        plan.classin_live_info = {}
        plan.classin_unit_id = 0
    
    # 7. 创建或获取默认单元
    unit_id = 0
    try:
        unit_response = await classin_sdk.lms.create_unit(
            course_id=int(class_obj.classin_id),
            name="课堂实录",
            publish_flag=2,
            content="课堂实录存放所有课节的录课视频"
        )
        unit_id = unit_response.get('data', {}).get('unitId', 0)
        settings.logger.info(f"创建/获取单元成功: unit_id={unit_id}")
    except Exception as e:
        settings.logger.error(f"创建单元失败: {str(e)}")
        return await ApiFailedResponse(f"创建ClassIn单元失败: {str(e)}")
    
    if not unit_id:
        return await ApiFailedResponse("无法创建或获取ClassIn单元")
    
    # 8. 重新创建ClassIn课堂活动
    settings.logger.info(f"开始重新创建班级{class_id}的ClassIn课堂活动")
    sync_success_count = 0
    sync_failed_count = 0
    failed_plans = []
    
    # 按开始时间排序，确保课节顺序正确
    sorted_plans = sorted(class_plans, key=lambda x: x.start_time)
    
    for index, plan in enumerate(sorted_plans):
        try:
            # 获取该课程计划的教师信息
            plan_teacher = await erp_account_teacher.get_by_id(db, plan.teacher_id)
            if not plan_teacher:
                failed_plans.append({
                    "plan_id": plan.id,
                    "error": "无法获取课程计划教师信息"
                })
                sync_failed_count += 1
                continue
            
            # 确保课程计划教师在ClassIn中已注册
            plan_teacher_uid = plan_teacher.classin_uid
            if not plan_teacher_uid:
                teacher_account = await erp_account.get_by_id(db, plan_teacher.account_id)
                if teacher_account:
                    register_response = await classin_sdk.user.register_user(
                        telephone=teacher_account.username,
                        nickname=teacher_account.employee_name,
                        add_to_school_member=2
                    )
                    plan_teacher_uid = register_response.get('data')
                    if plan_teacher_uid:
                        plan_teacher.classin_uid = plan_teacher_uid
                        plan_teacher.classin_sync = 1
                        plan_teacher.update_time = datetime.now()
                    else:
                        # 如果教师注册失败，使用主讲教师
                        plan_teacher_uid = main_teacher_uid
                        settings.logger.warning(f"课程计划{plan.id}的教师注册失败，使用主讲教师")
                else:
                    # 如果无法获取教师账户，使用主讲教师
                    plan_teacher_uid = main_teacher_uid
                    settings.logger.warning(f"课程计划{plan.id}无法获取教师账户，使用主讲教师")
            
            # 转换时间戳
            start_datetime = datetime.strptime(plan.start_time, '%Y-%m-%d %H:%M:%S') if isinstance(plan.start_time, str) else plan.start_time
            end_datetime = datetime.strptime(plan.end_time, '%Y-%m-%d %H:%M:%S') if isinstance(plan.end_time, str) else plan.end_time
            
            plan_start_timestamp = classin_sdk.get_time_stamp_from_date(start_datetime)
            plan_end_timestamp = classin_sdk.get_time_stamp_from_date(end_datetime)
            
            # 构建课节名称
            lesson_name = f"{class_obj.class_name}-课节{index + 1}"
            
            # 创建ClassIn课堂活动
            activity_response = await classin_sdk.lms.create_class_activity(
                course_id=class_obj.classin_id,
                unit_id=unit_id,
                name=lesson_name,
                start_time=plan_start_timestamp,
                end_time=plan_end_timestamp,
                teacher_uid=plan_teacher_uid,
                seat_num=seat_num,
                is_hd=0,
                is_auto_onstage=0,
                live_state=1,
                record_type=0,
                record_state=1,
                open_state=1
            )
            
            # 检查响应状态
            if activity_response.get('code') == 1:
                # 更新班级计划的ClassIn相关信息
                plan.classin_activity_id = activity_response.get('data', {}).get('activityId', 0)
                plan.classin_id = activity_response.get('data', {}).get('classId', 0)
                plan.classin_name = activity_response.get('data', {}).get('name', '')
                plan.classin_live_url = activity_response.get('data', {}).get('live_url', '')
                plan.classin_live_info = activity_response.get('data', {}).get('live_info', {})
                plan.classin_unit_id = unit_id
                plan.update_time = datetime.now()
                
                sync_success_count += 1
                settings.logger.info(f"创建ClassIn活动成功: plan_id={plan.id}, activity_id={plan.classin_activity_id}")
            else:
                error_msg = activity_response.get('msg', '未知错误')
                failed_plans.append({
                    "plan_id": plan.id,
                    "error": f"ClassIn API错误: {error_msg}"
                })
                sync_failed_count += 1
                settings.logger.error(f"创建ClassIn活动失败: plan_id={plan.id}, 错误: {error_msg}")
                
        except Exception as e:
            failed_plans.append({
                "plan_id": plan.id,
                "error": f"同步异常: {str(e)}"
            })
            sync_failed_count += 1
            settings.logger.error(f"同步课程计划{plan.id}到ClassIn异常: {str(e)}")
    
    # 10. 返回同步结果
    result = {
        "class_id": class_id,
        "class_name": class_obj.class_name,
        "total_plans": len(class_plans),
        "clear_stats": {
            "success_count": clear_success_count,
            "failed_count": clear_failed_count
        },
        "sync_stats": {
            "success_count": sync_success_count,
            "failed_count": sync_failed_count
        },
        "failed_plans": failed_plans if failed_plans else None
    }
    result = copy.deepcopy(result)


    # 9. 提交数据库更改
    try:
        await db.commit()
        settings.logger.info(f"班级{class_id}批量同步完成，成功{sync_success_count}个，失败{sync_failed_count}个")
    except Exception as e:
        await db.rollback()
        settings.logger.error(f"数据库提交失败: {str(e)}")
        return await ApiFailedResponse(f"数据库更新失败: {str(e)}")
    
    
    
    if sync_failed_count > 0:
        return await ApiSuccessResponse(
            result, 
            f"批量同步完成，成功同步{sync_success_count}个课程计划，{sync_failed_count}个失败"
        )
    else:
        return await ApiSuccessResponse(
            result, 
            f"批量同步成功，共同步{sync_success_count}个课程计划到ClassIn"
        )



# 批量修改班级计划
@router.post("/class_plan_batch_update")
async def class_plan_batch_update(
        params: List[ClassPlanBatchUpdate],
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 批量修改班级计划
    - 支持批量修改多个班级计划的时间、教师、教室等信息
    - 会进行时间冲突检查
    - 自动同步到ClassIn系统
    """
    if not params or len(params) == 0:
        return await ApiFailedResponse("参数不能为空")
    
    # 结果统计
    success_count = 0
    failed_updates = []
    
    # 批量处理每个班级计划
    for param in params:
        # 先查询当前计划信息
        current_plan = await erp_class_plan.get_one(db, id=param.plan_id)
        if not current_plan:
            failed_updates.append({
                "plan_id": param.plan_id,
                "error": "班级计划不存在"
            })
            continue
        
        # 检查是否修改了时间或资源配置
        time_or_resource_changed = False
        if ((param.start_time and param.start_time != current_plan.start_time) or 
            (param.end_time and param.end_time != current_plan.end_time) or 
            (param.teacher_id and param.teacher_id != current_plan.teacher_id) or 
            (param.room_id and param.room_id != current_plan.room_id)):
            time_or_resource_changed = True
        
        # 如果修改了时间或资源，需要进行冲突检查
        if time_or_resource_changed:
            # 确定检查参数
            check_start_time = param.start_time if param.start_time else current_plan.start_time
            check_end_time = param.end_time if param.end_time else current_plan.end_time
            check_teacher_id = param.teacher_id if param.teacher_id else current_plan.teacher_id
            check_room_id = param.room_id if param.room_id else current_plan.room_id
            
            # 检查教室和教师时间冲突，排除当前计划自身
            conflict_info = await check_time_conflicts(
                db,
                check_room_id,
                check_teacher_id,
                [{"start_time": check_start_time, "end_time": check_end_time}],
                exclude_plan_id=param.plan_id
            )
            
            # 如果存在冲突，记录失败信息
            if conflict_info:
                failed_updates.append({
                    "plan_id": param.plan_id,
                    "error": "时间冲突",
                    "conflicts": conflict_info
                })
                continue
        
        # 更新班级计划信息
        if param.start_time:
            current_plan.start_time = param.start_time
        if param.end_time:
            current_plan.end_time = param.end_time
        if param.teacher_id:
            current_plan.teacher_id = param.teacher_id
        if param.room_id:
            current_plan.room_id = param.room_id

        # 重新计算时长
        if param.start_time and param.end_time:
            start_datetime = datetime.strptime(param.start_time, '%Y-%m-%d %H:%M:%S')
            end_datetime = datetime.strptime(param.end_time, '%Y-%m-%d %H:%M:%S')
            time_duration = (end_datetime - start_datetime).total_seconds() / 3600
            current_plan.time_duration = round(time_duration, 2)
        
        current_plan.update_time = datetime.now()

        # 获取教师信息（用于ClassIn同步）
        teacher_obj = await erp_account_teacher.get_one(db, id=current_plan.teacher_id)
        if not teacher_obj:
            failed_updates.append({
                "plan_id": param.plan_id,
                "error": "教师不存在"
            })
            continue
        
        # 如果该班级计划有ClassIn相关ID，同步到ClassIn
        if current_plan.classin_id and int(current_plan.classin_id) > 0:
            try:
                # 获取班级信息
                class_obj = await erp_class.get_one(db, id=current_plan.class_id)
                if not class_obj or not hasattr(class_obj, 'classin_id') or not class_obj.classin_id:
                    # ClassIn信息不完整，但本地更新继续
                    settings.logger.warning(f"班级计划{param.plan_id}的ClassIn信息不完整，跳过ClassIn同步")
                else:
                    # 转换时间戳
                    final_start_time = param.start_time if param.start_time else current_plan.start_time
                    final_end_time = param.end_time if param.end_time else current_plan.end_time
                    start_datetime = datetime.strptime(final_start_time, '%Y-%m-%d %H:%M:%S')
                    end_datetime = datetime.strptime(final_end_time, '%Y-%m-%d %H:%M:%S')
                    
                    plan_start_timestamp = classin_sdk.get_time_stamp_from_date(start_datetime)
                    plan_end_timestamp = classin_sdk.get_time_stamp_from_date(end_datetime)
                    
                    # 调用ClassIn API编辑课堂活动
                    activity_response = await classin_sdk.lms.edit_class_activity(
                        # 必传
                        course_id=class_obj.classin_id,  
                        activity_id=current_plan.classin_activity_id,
                        # 选传
                        start_time=plan_start_timestamp,
                        end_time=plan_end_timestamp,
                        teacher_uid=teacher_obj.classin_uid,
                    )
                    
                    # 检查响应状态
                    if activity_response.get('code') != 1:
                        error_msg = activity_response.get('msg', '未知错误')
                        settings.logger.error(f"同步班级计划{param.plan_id}到ClassIn失败: {error_msg}")
            
            except Exception as e:
                settings.logger.error(f"同步班级计划{param.plan_id}到ClassIn异常: {str(e)}")
                # ClassIn同步失败不影响本地更新
        
        success_count += 1
        
    
    # 提交所有更改
    await db.commit()
    
    # 返回结果
    result = {
        "success_count": success_count,
        "total_count": len(params),
        "failed_updates": failed_updates
    }
    
    if failed_updates:
        return await ApiSuccessResponse(result, f"批量更新完成，成功{success_count}个，失败{len(failed_updates)}个")
    else:
        return await ApiSuccessResponse(result, f"批量更新成功，共更新{success_count}个班级计划")



# 查询班级计划
@router.get("/class_plan/{class_id}")
async def get_class_plan(
        class_id: int,
        has_checking_stats: bool = True,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询指定class_id班级计划
    - checking_stats
       - total: 总签到数
       - attend: 出勤数
       - absent: 缺勤数
       - leave: 请假数
       - late: 迟到数
       - online: 线上数
    - finish: 1 已结束 0 未开始
    """
    erp_class_checking = CF.get_crud(ErpClassChecking)
    data = await get_classplan_by_classid(db, class_id)
    if has_checking_stats:
        finish_class_plan_ids = [i.id for i in data]
        print(f"含有已结束的课程数: {len(finish_class_plan_ids)}")
        # 但是当finish为1时，表示该班级计划已结束, 还需要查询具体签到情况；
        class_checking_objs = await erp_class_checking.get_many(db, raw=[
            ErpClassChecking.class_plan_id.in_(finish_class_plan_ids),
        ])
    
        # 根据class_plan_id分组，并统计不同签到状态的数量 
        # check_status: 1 出勤 2 缺勤 3 请假 4 迟到 5 线上
        class_checking_stats_map = defaultdict(lambda: {"total": 0, "attend": 0, "absent": 0, "leave": 0, "late": 0, "online": 0})
        for i in class_checking_objs:
            stats = class_checking_stats_map[i.class_plan_id]
            stats["total"] += 1
            if i.check_status == 1:
                stats["attend"] += 1
            elif i.check_status == 2:
                stats["absent"] += 1
            elif i.check_status == 3:
                stats["leave"] += 1
            elif i.check_status == 4:
                stats["late"] += 1
            elif i.check_status == 5:
                stats["online"] += 1
    course_id_list = [i.course_id for i in data]
    course_outline = await erp_course_outline.get_many(db, raw=[
        ErpCourseOutline.course_id.in_(course_id_list),
    ])
    # 按照课程分组聚合大纲
    course_outline_map = defaultdict(list)
    for i in course_outline:
        course_outline_map[i.course_id].append(i)
    
    new_data = []
    for index, i in enumerate(data):
        item = dict(i)
        # 获取当前课程的所有大纲，并按sort字段排序
        current_course_outlines = course_outline_map.get(i.course_id, [])
        sorted_course_outlines = sorted(current_course_outlines, key=lambda x: x.id)
        # 检查索引是否超出大纲列表范围，防止IndexError
        # 如果课程计划的数量多于课程大纲的数量，或者两者顺序不完全匹配，
        # 直接使用 `index` 可能会导致 `IndexError`。
        if index < len(sorted_course_outlines):
            item['course_outline_index'] = sorted_course_outlines[index]
        else:
            # 如果当前索引没有对应的大纲，则设置为None。
            # 这表示无法为该课程计划找到匹配的大纲，或者大纲数量不足。
            item['course_outline_index'] = None
            # 可以选择在此处添加日志记录，以便后续排查问题
            # print(f"课程计划 {i.id} (索引 {index}) 对应的课程 {i.course_id} 没有在大纲列表中找到匹配的索引。总大纲数: {len(sorted_course_outlines)}")
        if has_checking_stats:
            item['checking_stats'] = class_checking_stats_map.get(i.id, {"total": 0, "attend": 0, "absent": 0, "leave": 0, "late": 0, "online": 0})
        new_data.append(item)
    return await ApiSuccessResponse(new_data)




#指定时间范围查询我的课表
@router.get("/class_plan_by_time")
async def class_plan_by_time(
        start_time: str,
        end_time: str,
        teacher_id: int = None,
        class_room_ids: str = None,
        current_teacher: bool = False,
        subject_id: int = None,
        category_id: int = None,
        grade_ids: str = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 指定时间范围查询我的课表
    - start_time: 2021-11-01 00:00:00
    - end_time: 2021-11-30 23:59:59
    - current_teacher: 是否查询当前教师课表, 传递则不查询teacher_id
    - subject_id: 科目id
    - category_id: 分类id
    - grade_ids: 年级id字符串，逗号分隔
    """
    if current_teacher:
        teacher_obj = await erp_account_teacher.get_one(db, account_id=user.uid)
        if not teacher_obj:
            return await ApiFailedResponse('无此教师')
        teacher_id = teacher_obj.id
    if grade_ids:
        grade_ids = grade_ids.split(',')
    class_room_ids = class_room_ids.split(',') if class_room_ids else None
    if class_room_ids:
        class_room_ids = [int(i) for i in class_room_ids]
    data = await query_class_plan(db, 
                                  teacher_id=teacher_id, 
                                  start_time=start_time, 
                                  end_time=end_time, 
                                  class_room_ids=class_room_ids,
                                  subject_id=subject_id,
                                  category_id=category_id,
                                  grade_ids=grade_ids)
    return await ApiSuccessResponse(data)
    

# 查询课时统计
@router.get("/class_times_statistics")
async def get_class_times_statistics(
        page: int = 1,
        page_size: int = 10,
        teacher_id: int = None,
        start_time: str = None,
        end_time: str = None,
        type_ids: str = None,
        term_ids: str = None,
        grade_ids: str = None,
        subject_ids: str = None,
        category_ids: str = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
        # conf: dict = Depends(get_config),
):
    """
    # 查询我的课时统计
    - type_ids, term_ids, grade_ids, subject_ids, category_ids: 逗号分隔的ID字符串，例如"1,2,3"
    """
    # 处理逗号分隔的ID字符串
    type_id_list = type_ids.split(',') if type_ids else None
    term_id_list = term_ids.split(',') if term_ids else None
    grade_id_list = grade_ids.split(',') if grade_ids else None
    subject_id_list = subject_ids.split(',') if subject_ids else None
    category_id_list = category_ids.split(',') if category_ids else None
    
    data = await query_teacher_class_times(db, 
                                           teacher_id,
                                           start_time, 
                                           end_time, 
                                           type_id_list,
                                           term_id_list,
                                           grade_id_list,
                                           subject_id_list,
                                           category_id_list,
                                           page, 
                                           page_size, 
                                           count=False)
    
    # 收集所有需要查询的class_id
    class_ids = [i.class_id for i in data]

    raws = [
        ErpClassPlan.class_id.in_(class_ids),
    ]
    if start_time:
        raws.append(ErpClassPlan.start_time >= start_time)
    if end_time:
        raws.append(ErpClassPlan.end_time <= end_time)
    
    # 一次性查询所有班级的课程计划, 这里需要加日期筛选
    all_class_plans = await erp_class_plan.get_many(db, raw=raws)
    
    # 按class_id分组课程计划，便于快速查找
    class_plan_map = {}
    for plan in all_class_plans:
        if plan.class_id not in class_plan_map:
            class_plan_map[plan.class_id] = []
        class_plan_map[plan.class_id].append(plan)
    
    # 在内存中处理数据
    new_data = []
    for i in data:
        item = dict(i)
        # 从映射中获取当前班级的课程计划
        class_plans = class_plan_map.get(i.class_id, [])
        # 过滤已完成的课程计划
        finished = [plan for plan in class_plans if plan.finish == 1]
        item['finished_num'] = len(finished)
        new_data.append(item)
        
    return await ApiSuccessResponse({
        "data": new_data,
        "count": len(data)
    })



# 查询教师课时统计
@router.get("/teacher_class_times")
async def teacher_class_times_by_time(
        start_time: str = None,
        end_time: str = None,
        term_ids: str = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
        conf: dict = Depends(get_config),
):
    """
    # 查询教师课时统计
    - 按教师聚合，统计时段内每个教师的总上课节数，以及各班型（长短期）分别的上课节数
    - 返回数据包含每位教师的详细上课记录
    入口：erp_class_plan表， 班型 ErpCourse.type_id
    关联关系： erp_class_plan.class_id = erp_class.id, erp_class.course_id = erp_course.id
    """
    # 处理参数
    term_id_list = term_ids.split(',') if term_ids else None
    
    # 调用模块函数处理统计数据
    from app_teach.modules import process_teacher_class_statistics
    data = await process_teacher_class_statistics(
        db, 
        start_time=start_time, 
        end_time=end_time, 
        term_ids=term_id_list,
        conf=conf
    )
    
    return await ApiSuccessResponse({
        "data": data,
        "count": len(data)
    })


# 查询班级学生列表
@router.get("/class_student_list")
async def class_student_list(
        class_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    data = await get_student_by_class_id(db, class_id)
    return await ApiSuccessResponse(data)


# 教室月利用率
@router.get("/class_room_utilization")
async def class_room_utilization(
        yyyy: str,
        center_id: int = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 教室月利用率
    ## 查询指定年份教室月利用率
    月利用率=教室月课节总课时/月基础课时总和
    1/2/7/8月每日基础课时8.33h
    3/4/5/6/9/10/11/12月: 周一~周四 0h; 周五 2.5h;周六周末 10h
    - center_id: 校区ID，可选参数，不传则查询所有校区教室
    """
    from app_teach.modules import calculate_classroom_utilization
    
    # 获取指定年份的课程计划数据
    year = int(yyyy)
    
    # 调用模块函数计算教室月利用率
    results = await calculate_classroom_utilization(db, year, center_id)
    
    return await ApiSuccessResponse(results)

# 周中教室利用率
@router.get("/class_room_utilization_weekday")
async def class_room_utilization_weekday(
        yyyy: str,
        center_id: int = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 周中教室利用率
    月周利用率=教室月周中课节总课时/月基础课时总和

    3/4/5/6/9/10/11/12月：
        周1、2、3、4基础课时2.5h
        周五基础课时0h
        周六周末基础课时0h
    其他月份不统计也不展示字段名，例如m1,只展示以上月份
    返回格式同@router.get("/class_room_utilization")

    """
    
    # 获取指定年份的课程计划数据
    year = int(yyyy)
    
    # 调用模块函数计算周中教室利用率
    results = await calculate_classroom_utilization_week(db, year, center_id)
    
    return await ApiSuccessResponse(results)


# 上课记录查询
@router.get("/class_record")
async def get_class_record(
    page: int = 1,
    page_size: int = 10,
    teacher_id: int = None,
    room_id: int = None,
    start_time: str = None,
    end_time: str = None,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 上课记录查询
    """
    data = await query_class_record(db, page, page_size, teacher_id, room_id, start_time, end_time, count=False)
    count = await query_class_record(db, page, page_size, teacher_id, room_id, start_time, end_time, count=True)
    return await ApiSuccessResponse({
        "data": data,
        "count": count
    })


# 开班进度查询
@router.get("/open_class_progress")
async def open_class_progress(
    page: int = 1,
    page_size: int = 10,
    class_name: str = None,
    teacher_id: int = None,
    approval_status: int = None,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 开班进度查询
    - class_name: 班级名称
    - teacher_id: 教师ID
    - approval_status: 审批状态 关联 ClassAuditStatus
    """
    # 调用查询函数获取数据
    data, total_count = await query_open_class_progress(
        db=db,
        page=page,
        page_size=page_size,
        class_name=class_name,
        teacher_id=teacher_id,
        approval_status=approval_status
    )
    
    # 格式化返回数据
    result = []
    for item in data:
        result.append({
            "class_id": item.class_id,
            "class_name": item.class_name,
            "course_id": item.course_id,
            "course_name": item.course_name,
            "teacher": {
                "id": item.teacher_id_obj,
                "name": item.teacher_name,
                "avatar": item.teacher_avatar
            },
            "audit_status": item.audit_status,
            "workflow": {
                "receipt_id": item.receipt_id,
                "instance_id": item.workflow_instance_id,
                "current_node_id": item.current_node_id,
                "current_node_name": item.current_node_name,
                "status": item.workflow_status
            },
            "creator": {
                "id": item.creator_id,
                "name": item.creator_name,
                "avatar": item.creator_avatar
            }
        })
    
    return await ApiSuccessResponse({
        "data": result,
        "count": total_count
    })



# 根据教师teacher_id查询下属老师teacher_id列表
@router.get("/child_teacher_id_list")
async def child_teacher_id_list(
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    data = await get_child_teacher_id_list(db, user.teacher_id)
    return await ApiSuccessResponse(data)


# 续报数据
@router.get("/class_renew_report")
async def query_class_renew_report(
    start_teacher_ids: str=None,    # -> 改为开始的教师 start_teacher_ids
    start_term_ids: str=None,
    start_grade_ids: str=None,
    start_p_grade_ids: str=None,
    start_type_ids: str=None,
    start_subject_ids: str=None,
    start_category_ids: str=None,
    
    end_term_ids: str=None,
    end_type_ids: str=None,

    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 续报数据统计模块
    目的：统计start_*条件下学生是否在end_*条件下进行过报名
    
    强一致性匹配：
    - 学科必须强一致性匹配（start_subject_ids=3,4的班，续报数据中3只能和subject_id=3的班比对，4只能和subject_id=4的班比对）
    
    统计数据：
    - 招生数据：基于start_*条件统计带班数、满班基数、报名人数、报名满班率
    - 续报数据：start_*条件学生在end_*条件下的续报情况（按学科强一致性匹配）
    - 退费数据：基于start_*条件统计课前退费、课中退费、课中退费率
    - 满班率：基于start_*条件班级招生一览表
    - 未续报名单：start_*条件应续报学生中未在end_*条件下报名的学生
    
    参数说明：
    - start_teacher_ids: 开始教师ID列表，逗号分隔，如 "1,2,3"
    - start_term_ids: 开始学期ID列表，逗号分隔，如 "1,2,3"
    - start_grade_ids: 开始年级ID列表，逗号分隔，如 "1,2,3"
    - start_p_grade_ids: 开始预科年级ID列表，逗号分隔，如 "1,2,3"
    - start_type_ids: 开始课程类型ID列表，逗号分隔，如 "1,2,3"
    - start_subject_ids: 开始科目ID列表，逗号分隔，如 "1,2,3"（强一致性匹配字段）
    - start_category_ids: 开始分类ID列表，逗号分隔，如 "1,2,3"
    - end_term_ids: 结束学期ID列表，逗号分隔，如 "1,2,3"
    - end_type_ids: 结束课程类型ID列表，逗号分隔，如 "1,2,3"
    """
    
    # 处理逗号分隔的ID字符串，转换为列表
    start_teacher_id_list = start_teacher_ids.split(',') if start_teacher_ids else None
    start_term_id_list = start_term_ids.split(',') if start_term_ids else None
    start_grade_id_list = start_grade_ids.split(',') if start_grade_ids else None
    start_p_grade_id_list = start_p_grade_ids.split(',') if start_p_grade_ids else None
    start_type_id_list = start_type_ids.split(',') if start_type_ids else None
    start_subject_id_list = start_subject_ids.split(',') if start_subject_ids else None
    start_category_id_list = start_category_ids.split(',') if start_category_ids else None
    
    end_term_id_list = end_term_ids.split(',') if end_term_ids else None
    end_type_id_list = end_type_ids.split(',') if end_type_ids else None
    
    data = await class_renew_report_module(db, 
                                           start_teacher_id=start_teacher_id_list,
                                           start_term_id=start_term_id_list,
                                           start_grade_id=start_grade_id_list,
                                           start_p_grade_id=start_p_grade_id_list,
                                           start_type_id=start_type_id_list,
                                           start_subject_id=start_subject_id_list,
                                           start_category_id=start_category_id_list,
                                           end_term_id=end_term_id_list,
                                        #    end_grade_id=end_grade_id_list,
                                        #    end_p_grade_id=end_p_grade_id_list,
                                           end_type_id=end_type_id_list,
                                        #    end_subject_id=end_subject_id_list,
                                        #    end_category_id=end_category_id_list,
                                           )
    return await ApiSuccessResponse(data)



# 满班数据
@router.get("/class_full_data")
async def query_class_full_data(
    teacher_id: int=None,
    term_id: int=None,
    grade_id: int=None,
    type_id: int=None,
    subject_id: int=None,
    category_id: int=None,
    start_time: str=None,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 满班数据
    """
    data = await class_full_data_module(db, term_id, grade_id, type_id, subject_id, category_id, start_time, teacher_id)
    return await ApiSuccessResponse(data)


# 班级满班数据表
@router.get("/class_full_data_detail/{class_id}")
async def query_class_full_data_detail(
    class_id: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 班级满班数据表
    - 展示班级的详细信息，包括课程计划对应的课消情况、学生情况
    - 此接口为/class_full_data接口的详情数据
    """
    data = await class_full_data_detail_module(db, class_id)
    return await ApiSuccessResponse(data)



# 同步班级数据到ClassIn
@router.post("/sync_class_to_classin")
async def sync_class_to_classin(
    class_id: int,
):
    """
    # 同步班级数据到ClassIn
    # - 【暂请勿调用】
    """
    asyncio.create_task(sync_class_data_to_classin(class_id))
    return await ApiSuccessResponse(True, '任务已提交')





#######################以下是旧版#################################

# 查询转班记录
@router.get("/class_transfer")
async def class_transfer(
        secret_key: str,
        page: int = 1,
        page_size: int = 10,
        stu_name: str = None,
        old_class_name: str = None,
        new_class_name: str = None,
        transfer_date: str = None,
        is_cancel: int = None,
        db: AsyncSession = Depends(get_uat_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询调课记录
    - transfer_date: 2021-10-09
    """
    if secret_key != settings.SECRET_KEY:
        return await ApiSuccessResponse(False, '无权限')
    data = await get_transfer_record(db, page, page_size,
                                     stu_name=stu_name,
                                     old_class_name=old_class_name,
                                     new_class_name=new_class_name,
                                     transfer_date=transfer_date,
                                     is_cancel=is_cancel)
    # 新增查询课程计划是属于第几节课
    rb_class_plan = CF.get_crud(RbClassPlan)
    new_data = []
    for i in data:
        item = dict(i)
        # 查询旧班级计划
        old_class_plans = await rb_class_plan.get_many(db, {"ClassId": i.old_class_id, "Status": 0})
        old_class_plans.sort(key=lambda x: x.ClassDate, reverse=False)
        old_plan_ids = [i.ClassPlanId for i in old_class_plans]
        old_class_plan_index = old_plan_ids.index(i.old_plan_id) + 1
        # 查询新班级计划
        new_class_plans = await rb_class_plan.get_many(db, {"ClassId": i.new_class_id, "Status": 0})
        new_class_plans.sort(key=lambda x: x.ClassDate, reverse=False)
        new_plan_ids = [i.ClassPlanId for i in new_class_plans]
        new_class_plan_index = new_plan_ids.index(i.new_plan_id) + 1
        item['old_class_plan_index'] = old_class_plan_index
        item['new_class_plan_index'] = new_class_plan_index
        item['old_class_date'] = old_class_plans[old_class_plan_index - 1].ClassDate
        item['new_class_date'] = new_class_plans[new_class_plan_index - 1].ClassDate
        new_data.append(item)

    count_data = await get_transfer_record(db,
                                           stu_name=stu_name,
                                           old_class_name=old_class_name,
                                           new_class_name=new_class_name,
                                           transfer_date=transfer_date,
                                           is_cancel=is_cancel)
    return await ApiSuccessResponse({
        "data": new_data,
        "count": len(count_data)
    })


# 查询转班记录
@router.get("/class_change")
async def class_change(
        secret_key: str,
        page: int = 1,
        page_size: int = 10,
        keyword: str = None,
        db: AsyncSession = Depends(get_uat_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询转班记录
    """
    if secret_key != settings.SECRET_KEY:
        return await ApiSuccessResponse(False, '无权限')
    data = await get_class_change_record(db, page, page_size, keyword)
    count_data = await get_class_change_record(db, keyword=keyword)
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 删除调课记录
@router.delete("/class_transfer/{transfer_id}")
async def delete_class_transfer(
        transfer_id: int,
        secret_key: str,
        user_id: int,
        db: AsyncSession = Depends(get_uat_db),
):
    """
    # 删除调课记录
    """
    if secret_key != settings.SECRET_KEY:
        return await ApiSuccessResponse(False, '无权限')
    record_obj = await db.get(RbStudentTempinvitation, transfer_id)
    if not record_obj:
        return await ApiSuccessResponse(False, '无此记录')
    record_obj.Status = 1
    record_obj.UpdateTime = datetime.now()
    record_obj.UpdateBy = user_id
    db.add(record_obj)
    await db.commit()
    return await ApiSuccessResponse(True)

# # 查询已付费转班规则
# @router.get("/class_transfer_rule_paid")
# async def get_class_transfer_rule_paid(
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
#     conf: dict = Depends(get_config),
# ):
#     """
#     # 查询已付费转班规则
#     """
#     subject_map = conf.get("subject_map")
#     grade_map = conf.get("grade_map")
#     data = await class_transfer_rule_paid_module(db)
#     return await ApiSuccessResponse({
#         "subject_map": subject_map,
#         "grade_map": grade_map,
#         "data": data,
#     })

# # 新增付费转班规则
# @router.post("/class_transfer_rule_paid")
# async def create_class_transfer_rule_paid(
#     params: ClassTransferRulePaidBase,
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
# ):
#     """
#     # 新增付费转班规则
#     """
#     await erp_class_change_rules_paid.create(db, commit=False, **{
#         "p_grade_id": params.p_grade_id,
#         "grade_id": params.grade_id,
#         "subject_id": params.subject_id,
#         "cate_id": params.cate_id,
#         "target_cate_ids": params.target_cate_ids,
#         "target_subject_ids": params.target_subject_ids,
#         "target_grade_ids": params.target_grade_ids,
#         "description": params.description,
#         "is_enabled": params.is_enabled,
#         "create_by": user.id,
#         "update_by": user.id,
#         "create_time": datetime.now(),
#         "update_time": datetime.now(),
#     })
#     await db.commit()
    
#     return await ApiSuccessResponse(True)


# # 更新付费转班规则
# @router.put("/class_transfer_rule_paid/{rule_id}")
# async def update_class_transfer_rule_paid(
#     rule_id: int,
#     params: ClassTransferRulePaidBase,
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
# ):
#     """
#     # 更新付费转班规则
#     """
#     rule = await erp_class_change_rules_paid.get_by_id(db, rule_id)
#     if not rule:
#         return await ApiSuccessResponse(False, '无此规则')
#     if params.p_grade_id and params.p_grade_id>0:
#         rule.p_grade_id = params.p_grade_id
#     if params.grade_id and params.grade_id>0:
#         rule.grade_id = params.grade_id
#     if params.subject_id and params.subject_id>0:
#         rule.subject_id = params.subject_id
#     if params.cate_id and params.cate_id>0:
#         rule.cate_id = params.cate_id
#     if params.target_cate_ids is not None:
#         rule.target_cate_ids = params.target_cate_ids
#     if params.target_subject_ids is not None:
#         rule.target_subject_ids = params.target_subject_ids
#     if params.target_grade_ids is not None:
#         rule.target_grade_ids = params.target_grade_ids
#     if params.description is not None:
#         rule.description = params.description
#     if params.is_enabled is not None:
#         rule.is_enabled = params.is_enabled
#     rule.update_by = user.id
#     rule.update_time = datetime.now()
#     await db.commit()
#     return await ApiSuccessResponse(True)


# # 删除付费转班规则
# @router.delete("/class_transfer_rule_paid/{rule_id}")
# async def delete_class_transfer_rule_paid(
#     rule_id: int,
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
# ):
#     """
#     # 删除付费转班规则
#     """
#     rule = await erp_class_change_rules_paid.get_by_id(db, rule_id)
#     if not rule:
#         return await ApiSuccessResponse(False, '无此规则')
#     rule.disable = 1
#     rule.update_by = user.id
#     rule.update_time = datetime.now()
#     await db.commit()
#     return await ApiSuccessResponse(True)

# # 查询未付费转班规则
# @router.get("/class_transfer_rule_unpaid")
# async def get_class_transfer_rule_unpaid(
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
#     conf: dict = Depends(get_config),
# ):
#     """
#     # 查询未付费转班规则
#     """
#     subject_map = conf.get("subject_map")
#     grade_map = conf.get("grade_map")
    
#     # 获取所有未付费转班规则
#     erp_class_change_rules = CF.get_crud(ErpClassChangeRules)
#     data = await erp_class_change_rules.get_many(db, raw=[
#         ErpClassChangeRules.disable == 0
#     ])
    
#     return await ApiSuccessResponse({
#         "subject_map": subject_map,
#         "grade_map": grade_map,
#         "data": data,
#     })

# # 新增未付费转班规则
# @router.post("/class_transfer_rule_unpaid")
# async def create_class_transfer_rule_unpaid(
#     params: ClassTransferRulePaidBase,  # 复用相同的请求模型
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
# ):
#     """
#     # 新增未付费转班规则
#     """
#     erp_class_change_rules = CF.get_crud(ErpClassChangeRules)
#     await erp_class_change_rules.create(db, commit=False, **{
#         "p_grade_id": params.p_grade_id,
#         "grade_id": params.grade_id,
#         "subject_id": params.subject_id,
#         "cate_id": params.cate_id,
#         "target_cate_ids": params.target_cate_ids,
#         "target_subject_ids": params.target_subject_ids,
#         "target_grade_ids": params.target_grade_ids,
#         "description": params.description,
#         "is_enabled": params.is_enabled,
#         "create_by": user.id,
#         "update_by": user.id,
#         "create_time": datetime.now(),
#         "update_time": datetime.now(),
#     })
#     await db.commit()
    
#     return await ApiSuccessResponse(True)

# # 更新未付费转班规则
# @router.put("/class_transfer_rule_unpaid/{rule_id}")
# async def update_class_transfer_rule_unpaid(
#     rule_id: int,
#     params: ClassTransferRulePaidBase,  # 复用相同的请求模型
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
# ):
#     """
#     # 更新未付费转班规则
#     """
#     erp_class_change_rules = CF.get_crud(ErpClassChangeRules)
#     rule = await erp_class_change_rules.get_by_id(db, rule_id)
#     if not rule:
#         return await ApiSuccessResponse(False, '无此规则')
    
#     if params.p_grade_id and params.p_grade_id>0:
#         rule.p_grade_id = params.p_grade_id
#     if params.grade_id and params.grade_id>0:
#         rule.grade_id = params.grade_id
#     if params.subject_id and params.subject_id>0:
#         rule.subject_id = params.subject_id
#     if params.cate_id and params.cate_id>0:
#         rule.cate_id = params.cate_id
#     if params.target_cate_ids is not None:
#         rule.target_cate_ids = params.target_cate_ids
#     if params.target_subject_ids is not None:
#         rule.target_subject_ids = params.target_subject_ids
#     if params.target_grade_ids is not None:
#         rule.target_grade_ids = params.target_grade_ids
#     if params.description is not None:
#         rule.description = params.description
#     if params.is_enabled is not None:
#         rule.is_enabled = params.is_enabled
    
#     rule.update_by = user.id
#     rule.update_time = datetime.now()
#     await db.commit()
    
#     return await ApiSuccessResponse(True)

# # 删除未付费转班规则
# @router.delete("/class_transfer_rule_unpaid/{rule_id}")
# async def delete_class_transfer_rule_unpaid(
#     rule_id: int,
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
# ):
#     """
#     # 删除未付费转班规则
#     """
#     erp_class_change_rules = CF.get_crud(ErpClassChangeRules)
#     rule = await erp_class_change_rules.get_by_id(db, rule_id)
#     if not rule:
#         return await ApiSuccessResponse(False, '无此规则')
    
#     rule.disable = 1
#     rule.update_by = user.id
#     rule.update_time = datetime.now()
#     await db.commit()
    
#     return await ApiSuccessResponse(True)

# # 查询调课规则
# @router.get("/class_reschedule_rule")
# async def get_class_reschedule_rule(
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
#     conf: dict = Depends(get_config),
# ):
#     """
#     # 查询调课规则
#     """
#     subject_map = conf.get("subject_map")
#     grade_map = conf.get("grade_map")
    
#     # 获取所有调课规则
#     erp_class_reschedule_rules = CF.get_crud(ErpClassRescheduleRules)
#     data = await erp_class_reschedule_rules.get_many(db, raw=[
#         ErpClassRescheduleRules.disable == 0
#     ])
    
#     return await ApiSuccessResponse({
#         "subject_map": subject_map,
#         "grade_map": grade_map,
#         "data": data,
#     })

# # 新增调课规则
# @router.post("/class_reschedule_rule")
# async def create_class_reschedule_rule(
#     params: ClassTransferRulePaidBase,  # 复用相同的请求模型
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
# ):
#     """
#     # 新增调课规则
#     """
#     erp_class_reschedule_rules = CF.get_crud(ErpClassRescheduleRules)
#     await erp_class_reschedule_rules.create(db, commit=False, **{
#         "p_grade_id": params.p_grade_id,
#         "grade_id": params.grade_id,
#         "subject_id": params.subject_id,
#         "cate_id": params.cate_id,
#         "target_cate_ids": params.target_cate_ids,
#         "target_subject_ids": params.target_subject_ids,
#         "target_grade_ids": params.target_grade_ids,
#         "description": params.description,
#         "is_enabled": params.is_enabled,
#         "create_by": user.id,
#         "update_by": user.id,
#         "create_time": datetime.now(),
#         "update_time": datetime.now(),
#     })
#     await db.commit()
    
#     return await ApiSuccessResponse(True)

# # 更新调课规则
# @router.put("/class_reschedule_rule/{rule_id}")
# async def update_class_reschedule_rule(
#     rule_id: int,
#     params: ClassTransferRulePaidBase,  # 复用相同的请求模型
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
# ):
#     """
#     # 更新调课规则
#     """
#     erp_class_reschedule_rules = CF.get_crud(ErpClassRescheduleRules)
#     rule = await erp_class_reschedule_rules.get_by_id(db, rule_id)
#     if not rule:
#         return await ApiSuccessResponse(False, '无此规则')
    
#     if params.p_grade_id and params.p_grade_id>0:
#         rule.p_grade_id = params.p_grade_id
#     if params.grade_id and params.grade_id>0:
#         rule.grade_id = params.grade_id
#     if params.subject_id and params.subject_id>0:
#         rule.subject_id = params.subject_id
#     if params.cate_id and params.cate_id>0:
#         rule.cate_id = params.cate_id
#     if params.target_cate_ids is not None:
#         rule.target_cate_ids = params.target_cate_ids
#     if params.target_subject_ids is not None:
#         rule.target_subject_ids = params.target_subject_ids
#     if params.target_grade_ids is not None:
#         rule.target_grade_ids = params.target_grade_ids
#     if params.description is not None:
#         rule.description = params.description
#     if params.is_enabled is not None:
#         rule.is_enabled = params.is_enabled
    
#     rule.update_by = user.id
#     rule.update_time = datetime.now()
#     await db.commit()
    
#     return await ApiSuccessResponse(True)

# # 删除调课规则
# @router.delete("/class_reschedule_rule/{rule_id}")
# async def delete_class_reschedule_rule(
#     rule_id: int,
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
# ):
#     """
#     # 删除调课规则
#     """
#     erp_class_reschedule_rules = CF.get_crud(ErpClassRescheduleRules)
#     rule = await erp_class_reschedule_rules.get_by_id(db, rule_id)
#     if not rule:
#         return await ApiSuccessResponse(False, '无此规则')
    
#     rule.disable = 1
#     rule.update_by = user.id
#     rule.update_time = datetime.now()
#     await db.commit()
    
#     return await ApiSuccessResponse(True)



# 统一的规则查询接口
@router.get("/class_rules/{rule_type}")
async def get_class_rules(
    rule_type: str,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
    conf: dict = Depends(get_config),
):
    """
    # 查询班级规则
    - rule_type: 规则类型
        - paid_transfer: 已付费转班规则
        - unpaid_transfer: 未付费转班规则
        - reschedule: 调课规则
    """
    subject_map = conf.get("subject_map")
    grade_map = conf.get("grade_map")
    
    # 根据规则类型选择不同的表
    if rule_type == RuleType.PAID_TRANSFER.value:
        data = await class_transfer_rule_paid_module(db)
    elif rule_type == RuleType.UNPAID_TRANSFER.value:
        # 获取所有未付费转班规则
        erp_class_change_rules = CF.get_crud(ErpClassChangeRules)
        data = await erp_class_change_rules.get_many(db, raw=[
            ErpClassChangeRules.disable == 0
        ])
    elif rule_type == RuleType.RESCHEDULE.value:
        # 获取所有调课规则
        erp_class_reschedule_rules = CF.get_crud(ErpClassRescheduleRules)
        data = await erp_class_reschedule_rules.get_many(db, raw=[
            ErpClassRescheduleRules.disable == 0
        ])
    else:
        return await ApiFailedResponse("无效的规则类型")
    

    # 将category 作为map附加到数据中
    category_objs = await erp_course_category.get_many(db)
    category_map = {str(obj.id): obj.category_name for obj in category_objs}
    
    return await ApiSuccessResponse({
        "subject_map": subject_map,
        "grade_map": grade_map,
        "category_map": category_map,
        "data": data,
    })

# 统一的规则创建接口
@router.post("/class_rules/{rule_type}")
async def create_class_rule(
    rule_type: str,
    params: ClassTransferRulePaidBase,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 新增班级规则
    - rule_type: 规则类型
        - paid_transfer: 已付费转班规则
        - unpaid_transfer: 未付费转班规则
        - reschedule: 调课规则
    """
    # 所有规则类型共用的基础数据
    rule_data = {
        "p_grade_id": params.p_grade_id,
        "grade_id": params.grade_id,
        "subject_id": params.subject_id,
        "cate_id": params.cate_id,
        "target_cate_ids": params.target_cate_ids,
        "target_subject_ids": params.target_subject_ids,
        "target_grade_ids": params.target_grade_ids,
        "description": params.description,
        "is_enabled": params.is_enabled,
        "create_by": user.uid,
        "update_by": user.uid,
        "create_time": datetime.now(),
        "update_time": datetime.now(),
    }
    
    # 根据规则类型选择不同的表
    if rule_type == RuleType.PAID_TRANSFER.value:
        await erp_class_change_rules_paid.create(db, commit=False, **rule_data)
    elif rule_type == RuleType.UNPAID_TRANSFER.value:
        erp_class_change_rules = CF.get_crud(ErpClassChangeRules)
        await erp_class_change_rules.create(db, commit=False, **rule_data)
    elif rule_type == RuleType.RESCHEDULE.value:
        erp_class_reschedule_rules = CF.get_crud(ErpClassRescheduleRules)
        await erp_class_reschedule_rules.create(db, commit=False, **rule_data)
    else:
        return await ApiFailedResponse("无效的规则类型")
    
    await db.commit()
    return await ApiSuccessResponse(True)

# 统一的规则更新接口
@router.put("/class_rules/{rule_type}/{rule_id}")
async def update_class_rule(
    rule_type: str,
    rule_id: int,
    params: ClassTransferRulePaidBase,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 更新班级规则
    - rule_type: 规则类型
        - paid_transfer: 已付费转班规则
        - unpaid_transfer: 未付费转班规则
        - reschedule: 调课规则
    """
    # 根据规则类型选择不同的表
    if rule_type == RuleType.PAID_TRANSFER.value:
        rule = await erp_class_change_rules_paid.get_by_id(db, rule_id)
        if not rule:
            return await ApiFailedResponse("无此规则")
    elif rule_type == RuleType.UNPAID_TRANSFER.value:
        erp_class_change_rules = CF.get_crud(ErpClassChangeRules)
        rule = await erp_class_change_rules.get_by_id(db, rule_id)
        if not rule:
            return await ApiFailedResponse("无此规则")
    elif rule_type == RuleType.RESCHEDULE.value:
        erp_class_reschedule_rules = CF.get_crud(ErpClassRescheduleRules)
        rule = await erp_class_reschedule_rules.get_by_id(db, rule_id)
        if not rule:
            return await ApiFailedResponse("无此规则")
    else:
        return await ApiFailedResponse("无效的规则类型")
    
    # 更新规则数据
    if params.p_grade_id and params.p_grade_id > 0:
        rule.p_grade_id = params.p_grade_id
    if params.grade_id and params.grade_id > 0:
        rule.grade_id = params.grade_id
    if params.subject_id and params.subject_id > 0:
        rule.subject_id = params.subject_id
    if params.cate_id and params.cate_id > 0:
        rule.cate_id = params.cate_id
    if params.target_cate_ids is not None:
        rule.target_cate_ids = params.target_cate_ids
    if params.target_subject_ids is not None:
        rule.target_subject_ids = params.target_subject_ids
    if params.target_grade_ids is not None:
        rule.target_grade_ids = params.target_grade_ids
    if params.description is not None:
        rule.description = params.description
    if params.is_enabled is not None:
        rule.is_enabled = params.is_enabled
    
    rule.update_by = user.uid
    rule.update_time = datetime.now()
    await db.commit()
    
    return await ApiSuccessResponse(True)

# 统一的规则删除接口
@router.delete("/class_rules/{rule_type}/{rule_id}")
async def delete_class_rule(
    rule_type: str,
    rule_id: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 删除班级规则
    - rule_type: 规则类型
        - paid_transfer: 已付费转班规则
        - unpaid_transfer: 未付费转班规则
        - reschedule: 调课规则
    """
    # 根据规则类型选择不同的表
    if rule_type == RuleType.PAID_TRANSFER.value:
        rule = await erp_class_change_rules_paid.get_by_id(db, rule_id)
        if not rule:
            return await ApiFailedResponse("无此规则")
    elif rule_type == RuleType.UNPAID_TRANSFER.value:
        erp_class_change_rules = CF.get_crud(ErpClassChangeRules)
        rule = await erp_class_change_rules.get_by_id(db, rule_id)
        if not rule:
            return await ApiFailedResponse("无此规则")
    elif rule_type == RuleType.RESCHEDULE.value:
        erp_class_reschedule_rules = CF.get_crud(ErpClassRescheduleRules)
        rule = await erp_class_reschedule_rules.get_by_id(db, rule_id)
        if not rule:
            return await ApiFailedResponse("无此规则")
    else:
        return await ApiFailedResponse("无效的规则类型")
    
    # 标记为删除
    rule.disable = 1
    rule.update_by = user.uid
    rule.update_time = datetime.now()
    await db.commit()
    
    return await ApiSuccessResponse(True)

# # 为了向后兼容的重定向接口（可选）
# @router.get("/class_transfer_rule_paid")
# async def get_class_transfer_rule_paid_redirect(
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
#     conf: dict = Depends(get_config),
# ):
#     """
#     # 查询已付费转班规则（重定向到统一接口）
#     """
#     return await get_class_rules(RuleType.PAID_TRANSFER.value, db, user, conf)

# @router.get("/class_transfer_rule_unpaid")
# async def get_class_transfer_rule_unpaid_redirect(
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
#     conf: dict = Depends(get_config),
# ):
#     """
#     # 查询未付费转班规则（重定向到统一接口）
#     """
#     return await get_class_rules(RuleType.UNPAID_TRANSFER.value, db, user, conf)

# @router.get("/class_reschedule_rule")
# async def get_class_reschedule_rule_redirect(
#     db: AsyncSession = Depends(get_default_db),
#     user: UserDict = Depends(role_required([])),
#     conf: dict = Depends(get_config),
# ):
#     """
#     # 查询调课规则（重定向到统一接口）
#     """
#     return await get_class_rules(RuleType.RESCHEDULE.value, db, user, conf)



# 续报规则
@router.get("/class_renew_rule")
async def get_class_renew_rule(
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 查询续报规则
    """
    data = await class_renew_rule_module(db)
    new_data = []
    for item in data:
        new_item = dict(item)
        # 附加报价单数量
        conditions = {
            "rule_id": item["id"],
        }
        new_item["offer_count"] = await get_offer_count(db, conditions=conditions, raw_conditions=[])
        new_data.append(new_item)
    return await ApiSuccessResponse(new_data)


# 修改续报规则
@router.put("/class_renew_rule/{rule_id}")
async def update_class_renew_rule(
    rule_id: int,
    params: ClassRenewRuleBase,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 修改续报规则
    """
    erp_class_renew_rules = CF.get_crud(ErpClassRenewRules)
    exist_rule = await erp_class_renew_rules.get_by_id(db, rule_id)
    if not exist_rule:
        return await ApiFailedResponse("无此续报规则")
    
    if params.current_class_id and params.current_class_id > 0:
        exist_rule.current_class_id = params.current_class_id
    if params.current_teacher_id and params.current_teacher_id > 0:
        exist_rule.current_teacher_id = params.current_teacher_id
    if params.term_id and params.term_id > 0:
        exist_rule.term_id = params.term_id
    if params.next_class_id and params.next_class_id > 0:
        exist_rule.next_class_id = params.next_class_id
    if params.next2_class_id and params.next2_class_id > 0:
        exist_rule.next2_class_id = params.next2_class_id
    if params.start_time:
        exist_rule.start_time = params.start_time
    if params.end_time:
        exist_rule.end_time = params.end_time
    if params.signup_start:
        exist_rule.signup_start = params.signup_start
        
    exist_rule.update_by = user.uid
    exist_rule.update_time = datetime.now()
    await db.commit()
    
    return await ApiSuccessResponse(True)
        
        
# 新增续报规则
@router.post("/class_renew_rule")
async def create_class_renew_rule(
    params: ClassRenewRuleBase,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 新增续报规则
    """
    erp_class_renew_rules = CF.get_crud(ErpClassRenewRules)
    await erp_class_renew_rules.create(db, commit=False, **{
        "current_class_id": params.current_class_id,
        "current_teacher_id": params.current_teacher_id,
        "term_id": params.term_id,
        "next_class_id": params.next_class_id,
        "next2_class_id": params.next2_class_id,
        "start_time": params.start_time,
        "end_time": params.end_time,
        "signup_start": params.signup_start,
        "create_by": user.uid,
        "update_by": user.uid,
    })
    await db.commit()
    return await ApiSuccessResponse(True)

# 删除续报规则
@router.delete("/class_renew_rule/{rule_id}")
async def delete_class_renew_rule(
    rule_id: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 删除续报规则
    """
    erp_class_renew_rules = CF.get_crud(ErpClassRenewRules)
    exist_rule = await erp_class_renew_rules.get_by_id(db, rule_id)
    if not exist_rule:
        return await ApiFailedResponse("无此续报规则")

    exist_rule.disable = 1
    exist_rule.update_by = user.uid
    exist_rule.update_time = datetime.now()
    await db.commit()
    
    return await ApiSuccessResponse(True)


# 报错的规则重新运行
@router.post("/class_renew_rule/retry/{rule_id}")
async def retry_class_renew_rule(
    rule_id: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 报错的规则重新运行
    """
    erp_class_renew_rules = CF.get_crud(ErpClassRenewRules)
    exist_rule = await erp_class_renew_rules.get_by_id(db, rule_id)
    if not exist_rule:
        return await ApiFailedResponse("无此续报规则")
    if exist_rule.disable == 1:
        return await ApiFailedResponse("规则已禁用")
    if exist_rule.run_status != 2:
        return await ApiFailedResponse("只能运行报错规则")
    this_rule_obj = copy.deepcopy(exist_rule)
    # asyncio.create_task(create_renew_order_for_class(this_rule_obj))
    return await ApiSuccessResponse("已运行任务,但此处的任务可能已经注释，请开启后再试")


# 线上线下互转
@router.post("/class_online_offline_transfer/{order_student_id}")
async def class_online_offline_transfer(
    order_student_id: int,
    is_online: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 线上线下互转
    """
    erp_order_student = CF.get_crud(ErpOrderStudent)
    exist_order_student = await erp_order_student.get_by_id(db, order_student_id)
    if not exist_order_student:
        return await ApiFailedResponse("无此学生")
    exist_order_student.is_online = is_online
    exist_order_student.update_by = user.uid
    exist_order_student.update_time = datetime.now()
    # 记录班级日志
    await add_class_log(db, exist_order_student.class_id, "线上线下互转", 12, f" {user.employee_name} 线上线下互转{exist_order_student.stu_id}")
    await db.commit()
    return await ApiSuccessResponse(True)


# 移除学生
@router.post("/class_remove_student")
async def class_remove_student(
    order_student_id: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 移除学生
    """
    erp_order_student = CF.get_crud(ErpOrderStudent)
    exist_order_student = await erp_order_student.get_by_id(db, order_student_id)
    if not exist_order_student:
        return await ApiFailedResponse("无此学生")
    exist_order_student.student_state = StudentState.CANCEL.value
    exist_order_student.update_by = user.uid
    exist_order_student.update_time = datetime.now()
    # 记录班级日志
    await add_class_log(db, exist_order_student.class_id, "移除学生", 11, f" {user.employee_name} 移除学生{exist_order_student.stu_id}")
    await db.commit()
    return await ApiSuccessResponse(True)


# 修改课次
@router.post("/modify_class_complete")
async def modify_class_complete(
    params: ModifyClassCompleteBase,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 修改课次
    """
    erp_order_student = CF.get_crud(ErpOrderStudent)
    exist_order_student = await erp_order_student.get_by_id(db, params.order_student_id)
    if not exist_order_student:
        return await ApiFailedResponse("无此学生")
    if params.modify_type == 1:
        msg = f"{user.employee_name} 增加{params.modify_num}课次"
        exist_order_student.complete_hours += params.modify_num
    else:
        msg = f"{user.employee_name} 减少{params.modify_num}课次"
        exist_order_student.complete_hours -= params.modify_num
    exist_order_student.update_by = user.uid
    exist_order_student.update_time = datetime.now()
    # 记录班级日志
    await add_class_log(db, exist_order_student.class_id, "修改课次", 10, msg)

    await db.commit()
    return await ApiSuccessResponse(True)


# 查询班级日志
@router.get("/class_log")
async def get_class_log(
    class_id: int,
    page: int = 1,
    page_size: int = 10,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 查询班级日志
    """
    erp_class_log = CF.get_crud(ErpClassLog)
    logs = await erp_class_log.get_many_with_pagination(db, page=page, page_size=page_size, condition={"class_id": class_id})
    count = await erp_class_log.count(db, condition={"class_id": class_id})
    return await ApiSuccessResponse({
        "data": logs,
        "count": count,
    })


# 查询教室占用情况
@router.get("/class_room_occupy")
async def get_class_room_occupy(
    start_time: str,
    end_time: str,
    class_room_ids: str=None,   # 教室id列表，用逗号分隔
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 查询教室占用情况
    > 指定时间内, 教室id列表, 返回教室占用情况, 按天排列，分为上午、中午、下午、晚上三个时间段
    看开始时间， 在以下时间段内， 就属于哪个时间段
        上午: 11:00以前
        中午：11:00-13:00以前
        下午：13:00以后-18:00以前
        晚上：18:00以后

    ## 返回格式：
    ```
    {
        "data": [
            {
                "class_room_id": 1,
                "class_room_name": "教室1",
                "detail": [
                    {
                        "date": "2025-06-27",
                        "value": {
                            "morning": [
                                {
                                    "start_time": "11:00",
                                    "end_time": "13:00",
                                    "class_id": 1,
                                    "class_name": "班级1",
                                    "class_room_id": 1,
                                    "class_room_name": "教室1",
                                    "teacher_name": "老师1",
                                }
                            ],
                            "afternoon": [
                                {
                                    "start_time": "13:00",
                                    "end_time": "18:00",
                                    "class_id": 1,
                                    "class_name": "班级1",
                                    "class_room_id": 1,
                                    "class_room_name": "教室1",
                                    "teacher_name": "老师1",
                                }
                            ],
                        }
                    }
                ]
            }
        ]
    }
    ```
    """
    from app_teach.modules import get_classroom_occupancy_module
    
    # 调用模块函数处理业务逻辑
    success, result = await get_classroom_occupancy_module(db, class_room_ids, start_time, end_time)
    
    if not success:
        return await ApiFailedResponse(result)
    
    return await ApiSuccessResponse(result)


# 查询班级二维码
@router.get(f"/class_qr_code")
async def query_class_qr_code(
        class_id: int,
        width: int = 430,
        auto_color: bool = False,
        is_hyaline: bool = False,
        force_regenerate: bool = False,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    查询班级二维码
    
    Args:
        class_id: 班级ID
        width: 二维码宽度，范围280-1280px，默认430
        auto_color: 是否自动配置线条颜色，默认False
        is_hyaline: 是否透明底色，默认False
        force_regenerate: 是否强制重新生成，默认False
    
    Returns:
        二维码URL或错误信息
    """
    from utils.tencent.wechatTools import WeChatMiniProgram
    
    # 验证参数
    if width < 280 or width > 1280:
        return await ApiFailedResponse('二维码宽度必须在280-1280px之间')
    
    # 查询班级信息
    class_obj = await erp_class.get_by_id(db, class_id)
    if not class_obj:
        return await ApiFailedResponse('无此班级')
    
    # 如果已有二维码且不强制重新生成，直接返回
    if class_obj.qr_code and not force_regenerate:
        return await ApiSuccessResponse({
            "qr_code_url": class_obj.qr_code,
            "class_id": class_id,
            "class_name": class_obj.class_name,
            "regenerated": False
        })
    
    try:
        # 构建小程序页面路径
        path = f'pagesCourse/pages/CourseDetail/CourseDetail?classId={class_id}&isOnline=0'
        
        # 创建小程序实例并生成二维码
        miniprogram = WeChatMiniProgram()
        qr_data = await miniprogram.generate_qr_code(
            path=path,
            width=width,
            auto_color=auto_color,
            is_hyaline=is_hyaline
        )
        
        # 上传到文件服务器
        s3 = S3Client()
        file_name = f'class_qr_code_{class_id}_{int(time.time())}.png'
        qr_url = await s3.upload_file(FILE_SERVER['bucket'], file_name, qr_data)
        
        # 更新班级二维码URL
        class_obj.qr_code = qr_url
        class_name = copy.deepcopy(class_obj.class_name)
        await db.commit()
        
        return await ApiSuccessResponse({
            "qr_code_url": qr_url,
            "class_id": class_id,
            "class_name": class_name,
            "regenerated": True
        })
        
    except Exception as e:
        settings.logger.error(f"生成班级二维码失败，class_id: {class_id}, 错误: {str(e)}")
        error_msg = str(e)
        return await ApiFailedResponse(f'生成二维码时发生未知错误: {error_msg}')
    

