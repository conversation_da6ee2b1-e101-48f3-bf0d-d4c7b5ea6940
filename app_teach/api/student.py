from datetime import datetime

from fastapi import APIRouter, Depends
from pydantic import BaseModel
from sqlalchemy import or_, select, and_
from sqlalchemy.ext.asyncio import AsyncSession

from app_teach.crud import get_student_list_crud, get_student_count, get_student_discount_and_orders, get_order_courses
from app_teach.modules import generate_serial_number
from app_teach.serializer import ErpStudentEntity
from models.m_discount import ErpStudentCouponCourse, ErpStudentDiscountCoupon, ErpStudentDiscountFixed
from models.m_student import ErpStudent, ErpStudentWechat
from utils.db.account_handler import UserDict, get_current_active_user
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db
from utils.enum.enum_order import DiscountStatus
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

router = APIRouter(prefix="/stu", tags=["学生"])

erp_student = CRUD(ErpStudent)
erp_student_wechat = CRUD(ErpStudentWechat)
erp_student_discount_fixed = CRUD(ErpStudentDiscountFixed)
erp_student_discount_coupon = CRUD(ErpStudentDiscountCoupon)
erp_student_discount_coupon_course = CRUD(ErpStudentCouponCourse)


# 分页查询学生列表
@router.get("/student")
async def query_student(
        page: int = 1,
        page_size: int = 10,
        keyword: str = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询学生列表
    """
    # 获取学生基本数据
    raw_data = await get_student_list_crud(db, page=page, page_size=page_size, keyword=keyword)
    # 将 Row 对象转换为字典列表
    data = [dict(row) for row in raw_data]
    # 获取所有学生ID
    student_ids = [student['stu_id'] for student in data]
    
    # 获取优惠和订单信息
    students_with_coupons, students_with_fixed, student_orders = await get_student_discount_and_orders(db, student_ids)
    
    # 查询每个学生的微信绑定数据
    student_wechat = {}
    if student_ids:
        wechat_stmt = (
            select(
                ErpStudentWechat.stu_id,
                ErpStudentWechat.id,
                ErpStudentWechat.wechat_open_id
            )
            .where(
                ErpStudentWechat.stu_id.in_(student_ids),
                ErpStudentWechat.disable == 0
            )
        )
        wechat_result = await db.execute(wechat_stmt)
        wechat_data = wechat_result.fetchall()
        
        # 将微信数据组织成以stu_id为键的字典
        for wechat in wechat_data:
            if wechat.stu_id not in student_wechat:
                student_wechat[wechat.stu_id] = []
            student_wechat[wechat.stu_id].append({
                'id': wechat.id,
                'wechat_open_id': wechat.wechat_open_id
            })
    
    # 将折扣、订单和微信绑定信息添加到学生数据中
    for student in data:
        student['has_coupon'] = student['stu_id'] in students_with_coupons
        student['has_fixed_discount'] = student['stu_id'] in students_with_fixed
        student['orders'] = student_orders.get(student['stu_id'], [])
        student['wechat_list'] = student_wechat.get(student['stu_id'], [])
    
    # 获取总数
    total = await get_student_count(db, keyword=keyword)
    return await ApiSuccessResponse(
        {
            "data": data,
            "total": total
        }
    )


# 查询order_ids对应的所有课程
@router.get("/order_courses")
async def get_order_courses_api(
    order_ids: str,
    user: UserDict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询order_ids对应的所有课程(可用于学生报名课程查询)
    
    Args:
        order_ids: 订单ID列表,以逗号分隔
        
    Returns:
        课程信息列表
    """
    # 将order_ids字符串转为列表
    order_id_list = [int(x) for x in order_ids.split(',')]
    
    # 查询课程信息
    courses = await get_order_courses(db, order_id_list)
    
    return await ApiSuccessResponse({
        "data": [dict(course) for course in courses]
    })


# 查询学生详情
@router.get("/student/{stu_id}")
async def query_student_detail(
        stu_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询学生详情
    """
    data = await erp_student.get_by_id(db, stu_id)
    # 获取优惠券和固定折扣
    
    discount_fixed = await erp_student_discount_fixed.get_one(db, stu_id=stu_id)
    discount_coupon = await erp_student_discount_coupon.get_many(db, {'stu_id': stu_id, 'status': DiscountStatus.AVAILABLE.value})
    # 学生的优惠券对应课程
    coupon_course_map = {}
    for coupon in discount_coupon:
        coupon_course = await erp_student_discount_coupon_course.get_many(db, {'coupon_id': coupon.id})
        course_ids = [course.course_id for course in coupon_course]
        coupon_course_map[coupon.id] = course_ids
    # 组装数据
    data.discount_fixed = discount_fixed
    data.discount_coupon = coupon_course_map

    return await ApiSuccessResponse(data)


# 创建学生
@router.post("/student")
async def create_student(
        entity: ErpStudentEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 创建学生
    """
    # 根据ErpStudent字段创建学生
    stu_serial = generate_serial_number(db, ErpStudent)
    student_data = {
        'stu_username': entity.stu_username,
        'stu_name': entity.stu_name,
        'stu_birth': entity.stu_birth,
        'stu_gender': entity.stu_gender,
        'stu_avatar': entity.stu_avatar,
        'stu_area': entity.stu_area,
        'stu_address': entity.stu_address,
        'stu_grade': entity.stu_grade,
        'stu_idcard': entity.stu_idcard,
        'stu_school_name': entity.stu_school_name,
        'stu_wallet_amount': 0.00,
        'stu_serial': stu_serial,
        'classin_uid': 0,
        'classin_sync': 0,
        'update_time': datetime.now(),
        'create_time': datetime.now(),
        'disable': 0,
        'mall_user_id': 0
    }
    obj = await erp_student.create(db, commit=True, **student_data)
    return await ApiSuccessResponse(obj)


# 删除学生
@router.delete("/student/{stu_id}")
async def delete_student(
        stu_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除学生
    """
    obj = await erp_student.delete_one(db, stu_id)
    return await ApiSuccessResponse(obj)


# 删除微信绑定
@router.delete("/student/wechat/{wechat_bind_id}")
async def delete_student_wechat(
        wechat_bind_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 删除微信绑定
    - 删除指定学生的所有微信绑定记录
    """
    # 检查学生是否存在
    bind_obj = await erp_student_wechat.get_by_id(db, wechat_bind_id)
    if not bind_obj:
        return await ApiFailedResponse("微信绑定不存在")
    bind_obj.disable = 1
    bind_obj.update_time = datetime.now()
    
    # 提交事务
    await db.commit()
    
    return await ApiSuccessResponse(True)




# 拉黑学生
@router.post("/student/blacklist/{stu_id}")
async def blacklist_student(
        stu_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 拉黑学生
    """
    obj = await erp_student.get_by_id(db, stu_id)
    if not obj:
        return await ApiFailedResponse("学生不存在")
    obj.is_blacklist = 1
    obj.update_time = datetime.now()
    await db.commit()
    return await ApiSuccessResponse(True)


# 取消拉黑学生
@router.post("/student/cancel_blacklist/{stu_id}")
async def cancel_blacklist_student(
        stu_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 取消拉黑学生
    """
    obj = await erp_student.get_by_id(db, stu_id)
    if not obj:
        return await ApiFailedResponse("学生不存在")
    obj.is_blacklist = 0
    obj.update_time = datetime.now()
    await db.commit()
    return await ApiSuccessResponse(True)