import asyncio
import copy
from datetime import datetime
from io import BytesIO
from sqlalchemy import or_
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Depends, UploadFile

from app_teach.crud import category_map, course_subject_configuration, course_configuration
from app_teach.modules import download_custom_exam_template_replace, travel_custom_exam_excel
from app_teach.serializer import StudyReport, StudyClassReport
from models.m_class import ErpClass, ErpExam, ErpExamQuestions, ErpExamScore, ErpStudyReport, ErpStudyClassReport
from models.m_order import ErpOrderStudent
from models.m_student import ErpStudent
from models.m_teacher import ErpAccountTeacher
from settings import FILE_SERVER
from utils.db.account_handler import UserDict, role_required
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db, get_uat_db
from utils.db.default_crud import generate_crud_routes
from utils.enum.enum_order import StudentState
from utils.file.minio_handler import S3Client
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

router = APIRouter(prefix="/class_report", tags=["班级报告"])

erp_study_report = CRUD(ErpStudyReport)
erp_study_class_report = CRUD(ErpStudyClassReport)
erp_class = CRUD(ErpClass)
erp_order_student = CRUD(ErpOrderStudent)
erp_student = CRUD(ErpStudent)
erp_account_teacher = CRUD(ErpAccountTeacher)
erp_exam = CRUD(ErpExam)
erp_exam_questions = CRUD(ErpExamQuestions)
erp_exam_score = CRUD(ErpExamScore)



# 创建报告
@router.post("/report")
async def create_report(
        report: StudyReport,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增报告
    """
    obj = await erp_study_report.create(db, commit=True, **{
        "report_name": report.report_name,
        "desc": report.desc,
    })
    return await ApiSuccessResponse(obj)


# 修改报告
@router.put("/report/{report_id}")
async def update_report(
        report_id: int,
        report: StudyReport,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改报告
    """
    await erp_study_report.update_one(db, report_id, {
        "report_name": report.report_name,
        "desc": report.desc,
    })
    return await ApiSuccessResponse(True)


# 删除报告
@router.delete("/report/{report_id}")
async def delete_report(
        report_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除报告
    """
    await erp_study_report.delete_one(db, report_id)
    return await ApiSuccessResponse(True)


# 分页查询报告
@router.get("/report")
async def get_report(
        page: int = 1,
        page_size: int = 10,
        keyword: str = None,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询报告
    """
    raws = [
        or_(
            ErpStudyReport.report_name.like(f"%{keyword}%"),
            ErpStudyReport.desc.like(f"%{keyword}%")
        )
    ]
    data = await erp_study_report.get_many_with_pagination(db, page, page_size, raw=raws)
    count_data = await erp_study_report.get_many(db, raw=raws)

    for i in data:
        # 实时查询报告对应班级数量
        class_count = await erp_study_class_report.get_many(db, {"study_report_id": i.id})
        i.class_count = len(class_count)
    return await ApiSuccessResponse({
        "data": data,
        "total": len(count_data)
    })


# 根据报告Id查询报告
@router.get("/report/{report_id}")
async def get_report_by_id(
        report_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 查询指定报告
    """
    data = await erp_study_report.get_by_id(db, report_id)
    return await ApiSuccessResponse(data)


# 新增报告推送
@router.post("/class_report")
async def create_class_report(
        class_report: StudyClassReport,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增报告推送
    """
    for class_id in class_report.class_ids:
        # 先查询是否已经存在
        exist = await erp_study_class_report.get_one(db,
                                                     study_report_id=class_report.study_report_id,
                                                     class_id=class_id)
        if not exist:
            await erp_study_class_report.create(db, commit=False, **{
                "study_report_id": class_report.study_report_id,
                "class_id": class_id
            })
    await db.commit()
    return await ApiSuccessResponse(True)


# 删除报告推送
@router.delete("/class_report/{study_class_report_id}")
async def delete_class_report(
        study_class_report_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除指定推送报告
    """
    await erp_study_class_report.delete_one(db, study_class_report_id)
    return await ApiSuccessResponse(True)


# # 根据报告Id查询推送班级 分页
# @router.get("/class_report/{study_report_id}")
# async def get_class_report(
#         study_report_id: int,
#         page: int = 1,
#         page_size: int = 10,
#         db: AsyncSession = Depends(get_default_db),
#         uat_db: AsyncSession = Depends(get_uat_db),
#         user: UserDict = Depends(role_required([])),
# ):
#     """
#     # 查询指定报告推送班级
#     """
#     data = await erp_study_class_report.get_many_with_pagination(db, page, page_size,
#                                                                  {"study_report_id": study_report_id}
#                                                                  )
#     class_data = await rb_class.get_many(uat_db)
#     class_dict = {i.ClassId: i for i in class_data}
#     for i in data:
#         i.class_name = class_dict.get(i.class_id).ClassName
#     count_data = await erp_study_class_report.get_many(db, {"study_report_id": study_report_id})

#     return await ApiSuccessResponse({
#         "data": data,
#         "total": len(count_data)
#     })


# # 筛选班级
# @router.get("/class")
# async def get_class(
#         page: int = None,
#         page_size: int = None,

#         subject_id: int = None,
#         category_config_id: int = None,
#         shift_term_id: int = None,
#         class_name: str = None,
#         shift_grade_id: int = None,
#         yyyy: int = None,
#         only_my_class: int = None,
#         shift_class_type: int = None,
#         db: AsyncSession = Depends(get_uat_db),
#         user: UserDict = Depends(role_required([])),
# ):
#     """
#     # 查询班级
#     - yyyy 年份
#     - only_my_class 是否只查询我的班级
#     - shift_class_type 33 长期班 31 短期班
#     """
#     teacher_id = None
#     if only_my_class:
#         teacher = await rb_teacher.get_one(db, TeacherTel=user.username)
#         teacher_id = teacher.TId
#         if not teacher_id:
#             return await ApiFailedResponse("未查询到教师信息, 请联系管理员检查手机号是否正确")
#     data = await query_course(db, page, page_size,
#                               subject_id=subject_id,
#                               category_config_id=category_config_id,
#                               shift_term_id=shift_term_id,
#                               class_name=class_name,
#                               shift_grade_id=shift_grade_id,
#                               yyyy=yyyy,
#                               teacher_id=teacher_id,
#                               shift_class_type=shift_class_type,
#                               )
#     return await ApiSuccessResponse(data)


@router.get('/get_idmap/')
async def _(db: AsyncSession = Depends(get_uat_db),
            user: UserDict = Depends(role_required([])),
            ):
    """
    # 获取各类ID
    ## 返回
    - **shift_term_map**：课程期段
    - **grade_map**：年级
    - **grade_parent_map**：父年级
    - **category_config**：班级类型
    - **subject_map**：科目
    - **course_type**：班型
    """
    course_data = await course_configuration(db, 0)
    course_config = [{"ConfigType": i['ConfigType'], "Id": i['Id'], "ConfigName": i["ConfigName"]} for i in course_data]
    subject_data = await course_subject_configuration(db, 0)
    subject_config = [{"Id": i['Id'], "SubjectName": i["SubjectName"]} for i in subject_data]
    course_category = await category_map(db, 0)
    category_config = [{"CateId": i['CateId'], "CateName": i['CateName']} for i in course_category]

    # 数据收集
    data = {
        "shift_term_ids": [i for i in course_config if i['ConfigType'] == 1],
        "grade_ids": [i for i in course_config if i['ConfigType'] == 2],
        "grade_parent_ids": [i for i in course_config if i['ConfigType'] == 5],
        "category_config_ids": category_config,
        "subject_ids": subject_config,
        "course_type": [i for i in course_config if i['ConfigType'] == 3],
    }
    return await ApiSuccessResponse(data, 'IdMap')



# 下载自定义考试模版
@router.get(f"/download_custom_exam_temp")
async def download_custom_exam_temp(
        class_id: int,
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 下载自定义考试模版
    """
    class_obj = await erp_class.get_by_id(db, class_id)
    order_student_objs = await erp_order_student.get_many(db, raw=[ErpOrderStudent.class_id == class_id,
                                                                  ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value])])
    student_ids = [order_student.stu_id for order_student in order_student_objs]
    student_objs = await erp_student.get_many(db, raw=[ErpStudent.id.in_(student_ids)])
    student_data = [{'class_id': class_id, 
                     'class_name': class_obj.class_name, 
                     'stu_id': student.id, 
                     'stu_name': student.stu_name} for student in student_objs]
    loop = asyncio.get_event_loop()
    filename, content = await loop.run_in_executor(None, download_custom_exam_template_replace, class_obj.class_name, student_data)
    s3 = S3Client()
    url = await s3.upload_file(bucket_name=FILE_SERVER['bucket'], original_filename=filename, file_content=content)
    return await ApiSuccessResponse(url, '自定义考试模版')



# 上传自定义考试成绩
@router.post(f"/upload_custom_exam_score")
async def upload_custom_exam_score(
        file: UploadFile,
        exam_name: str,
        class_id: int,
        upload_type: int = 0,
        type: int =1,
        analysis_type: int =1,
        user: UserDict = Depends(role_required([])),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 上传自定义考试成绩
    
    ## 接口说明
    教师上传自定义考试成绩，支持Excel文件格式
    
    ## 参数说明
    - **file** (UploadFile): 上传的Excel文件
    - **exam_name** (str): 考试名称
    - **class_id** (int): 班级ID
    - **upload_type** (int): 上传类型，0为自定义测验，1为诊断反馈，默认为0
    
    ## 返回格式
    ```json
    {
        "code": 200,
        "msg": "上传成功",
        "data": true
    }
    ```
    
    ## 业务逻辑
    1. 验证班级是否存在且属于当前教师
    2. 读取上传的Excel文件内容
    3. 创建考试记录
    4. 解析Excel数据并创建考试题目和成绩记录
    5. 提交数据库事务
    """
    # # 验证班级是否存在且当前教师有权限访问（主教师或助教）
    # has_permission = await verify_teacher_or_assistant_permission(db, class_id, user.teacher_id)
    # if not has_permission:
    #     return await ApiFailedResponse("班级不存在或无权限访问")
    
    # 读取上传的文件内容
    try:
        content = await file.read()
        virtual_workbook = BytesIO(content)
    except Exception as e:
        return await ApiFailedResponse(f"文件读取失败: {str(e)}")

    erp_account_teacher = CRUD(ErpAccountTeacher)
    # 获取教师账户信息
    account_teacher_obj = await erp_account_teacher.get_one(db, account_id=user.uid)
    if not account_teacher_obj:
        return await ApiFailedResponse("教师账户信息不存在")
    
    try:
        # 创建考试记录
        new_exam_obj = await erp_exam.get_one(db, exam_name = exam_name, class_id = class_id, upload_type = upload_type)
        if not new_exam_obj:
            new_exam_obj = await erp_exam.create(db, commit=False, **{
                "exam_name": exam_name,
                "class_id": class_id,
                "create_by": account_teacher_obj.account_id,
                "create_time": datetime.now(),
                "update_by": account_teacher_obj.account_id,
                "update_time": datetime.now(),
                "type": type,
                "upload_type": upload_type,
                "analysis_type": analysis_type,
            })
        
        exam_id = new_exam_obj.id
        
        # 解析Excel文件
        headers_data, exam_score_data = await travel_custom_exam_excel(exam_id, virtual_workbook, type)
        
        if not headers_data or not exam_score_data:
            return await ApiFailedResponse("Excel文件格式错误或数据为空")
        
        # 创建考试题目记录
        for exam_question in headers_data:
            # 检查题目是否已存在
            existing_question = await erp_exam_questions.get_one(db,
                exam_id=exam_id,
                sort=exam_question['sort']
            )
            if existing_question:
                # 更新现有题目
                for key, value in exam_question.items():
                    if key != 'exam_id' and key != 'sort':  # 不更新主键相关字段
                        setattr(existing_question, key, value)
            else:
                # 创建新题目
                await erp_exam_questions.create(db, commit=False, **exam_question)
        
        # 创建考试成绩记录
        for exam_score in exam_score_data:
            # 检查学生成绩是否已存在
            existing_score = await erp_exam_score.get_one(db,
                exam_id=exam_id,
                stu_id=exam_score['stu_id']
            )
            if existing_score:
                # 更新现有成绩
                for key, value in exam_score.items():
                    if key != 'exam_id' and key != 'stu_id':  # 不更新主键相关字段
                        setattr(existing_score, key, value)
                existing_score.update_time = datetime.now()
            else:
                # 创建新成绩记录
                await erp_exam_score.create(db, commit=False, **exam_score)
        
        # 提交事务
        await db.commit()
        
        # 使用deepcopy返回对象，避免会话分离问题
        new_exam_obj = copy.deepcopy(new_exam_obj)
        
        return await ApiSuccessResponse(True, '上传成功')
        
    except Exception as e:
        # 回滚事务
        return await ApiFailedResponse(f"数据处理失败: {str(e)}")

