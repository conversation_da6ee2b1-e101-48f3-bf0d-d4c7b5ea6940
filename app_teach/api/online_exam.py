import copy
import random
from datetime import datetime
from typing import Optional
from pydantic import BaseModel
from sqlalchemy.util import await_only

import settings
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Depends
from app_desk.modules import assign_consultant, erp_student_plus
from app_teach.crud import get_stu_info, get_stu_paper, get_exam_detail, get_stu_score_history
from app_teach.modules import get_paper_question
from app_teach.serializer import AnswerQuestionCreate, RandomChoicePaperOption
from app_user.jjyw_verify import get_current_user, UserDictJJYW
from models.m_desk import ErpStudentConsultant
from models.m_online_exam import ErpOnlineQuestion, ErpOnlinePaper, ErpOnlinePaperQuestion, ErpOnlineQuestionOption, \
    ErpOnlineStuPaper, ErpOnlineStuScore, ErpOnlinePaperCourse
from models.old_models.old_student import RbStudent, RbAccount
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db, get_uat_db
from utils.db.model_handler import ModelDataHelper
from utils.net.http_handler import HttpHandler
from utils.other.config_handler import get_config
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse
import pandas as pd

router = APIRouter(prefix="/online_exam", tags=["【诊断】线上诊断"])

erp_online_question = CRUD(ErpOnlineQuestion)
erp_online_paper = CRUD(ErpOnlinePaper)
erp_online_paper_question = CRUD(ErpOnlinePaperQuestion)
erp_online_question_option = CRUD(ErpOnlineQuestionOption)
erp_online_stu_paper = CRUD(ErpOnlineStuPaper)
erp_online_stu_score = CRUD(ErpOnlineStuScore)
erp_online_paper_course = CRUD(ErpOnlinePaperCourse)
rb_student = CRUD(RbStudent)
rb_account = CRUD(RbAccount)

allow_role = []


@router.get("/verify_invitation_code")
async def compare_invitation_code(
        code: int,
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 验证邀请码
    """
    base_url = "http://dashboard.server.jjsw.vip:18000"

    code_resp = await HttpHandler.get(
        url=base_url + '/plus_api/get_password_nologin?verify=werqa342as8864..1wqre')
    online_code = code_resp.get('code')
    if not online_code:
        return await ApiFailedResponse('网络连接失败')
    if code != int(online_code):
        return await ApiFailedResponse('邀请码错误')
    return await ApiSuccessResponse(True, '邀请码正确')


@router.get("/exam_params")
async def query_exam_params(
        identity_key: str,
        conf: dict = Depends(get_config),
        # jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 查询参数
    - identity_key:
      - question_difficulty_level: 查询难度
      - question_grade: 查询年级
      - question_subject: 查询科目
      - question_p_grade: 查询年级大类
      - question_knowledge: 查询知识点
    """
    v = conf.get(identity_key)
    return await ApiSuccessResponse(v)


# 检测同年级科目是否做过题
@router.get("/check_exam")
async def check_exam(
        grade_id: int,
        subject_id: int,
        uat_db: AsyncSession = Depends(get_uat_db),
        default_db: AsyncSession = Depends(get_default_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 检测同年级科目是否做过题
    """
    stu_info = await get_stu_info(uat_db, jjyw_user.uid)
    if not stu_info:
        return await ApiFailedResponse(f'未查到学生信息:{jjyw_user.uid}')
    exist_paper = await get_stu_paper(db=default_db, stu_id=stu_info.StuId, grade_id=grade_id, subject_id=subject_id)
    if exist_paper:
        resp_questions = await get_paper_question(db=default_db, paper_id=exist_paper.paper_id)  # 调用接口
        questions = resp_questions.questions
        resp_data = ModelDataHelper.model_to_dict(resp_questions)
        resp_data['stu_paper'] = exist_paper
        resp_data['questions'] = questions
        return await ApiSuccessResponse(resp_data)
    else:
        return await ApiSuccessResponse(False)


@router.post("/exam")
async def create_exam(
        params: RandomChoicePaperOption,
        conf: dict = Depends(get_config),
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 随机抽取试卷
    - 抽取的试卷需要满足当前时间在开始时间和结束时间之间
    - 线上测试学生端相关，接口请使用进阶与我token
    - grade_id 年级id (几年级的id)
    - subject_id 科目id
    - lock_secret 解锁高级题目的秘钥， 在conf中配置(已弃用)
    """
    stu_info = await get_stu_info(uat_db, jjyw_user.uid)
    raw = [
        ErpOnlinePaper.is_active == 1,
        ErpOnlinePaper.is_lock == 0,
        ErpOnlinePaper.start_time < datetime.now(),
        ErpOnlinePaper.end_time > datetime.now(),
    ]
    if params.subject_id:
        raw.append(ErpOnlinePaper.subject_id == params.subject_id)
    if params.grade_id:
        raw.append(ErpOnlinePaper.grade_id == params.grade_id)
    # print(params.subject_id, params.grade_id)
    exist_paper = await get_stu_paper(db=default_db, stu_id=stu_info.StuId, grade_id=params.grade_id,
                                      subject_id=params.subject_id)
    if exist_paper:
        print(
            f'已存在同年级同学科诊断，提取:{exist_paper.paper_id}, 进度{exist_paper.current_process}/{exist_paper.all_process}')
        if exist_paper.all_process != exist_paper.current_process:
            if exist_paper.all_process <= 0:
                return await ApiFailedResponse(f'旧诊断的进度有误,请检查')
            resp_questions = await get_paper_question(db=default_db, paper_id=exist_paper.paper_id)  # 调用接口
            questions = resp_questions.questions
            resp_data = ModelDataHelper.model_to_dict(resp_questions)
            resp_data['stu_paper'] = exist_paper
            resp_data['questions'] = questions
            return await ApiSuccessResponse(resp_data)
        else:
            return await ApiSuccessResponse(exist_paper, '该学生已完成所选年级学科的诊断')
    paper = await erp_online_paper.get_many(default_db, raw=raw)
    if not paper:
        return await ApiFailedResponse('该条件下暂无试卷可用')
    random_paper = random.choice(paper)
    # print(f" 选择试卷：{random_paper.id}")
    resp_questions = await get_paper_question(db=default_db, paper_id=random_paper.id)  # 调用接口
    question_data = resp_questions.questions
    if not question_data:
        return await ApiFailedResponse(f'请提醒教师完善在线诊断题目， 题号：{random_paper.id}')
    stu_paper_item = {
        "paper_id": random_paper.id,
        "erp_stu_id": stu_info.StuId,
        "current_process": 0,
        "all_process": len(question_data),
    }
    stu_paper = await erp_online_stu_paper.create(default_db, commit=False, **stu_paper_item)
    resp_data = ModelDataHelper.model_to_dict(resp_questions)
    r_question_data = copy.deepcopy(question_data)
    r_stu_paper = copy.deepcopy(stu_paper)
    resp_data['stu_paper'] = r_stu_paper
    resp_data['questions'] = r_question_data
    await default_db.commit()
    return await ApiSuccessResponse(resp_data)


@router.get("/start_exam/{paper_stu_id}")
async def start_exam(
        paper_stu_id: int = None,
        conf: dict = Depends(get_config),
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 获取下一题
    - paper_stu_id： stu_paper下的id
    - process 进度
    - done 是否做完
    """
    exist_paper = await erp_online_stu_paper.get_one(default_db, id=paper_stu_id)
    current_process = exist_paper.current_process
    all_process = exist_paper.all_process
    if current_process == all_process:
        return await ApiSuccessResponse({
            "paper_stu_id": paper_stu_id,
            "process": f"{current_process}/{all_process}",
            "done": True,
        })
    # print(f'做题进度：{current_process}/{all_process}')
    sort_num = current_process + 1
    paper_question = await erp_online_paper_question.get_one(default_db, **{
        "paper_id": exist_paper.paper_id,
        "sort": sort_num,
    })
    # print(paper_question.question_id)
    question = await erp_online_question.get_one(default_db, id=paper_question.question_id)
    options = await erp_online_question_option.get_many(default_db, {"question_id": paper_question.question_id})

    return await ApiSuccessResponse({
        "paper_stu_id": paper_stu_id,
        "process": f"{current_process}/{all_process}",
        "done": False,
        "paper_question_id": paper_question.id,
        "question": question,
        "options": options,
    })


# 获取上一题
@router.get("/last_exam/{paper_stu_id}")
async def last_exam(
        sort_num: int,
        paper_stu_id: int,
        conf: dict = Depends(get_config),
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 获取上一题
    - paper_stu_id： stu_paper下的id
    - process 进度
    - done 是否做完
    """
    exist_paper = await erp_online_stu_paper.get_one(default_db, id=paper_stu_id)
    current_process = exist_paper.current_process
    all_process = exist_paper.all_process
    paper_question = await erp_online_paper_question.get_one(default_db, **{
        "paper_id": exist_paper.paper_id,
        "sort": sort_num,
    })
    question = await erp_online_question.get_one(default_db, id=paper_question.question_id)
    options = await erp_online_question_option.get_many(default_db, {"question_id": paper_question.question_id})
    #  上一题还需要返回上一题的答案
    stu_score = await erp_online_stu_score.get_many(default_db, {
        "paper_stu_id": paper_stu_id,
        "paper_question_id": paper_question.id})

    return await ApiSuccessResponse({
        "paper_stu_id": paper_stu_id,
        "process": f"{current_process}/{all_process}",
        "done": False,
        "paper_question_id": paper_question.id,
        "question": question,
        "options": options,
        "stu_score": stu_score
    })


@router.post("/answer_exam")
async def answer_exam(
        answer: AnswerQuestionCreate,
        conf: dict = Depends(get_config),
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 作答
    - paper_stu_id： stu_paper下的id
    - 需要传递question_type用于判断是选择题还是填空题
    - choice_option_id 选择题的选项id,填空题放入answers.option_id中
    """
    exist = await erp_online_stu_score.get_one(default_db,
                                               paper_stu_id=answer.paper_stu_id,
                                               paper_question_id=answer.paper_question_id)
    if exist:
        return await ApiFailedResponse('不允许重复做题')
    create_item = {
        "paper_stu_id": answer.paper_stu_id,
    }
    paper_question = await erp_online_paper_question.get_by_id(default_db, answer.paper_question_id)
    question = await erp_online_question.get_by_id(default_db, paper_question.question_id)
    if answer.choice_option_id:
        option = await erp_online_question_option.get_by_id(default_db, answer.choice_option_id)
        if not option:
            return await ApiFailedResponse('选项id未查到')
        if option.question_id != paper_question.question_id:
            return await ApiFailedResponse('选项参数错误')
        stu_score = 0
        is_correct = 0
        if answer.question_type == 1:
            if option.correct > 0:
                stu_score = question.score
                is_correct = 1
            create_item.update({"is_correct": is_correct})
            create_item.update({"choice_option_id": answer.choice_option_id})
            create_item.update({"paper_question_id": answer.paper_question_id})
            create_item.update({"disable": 0})
            create_item.update({"stu_score": stu_score})
            await erp_online_stu_score.create(default_db, commit=False, **create_item)
        else:
            return await ApiFailedResponse('question_type参数错误1')
    else:
        if answer.question_type == 2:  # 填空题
            answers = answer.answers
            for i in answers:
                option_score = 0
                is_correct = 0
                option_obj = await erp_online_question_option.get_by_id(default_db, i.option_id)
                if str(i.answer) == str(option_obj.answer):
                    is_correct = 1
                    option_score = option_obj.option_score
                create_item.update({"stu_score": option_score})
                create_item.update({"choice_option_id": i.option_id})
                create_item.update({"paper_question_id": answer.paper_question_id})
                create_item.update({"answer": i.answer})
                create_item.update({"disable": 0})
                create_item.update({"is_correct": is_correct})
                await erp_online_stu_score.create(default_db, commit=False, **create_item)
        else:
            return await ApiFailedResponse('question_type参数错误2')

    stu_paper = await erp_online_stu_paper.get_by_id(default_db, answer.paper_stu_id)
    # print(f'已提交第{stu_paper.current_process + 1}题')
    stu_paper.current_process += 1
    stu_paper.update_time = datetime.now()
    if stu_paper.current_process == stu_paper.all_process:
        # 题目已经做完，可以进入客户分配
        flag = await assign_consultant(default_db, stu_paper.erp_stu_id, user_id=20000)
        if flag:
            settings.logger.info(f'答题完毕，学生{stu_paper.erp_stu_id}已成功分配顾问')
        else:
            settings.logger.error(f'答题完毕，学生{stu_paper.erp_stu_id}分配顾问失败')

    await default_db.commit()
    return await ApiSuccessResponse(True)


# 查询作答过的题及其选项
@router.get("/answer_list/{paper_stu_id}")
async def answer_list(
        paper_stu_id: int = None,
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        # jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 查询作答过的选项
    """
    data = await get_stu_score_history(default_db, paper_stu_id=paper_stu_id)
    return await ApiSuccessResponse(data)


class UpdateAnswer(BaseModel):
    score_id: int
    question_type: int
    option_id: Optional[int] = None
    answer: Optional[list] = None


# 修改选项并重新计算得分，要分选择题还是填空题
@router.post("/update_answer")
async def update_answer(
        params: UpdateAnswer,
        conf: dict = Depends(get_config),
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 修改作答
    - score_id: answer_list中获取的score_id
    - question_type: 1选择题 2填空题
    - option_id: 选项id或填空id
    - answer: 填空题的答案(question_type=2)
    """
    score_obj = await erp_online_stu_score.get_by_id(default_db, params.score_id)
    if not score_obj:
        return await ApiFailedResponse(f'未查到该答题记录:{params.score_id}')
    if params.question_type == 1:  # 选择题
        option_obj = await erp_online_question_option.get_by_id(default_db, params.option_id)
        if not option_obj:
            return await ApiFailedResponse('选项id未查到')
        score_obj.choice_option_id = params.option_id
        if option_obj.correct == 1:
            question_obj = await erp_online_question.get_by_id(default_db, option_obj.question_id)
            score_obj.stu_score = question_obj.score
            score_obj.is_correct = 1
            score_obj.disable = 0
        if option_obj.correct == 0:
            score_obj.stu_score = 0
            score_obj.is_correct = 0
            score_obj.disable = 0

    elif params.question_type == 2:  # 填空题
        if params.answer:
            option_id = score_obj.choice_option_id
            option_obj = await erp_online_question_option.get_by_id(default_db, option_id)
            score_obj.answer = params.answer
            if type(params.answer) == list:
                params.answer = params.answer[0]
            if str(params.answer) == str(option_obj.answer):
                score_obj.stu_score = option_obj.option_score
                score_obj.is_correct = 1
                score_obj.disable = 0
            else:
                score_obj.stu_score = 0
                score_obj.is_correct = 0
                score_obj.disable = 0
    else:
        return await ApiFailedResponse('question_type参数错误')
    await default_db.commit()
    return await ApiSuccessResponse(True)


@router.get("/exam_score/{paper_stu_id}")
async def exam_score(
        paper_stu_id: int = None,
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        # jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 查询当前诊断成绩
    """
    if not paper_stu_id:
        return await ApiFailedResponse('paper_stu_id参数错误')
    data = await erp_online_stu_score.get_many(default_db, {
        "paper_stu_id": paper_stu_id
    })

    correct = [i for i in data if i.is_correct > 0]
    error = [i for i in data if i.is_correct == 0]
    score = [i.stu_score for i in data if i.stu_score > 0]

    paper_stu_obj = await erp_online_stu_paper.get_by_id(default_db, paper_stu_id)
    paper_id = paper_stu_obj.paper_id
    if not paper_id:
        return await ApiFailedResponse('该paper_stu_id无法查到paper')
    paper_info = await erp_online_paper.get_by_id(default_db, paper_id)
    stu_sum_score = sum(score)
    paper_courses = await erp_online_paper_course.get_many(default_db, raw=[
        ErpOnlinePaperCourse.paper_id == paper_id,
        ErpOnlinePaperCourse.min_score <= stu_sum_score,
        ErpOnlinePaperCourse.max_score >= stu_sum_score,
        ErpOnlinePaperCourse.disable == 0,
    ])
    if not paper_courses:
        mark = '未匹配到班型'
    elif len(paper_courses) > 1:
        mark = '班型设置重复'
    else:
        mark = paper_courses[0].course_name
        # 匹配到班型
    return await ApiSuccessResponse({
        "score": stu_sum_score,
        "error_num": len(error),
        "correct_num": len(correct),
        "paper_stu_id": paper_stu_id,
        "suggest": mark,
        "teacher_msg": paper_courses[0].teacher_msg if paper_courses else '',
        "paper_info": paper_info
    })


# 退出接口清空做题进度
@router.get("/exit_exam/{paper_stu_id}")
async def exit_exam(
        paper_stu_id: int = None,
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        # jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 退出诊断
    """
    if not paper_stu_id:
        return await ApiFailedResponse('参数错误')
    paper_stu_obj = await erp_online_stu_paper.get_by_id(default_db, paper_stu_id)
    if not paper_stu_obj:
        return await ApiFailedResponse('未查到该诊断')
    paper_stu_obj.current_process = 0

    erp_online_stu_score_objs = await erp_online_stu_score.get_many(default_db, {
        "paper_stu_id": paper_stu_id
    })
    for i in erp_online_stu_score_objs:
        await erp_online_stu_score.delete_one(default_db, i.id, commit=False)

    await default_db.commit()
    return await ApiSuccessResponse(True)


# 查询课程顾问二维码
@router.get("/consultant_qr")
async def consultant_qr(
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    stu_info = await get_stu_info(uat_db, jjyw_user.uid)
    erp_student_consultant = CRUD(ErpStudentConsultant)
    stu_plus = await erp_student_plus.get_one(default_db, stu_id=stu_info.StuId)
    if not stu_plus:
        return await ApiFailedResponse('未查到学生信息')
    consultant = await erp_student_consultant.get_one(default_db, account_id=stu_plus.consultant_id)
    if not consultant:
        return await ApiFailedResponse('未查到课程顾问信息')
    return await ApiSuccessResponse(consultant.consultant_qr)


@router.get("/exam_report/{paper_stu_id}")
async def exam_report(
        paper_stu_id: int = None,
        conf: dict = Depends(get_config),
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 诊断报告
    """
    data = await get_exam_detail(default_db, paper_stu_id)
    dict_data = [dict(i) for i in data]
    # 总分
    sum_score = sum([i['score'] for i in dict_data])
    stu_sum_score = sum([i['stu_score'] for i in dict_data])
    teacher_msg = ''
    course_name = ''

    # 教师评价
    paper_stu = await erp_online_stu_paper.get_by_id(default_db, paper_stu_id)
    comments = await erp_online_paper_course.get_many(default_db, raw=[
        ErpOnlinePaperCourse.paper_id == paper_stu.paper_id,
        ErpOnlinePaperCourse.min_score <= stu_sum_score,
        ErpOnlinePaperCourse.max_score > stu_sum_score,
    ])
    if comments and len(comments) == 1:
        comment = comments[0]
        teacher_msg = comment.teacher_msg
        course_name = comment.course_name
    # 组装各维度数据
    df = pd.DataFrame(dict_data)
    # 按知识点分组数据
    knowledge_point_grouped = df.groupby('knowledge_point').agg({
        'stu_score': 'sum',
        'score': 'sum',
        'is_correct': 'count'
    }).rename(columns={'is_correct': 'count'}).reset_index()
    # 计算stu_count
    knowledge_point_grouped['stu_count'] = df[df['is_correct'] == 1].groupby('knowledge_point')[
        'is_correct'].count().reset_index(drop=True)
    # 按难度分组数据
    difficulty_level_grouped = df.groupby('difficulty_level').agg({
        'stu_score': 'sum',
        'score': 'sum',
        'is_correct': 'count'
    }).rename(columns={'is_correct': 'count'}).reset_index()
    # 计算stu_count
    difficulty_level_grouped['stu_count'] = df[df['is_correct'] == 1].groupby('difficulty_level')[
        'is_correct'].count().reset_index(drop=True)
    # 转换为所需格式
    knowledge_point_data = [
        {
            'knowledge_point': row['knowledge_point'],
            'score': row['score'],
            'stu_score': row['stu_score'],
            'count': row['count'],
            'stu_count': row['stu_count']
        } for index, row in knowledge_point_grouped.iterrows()
    ]
    question_difficulty_level = conf.get('question_difficulty_level')
    # print(question_difficulty_level)
    difficulty_level_data = [
        {
            'difficulty_level': question_difficulty_level.get(str(int(row['difficulty_level']))),
            'score': row['score'],
            'stu_score': row['stu_score'],
            'count': row['count'],
            'stu_count': row['stu_count']
        } for index, row in difficulty_level_grouped.iterrows()
    ]

    stu_info = await rb_student.get_by_id(uat_db, paper_stu.erp_stu_id)
    paper_info = await erp_online_paper.get_by_id(default_db, paper_stu.paper_id)
    return await ApiSuccessResponse({
        "knowledge_point_data": knowledge_point_data,
        "difficulty_level_data": difficulty_level_data,
        "sum_score": sum_score,
        "stu_sum_score": stu_sum_score,
        "teacher_msg": teacher_msg,
        "course_name": course_name,
        "stu_name": stu_info.StuName,
        "stu_id": stu_info.StuId,
        "stu_icon": stu_info.StuIcon,
        "paper_name": paper_info.paper_name,
    })
