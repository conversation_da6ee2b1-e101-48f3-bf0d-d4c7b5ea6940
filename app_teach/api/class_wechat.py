import copy
from fastapi import APIRouter, Depends
from pydantic import BaseModel
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime

from models.m_class import ErpClass, ErpClassLog
from models.models import ErpAccount
from models.m_teacher import ErpAccountTeacher
from models.m_order import ErpOrderStudent
from models.m_wechat import ErpWechatExternaluser, ErpWechatClassuser
from modules.qy_wechat.qy_wechat_relate import QyWechatRelate
from settings import CF, logger
from utils.db.account_handler import UserDict, role_required
from utils.db.db_handler import get_default_db
from utils.enum.enum_order import StudentState
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse
from utils.enum.enum_class import ClassLogType
from app_teach.modules import add_class_log, get_class_group_user_list
from models.m_wechat import ErpWechatChannel
from models.m_student import ErpStudent

router = APIRouter(prefix="/class_wechat", tags=["班级和企微群"])

erp_class = CF.get_crud(ErpClass)
erp_account = CF.get_crud(ErpAccount)
erp_account_teacher = CF.get_crud(ErpAccountTeacher)
erp_order_student = CF.get_crud(ErpOrderStudent)
erp_wechat_externaluser = CF.get_crud(ErpWechatExternaluser)
erp_class_log = CF.get_crud(ErpClassLog)
erp_wechat_classuser = CF.get_crud(ErpWechatClassuser)
erp_wechat_channel = CF.get_crud(ErpWechatChannel)
erp_student = CF.get_crud(ErpStudent)

qy_wechat_relate = QyWechatRelate()

class SetClassWechatRelationRequest(BaseModel):
    """手动设置班级企微群关联请求模型"""
    class_id: int
    qwechat_id: str
    qwechat_name: str

class SetClassJoinChatRequest(BaseModel):
    """推送入群码请求模型"""
    class_id: int
    stu_ids: List[int]

class SetClassChatIdRequest(BaseModel):
    """一键拉群后设置关联请求模型"""
    class_id: int
    chat_name: str
    chat_id: str

@router.get("/teacher_all_qw_group_chat_list")
async def get_teacher_all_qw_group_chat_list(
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    获取老师名下所有群聊
    验证是否老师，获取企微配置，查询老师的群聊列表
    """
    try:
        # 验证是否老师
        teacher_obj = await erp_account_teacher.get_one(db, account_id=user.uid)
        if not teacher_obj:
            return await ApiFailedResponse(message="非老师无法操作")
        
        account_obj = await erp_account.get_by_id(db, user.uid)
        if not account_obj or not account_obj.qy_wechat_userid:
            return await ApiFailedResponse(message="未绑定企业微信")
        
        # 获取群聊列表
        result = await qy_wechat_relate.get_groupchat_list(
            owner_filter={"userid_list": [account_obj.qy_wechat_userid]},
            status_filter=0,
            limit=1000
        )
        
        if not result:
            return await ApiFailedResponse(message="获取群聊列表失败")
        
        # 处理群聊列表数据
        group_list = []
        if result.get('group_chat_list'):
            for item in result['group_chat_list']:
                chat_detail = await qy_wechat_relate.get_groupchat_detail(item['chat_id'])
                if chat_detail and chat_detail.get('group_chat'):
                    group_chat = chat_detail['group_chat']
                    group_list.append({
                        "chat_id": group_chat.get('chat_id'),
                        "name": group_chat.get('name'),
                        "create_time": group_chat.get('create_time')
                    })
        
        return await ApiSuccessResponse(group_list)
        
    except Exception as e:
        return await ApiFailedResponse(message=f"获取群聊列表失败：{str(e)}")

@router.post("/set_class_wechat_id_relation")
async def set_class_wechat_id_relation(
    params: SetClassWechatRelationRequest,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    手动绑定班级和企微群的关联关系
    更新班级表的qwechat_id和qwechat_name字段
    记录操作日志
    """
    try:
        # 验证班级是否存在
        class_obj = await erp_class.get_by_id(db, params.class_id)
        if not class_obj:
            return await ApiFailedResponse(message="班级不存在")
        
        # 更新班级的企微群信息
        await erp_class.update_one(db, params.class_id, {
            "qwechat_id": params.qwechat_id,
            "qwechat_name": params.qwechat_name,
            "update_by": user.uid
        })
        
        # 记录操作日志
        log_content = f"用户：{user.uid} - 添加企微群关联：{params.qwechat_name}({params.qwechat_id})"
        log_type = ClassLogType.ChangeClassLog.value
        log_name = "绑定企微群"
        await add_class_log(db, params.class_id, log_name, log_type, log_content)
        
        return await ApiSuccessResponse(True)
        
    except Exception as e:
        return await ApiFailedResponse(message=f"绑定失败：{str(e)}")

@router.post("/set_class_join_chat")
async def set_class_join_chat(
    params: SetClassJoinChatRequest,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    推送入群码给学生
    创建入群码 create_group_chat_join_way
    通过企微群发功能推送入群码给学生
    要求学生已添加老师企微
    """
    class_obj = await erp_class.get_by_id(db, params.class_id)
    if not class_obj:
        return await ApiFailedResponse(message="班级不存在")
    teacher_obj = await erp_account_teacher.get_by_id(db, class_obj.teacher_id)
    if not teacher_obj:
        return await ApiFailedResponse(message="老师不存在")

    # # 验证老师身份
    # teacher_obj = await erp_account_teacher.get_one(db, account_id=user.uid)
    # if not teacher_obj:
    #     return await ApiFailedResponse(message="不是老师，无法操作")
    
    account_obj = await erp_account.get_by_id(db, teacher_obj.account_id)
    if not account_obj or not account_obj.qy_wechat_userid:
        return await ApiFailedResponse(message="老师未绑定企业微信")
    
    teacher_account = account_obj
    
    if not class_obj.qwechat_id:
        return await ApiFailedResponse(message="班级未绑定群聊，无法推送")
    
    # 查询要推送的学生(需要是已添加老师企微的学生)
    # 这里需要查询学生的企微外部联系人信息
    # 简化实现：假设学生已添加老师企微，可以直接推送
    print(teacher_obj.id, params.stu_ids)
    contact_list = await erp_wechat_externaluser.get_many(db, raw=[
        ErpWechatExternaluser.teacher_id == teacher_obj.id,
        ErpWechatExternaluser.stu_id.in_(params.stu_ids),
        ErpWechatExternaluser.disable == 0
    ])
    
    if not contact_list:
        return await ApiFailedResponse(message="没有可推送的学生(学生未添加老师企微)")
    
    # 创建入群码
    join_way_result = await qy_wechat_relate.add_join_way(
        scene=2,  # 通过二维码联系
        chat_id_list=[class_obj.qwechat_id],
        remark=f"班级{class_obj.class_name}入群码",
        state=f"class_{params.class_id}"
    )
    
    if not join_way_result or join_way_result.get('errcode') != 0:
        return await ApiFailedResponse(message="创建入群码失败")
    
    config_id = join_way_result.get('config_id')
    qr_code = join_way_result.get('qr_code', '')
    
    # 构建推送消息内容
    message_content = f"""
📚 班级入群邀请

亲爱的家长，您好！

您的孩子已成功报名 {class_obj.class_name}，请扫描下方二维码加入班级群聊，我们将在群内及时发布课程通知、作业安排等重要信息。

班级：{class_obj.class_name}
老师：{teacher_account.employee_name}

请点击链接或扫描二维码加入群聊：
{qr_code}

如有任何疑问，请随时联系我们。
    """.strip()
    
    # 批量推送消息给学生家长
    success_count = 0
    failed_count = 0
    
    for contact in contact_list:
        try:
            # 发送文本消息
            send_result = await qy_wechat_relate.send_external_contact_message(
                userid=account_obj.qy_wechat_userid,
                external_userid=contact.external_user_id,
                msgtype="text",
                content={"content": message_content}
            )
            
            if send_result:
                success_count += 1
            else:
                failed_count += 1
                
        except Exception as send_error:
            logger.warning(f"推送入群码给学生家长失败: {contact.external_user_id}, 错误: {str(send_error)}")
            failed_count += 1
    
    return await ApiSuccessResponse({
        "message": "入群码推送完成",
        "join_way_config_id": config_id,
        "qr_code": qr_code,
        "total_count": len(contact_list),
        "success_count": success_count,
        "failed_count": failed_count
    })
        

@router.post("/set_class_chat_id")
async def set_class_chat_id(
    params: SetClassChatIdRequest,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    一键拉群后设置关联
    客户端创建群聊后，设置班级关联的群聊ID
    """
    try:
        # 验证班级
        class_obj = await erp_class.get_by_id(db, params.class_id)
        if not class_obj:
            return await ApiFailedResponse(message="班级不存在")
        
        # 更新班级的企微群信息
        await erp_class.update_one(db, params.class_id, {
            "qwechat_id": params.chat_id,
            "qwechat_name": params.chat_name,
            "update_by": user.uid
        })
        
        # 记录操作日志
        log_content = f"用户：{user.uid} - 一键拉群：{params.chat_name}({params.chat_id})"
        log_type = ClassLogType.ChangeClassLog.value
        log_name = "一键拉群"
        await add_class_log(db, params.class_id, log_name, log_type, log_content)
        
        return await ApiSuccessResponse({
            "message": "群聊关联成功",
            "class_id": params.class_id,
            "chat_id": params.chat_id,
            "chat_name": params.chat_name
        })
        
    except Exception as e:
        return await ApiFailedResponse(message=f"设置群聊关联失败：{str(e)}")

@router.post("/update_class_chat_external_user/{chat_id}")
async def update_class_chat_external_user(
    chat_id: str,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    实时同步群成员变化
    根据群聊ID获取群成员信息，同步外部联系人变化
    """
    try:
        # 验证班级是否存在
        class_obj = await erp_class.get_one(db, qwechat_id=chat_id)
        if not class_obj:
            return await ApiFailedResponse(message="未找到关联的班级")
        
        # 获取群聊详情
        chat_detail = await qy_wechat_relate.get_groupchat_detail(chat_id)
        if not chat_detail or chat_detail.get('errcode') != 0:
            return await ApiFailedResponse(message="获取群聊详情失败")
        
        group_chat = chat_detail.get('group_chat', {})
        member_list = group_chat.get('member_list', [])
        
        # 提取外部联系人
        current_external_members = [
            member.get('userid') for member in member_list 
            if member.get('type') == 2  # 外部联系人
        ]
        
        # 获取数据库中现有的群成员记录
        existing_records = await erp_wechat_classuser.get_many(db, raw=[
            ErpWechatClassuser.wechat_id == chat_id,
            ErpWechatClassuser.disable == 0
        ])
        
        existing_external_users = {record.external_user_id for record in existing_records if record.external_user_id}
        
        # 找出新增的成员
        new_members = set(current_external_members) - existing_external_users
        # 找出退出的成员
        left_members = existing_external_users - set(current_external_members)
        
        # 处理新增成员
        for external_user_id in new_members:
            if external_user_id:
                await erp_wechat_classuser.create(db, commit=False, **{
                    'class_id': class_obj.id,
                    'wechat_id': chat_id,
                    'external_user_id': external_user_id,
                    'state': 0,  # 在群状态
                    'create_time': datetime.now()
                })
        
        # 处理退出成员
        for external_user_id in left_members:
            if external_user_id:
                # 更新状态为已退群
                existing_record = await erp_wechat_classuser.get_one(db, 
                    wechat_id=chat_id, 
                    external_user_id=external_user_id
                )
                if existing_record:
                    await erp_wechat_classuser.update_one(db, existing_record.id, {
                        'state': 1,  # 已退群
                        'update_time': datetime.now()
                    })
        
        # 确保所有记录的class_id正确
        for record in existing_records:
            if record.class_id != class_obj.id:
                await erp_wechat_classuser.update_one(db, record.id, {
                    'class_id': class_obj.id,
                    'update_time': datetime.now()
                })
        
        await db.commit()
        
        return await ApiSuccessResponse({
            "message": "群成员同步成功",
            "class_id": class_obj.id,
            "chat_id": chat_id,
            "external_member_count": len(current_external_members),
            "total_member_count": len(member_list),
            "new_members_count": len(new_members),
            "left_members_count": len(left_members)
        })
        
    except Exception as e:
        await db.rollback()
        return await ApiFailedResponse(message=f"同步群成员失败：{str(e)}")

@router.post("/update_class_chat_name/{chat_id}")
async def update_class_chat_name(
    chat_id: str,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    自动更新群名称
    根据群聊ID获取最新的群名称并更新到班级表
    """
    try:
        # 验证班级是否存在
        class_obj = await erp_class.get_one(db, qwechat_id=chat_id)
        if not class_obj:
            return await ApiFailedResponse(message="未找到关联的班级")
        
        # 获取群聊详情
        chat_detail = await qy_wechat_relate.get_groupchat_detail(chat_id)
        if not chat_detail or chat_detail.get('errcode') != 0:
            return await ApiFailedResponse(message="获取群聊详情失败")
        
        group_chat = chat_detail.get('group_chat', {})
        chat_name = group_chat.get('name', '')
        
        if not chat_name:
            return await ApiFailedResponse(message="获取群名称失败")
        
        # 更新班级的群名称
        await erp_class.update_one(db, class_obj.id, {
            "qwechat_name": chat_name,
            "update_by": user.uid
        })
        
        return await ApiSuccessResponse({
            "message": "群名称更新成功",
            "old_name": class_obj.qwechat_name,
            "new_name": chat_name
        })
        
    except Exception as e:
        return await ApiFailedResponse(message=f"更新群名称失败：{str(e)}")

@router.post("/create_class_group_chat")
async def create_class_group_chat(
    class_id: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    一键拉群 - 为班级创建入群邀请或向已有群聊添加成员
    如果班级已绑定群聊，则只添加未进群的学生
    如果班级未绑定群聊，则提供创建群聊的指导
    
    注意：企业微信API不支持直接创建包含外部联系人的客户群聊，
    客户群聊需要在企业微信客户端中手动创建
    """
    # 验证班级
    class_obj = await erp_class.get_by_id(db, class_id)
    if not class_obj:
        return await ApiFailedResponse(message="班级不存在")
    
    # 获取可拉群的用户列表
    user_list_result = await get_class_group_user_list(class_id, db)
    if not user_list_result:
        return await ApiFailedResponse(message="获取用户列表失败")
    
    # 现在直接返回数据字典
    user_data = user_list_result
    teacher_userids = user_data.get('teacher_userids', [])
    student_userids = user_data.get('student_userids', [])
    
    # 如果班级已绑定企微群，则进行增量添加
    if class_obj.qwechat_id:
        return await _add_members_to_existing_group(
            db, class_obj, student_userids, user
        )
    
    # 如果班级未绑定企微群，则提供创建指导
    return await _provide_group_creation_guidance(
        db, class_obj, teacher_userids, student_userids, user
    )
    


async def _add_members_to_existing_group(db, class_obj, student_userids, user):
    """向已有群聊添加成员"""
    try:
        chat_id = class_obj.qwechat_id
        
        # 获取群聊当前成员
        chat_detail = await qy_wechat_relate.get_groupchat_detail(chat_id)
        if not chat_detail or chat_detail.get('errcode') != 0:
            return await ApiFailedResponse(message="获取群聊详情失败，无法添加成员")
        
        group_chat = chat_detail.get('group_chat', {})
        member_list = group_chat.get('member_list', [])
        
        # 提取当前群聊中的外部联系人
        current_external_members = {
            member.get('userid') for member in member_list 
            if member.get('type') == 2  # 外部联系人
        }
        
        # 找出需要添加的新成员（排除已在群中的）
        new_members_to_add = [
            userid for userid in student_userids 
            if userid not in current_external_members
        ]
        
        if not new_members_to_add:
            return await ApiSuccessResponse({
                "message": "所有学生都已在群中，无需添加",
                "class_id": class_obj.id,
                "chat_id": chat_id,
                "chat_name": class_obj.qwechat_name,
                "new_members_count": 0,
                "existing_members_count": len(current_external_members)
            })
        
        # 尝试直接添加成员到群聊
        # 注意：企业微信客户群聊不支持直接通过API添加外部联系人
        # 这里我们创建一个自动邀请机制
        
        # 1. 首先创建入群邀请链接
        join_way_result = await qy_wechat_relate.add_join_way(
            scene=2,  # 通过二维码联系
            chat_id_list=[chat_id],
            remark=f"班级{class_obj.class_name}新成员入群",
            state=f"class_{class_obj.id}_add_members"
        )
        
        if not join_way_result or join_way_result.get('errcode') != 0:
            return await ApiFailedResponse(message="创建入群邀请失败")
        
        config_id = join_way_result.get('config_id')
        qr_code = join_way_result.get('qr_code', '')
        
        # 2. 获取当前用户（老师）的企微信息
        teacher_obj = await erp_account_teacher.get_one(db, account_id=user.uid)
        account_obj = await erp_account.get_by_id(db, user.uid)
        
        # 3. 发送入群邀请消息给每个新成员
        success_count = 0
        failed_count = 0
        
        invitation_message = f"""
📚 班级群聊邀请

亲爱的家长，您好！

您的孩子所在的班级 {class_obj.class_name} 已创建群聊，请点击下方链接加入班级群聊：

{qr_code if qr_code else '请联系老师获取入群二维码'}

班级：{class_obj.class_name}
老师：{account_obj.employee_name if account_obj else ''}

加入群聊后，我们将及时发布课程通知、作业安排等重要信息。

如有任何疑问，请随时联系我们。
        """.strip()
        
        for external_userid in new_members_to_add:
            try:
                # 发送邀请消息
                send_result = await qy_wechat_relate.send_external_contact_message(
                    userid=account_obj.qy_wechat_userid,
                    external_userid=external_userid,
                    msgtype="text",
                    content={"content": invitation_message}
                )
                
                if send_result:
                    success_count += 1
                    # 记录待加入状态
                    await erp_wechat_classuser.create(db, commit=False, **{
                        'class_id': class_obj.id,
                        'wechat_id': chat_id,
                        'external_user_id': external_userid,
                        'state': 2,  # 待加入状态
                        'create_time': datetime.now()
                    })
                else:
                    failed_count += 1
                    
            except Exception as send_error:
                logger.warning(f"发送入群邀请给外部联系人失败: {external_userid}, 错误: {str(send_error)}")
                failed_count += 1
        
        # 记录操作日志
        log_content = f"用户：{user.uid} - 向群聊邀请新成员：{len(new_members_to_add)}人，成功发送邀请：{success_count}人"
        log_type = ClassLogType.ChangeClassLog.value
        log_name = "群聊邀请成员"
        await add_class_log(db, class_obj.id, log_name, log_type, log_content)
        
        await db.commit()
        
        return await ApiSuccessResponse({
            "message": f"已向{success_count}位新成员发送入群邀请",
            "class_id": class_obj.id,
            "chat_id": chat_id,
            "chat_name": class_obj.qwechat_name,
            "new_members_count": len(new_members_to_add),
            "existing_members_count": len(current_external_members),
            "invitation_sent_count": success_count,
            "invitation_failed_count": failed_count,
            "join_way_config_id": config_id,
            "qr_code": qr_code,
            "note": f"已自动发送入群邀请给{success_count}位新成员，他们将收到包含入群链接的消息"
        })
        
    except Exception as e:
        await db.rollback()
        return await ApiFailedResponse(message=f"添加群成员失败：{str(e)}")


async def _provide_group_creation_guidance(db, class_obj, teacher_userids, student_userids, user):
    """创建新的群聊"""
    # 构建群成员列表 - 企业微信API只需要userid字符串列表
    userlist = []
    
    # 添加老师成员
    userlist.extend(teacher_userids)
    
    # 注意：企业微信的应用群聊(appchat)只能包含企业内部成员
    # 外部联系人不能直接通过create_groupchat添加
    # 我们只创建包含老师的群聊，然后通过邀请机制添加学生
    
    # 构建群名称
    chat_name = f"{class_obj.class_name}-班级群"
    
    # 调用企微API创建群聊（只包含内部成员）
    create_result = await qy_wechat_relate.create_groupchat(
        name=chat_name,
        owner=teacher_userids[0],  # 主讲老师作为群主
        userlist=userlist,
        chatid="",  # 让企微自动生成
        agentid=0  # 使用默认应用
    )
    
    if not create_result or create_result.get('errcode') != 0:
        return await ApiFailedResponse(message=f"创建群聊失败: {create_result.get('errmsg', '未知错误')}")
    
    chat_id = create_result.get('chatid')
    if not chat_id:
        return await ApiFailedResponse(message="创建群聊成功但未获取到群聊ID")
    
    # 更新班级的企微群信息
    await erp_class.update_one(db, class_obj.id, {
        "qwechat_id": chat_id,
        "qwechat_name": chat_name,
        "update_by": user.uid
    })
    
    # 创建入群邀请链接给学生
    student_invitation_result = None
    if student_userids:
        try:
            # 创建入群邀请链接
            join_way_result = await qy_wechat_relate.add_join_way(
                scene=2,  # 通过二维码联系
                chat_id_list=[chat_id],
                remark=f"班级{class_obj.class_name}学生入群",
                state=f"class_{class_obj.id}_new_group"
            )
            
            if join_way_result and join_way_result.get('errcode') == 0:
                student_invitation_result = {
                    "config_id": join_way_result.get('config_id'),
                    "qr_code": join_way_result.get('qr_code', '')
                }
                
                # 初始化群成员记录（待加入状态）
                for student_userid in student_userids:
                    await erp_wechat_classuser.create(db, commit=False, **{
                        'class_id': class_obj.id,
                        'wechat_id': chat_id,
                        'external_user_id': student_userid,
                        'state': 2,  # 待加入状态
                        'create_time': datetime.now()
                    })
        except Exception as e:
            logger.warning(f"创建学生入群邀请失败: {str(e)}")
    
    # 记录操作日志
    log_content = f"用户：{user.uid} - 一键拉群成功：{chat_name}({chat_id})，包含{len(userlist)}位老师"
    if student_userids:
        log_content += f"，已为{len(student_userids)}位学生创建入群邀请"
    log_type = ClassLogType.ChangeClassLog.value
    log_name = "一键拉群"
    await add_class_log(db, class_obj.id, log_name, log_type, log_content)
    
    result = {
        "message": "一键拉群成功",
        "class_id": class_obj.id,
        "chat_id": chat_id,
        "chat_name": chat_name,
        "teacher_count": len(userlist),
        "student_count": len(student_userids) if student_userids else 0,
        "note": "群聊已创建并包含所有老师。" + (
            f"已为{len(student_userids)}位学生创建入群邀请，请通过推送入群码功能邀请学生加入。" 
            if student_userids else "无学生需要邀请。"
        )
    }
    
    if student_invitation_result:
        result["student_invitation"] = student_invitation_result
    
    result = copy.deepcopy(result)
    await db.commit()
    return await ApiSuccessResponse(result)
    

@router.post("/test_push_teacher_card")
async def test_push_teacher_card(
    order_student_id: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    测试推送老师名片功能
    用于手动触发老师名片推送，方便调试和测试
    """
    from app_teach.modules import auto_push_teacher_card_after_enroll
    
    # 获取学生订单信息
    order_student_obj = await erp_order_student.get_by_id(db, order_student_id)
    if not order_student_obj:
        return await ApiFailedResponse(message="学生订单不存在")
    
    # 调用推送老师名片功能
    result = await auto_push_teacher_card_after_enroll(
        order_student_id=order_student_obj.id,
        stu_id=order_student_obj.stu_id,
        class_id=order_student_obj.class_id
    )
    if result:
        return await ApiSuccessResponse(True)
    else:
        return await ApiFailedResponse(message="老师名片推送失败，请查看日志")
    

@router.get("/channel_teacher_info")
async def get_channel_teacher_info(
    teacher_id: int,
    stu_id: int,
    class_id: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    订单支付后，添加老师二维码页面的获取二维码接口
    
    Args:
        teacher_id: 老师ID
        stu_id: 学生ID
        class_id: 班级ID
        
    Returns:
        包含班级名称、老师姓名、企微二维码、群聊二维码的信息
    """
    # 获取班级信息
    class_obj = await erp_class.get_by_id(db, class_id)
    if not class_obj:
        return await ApiFailedResponse(message="班级不存在")
    
    # 获取老师信息
    teacher_obj = await erp_account_teacher.get_by_id(db, teacher_id)
    if not teacher_obj:
        return await ApiFailedResponse(message="老师不存在")
        
    # 获取老师账号信息
    teacher_account = await erp_account.get_by_id(db, teacher_obj.account_id)
    if not teacher_account:
        return await ApiFailedResponse(message="老师账号不存在")
    
    # 获取学生信息
    student_obj = await erp_student.get_by_id(db, stu_id)
    if not student_obj:
        return await ApiFailedResponse(message="学生不存在")
    
    # 查找现有的企微渠道码
    existing_channel = await erp_wechat_channel.get_one(db, 
        teacher_id= teacher_id,
        stu_id= stu_id,
        class_id= class_id,
        disable= 0
    )
    
    wx_qr_code = ""
    
    # 如果没有现有渠道码且老师绑定了企微，则创建新的渠道码
    if not existing_channel and teacher_account.qy_wechat_userid:
        try:
            # 创建企微渠道码
            qy_wechat = QyWechatRelate()
            
            # 构造state参数，格式：cid=渠道ID|tid=老师ID|sid=学生ID
            state = f"cid=0|tid={teacher_id}|sid={stu_id}"
            
            # 创建联系我方式（企微渠道码）
            contact_way_result = await qy_wechat.add_contact_way(
                type=1,  # 单人
                scene=2,  # 通过二维码联系
                user=[teacher_account.qy_wechat_userid],
                remark=f"{student_obj.stu_name}家长添加{teacher_account.employee_name}老师",
                skip_verify=True,  # 无需验证
                state=state
            )
            
            if contact_way_result and contact_way_result.get('errcode') == 0:
                config_id = contact_way_result.get('config_id')
                wx_qr_code = contact_way_result.get('qr_code', '')
                
                # 保存渠道码配置到数据库
                channel_data = {
                    'wx_config_id': config_id,
                    'wx_qrcode': wx_qr_code,
                    'name': f"{student_obj.stu_name}家长添加{teacher_account.employee_name}老师",
                    'teacher_id': teacher_id,
                    'stu_id': stu_id,
                    'class_id': class_id,
                    'remark': f"自动创建的老师渠道码",
                    'create_time': datetime.now(),
                    'update_time': datetime.now(),
                    'create_by': user.uid,
                    'update_by': user.uid
                }
                
                await erp_wechat_channel.create(db, commit=False, **channel_data)
                
        except Exception as e:
            logger.warning(f"创建企微渠道码失败: {str(e)}")
    elif existing_channel:
        wx_qr_code = existing_channel.wx_qrcode or ""
    
    # 查询入群码
    chat_qr_code = ""
    if class_obj.qwechat_id:
        try:
            # 创建或获取入群码
            join_way_result = await qy_wechat_relate.add_join_way(
                scene=2,  # 通过二维码联系
                chat_id_list=[class_obj.qwechat_id],
                remark=f"班级{class_obj.class_name}入群码",
                state=f"class_{class_id}"
            )
            
            if join_way_result and join_way_result.get('errcode') == 0:
                chat_qr_code = join_way_result.get('qr_code', '')
                
        except Exception as e:
            logger.warning(f"获取入群码失败: {str(e)}")
    result = {
        "class_name": class_obj.class_name,
        "teacher_name": teacher_account.employee_name,
        "wx_qr_code": wx_qr_code,
        "chat_qr_code": chat_qr_code
    }
    result = copy.deepcopy(result)
    # 提交数据库事务
    await db.commit()
    
    return await ApiSuccessResponse(result)
