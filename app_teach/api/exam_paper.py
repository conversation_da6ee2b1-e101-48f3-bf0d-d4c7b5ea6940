
from collections import defaultdict
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Depends

from app_teach.modules import get_paper_question
from app_teach.serializer import PaperCreate, PaperQuestionCreate, PaperUpdate, ErpOnlinePaperCourseBase, \
    CreateQuestion, QuestionDetail, QuestionOptionBase
from models.m_online_exam import ErpOnlineQuestion, ErpOnlinePaper, ErpOnlinePaperQuestion, ErpOnlineQuestionOption, \
    ErpOnlineStuPaper, ErpOnlineStuScore, ErpOnlinePaperCourse
from utils.db.account_handler import UserDict, role_required, erp_account
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db
from utils.db.default_crud import generate_crud_routes
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

router = APIRouter(prefix="/paper", tags=["【诊断】试卷"])

erp_online_question = CRUD(ErpOnlineQuestion)
erp_online_paper = CRUD(ErpOnlinePaper)
erp_online_paper_question = CRUD(ErpOnlinePaperQuestion)
erp_online_question_option = CRUD(ErpOnlineQuestionOption)
erp_online_stu_paper = CRUD(ErpOnlineStuPaper)
erp_online_stu_score = CRUD(ErpOnlineStuScore)

allow_role = []


@router.post("/paper")
async def create_paper(
        paper: PaperCreate,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 新增试卷
    下面是所有字段及含义
    - paper_name: 试卷名
    - is_active: 是否激活
    - grade_id: 年级
    - p_grade_id: 年级大类
    - subject_id: 科目
    - is_lock: 是否锁定， 默认0
    - start_time: 开始时间
    - end_time: 结束时间
    - passing_score: 及格分
    - total_score: 总分

    """
    obj = await erp_online_paper.create(db, **{
        "paper_name": paper.paper_name,
        "is_active": paper.is_active or 1,
        "subject_id": paper.subject_id,
        "grade_id": paper.grade_id,
        "p_grade_id": paper.grade_id,
        "is_lock": paper.is_lock or 0,
        "create_by": user.uid,
        "start_time": paper.start_time,
        "end_time": paper.end_time,
        "passing_score": paper.passing_score,
        "total_score": paper.total_score
    })
    return await ApiSuccessResponse(obj)


@router.delete("/paper/{paper_id}")
async def delete_paper(
        paper_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 删除试卷
    """
    await erp_online_paper.delete_one(db, paper_id, commit=False)
    await erp_online_paper_question.delete_many(db, {"paper_id": paper_id}, commit=False)
    await db.commit()
    return await ApiSuccessResponse(True)


@router.put("/paper/{paper_id}")
async def update_paper(
        paper_id: int,
        paper: PaperUpdate,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 修改试卷
    """
    update_item = {}
    if paper.paper_name:
        update_item.update({"paper_name": paper.paper_name})
    if paper.is_active:
        update_item.update({"is_active": paper.is_active})
    if paper.subject_id:
        update_item.update({"subject_id": paper.subject_id})
    if paper.grade_id:
        update_item.update({"grade_id": paper.grade_id})
    if paper.is_lock:
        update_item.update({"is_lock": paper.is_lock})
    if paper.start_time:
        update_item.update({"start_time": paper.start_time})
    if paper.end_time:
        update_item.update({"end_time": paper.end_time})
    if paper.passing_score:
        update_item.update({"passing_score": paper.passing_score})
    if paper.total_score:
        update_item.update({"total_score": paper.total_score})

    obj = await erp_online_paper.update_one(db, paper_id, update_item)
    return await ApiSuccessResponse(obj)


@router.get("/paper")
async def query_paper(page: int = 1, page_size: int = 10,
                      grade_id: int = None, subject_id: int = None,
                      db: AsyncSession = Depends(get_default_db),
                      user: UserDict = Depends(role_required(allow_role)),
                      ):
    """
    # 试卷分页查询
    - id及对应码请访问 `app_teach/online_exam/exam_params`
    - grade_id: 年级id
    - subject_id: 科目id
    """
    raw = [
        ErpOnlinePaper.disable == 0
    ]
    if grade_id:
        raw.append(ErpOnlinePaper.grade_id == grade_id)
    if subject_id:
        raw.append(ErpOnlinePaper.subject_id == subject_id)
    data = await erp_online_paper.get_many_with_pagination(db, page, page_size, raw=raw)
    count_data = await erp_online_paper.get_many(db, raw=raw)
    account = await erp_account.get_many(db)
    account_map = {i.id: i for i in account}
    for i in data:
        account = account_map.get(i.create_by)
        i.create_by_name = account.employee_name if account else "未知用户"
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


@router.get("/paper/{paper_id}")
async def query_paper(paper_id: int,
                      db: AsyncSession = Depends(get_default_db),
                      user: UserDict = Depends(role_required(allow_role)),
                      ):
    """
    # 试卷详情
    - paper
        - question
            - question_type 1 选择 2 填空
            - options 选项(option_content-选项内容, correct) 填空(option_content-排序, answer)
    """
    paper = await get_paper_question(db, paper_id)
    question_ids = [i.id for i in paper.questions]
    # 查询题目选项
    question_options = await erp_online_question_option.get_many(db, raw=[
        ErpOnlineQuestionOption.question_id.in_(question_ids)
    ])
    question_options_map = defaultdict(list)
    for i in question_options:
        question_options_map[i.question_id].append(i)
    # 组装数据
    for i in paper.questions:
        if i.question_type == 2:
            i.blanks = question_options_map.get(i.id)
        else:
            i.options = question_options_map.get(i.id)
    return await ApiSuccessResponse(paper)


# 试卷题目
@router.post("/paper_question")
async def create_paper_question(
        paper_question_entity: PaperQuestionCreate,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 新增试卷题目
    - content
    - difficulty_level: 难度等级,需查询映射表
    - question_type： 1 选择 2 填空
    """
    # 先查询试卷是否存在
    paper = await erp_online_paper.get_by_id(db, paper_question_entity.paper_id)
    if not paper:
        return await ApiSuccessResponse(False, message="试卷不存在")
    # 验证分数是否超过总分
    total_score = paper.total_score
    # 查询已提交题的分数
    # paper_question = await erp_online_paper_question.get_many(db, {"paper_id": paper_question_entity.paper_id})
    # question_ids = [i.question_id for i in paper_question]
    # questions = await erp_online_question.get_many(db, )
    # print(paper_question)
    # exists_score = sum([i.question.score for i in paper_question])
    # if exists_score + paper_question_entity.question.score > total_score:
    #     return await ApiSuccessResponse(False, message="试卷分数超过总分,请重新设置")
    paper_id = paper_question_entity.paper_id
    question = paper_question_entity.question
    sort = paper_question_entity.sort
    question_item = {
        "content": question.content,
        "difficulty_level": question.difficulty_level,
        "score": question.score,
        "knowledge_point": question.knowledge_point,
        "question_type": question.question_type,
        "analysis": question.analysis,
        "create_by": user.uid,
    }
    question_obj = await erp_online_question.create(db, **question_item, commit=False)
    await erp_online_paper_question.create(db, **{
        "paper_id": paper_id,
        "question_id": question_obj.id,
        "sort": sort
    }, commit=False)
    # 保存选项
    if question.question_type == 1:
        for i in question.options:
            await erp_online_question_option.create(db, **{
                "question_id": question_obj.id,
                "option_content": i.option_content,
                "correct": i.correct
            }, commit=False)
    # 保存填空
    elif question.question_type == 2:
        for i in question.blanks:
            await erp_online_question_option.create(db, **{
                "question_id": question_obj.id,
                "option_content": i.option_content,
                "option_score": i.option_score,
                "answer": i.answer
            }, commit=False)
    else:
        return await ApiFailedResponse('题目类型错误')
    await db.commit()
    return await ApiSuccessResponse(True)


@router.delete("/paper_question/{paper_question_id}")
async def delete_paper_question(
        paper_question_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 删除试卷题目
    """
    await erp_online_paper_question.delete_one(db, paper_question_id)
    return await ApiSuccessResponse(True)


# 新增题目
@router.post("/question")
async def create_question(
        question: CreateQuestion,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 新增题目
    - question_type： 1 选择 2 填空
    """
    question_item = {
        "content": question.content,
        "difficulty_level": question.difficulty_level,
        "score": question.score,
        "knowledge_point": question.knowledge_point,
        "question_type": question.question_type,
        "analysis": question.analysis,
        "create_by": user.uid,
    }
    question_obj = await erp_online_question.create(db, **question_item, commit=False)
    if question.options and question.question_type == 1:
        for i in question.options:
            await erp_online_question_option.create(db, **{
                "question_id": question_obj.id,
                "option_content": i.option_content,
                "correct": i.correct
            }, commit=False)
    if question.blanks and question.question_type == 2:
        for i in question.blanks:
            await erp_online_question_option.create(db, **{
                "question_id": question_obj.id,
                "option_content": i.option_content,
                "answer": i.answer
            }, commit=False)
    # 增加paper和question的关联
    if question.paper_id:
        await erp_online_paper_question.create(db, **{
            "paper_id": question.paper_id,
            "question_id": question_obj.id,
            "sort": question.sort
        }, commit=False)
    await db.commit()
    return await ApiSuccessResponse(question_obj)


# 删除题目
@router.delete("/question/{paper_question_id}")
async def delete_question(
        paper_question_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 删除题目（删除题目和试卷的关联）
    """
    await erp_online_paper_question.delete_one(db, paper_question_id)
    return await ApiSuccessResponse(True)


# 修改题目
@router.put("/question/{question_id}")
async def update_question(
        question_id: int,
        question: CreateQuestion,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 修改题目
    -  options: 请用单独接口更新
       blanks: 请用单独接口更新
    """
    question_obj = await erp_online_question.get_by_id(db, question_id)
    if not question_obj:
        return await ApiSuccessResponse(False, message="题目不存在")
    if question.content and question.content != question_obj.content:
        question_obj.content = question.content
    if question.difficulty_level and question.difficulty_level != question_obj.difficulty_level:
        question_obj.difficulty_level = question.difficulty_level
    if question.score and question.score != question_obj.score:
        question_obj.score = question.score
    if question.knowledge_point and question.knowledge_point != question_obj.knowledge_point:
        question_obj.knowledge_point = question.knowledge_point
    if question.analysis and question.analysis != question_obj.analysis:
        question_obj.analysis = question.analysis
    if question.question_type and question.question_type != question_obj.question_type:
        question_obj.question_type = question.question_type

    await db.commit()
    return await ApiSuccessResponse(True)


# 选项或填空的增删改查
# 新增选项
@router.post("/question_option")
async def create_option(
        option: QuestionDetail,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 新增选项或填空
    - option_content: 选项内容或第几空
    """
    if option.option:
        for i in option.option:
            await erp_online_question_option.create(db, **{
                "question_id": option.question_id,
                "option_content": i.option_content,
                "correct": i.correct
            }, commit=False)
    if option.blank:
        for i in option.blank:
            await erp_online_question_option.create(db, **{
                "question_id": option.question_id,
                "option_content": i.option_content,
                "answer": i.answer,
                "option_score": i.option_score
            }, commit=False)
    await db.commit()
    return await ApiSuccessResponse(True)


# 删除选项
@router.delete("/question_option/{option_id}")
async def delete_option(
        option_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 删除选项或填空
    """
    await erp_online_question_option.delete_one(db, option_id)
    return await ApiSuccessResponse(True)


# 修改选项
@router.put("/question_option/{option_id}")
async def update_option(
        option_id: int,
        option: QuestionOptionBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 修改选项或填空
    """
    option_obj = await erp_online_question_option.get_by_id(db, option_id)
    if not option_obj:
        return await ApiSuccessResponse(False, message="选项不存在")
    if option.option_content and option.option_content != option_obj.option_content:
        option_obj.option_content = option.option_content
    if option.correct and option.correct != option_obj.correct:
        # 这里修改选项的正确答案时，需要将原来的正确答案修改为0
        option_obj.correct = option.correct
        if option.old_correct_id:
            old_correct_option = await erp_online_question_option.get_by_id(db, option.old_correct_id)
            if not old_correct_option:
                return await ApiSuccessResponse(False, message="原选项不存在")
            old_correct_option.correct = 0
    if option.answer and option.answer != option_obj.answer:
        option_obj.answer = option.answer
    if option.option_score and option.option_score != option_obj.option_score:
        option_obj.option_score = option.option_score
    await db.commit()
    return await ApiSuccessResponse(True)


# 查询选项
@router.get("/question_option/{question_id}")
async def query_option(
        question_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required(allow_role)),
):
    """
    # 查询选项或填空
    """
    options = await erp_online_question_option.get_many(db, {"question_id": question_id})
    return await ApiSuccessResponse(options)


paper_course_router = generate_crud_routes(
    db_model=ErpOnlinePaperCourse,
    schema=ErpOnlinePaperCourseBase,
    AuthModel=UserDict,
    default_db=get_default_db,
    prefix="paper_course",
    title="试卷对应录取班级"
)

router.include_router(paper_course_router)


@router.get("/paper_course_by_paperid/{paper_id}", description=f"# 查询指定试卷课程")
async def read_item(paper_id: int, db: AsyncSession = Depends(get_default_db),
                    user: UserDict = Depends(role_required([]))):
    crud_instance = CRUD(ErpOnlinePaperCourse)
    objs = await crud_instance.get_many(db, {
        "paper_id": paper_id
    })
    return await ApiSuccessResponse(objs)
