from datetime import datetime
from collections import defaultdict
from app_teach.crud import get_class_by_class_id, get_class_plan_by_id, get_current_class, get_current_plan_info, get_order_data, get_target_class, get_target_class_plan, get_un_end_class_modules, get_valid_class, get_reschedule_record_list, get_transfer_record_list
from app_teach.modules import can_change_class, can_reschedule_class, format_class_info, get_class_student_study_num
from app_teach.serializer import CancelRescheduleClassParams, PaidTransferClassParams, RescheduleClassParams
from models.m_class import ErpClass, ErpClassPlan, ErpClassRescheduling, ErpClassTransfor, ErpCourseOutline
from models.m_order import ErpOrderStudent
from models.m_student import ErpStudent
from modules.classin.classinApiHandler import ClassInSDK
import settings
from utils.db.account_handler import UserDict, role_required
from utils.db.crud_handler import CRUD
from utils.enum.enum_order import StudentState
from utils.other.config_handler import get_config
from utils.response.response_handler import ApiFailedResponse, ApiSuccessResponse
from fastapi import APIRouter
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from utils.db.db_handler import get_default_db, get_redis
from aioredis import Redis


router = APIRouter(prefix="/class_transfor", tags=["转班和调课"])

erp_order_student = CRUD(ErpOrderStudent)
erp_class_transfor = CRUD(ErpClassTransfor)
erp_student = CRUD(ErpStudent)
erp_class_rescheduling = CRUD(ErpClassRescheduling)
erp_class_plan = CRUD(ErpClassPlan)
erp_class = CRUD(ErpClass)
erp_course_outline = CRUD(ErpCourseOutline)


@router.get("/paid_transfer_current_class")
async def query_paid_transfer_current_class(
    stu_id: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
    conf: dict = Depends(get_config),
):
    """
    # 已付费转班 查询当前可转班级
    """
    # settings.logger.info(f"查询可转班级 - 学生ID: {stu_id}")
    
    try:
        # 获取学生信息
        student_obj = await erp_student.get_by_id(db, stu_id)
        if not student_obj:
            # settings.logger.error(f"学生信息不存在 - 学生ID: {stu_id}")
            return await ApiFailedResponse("学生信息不存在")
        
        current_class_objs = await get_current_class(db, stu_id, paid=True)
        # settings.logger.info(f"学生 {stu_id} 当前班级数量: {len(current_class_objs)}")
        
        if not current_class_objs:
            # settings.logger.warning(f"学生 {stu_id} 没有找到可转班的班级")
            return await ApiSuccessResponse([], "暂无可转班的班级")

        # 附加在班学生信息
        class_ids = [i.class_id for i in current_class_objs]
        order_student_objs = await erp_order_student.get_many(db, raw=[
            ErpOrderStudent.class_id.in_(class_ids),
            ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value, StudentState.WAIT_PAY.value])
        ])
        # 按照班级分组并统计数量，组合成{class_id:student_num}
        class_student_num = {}
        for i in order_student_objs:
            if i.class_id not in class_student_num:
                class_student_num[i.class_id] = 0
            class_student_num[i.class_id] += 1
        
        can_transfer_class_objs = []
        # 限制和附加课程信息
        for class_obj in current_class_objs:
            # 转换为字典以便添加属性
            class_dict = dict(class_obj)
            
            class_dict['can_transfer'] = True
            class_dict['stu_id'] = stu_id
            class_dict['stu_name'] = student_obj.stu_name
            class_dict['stu_username'] = student_obj.stu_username
            if class_dict['can_transfer']:
                # 使用公用方法格式化班级信息
                formatted_class = await format_class_info(db, class_dict, conf, include_capacity_info=True)
                can_transfer_class_objs.append(formatted_class)
            else:
                # 加上剩余名额sign_num（对于不能转班的也显示基本信息）
                class_dict['sign_num'] = class_student_num.get(class_dict.get('class_id'), 0)
        
        # settings.logger.info(f"学生 {stu_id} 可转班级数量: {len(can_transfer_class_objs)}")
        return await ApiSuccessResponse(can_transfer_class_objs)
        
    except Exception as e:
        # settings.logger.error(f"查询可转班级失败 - 学生ID: {stu_id}, 错误: {str(e)}")
        return await ApiFailedResponse("查询可转班级失败，请稍后重试")



# 获取可转入的目标班级
@router.get("/paid_transfer_target_class")
async def query_paid_transfer_target_class(
    stu_id: int,
    page: int = 1,
    page_size: int = 10,
    current_class_id: int = None,  # 原班级ID
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
    conf: dict = Depends(get_config),
):
    """
    # 获取可转入的目标班级
    # - 当前时间在限制日期之间的可以转班，否则均不可转班 -> config.transfer_class_date_limit
    - valid_class 新班级未上课节数
    - residue_num 新班级剩余名额
    """
    # settings.logger.info(f"查询可转入目标班级 - 学生ID: {stu_id}, 原班级ID: {current_class_id}, 页码: {page}")
    
    # 参数验证
    if not current_class_id:
        # settings.logger.warning(f"查询目标班级参数错误 - 学生ID: {stu_id}, 原班级ID为空")
        return await ApiFailedResponse('原班级ID不能为空')
    
    # 获取当前班级信息
    current_class_obj = await get_class_by_class_id(db, current_class_id)
    if not current_class_obj:
        # settings.logger.error(f"原班级不存在 - 学生ID: {stu_id}, 班级ID: {current_class_id}")
        return await ApiFailedResponse('原班级不存在')
    
    # settings.logger.info(f"原班级信息 - 班级ID: {current_class_id}, 年级: {current_class_obj.grade_id}, 学科: {current_class_obj.subject_id}, 班型: {current_class_obj.category_id}")
    
    # 获取转班限制条件
    # transfer_class_date_limit = conf.get('transfer_class_date_limit', {})   # 转班限制日期
    # grade_date_limit = transfer_class_date_limit.get(str(current_class_obj.grade_id), [])
    # if not grade_date_limit or len(grade_date_limit) != 2:
    #     settings.logger.error(f"转班限制日期配置错误 - 年级ID: {current_class_obj.grade_id}")
    #     return await ApiFailedResponse('该年级暂不支持转班，请联系客服')
    
    # 根据rules确定可转入的班级
    conditions = await can_change_class(db, 
                                        current_class_obj.grade_id, 
                                        current_class_obj.category_id, 
                                        current_class_obj.subject_id)
    if not conditions:
        # settings.logger.warning(f"未找到转班规则 - 年级: {current_class_obj.grade_id}, 班型: {current_class_obj.category_id}, 学科: {current_class_obj.subject_id}")
        return await ApiFailedResponse('当前班级暂不支持转班，请联系客服')
    
    # settings.logger.info(f"转班规则匹配成功 - 目标班型: {conditions['cate_target']}, 目标学科: {conditions['subject_target']}, 目标年级: {conditions['grade_target']}")
    
    # 获取可转入的目标班级
    target_class_objs = await get_target_class(db, page, page_size, current_class_id, conditions['cate_target'], conditions['subject_target'], conditions['grade_target'])
    # settings.logger.info(f"查询到目标班级数量: {len(target_class_objs)}")
    
    new_class_objs = []
    for class_obj in target_class_objs:
        # 验证目标班级是否可以转入
        # 验证班级人数 
        normal_student_num = await get_class_student_study_num(db, class_obj.class_id)
        if normal_student_num >= class_obj.class_capacity:
            # 班级人数已满，无法转入
            settings.logger.info(f"班级 {class_obj.class_id} 人数已满，跳过 - 当前人数: {normal_student_num}, 容量: {class_obj.class_capacity}")
            continue
        
        # 使用公用方法格式化班级信息
        formatted_class = await format_class_info(db, class_obj, conf, include_capacity_info=True)
        
        # settings.logger.info(f"可转入班级 - ID: {formatted_class.get('id')}, 剩余名额: {formatted_class.get('residue_num')}, 未上课节数: {formatted_class.get('valid_class')}")
        new_class_objs.append(formatted_class)
    
    # settings.logger.info(f"最终可转入班级数量: {len(new_class_objs)}")
    if not new_class_objs:
        return await ApiSuccessResponse([], "暂无可转入的班级，请稍后再试")
        
    return await ApiSuccessResponse(new_class_objs)
        

# 转班
@router.post("/paid_transfer_class")
async def paid_transfer_class_api(
    params: PaidTransferClassParams,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
    redis_client: Redis = Depends(get_redis),
    conf: dict = Depends(get_config),
):
    """
    # 转班
    - old_order_student_id 原学生订单ID
    - new_class_id 新班级ID
    - transfor_reason 转班原因
    - transfor_num 转班次数
    - is_inner 是否内部转班  学生自己转传0, 教务转传1
    """
    settings.logger.info(f"开始转班操作 - 学生ID: {params.stu_id}, 原订单ID: {params.old_order_student_id}, 新班级ID: {params.new_class_id}, 转班原因: {params.transfor_reason}")

    # 避免重复转班
    erp_class_transfor_obj = await erp_class_transfor.get_one(db, 
                                                              old_order_student_id=params.old_order_student_id,
                                                              new_class_id=params.new_class_id)
    if erp_class_transfor_obj:
        msg = f"已有转班记录，请勿重复转班 - 学生ID: {params.stu_id}, 原订单ID: {params.old_order_student_id}, 新班级ID: {params.new_class_id}"
        settings.logger.warning(msg)
        return await ApiFailedResponse(msg)
    
    # 添加redis转班锁 old_order_student_id 和 new_class_id 的锁 60s过期
    lock_key = f"transfer_class_lock:{params.old_order_student_id}:{params.new_class_id}"
    lock = await redis_client.get(lock_key)
    if lock:
        msg = f"转班正在处理中，请勿重复转班 - 学生ID: {params.stu_id}, 原订单ID: {params.old_order_student_id}, 新班级ID: {params.new_class_id}"
        settings.logger.warning(msg)
        return await ApiFailedResponse(msg)
    # 添加锁
    await redis_client.set(lock_key, "1", ex=60)
    
    # 参数验证
    if not params.old_order_student_id or not params.new_class_id:
        msg = f"转班参数错误 - 学生ID: {params.stu_id}, 原订单ID: {params.old_order_student_id}, 新班级ID: {params.new_class_id}"
        settings.logger.warning(msg)
        return await ApiFailedResponse(msg)
    
    if params.transfor_num <= 0:
        msg = f"转班课时数错误 - 学生ID: {params.stu_id}, 转班课时: {params.transfor_num}"
        settings.logger.warning(msg)
        return await ApiFailedResponse(msg)

    current_order_student_obj = await erp_order_student.get_by_id(db, params.old_order_student_id)
    if not current_order_student_obj:
        msg = f"原学生订单不存在 - 学生ID: {params.stu_id}, 订单ID: {params.old_order_student_id}"
        settings.logger.error(msg)
        return await ApiFailedResponse(msg)
    
    # 验证订单归属
    if current_order_student_obj.stu_id != params.stu_id:
        msg = f"订单归属验证失败 - 当前学生ID: {params.stu_id}, 订单学生ID: {current_order_student_obj.stu_id}"
        settings.logger.error(msg)
        return await ApiFailedResponse(msg)
    
    # 验证原学生订单状态
    if current_order_student_obj.student_state not in [StudentState.NORMAL.value, StudentState.TRANSFER_IN.value]:
        msg = f"原学生订单状态不正确 - 学生ID: {params.stu_id}, 订单状态: {current_order_student_obj.student_state}"
        settings.logger.error(msg)
        return await ApiFailedResponse(msg)
    
    settings.logger.info(f"原订单验证通过 - 订单ID: {params.old_order_student_id}, 班级ID: {current_order_student_obj.class_id}, 状态: {current_order_student_obj.student_state}")
    
    # 获取学生信息
    student_obj = await erp_student.get_by_id(db, current_order_student_obj.stu_id)
    if not student_obj:
        msg = f"学生信息不存在 - 学生ID: {params.stu_id}"
        settings.logger.error(msg)
        return await ApiFailedResponse(msg)
    
    # 获取旧班级信息（用于ClassIn同步）
    old_class_obj = await get_class_by_class_id(db, current_order_student_obj.class_id)
    if not old_class_obj:
        msg = f"原班级信息不存在 - 学生ID: {params.stu_id}, 原班级ID: {current_order_student_obj.class_id}"
        settings.logger.error(msg)
        return await ApiFailedResponse(msg)
    
    # 获取新班级信息
    erp_class_obj = await get_class_by_class_id(db, params.new_class_id)
    if not erp_class_obj:
        msg = f"新班级不存在 - 学生ID: {params.stu_id}, 新班级ID: {params.new_class_id}"
        settings.logger.error(msg)
        return await ApiFailedResponse(msg)

    # 获取新班级计划课节数（直接从班级对象获取）
    planning_class_times = erp_class_obj.planning_class_times or 0
    if planning_class_times < params.transfor_num:
        msg = f"新班级计划课节不足 - 班级ID: {params.new_class_id}, 计划课节: {planning_class_times}, 需要课节: {params.transfor_num}"
        settings.logger.warning(msg)
        return await ApiFailedResponse(msg)
    
    # 验证名额
    normal_student_num = await get_class_student_study_num(db, params.new_class_id)
    if normal_student_num >= erp_class_obj.class_capacity:
        msg = f"新班级名额已满 - 班级ID: {params.new_class_id}, 当前人数: {normal_student_num}, 容量: {erp_class_obj.class_capacity}"
        settings.logger.warning(msg)
        return await ApiFailedResponse(msg)
    
    settings.logger.info(f"转班前置验证通过，开始创建转班记录")
    
    # 创建转班学生订单
    new_order_student_obj = await erp_order_student.create(db, commit=False, **{
        'class_id': params.new_class_id,
        'stu_id': current_order_student_obj.stu_id,
        'student_state': StudentState.TRANSFER_IN.value,     # 未付款的转班，这里需要为待付款-WAIT_PAY（锁定名额）
        'create_by': user.uid,
        'create_time': datetime.now(),
        'update_by': user.uid,
        'update_time': datetime.now(),
        'total_hours': params.transfor_num,
        'complete_hours': 0,
        'order_class_type': 1,
        'p_id': current_order_student_obj.id,
    })
    
    # 修改旧班级状态，如果就转出数量<原来的总数，那么不改变学生状态; 如果是全部转出，那么需要改变学生状态TRANSFER_OUT
    if (current_order_student_obj.total_hours - current_order_student_obj.complete_hours) > params.transfor_num:
        current_order_student_obj.update_time = datetime.now()
        current_order_student_obj.total_hours = current_order_student_obj.total_hours - params.transfor_num
    else:
        current_order_student_obj.total_hours = 0
        current_order_student_obj.student_state = StudentState.TRANSFER_OUT.value    # 旧班级统一为转出，不做其他区分
        current_order_student_obj.update_time = datetime.now()

    
    # 创建转班记录
    await erp_class_transfor.create(db, commit=False, **{
        'old_order_student_id': current_order_student_obj.id,
        'new_order_student_id': new_order_student_obj.id,
        'old_class_id': current_order_student_obj.class_id,
        'new_class_id': params.new_class_id,
        'transfor_reason': params.transfor_reason,
        'transfor_num': params.transfor_num,
        'create_by': user.uid,
        'create_time': datetime.now(),
        'update_by': user.uid,
        'update_time': datetime.now(),
        'is_inner': params.is_inner,
    })
    
    settings.logger.info(f"转班记录创建成功，开始同步ClassIn")
    
    # 同步ClassIn
    classin_sync_success = True
    try:
        classin_sdk = ClassInSDK()
        # 1. 添加该学生在新班级的数据
        # 添加学生到课程
        add_resp = await classin_sdk.classroom.add_course_student(
            course_id=erp_class_obj.classin_id,
            student_uid=student_obj.classin_uid,
            student_name=student_obj.stu_name,
            identity=1   # 1-学生，2-旁听
        )
        settings.logger.info(f'添加学生到新课程成功 - 学生: {student_obj.stu_name}, 课程ID: {erp_class_obj.classin_id}, 响应: {add_resp}')
        
        # 2. 移除该学生在旧班级的数据,全部转出时，需要删除学生
        if current_order_student_obj.total_hours <= params.transfor_num:
            remove_resp = await classin_sdk.classroom.delete_course_student(
                course_id=old_class_obj.classin_id,
                student_uid=student_obj.classin_uid,
                identity=1   # 1-学生，2-旁听
            )
            settings.logger.info(f'从旧课程移除学生成功 - 学生: {student_obj.stu_name}, 课程ID: {old_class_obj.classin_id}, 响应: {remove_resp}')
        
    except Exception as e:
        classin_sync_success = False
        settings.logger.error(f'同步ClassIn失败 - 学生ID: {params.stu_id}, 错误: {str(e)}')
    finally:
        # 提交数据库事务
        await db.commit()
    
    if classin_sync_success:
        return await ApiSuccessResponse(True)
    else:
        return await ApiSuccessResponse(False)
    
########################### 以下是调课信息 #########################################


@router.get("/reschedule_current_class")
async def query_paid_transfer_current_class(
    stu_id:int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
    conf: dict = Depends(get_config),
):
    """
    #  查询当前可调课班级
    """
    settings.logger.info(f"查询可调课班级 - 学生ID: {stu_id}")
    
    current_class_objs = await get_current_class(db, stu_id, paid=True)
    settings.logger.info(f"学生 {stu_id} 当前班级数量: {len(current_class_objs)}")
    
    if not current_class_objs:
        settings.logger.warning(f"学生 {stu_id} 没有找到可调课的班级")
        return await ApiSuccessResponse([], "暂无可调课的班级")
    
    # # 从数据库获取调课限制条件
    # rescheduling_class_date_limit = conf.get('rescheduling_class_date_limit', {})   # 调课限制日期
    # settings.logger.info(f"调课时间限制配置: {rescheduling_class_date_limit}")
    
    can_reschedule_class_objs = []
    # 限制和附加课程信息
    for class_obj in current_class_objs:
        # 使用公用方法格式化班级信息
        formatted_class = await format_class_info(db, class_obj, conf, include_capacity_info=True)
        # 保留调课相关的特殊字段
        formatted_class['can_reschedule'] = True
        can_reschedule_class_objs.append(formatted_class)
    
    settings.logger.info(f"学生 {stu_id} 可调课班级数量: {len(can_reschedule_class_objs)}")
    return await ApiSuccessResponse(can_reschedule_class_objs)


# 根据plan_id获取上课计划
@router.get("/class_plan_by_id/{class_plan_id}")
async def get_class_plan(
    class_plan_id: int,
    stu_id:int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 根据plan_id获取上课计划
    """
    # settings.logger.info(f"根据plan_id获取上课计划 - 学生ID: {stu_id}, 上课计划ID: {class_plan_id}")

    class_plan_obj = await get_class_plan_by_id(db, class_plan_id)
    if not class_plan_obj:
        settings.logger.error(f"上课计划不存在 - 学生ID: {stu_id}, 上课计划ID: {class_plan_id}")
        return await ApiFailedResponse('上课计划不存在')
    
    return await ApiSuccessResponse(class_plan_obj)
    

# 获取可调课的目标班级
@router.get("/reschedule_target_class")
async def query_reschedule_target_class(
    stu_id:int,
    page: int = 1,
    page_size: int = 10,
    current_class_id: int = None,  # 原班级ID
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
    conf: dict = Depends(get_config),
):
    """
    # 获取可调课的目标班级
    - 当前时间在限制日期之间的可以调课，否则均不可调课 -> config.rescheduling_class_date_limit
    - valid_class 新班级未上课节数
    - residue_num 新班级剩余名额
    """
    settings.logger.info(f"查询可调课目标班级 - 学生ID: {stu_id}, 原班级ID: {current_class_id}, 页码: {page}")
    
    # 参数验证
    if not current_class_id:
        settings.logger.warning(f"查询调课目标班级参数错误 - 学生ID: {stu_id}, 原班级ID为空")
        return await ApiFailedResponse('原班级ID不能为空')
    
    # 获取当前班级信息
    current_class_obj = await get_class_by_class_id(db, current_class_id)
    if not current_class_obj:
        settings.logger.error(f"原班级不存在 - 学生ID: {stu_id}, 班级ID: {current_class_id}")
        return await ApiFailedResponse('原班级不存在')
    
    settings.logger.info(f"原班级信息 - 班级ID: {current_class_id}, 年级: {current_class_obj.grade_id}, 学科: {current_class_obj.subject_id}, 班型: {current_class_obj.category_id}")
    
    # 获取调课限制条件
    rescheduling_class_date_limit = conf.get('rescheduling_class_date_limit', {})   # 调课限制日期
    grade_date_limit = rescheduling_class_date_limit.get(str(current_class_obj.grade_id), [])
    if not grade_date_limit or len(grade_date_limit) != 2:
        settings.logger.error(f"调课限制日期配置错误 - 年级ID: {current_class_obj.grade_id}")
        return await ApiFailedResponse('抱歉，该年级暂不支持调课功能，如有疑问请联系客服')
    
    # 根据rules确定可调课的班级
    conditions = await can_reschedule_class(db, 
                                        current_class_obj.grade_id, 
                                        current_class_obj.category_id, 
                                        current_class_obj.subject_id)
    if not conditions:
        settings.logger.warning(f"未找到调课规则 - 年级: {current_class_obj.grade_id}, 班型: {current_class_obj.cate_id}, 学科: {current_class_obj.subject_id}")
        return await ApiFailedResponse('当前班级暂不支持调课，请联系客服')
    
    settings.logger.info(f"调课规则匹配成功 - 目标班型: {conditions['cate_target']}, 目标学科: {conditions['subject_target']}, 目标年级: {conditions['grade_target']}")
    
    # 获取可调课的目标班级
    target_class_objs = await get_target_class(db, page, page_size, current_class_id, conditions['cate_target'], conditions['subject_target'], conditions['grade_target'])
    settings.logger.info(f"查询到调课目标班级数量: {len(target_class_objs)}")
    
    new_class_objs = []
    for class_obj in target_class_objs:
        # 验证目标班级是否可以调课
        # 验证班级人数 
        normal_student_num = await get_class_student_study_num(db, class_obj.class_id)
        if normal_student_num >= class_obj.class_capacity:
            # 班级人数已满，无法调课
            settings.logger.info(f"班级 {class_obj.class_id} 人数已满，跳过 - 当前人数: {normal_student_num}, 容量: {class_obj.class_capacity}")
            continue
        
        # 使用公用方法格式化班级信息
        formatted_class = await format_class_info(db, class_obj, conf, include_capacity_info=True)
        
        settings.logger.info(f"可调课班级 - ID: {formatted_class.get('id')}, 剩余名额: {formatted_class.get('residue_num')}, 未上课节数: {formatted_class.get('valid_class')}")
        new_class_objs.append(formatted_class)
    
    settings.logger.info(f"最终可调课班级数量: {len(new_class_objs)}")
    if not new_class_objs:
        return await ApiSuccessResponse([], "暂无可调课的班级，请稍后再试")
        
    return await ApiSuccessResponse(new_class_objs)
        

# 获取我的排课
@router.get("/my_class_plan")
async def get_my_class_plan(
    class_id: int,
    stu_id:int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 获取某班级我的排课信息
    - 查出的排课有关联是否进行过我的调出课
    - 包含排课对应的大纲信息
    """
    settings.logger.info(f"查询学生排课信息 - 学生ID: {stu_id}, 班级ID: {class_id}")
    
    # 参数验证
    if not class_id:
        settings.logger.warning(f"查询排课参数错误 - 学生ID: {stu_id}, 班级ID为空")
        return await ApiFailedResponse('班级ID不能为空')

    current_plan_info = await get_current_plan_info(db, class_id, stu_id)
    settings.logger.info(f"学生 {stu_id} 班级 {class_id} 排课信息数量: {len(current_plan_info)}")
    
    if not current_plan_info:
        settings.logger.warning(f"学生 {stu_id} 班级 {class_id} 没有找到排课信息")
        return await ApiSuccessResponse([], "暂无排课信息")
    
    # 获取课程ID列表
    course_id_list = [i.course_id for i in current_plan_info if i.course_id]
    course_id_list = list(set(course_id_list))  # 去重
    
    # 查询课程大纲
    course_outline = []
    if course_id_list:
        course_outline = await erp_course_outline.get_many(db, raw=[
            ErpCourseOutline.course_id.in_(course_id_list),
            ErpCourseOutline.disable == 0
        ])
    
    # 按照课程分组聚合大纲
    course_outline_map = defaultdict(list)
    for outline in course_outline:
        course_outline_map[outline.course_id].append(outline)
    
    # 为每个排课信息添加大纲信息
    new_data = []
    for index, plan_info in enumerate(current_plan_info):
        item = dict(plan_info)
        
        # 获取当前课程的所有大纲，并按id字段排序
        current_course_outlines = course_outline_map.get(plan_info.course_id, [])
        sorted_course_outlines = sorted(current_course_outlines, key=lambda x: x.id)
        
        # 检查索引是否超出大纲列表范围，防止IndexError
        # 如果课程计划的数量多于课程大纲的数量，或者两者顺序不完全匹配，
        # 直接使用 `index` 可能会导致 `IndexError`。
        if index < len(sorted_course_outlines):
            item['course_outline_index'] = sorted_course_outlines[index]
        else:
            # 如果当前索引没有对应的大纲，则设置为None。
            # 这表示无法为该课程计划找到匹配的大纲，或者大纲数量不足。
            item['course_outline_index'] = None
            # 可以选择在此处添加日志记录，以便后续排查问题
            # settings.logger.info(f"课程计划 {plan_info.plan_id} (索引 {index}) 对应的课程 {plan_info.course_id} 没有在大纲列表中找到匹配的索引。总大纲数: {len(sorted_course_outlines)}")
        
        new_data.append(item)
    
    settings.logger.info(f"学生 {stu_id} 班级 {class_id} 排课信息处理完成，包含大纲信息")
    return await ApiSuccessResponse(new_data)
        


# 获取目标班级的排课
@router.get("/my_target_class_plan")
async def get_my_target_class_plan(
    class_id: int,
    stu_id:int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 获取目标班级的排课
    - 查出的排课有关联是否进行过我的调入课
    """
    settings.logger.info(f"查询目标班级排课信息 - 学生ID: {stu_id}, 班级ID: {class_id}")
    
    # 参数验证
    if not class_id:
        settings.logger.warning(f"查询目标班级排课参数错误 - 学生ID: {stu_id}, 班级ID为空")
        return await ApiFailedResponse('班级ID不能为空')
    
    target_plan_info = await get_target_class_plan(db, class_id, stu_id)
    settings.logger.info(f"学生 {stu_id} 目标班级 {class_id} 排课信息数量: {len(target_plan_info)}")
    
    if not target_plan_info:
        settings.logger.warning(f"学生 {stu_id} 目标班级 {class_id} 没有找到排课信息")
        return await ApiSuccessResponse([], "暂无排课信息")
    
    return await ApiSuccessResponse(target_plan_info)
        
# 调课
@router.post("/reschedule_class")
async def create_reschedule_class(
    params: RescheduleClassParams,
    stu_id:int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 调课
    - order_student_id 学生订单ID
    - old_plan_id 原排课ID
    - new_plan_id 新排课ID
    - reschedule_reason 调课原因
    - is_inner 是否教务调课
    """
    settings.logger.info(f"开始调课操作 - 学生ID: {stu_id}, 订单ID: {params.order_student_id}, 原排课ID: {params.old_plan_id}, 新排课ID: {params.new_plan_id}, 调课原因: {params.reschedule_reason}")
    
    # 参数验证
    if not params.order_student_id or not params.old_plan_id or not params.new_plan_id:
        settings.logger.warning(f"调课参数错误 - 学生ID: {stu_id}, 订单ID: {params.order_student_id}, 原排课ID: {params.old_plan_id}, 新排课ID: {params.new_plan_id}")
        return await ApiFailedResponse('学生订单ID、原排课ID和新排课ID不能为空')
    
    if params.old_plan_id == params.new_plan_id:
        settings.logger.warning(f"调课参数错误 - 学生ID: {stu_id}, 原排课ID和新排课ID相同: {params.old_plan_id}")
        return await ApiFailedResponse('原排课和新排课不能相同')
    
    # 验证学生订单归属
    erp_order_student = CRUD(ErpOrderStudent)
    order_student_obj = await erp_order_student.get_by_id(db, params.order_student_id)
    if not order_student_obj:
        settings.logger.error(f"学生订单不存在 - 学生ID: {stu_id}, 订单ID: {params.order_student_id}")
        return await ApiFailedResponse('学生订单不存在')
    
    if order_student_obj.stu_id != stu_id:
        settings.logger.error(f"订单归属验证失败 - 当前学生ID: {stu_id}, 订单学生ID: {order_student_obj.stu_id}")
        return await ApiFailedResponse('无权操作此订单')
    
    if order_student_obj.self_change_class > 3:
        settings.logger.error(f"学生调课次数超过3次 - 学生ID: {stu_id}, 订单ID: {params.order_student_id}")
        return await ApiFailedResponse('学生调课次数超过3次，无法进行调课')
    
    settings.logger.info(f"学生订单验证通过 - 订单ID: {params.order_student_id}, 班级ID: {order_student_obj.class_id}")
    
    # 创建调课记录
    await erp_class_rescheduling.create(db, commit=False, **{
        'order_student_id': params.order_student_id,
        'old_plan_id': params.old_plan_id,
        'new_plan_id': params.new_plan_id,
        'reschedule_reason': params.reschedule_reason,
        'create_by': user.uid,
        'create_time': datetime.now(),
        'update_by': user.uid,
        'update_time': datetime.now(),
        'is_inner': params.is_inner,
    })
    
    settings.logger.info(f"调课记录创建成功，开始同步ClassIn")
    
    # 同步classin
    old_plan_obj = await erp_class_plan.get_by_id(db, params.old_plan_id)
    new_plan_obj = await erp_class_plan.get_by_id(db, params.new_plan_id)
    if not old_plan_obj or not new_plan_obj:
        settings.logger.error(f"排课信息不存在 - 原排课ID: {params.old_plan_id}, 新排课ID: {params.new_plan_id}")
        return await ApiFailedResponse('排课信息不存在，调课失败')
    if not old_plan_obj.classin_activity_id or not new_plan_obj.classin_activity_id:
        settings.logger.error(f"ClassIn活动ID不存在 - 原活动ID: {old_plan_obj.classin_activity_id}, 新活动ID: {new_plan_obj.classin_activity_id}")
    # 获取班级信息
    old_class_obj = await erp_class.get_by_id(db, old_plan_obj.class_id)
    new_class_obj = await erp_class.get_by_id(db, new_plan_obj.class_id)
    if not old_class_obj or not new_class_obj:
        settings.logger.error(f"班级信息不存在 - 原班级ID: {old_plan_obj.class_id}, 新班级ID: {new_plan_obj.class_id}")
        return await ApiFailedResponse('班级信息异常，无法完成调课，请联系客服处理')
    
    # 获取学生信息
    erp_student = CRUD(ErpStudent)
    stu_obj = await erp_student.get_by_id(db, order_student_obj.stu_id)
    if not stu_obj:
        settings.logger.error(f"学生信息不存在 - 学生ID: {order_student_obj.stu_id}")
        return await ApiFailedResponse('学生信息不存在，调课失败')
    
    settings.logger.info(f"调课信息验证通过 - 学生: {stu_obj.stu_name}, 原班级: {old_class_obj.class_name}, 新班级: {new_class_obj.class_name}")
    
    # 同步ClassIn
    classin_sync_success = True
    try:
        classin_sdk = ClassInSDK()
        # 1. 添加该学生在新班级课节的数据
        add_resp = await classin_sdk.lms.add_activity_student(
            course_id=new_class_obj.classin_id,
            activity_id=new_plan_obj.classin_activity_id,
            student_uids=[stu_obj.classin_uid]
        )
        settings.logger.info(f'添加学生到新活动成功 - 学生: {stu_obj.stu_name}, 课程ID: {new_class_obj.classin_id}, 活动ID: {new_plan_obj.classin_activity_id}, 响应: {add_resp}')
        
        # 2. 移除该学生在旧班级课节的数据
        remove_resp = await classin_sdk.lms.delete_activity_student(
            course_id=old_class_obj.classin_id,
            activity_id=old_plan_obj.classin_activity_id,
            student_uids=[stu_obj.classin_uid]
        )
        settings.logger.info(f'从旧活动移除学生成功 - 学生: {stu_obj.stu_name}, 课程ID: {old_class_obj.classin_id}, 活动ID: {old_plan_obj.classin_activity_id}, 响应: {remove_resp}')
        
    except Exception as e:
        classin_sync_success = False
        settings.logger.error(f'同步ClassIn失败 - 学生ID: {stu_id}, 错误: {str(e)}')
    
    if classin_sync_success:
        order_student_obj.self_change_class = int(order_student_obj.self_change_class) + 1
    await db.commit()
    
    if classin_sync_success:
        return await ApiSuccessResponse('调课成功')
    else:
        return await ApiSuccessResponse('调课成功，但课程平台同步可能有问题，如有问题请联系客服')
        


# 取消目标班级的调课
@router.post("/cancel_reschedule_class")
async def cancel_target_class_reschedule(
    params: CancelRescheduleClassParams,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 取消目标班级的调课
    - reschedule_id 调课ID
    """
    settings.logger.info(f"开始取消调课操作 - 学生ID: {params.stu_id}, 调课ID: {params.reschedule_id}")
    
    # 参数验证
    if not params.reschedule_id:
        settings.logger.warning(f"取消调课参数错误 - 学生ID: {params.stu_id}, 调课ID为空")
        return await ApiFailedResponse('调课ID不能为空')
    
    reschedule_obj = await erp_class_rescheduling.get_by_id(db, params.reschedule_id)
    if not reschedule_obj:
        settings.logger.error(f"调课信息不存在 - 学生ID: {params.stu_id}, 调课ID: {params.reschedule_id}")
        return await ApiFailedResponse('调课信息不存在')
    
    # 验证调课记录归属
    erp_order_student = CRUD(ErpOrderStudent)
    order_student_obj = await erp_order_student.get_by_id(db, reschedule_obj.order_student_id)
    if not order_student_obj or order_student_obj.stu_id != params.stu_id:
        settings.logger.error(f"调课记录归属验证失败 - 学生ID: {params.stu_id}, 调课ID: {params.reschedule_id}")
        return await ApiFailedResponse('无权操作此调课记录')
    
    settings.logger.info(f"调课记录验证通过 - 调课ID: {params.reschedule_id}, 原排课ID: {reschedule_obj.old_plan_id}, 新排课ID: {reschedule_obj.new_plan_id}")
    
    # 数据库先移除调课
    reschedule_obj.disable = 1
    reschedule_obj.update_by = user.uid
    reschedule_obj.update_time = datetime.now()
    
    # 获取排课信息
    old_plan_obj = await erp_class_plan.get_by_id(db, reschedule_obj.old_plan_id)
    new_plan_obj = await erp_class_plan.get_by_id(db, reschedule_obj.new_plan_id)
    if not old_plan_obj or not new_plan_obj:
        settings.logger.error(f"排课信息不存在 - 原排课ID: {reschedule_obj.old_plan_id}, 新排课ID: {reschedule_obj.new_plan_id}")
        return await ApiFailedResponse('排课信息不存在')
    
    # 获取班级信息
    old_class_obj = await erp_class.get_by_id(db, old_plan_obj.class_id)
    new_class_obj = await erp_class.get_by_id(db, new_plan_obj.class_id)
    if not old_class_obj or not new_class_obj:
        settings.logger.error(f"班级信息不存在 - 原班级ID: {old_plan_obj.class_id}, 新班级ID: {new_plan_obj.class_id}")
        return await ApiFailedResponse('班级信息异常，无法取消调课，请联系客服处理')
    
    # 获取学生信息
    erp_student = CRUD(ErpStudent)
    stu_obj = await erp_student.get_by_id(db, order_student_obj.stu_id)
    if not stu_obj:
        settings.logger.error(f"学生信息不存在 - 学生ID: {order_student_obj.stu_id}")
        return await ApiFailedResponse('学生信息不存在')
    
    settings.logger.info(f"取消调课信息验证通过 - 学生: {stu_obj.stu_name}, 原班级: {old_class_obj.class_name}, 新班级: {new_class_obj.class_name}")
    
    # 删除新班级课节的学生并增加旧班级课节的学生
    classin_sync_success = True
    try:
        classin_sdk = ClassInSDK()
        # 1. 删除该学生在新班级课节的数据
        delete_resp = await classin_sdk.lms.delete_activity_student(
            course_id=new_class_obj.classin_id,
            activity_id=new_plan_obj.classin_activity_id,
            student_uids=[stu_obj.classin_uid]
        )
        settings.logger.info(f'从新活动删除学生成功 - 学生: {stu_obj.stu_name}, 课程ID: {new_class_obj.classin_id}, 活动ID: {new_plan_obj.classin_activity_id}, 响应: {delete_resp}')
        
        # 2. 增加该学生到旧班级课节的数据
        add_resp = await classin_sdk.lms.add_activity_student(
            course_id=old_class_obj.classin_id,
            activity_id=old_plan_obj.classin_activity_id,
            student_uids=[stu_obj.classin_uid]
        )
        settings.logger.info(f'增加学生到旧活动成功 - 学生: {stu_obj.stu_name}, 课程ID: {old_class_obj.classin_id}, 活动ID: {old_plan_obj.classin_activity_id}, 响应: {add_resp}')
        
    except Exception as e:
        classin_sync_success = False
        settings.logger.error(f'同步ClassIn失败 - 学生ID: {params.stu_id}, 错误: {str(e)}')
    
    # 提交数据库事务
    await db.commit()
    
    if classin_sync_success:
        return await ApiSuccessResponse('取消调课成功')
    else:
        return await ApiSuccessResponse('取消调课成功，但课程平台同步可能有延迟，如有问题请联系客服')


# 获取学生未结课班级
@router.get("/get_un_end_class")
async def get_un_end_class(
    stu_id:int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 获取学生未结课的班级（erp_class.class_status !=3）
    - 返回信息： 学生id、姓名、手机号、在读class_id、班级名称、已上课节数、总课节数、order_student_id
    """
    un_end_classes = await get_un_end_class_modules(db, stu_id)

    # 将结果转换为字典列表
    result_data = []
    for row in un_end_classes:
        class_info = {
            'stu_id': row.stu_id,
            'stu_name': row.stu_name,
            'stu_username': row.stu_username,
            'class_id': row.class_id,
            'class_name': row.class_name,
            'complete_hours': float(row.complete_hours) if row.complete_hours else 0.0,
            'total_hours': float(row.total_hours) if row.total_hours else 0.0,
            'order_student_id': row.order_student_id,
        }
        result_data.append(class_info)
    if not result_data:
        return await ApiSuccessResponse([], "暂无未结课班级")
    
    return await ApiSuccessResponse(result_data)
        



# 获取学员名单
@router.get("/get_stu_list")
async def get_stu_list(
    page: int = 1,
    page_size: int = 10,
    course_id: int = None,
    class_id: int = None,
    stu_name: str = None, # 模糊搜
    stu_username: str = None,  # 精确搜
    start_time: str = None,
    end_time: str = None,
    student_state: int = None,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 分页获取学员名单
    - 返回信息： 学生id、order_student_id、姓名、手机号、在读class_id、
    班级名称、课程名称、关联订单（erp_order_student.id=erp_order.order_student_id,因此可能是多个订单）、
    老师、教室，报名时间、应收金额、实收金额、退款详情（erp_order中的退款的项，多个）、已上课节数、总课节数，学生状态（student_state）、类型（转班/正常报入）
    """
    settings.logger.info(f"查询学员名单 - 页码: {page}, 每页: {page_size}, 课程ID: {course_id}, 班级ID: {class_id}, 学生姓名: {stu_name}, 手机号: {stu_username}")
    
    try:
        from app_teach.crud import get_student_list_with_conditions, get_order_financial_data, get_refund_details, get_transfer_info, get_order_data
        from app_teach.modules import process_student_list_data
        
        # 查询学员基础数据
        student_data, total_count = await get_student_list_with_conditions(
            db, page, page_size, course_id, class_id, stu_name, stu_username, start_time, end_time, student_state
        )
        
        settings.logger.info(f"查询到学员数据 {len(student_data)} 条，总数: {total_count}")
        
        if not student_data:
            return await ApiSuccessResponse({
                "data": [],
                "total_count": 0,
                "page": page,
                "page_size": page_size
            })
        
        # 获取所有order_student_id用于查询关联信息
        order_student_ids = [row.order_student_id for row in student_data]
        
        # 并行查询关联数据
        order_financial_data = await get_order_financial_data(db, order_student_ids)
        order_data = await get_order_data(db, order_student_ids)
        refund_data = await get_refund_details(db, order_student_ids)
        transfer_data = await get_transfer_info(db, order_student_ids)
        
        # 处理和组装最终数据
        result_data = await process_student_list_data(student_data, order_financial_data, refund_data, transfer_data, order_data)
        
        settings.logger.info(f"学员名单查询完成，返回 {len(result_data)} 条数据")
        
        return await ApiSuccessResponse({
            "data": result_data,
            "total_count": total_count,
            "page": page,
            "page_size": page_size
        })
        
    except Exception as e:
        settings.logger.error(f"查询学员名单失败 - 错误: {str(e)}")
        return await ApiFailedResponse("查询学员名单失败，请稍后重试")
        

# 获取学员名单 - 优化版本
@router.get("/get_stu_list_optimized")
async def get_stu_list_optimized(
    page: int = 1,
    page_size: int = 10,
    course_id: int = None,
    class_id: int = None,
    stu_name: str = None, # 模糊搜
    stu_username: str = None,  # 精确搜
    start_time: str = None,
    end_time: str = None,
    student_state: int = None,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 分页获取学员名单 - 性能优化版本
    - 优化：减少数据库查询次数，从5次查询优化为2次查询
    - 返回信息：学生id、order_student_id、姓名、手机号、在读class_id、
    班级名称、课程名称、关联订单、老师、教室，报名时间、应收金额、实收金额、
    退款详情、已上课节数、总课节数，学生状态、类型（转班/正常报入）
    """
    settings.logger.info(f"查询学员名单(优化版) - 页码: {page}, 每页: {page_size}, 课程ID: {course_id}, 班级ID: {class_id}, 学生姓名: {stu_name}, 手机号: {stu_username}")
    
    try:
        from app_teach.crud import get_student_list_optimized, get_additional_data_optimized, process_student_list_data_optimized
        
        # 第一次查询：获取学员基础数据和财务汇总
        student_data, total_count = await get_student_list_optimized(
            db, page, page_size, course_id, class_id, stu_name, stu_username, start_time, end_time, student_state
        )
        
        settings.logger.info(f"查询到学员数据 {len(student_data)} 条，总数: {total_count}")
        
        if not student_data:
            return await ApiSuccessResponse({
                "data": [],
                "total_count": 0,
                "page": page,
                "page_size": page_size
            })
        
        # 获取所有order_student_id用于查询详细信息
        order_student_ids = [row.order_student_id for row in student_data]
        
        # 第二次查询：获取退款详情、订单详情和转班详情
        refund_data, order_data, transfer_data = await get_additional_data_optimized(db, order_student_ids)
        
        # 处理和组装最终数据
        result_data = await process_student_list_data_optimized(student_data, refund_data, order_data, transfer_data)
        
        settings.logger.info(f"学员名单查询完成(优化版)，返回 {len(result_data)} 条数据")
        
        return await ApiSuccessResponse({
            "data": result_data,
            "total_count": total_count,
            "page": page,
            "page_size": page_size
        })
        
    except Exception as e:
        settings.logger.error(f"查询学员名单失败(优化版) - 错误: {str(e)}")
        return await ApiFailedResponse("查询学员名单失败，请稍后重试")
    

# 获取学生班级信息
@router.get("/get_stu_class_info")
async def get_stu_class_info(
    order_student_id: int,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 获取班级信息[老班级信息]
    - 传入参数： class_id, order_student_id
    - 返回信息： 原班级名称、课节单价、stu_id、学生姓名，完成课节数，总课节数
    """
    # 获取学生订单信息
    order_student_obj = await erp_order_student.get_by_id(db, order_student_id)
    if not order_student_obj:
        settings.logger.error(f"学生订单不存在 - 学生订单ID: {order_student_id}")
        return await ApiFailedResponse('学生订单不存在')
    
    settings.logger.info(f"学生订单验证通过 - 学生ID: {order_student_obj.stu_id}, 班级ID: {order_student_obj.class_id}")
    class_id = order_student_obj.class_id
    # 获取班级信息
    class_obj = await get_class_by_class_id(db, class_id)
    if not class_obj:
        settings.logger.error(f"班级不存在 - 班级ID: {class_id}")
        return await ApiFailedResponse('班级不存在')
    
    # 获取学生信息
    student_obj = await erp_student.get_by_id(db, order_student_obj.stu_id)
    if not student_obj:
        settings.logger.error(f"学生信息不存在 - 学生ID: {order_student_obj.stu_id}")
        return await ApiFailedResponse('学生信息不存在')
    
    # 获取订单信息以获取课节单价
    from models.m_order import ErpOrder
    from sqlalchemy import select
    
    order_query = select(ErpOrder.lesson_price).where(ErpOrder.order_student_id == order_student_id)
    order_result = await db.execute(order_query)
    order_row = order_result.first()
    
    lesson_price = 0.0
    if order_row and order_row.lesson_price:
        lesson_price = float(order_row.lesson_price)
    
    settings.logger.info(f"班级信息查询成功 - 班级: {class_obj.class_name}, 学生: {student_obj.stu_name}, 课节单价: {lesson_price}")
    
    # 组装返回数据
    result_data = {
        'class_id': class_id,
        'class_name': class_obj.class_name,
        'course_name': class_obj.course_name,
        'teacher_name': class_obj.teacher_name,
        'subject_id': class_obj.subject_id,
        'grade_id': class_obj.grade_id,
        'type_id': class_obj.type_id,
        'p_grade_id': class_obj.p_grade_id,
        'category_id': class_obj.category_id,
        'term_id': class_obj.term_id,
        'lesson_price': lesson_price,
        'stu_id': order_student_obj.stu_id,
        'stu_name': student_obj.stu_name,
        'complete_hours': float(order_student_obj.complete_hours) if order_student_obj.complete_hours else 0.0,
        'total_hours': float(order_student_obj.total_hours) if order_student_obj.total_hours else 0.0,
        'order_student_id': order_student_id
    }
    
    settings.logger.info(f"班级信息返回成功 - 数据: {result_data}")
    return await ApiSuccessResponse(result_data)


# 查询调课记录
@router.get("/get_reschedule_record")
async def get_reschedule_record(
    page: int = 1,
    page_size: int = 10,
    stu_name: str = None,
    class_name: str = None,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 查询调课记录 分页
    关联表： erp_class_rescheduling
    返回字段：学生姓名，电话，调课原因，原班级id和名称，原课节start_time和end_time,授课教师，现班级id和名称，现课节start_time和end_time,授课教师，创建时间，操作人，是否删除，更新人
    """
    # 参数验证
    if page < 1:
        page = 1
    if page_size < 1 or page_size > 100:
        page_size = 10
    
    # 查询调课记录
    reschedule_data, total_count = await get_reschedule_record_list(db, page, page_size, stu_name, class_name)
    
    # 组装返回数据
    result_data = []
    for row in reschedule_data:
        record_info = {
            'reschedule_id': row.reschedule_id,
            'order_student_id': row.order_student_id,
            'stu_id': row.stu_id,
            'stu_name': row.stu_name,
            'stu_phone': row.stu_phone,
            'reschedule_reason': row.reschedule_reason,
            
            # 原班级和课节信息
            'old_class_id': row.old_class_id,
            'old_class_name': row.old_class_name,
            'old_start_time': row.old_start_time.strftime('%Y-%m-%d %H:%M:%S') if row.old_start_time else None,
            'old_end_time': row.old_end_time.strftime('%Y-%m-%d %H:%M:%S') if row.old_end_time else None,
            'old_teacher_name': row.old_teacher_name,
            'old_class_plan_index': row.old_class_plan_index,  # 原课节序号
            
            # 新班级和课节信息
            'new_class_id': row.new_class_id,
            'new_class_name': row.new_class_name,
            'new_start_time': row.new_start_time.strftime('%Y-%m-%d %H:%M:%S') if row.new_start_time else None,
            'new_end_time': row.new_end_time.strftime('%Y-%m-%d %H:%M:%S') if row.new_end_time else None,
            'new_teacher_name': row.new_teacher_name,
            'new_class_plan_index': row.new_class_plan_index,  # 新课节序号
            
            # 操作信息
            'create_time': row.create_time.strftime('%Y-%m-%d %H:%M:%S') if row.create_time else None,
            'update_time': row.update_time.strftime('%Y-%m-%d %H:%M:%S') if row.update_time else None,
            'create_by_name': row.create_by_name,
            'update_by_name': row.update_by_name,
            'is_disabled': row.disable,
            'is_inner': row.is_inner,
        }
        result_data.append(record_info)
    
    settings.logger.info(f"调课记录查询完成，返回 {len(result_data)} 条数据")
    
    return await ApiSuccessResponse({
        "data": result_data,
        "total_count": total_count,
        "page": page,
        "page_size": page_size
    })
    


# 查询转班记录
@router.get("/get_transfer_record")
async def get_transfer_record(
    page: int = 1,
    page_size: int = 10,
    stu_name: str = None,
    class_name: str = None,
    db: AsyncSession = Depends(get_default_db),
    user: UserDict = Depends(role_required([])),
):
    """
    # 查询转班记录 分页
    关联表：erp_class_transfer
    返回字段：学生姓名，电话，转班原因，原班级id和名称，old_erp_order_student_id，现班级id和名称，现erp_order_student_id，创建时间，操作人，更新人
    """
    # 参数验证
    if page < 1:
        page = 1
    if page_size < 1 or page_size > 100:
        page_size = 10
    
    # 查询转班记录
    transfer_data, total_count = await get_transfer_record_list(db, page, page_size, stu_name, class_name)
    
    # 组装返回数据
    result_data = []
    for row in transfer_data:
        record_info = {
            'transfer_id': row.transfer_id,
            'old_order_student_id': row.old_order_student_id,
            'new_order_student_id': row.new_order_student_id,
            'stu_name': row.stu_name,
            'stu_phone': row.stu_phone,
            'transfor_reason': row.transfor_reason,
            'transfor_num': row.transfor_num,
            
            # 原班级信息
            'old_class_id': row.old_class_id,
            'old_class_name': row.old_class_name,
            
            # 新班级信息
            'new_class_id': row.new_class_id,
            'new_class_name': row.new_class_name,
            # 教师信息
            'old_teacher_name': row.old_teacher_name,
            'new_teacher_name': row.new_teacher_name,
            
            # 操作信息
            'create_time': row.create_time.strftime('%Y-%m-%d %H:%M:%S') if row.create_time else None,
            'update_time': row.update_time.strftime('%Y-%m-%d %H:%M:%S') if row.update_time else None,
            'create_by_name': row.create_by_name,
            'update_by_name': row.update_by_name,
            'is_inner': row.is_inner,
        }
        result_data.append(record_info)
    
    settings.logger.info(f"转班记录查询完成，返回 {len(result_data)} 条数据")
    
    return await ApiSuccessResponse({
        "data": result_data,
        "total_count": total_count,
        "page": page,
        "page_size": page_size
    })
        