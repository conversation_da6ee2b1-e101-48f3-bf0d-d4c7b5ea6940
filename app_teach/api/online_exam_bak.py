import copy
import random
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Depends

from app_teach.crud import get_stu_info, get_stu_paper, get_exam_detail
from app_teach.modules import get_paper_question
from app_teach.serializer import AnswerQuestionCreate
from app_user.jjyw_verify import get_current_user, UserDictJJYW
from models.m_online_exam import ErpOnlineQuestion, ErpOnlinePaper, ErpOnlinePaperQuestion, ErpOnlineQuestionOption, \
    ErpOnlineStuPaper, ErpOnlineStuScore, ErpOnlinePaperCourse
from models.old_models.old_student import RbStudent, RbAccount
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db, get_uat_db
from utils.db.model_handler import ModelDataHelper
from utils.other.config_handler import get_config
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse
import pandas as pd

router = APIRouter(prefix="/online_exam", tags=["【诊断】线上诊断"])

erp_online_question = CRUD(ErpOnlineQuestion)
erp_online_paper = CRUD(ErpOnlinePaper)
erp_online_paper_question = CRUD(ErpOnlinePaperQuestion)
erp_online_question_option = CRUD(ErpOnlineQuestionOption)
erp_online_stu_paper = CRUD(ErpOnlineStuPaper)
erp_online_stu_score = CRUD(ErpOnlineStuScore)
erp_online_paper_course = CRUD(ErpOnlinePaperCourse)
rb_student = CRUD(RbStudent)
rb_account = CRUD(RbAccount)

allow_role = []


@router.get("/exam_params")
async def query_exam_params(
        identity_key: str,
        conf: dict = Depends(get_config),
        # jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 查询参数
    - identity_key:
      - question_difficulty_level: 查询难度
      - question_grade: 查询年级
      - question_subject: 查询科目
      - question_p_grade: 查询年级大类
      - question_knowledge: 查询知识点
    """
    v = conf.get(identity_key)
    return await ApiSuccessResponse(v)


@router.post("/exam")
async def create_exam(
        grade_id: int = None,
        subject_id: int = None,
        lock_secret: str = None,
        conf: dict = Depends(get_config),
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 随机抽取试卷
    - online_exam 接口登录时请使用进阶与我token
    - grade_id 年级id
    - subject_id 科目id
    - lock_secret 解锁高级题目的秘钥， 在conf中配置
    -
    """
    stu_info = await get_stu_info(uat_db, jjyw_user.uid)
    condition = {
        "is_active": 1,
        "is_lock": 0,
    }
    if subject_id:
        condition.update({"subject_id": subject_id})
    if grade_id:
        condition.update({"grade_id": grade_id})
    if lock_secret:
        db_secret = conf.get('secret').get('question_secret')
        if db_secret == lock_secret:
            condition.update({"is_lock": 1})
        else:
            return await ApiFailedResponse('Error password')
    # exist_paper = await erp_online_stu_paper.get_one(default_db,
    #                                                  erp_stu_id=stu_info.StuId,
    #                                                  )
    exist_paper = await get_stu_paper(db=default_db, stu_id=stu_info.StuId, subject_id=subject_id, grade_id=grade_id)
    if exist_paper:
        if exist_paper.all_process != exist_paper.current_process:
            if exist_paper.all_process <= 0:
                return await ApiFailedResponse(f'请提醒教师完善在线诊断题目')
            print(f'已存在paper:{exist_paper.paper_id}, 进度{exist_paper.current_process}/{exist_paper.all_process}')
            resp_questions = await get_paper_question(db=default_db, paper_id=exist_paper.paper_id)  # 调用接口
            questions = resp_questions.questions
            resp_data = ModelDataHelper.model_to_dict(resp_questions)
            resp_data['stu_paper'] = exist_paper
            resp_data['questions'] = questions
            return await ApiSuccessResponse(resp_data)
        else:
            return await ApiFailedResponse('您已完成过该年级科目的诊断')
    paper = await erp_online_paper.get_many(default_db, condition=condition)
    random_paper = random.choice(paper)
    resp_questions = await get_paper_question(db=default_db, paper_id=random_paper.paper_id)  # 调用接口
    question_data = resp_questions.questions
    print('question_data:', question_data)

    if not question_data:
        return await ApiFailedResponse(f'请提醒教师完善在线诊断题目， 题号：{random_paper.paper_id}')
    stu_paper_item = {
        "paper_id": random_paper.paper_id,
        "erp_stu_id": stu_info.StuId,
        "current_process": 0,
        "all_process": len(question_data),
    }
    stu_paper = await erp_online_stu_paper.create(default_db, commit=False, **stu_paper_item)
    resp_data = ModelDataHelper.model_to_dict(resp_questions)
    r_question_data = copy.deepcopy(question_data)
    r_stu_paper = copy.deepcopy(stu_paper)
    resp_data['stu_paper'] = r_stu_paper
    resp_data['questions'] = r_question_data
    await default_db.commit()
    return await ApiSuccessResponse(resp_data)


@router.get("/start_exam/{paper_stu_id}")
async def start_exam(
        paper_stu_id: int = None,
        conf: dict = Depends(get_config),
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 获取当前需要做的题目
    - paper_stu_id： stu_paper下的id
    - process 进度
    - done 是否做完
    """
    exist_paper = await erp_online_stu_paper.get_one(default_db, id=paper_stu_id)
    current_process = exist_paper.current_process
    all_process = exist_paper.all_process
    if current_process == all_process:
        return await ApiSuccessResponse({
            "paper_stu_id": paper_stu_id,
            "process": f"{current_process}/{all_process}",
            "done": True,
        })
    print(f'做题进度：{current_process}/{all_process}')
    sort_num = current_process + 1
    paper_question = await erp_online_paper_question.get_one(default_db, **{
        "paper_id": exist_paper.paper_id,
        "sort": sort_num,
    })
    # print(paper_question.question_id)
    question = await erp_online_question.get_one(default_db, id=paper_question.question_id)
    options = await erp_online_question_option.get_many(default_db, {"question_id": paper_question.question_id})

    return await ApiSuccessResponse({
        "paper_stu_id": paper_stu_id,
        "process": f"{current_process}/{all_process}",
        "done": False,
        "paper_question_id": paper_question.id,
        "question": question,
        "options": options,
    })


@router.post("/answer_exam")
async def answer_exam(
        answer: AnswerQuestionCreate,
        conf: dict = Depends(get_config),
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 获取当前需要做的题目
    - paper_stu_id： stu_paper下的id
    """
    exist = await erp_online_stu_score.get_one(default_db,
                                               paper_stu_id=answer.paper_stu_id,
                                               paper_question_id=answer.paper_question_id)
    if exist:
        return await ApiFailedResponse('不允许重复做题')
    create_item = {
        "paper_stu_id": answer.paper_stu_id,
        "paper_question_id": answer.paper_question_id,
        "choice_option_id": answer.choice_option_id,
    }
    paper_question = await erp_online_paper_question.get_by_id(default_db, answer.paper_question_id)
    question = await erp_online_question.get_by_id(default_db, paper_question.question_id)
    option = await erp_online_question_option.get_by_id(default_db, answer.choice_option_id)
    if not option:
        return await ApiFailedResponse('选项id未查到')
    if option.question_id != paper_question.question_id:
        return await ApiFailedResponse('选项参数错误')
    stu_score = 0
    is_correct = 0
    if option.correct > 0:
        stu_score = question.score
        is_correct = 1
    create_item.update({"is_correct": is_correct})
    create_item.update({"stu_score": stu_score})
    await erp_online_stu_score.create(default_db, commit=False, **create_item)
    stu_paper = await erp_online_stu_paper.get_by_id(default_db, answer.paper_stu_id)
    print(f'已提交第{stu_paper.current_process + 1}题')
    stu_paper.current_process += 1
    stu_paper.update_time = datetime.now()
    await default_db.commit()
    return await ApiSuccessResponse(True)


@router.get("/exam_score/{paper_stu_id}")
async def exam_score(
        paper_stu_id: int = None,
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 查询当前诊断成绩
    """
    data = await erp_online_stu_score.get_many(default_db, {
        "paper_stu_id": paper_stu_id
    })
    correct = [i for i in data if i.is_correct > 0]
    error = [i for i in data if i.is_correct == 0]
    score = [i.stu_score for i in data if i.stu_score > 0]
    return await ApiSuccessResponse({
        "score": sum(score),
        "error_num": len(error),
        "correct_num": len(correct),
        "paper_stu_id": paper_stu_id
    })


@router.get("/exam_report/{paper_stu_id}")
async def exam_report(
        paper_stu_id: int = None,
        conf: dict = Depends(get_config),
        default_db: AsyncSession = Depends(get_default_db),
        uat_db: AsyncSession = Depends(get_uat_db),
        jjyw_user: UserDictJJYW = Depends(get_current_user),
):
    """
    # 诊断报告
    """
    data = await get_exam_detail(default_db, paper_stu_id)
    dict_data = [dict(i) for i in data]
    # 总分
    sum_score = sum([i['score'] for i in dict_data])
    stu_sum_score = sum([i['stu_score'] for i in dict_data])
    teacher_msg = ''
    course_name = ''

    # 教师评价
    paper_stu = await erp_online_stu_paper.get_by_id(default_db, paper_stu_id)
    comments = await erp_online_paper_course.get_many(default_db, raw=[
        ErpOnlinePaperCourse.paper_id == paper_stu.paper_id,
        ErpOnlinePaperCourse.min_score <= stu_sum_score,
        ErpOnlinePaperCourse.max_score > stu_sum_score,
    ])
    if comments and len(comments) == 1:
        comment = comments[0]
        teacher_msg = comment.teacher_msg
        course_name = comment.course_name
    # 组装各维度数据
    df = pd.DataFrame(dict_data)
    # 按知识点分组数据
    knowledge_point_grouped = df.groupby('knowledge_point').agg({
        'stu_score': 'sum',
        'score': 'sum',
        'is_correct': 'count'
    }).rename(columns={'is_correct': 'count'}).reset_index()
    # 计算stu_count
    knowledge_point_grouped['stu_count'] = df[df['is_correct'] == 1].groupby('knowledge_point')[
        'is_correct'].count().reset_index(drop=True)
    # 按难度分组数据
    difficulty_level_grouped = df.groupby('difficulty_level').agg({
        'stu_score': 'sum',
        'score': 'sum',
        'is_correct': 'count'
    }).rename(columns={'is_correct': 'count'}).reset_index()
    # 计算stu_count
    difficulty_level_grouped['stu_count'] = df[df['is_correct'] == 1].groupby('difficulty_level')[
        'is_correct'].count().reset_index(drop=True)
    # 转换为所需格式
    knowledge_point_data = [
        {
            'knowledge_point': row['knowledge_point'],
            'score': row['score'],
            'stu_score': row['stu_score'],
            'count': row['count'],
            'stu_count': row['stu_count']
        } for index, row in knowledge_point_grouped.iterrows()
    ]
    question_difficulty_level = conf.get('question_difficulty_level')
    # print(question_difficulty_level)
    difficulty_level_data = [
        {
            'difficulty_level': question_difficulty_level.get(str(int(row['difficulty_level']))),
            'score': row['score'],
            'stu_score': row['stu_score'],
            'count': row['count'],
            'stu_count': row['stu_count']
        } for index, row in difficulty_level_grouped.iterrows()
    ]

    stu_info = await rb_student.get_by_id(uat_db, paper_stu.erp_stu_id)
    paper_info = await erp_online_paper.get_by_id(default_db, paper_stu.paper_id)
    return await ApiSuccessResponse({
        "knowledge_point_data": knowledge_point_data,
        "difficulty_level_data": difficulty_level_data,
        "sum_score": sum_score,
        "stu_sum_score": stu_sum_score,
        "teacher_msg": teacher_msg,
        "course_name": course_name,
        "stu_name": stu_info.StuName,
        "stu_id": stu_info.StuId,
        "stu_icon": stu_info.StuIcon,
        "paper_name": paper_info.paper_name,
    })
