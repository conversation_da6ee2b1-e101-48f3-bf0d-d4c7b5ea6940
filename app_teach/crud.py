from datetime import datetime

from sqlalchemy import select, and_, or_, func, exists, case
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased

from models.m_class import ErpClass, ErpClassChecking, ErpClassRenewRules, ErpClassRescheduling, ErpClassTransfor, ErpCourse, ErpCourseCategory, ErpCourseTerm, ErpClassPlan, ErpCourseTextbook
from models.m_discount import ErpStudentDiscountCoupon, ErpStudentDiscountFixed
from models.m_office import ErpOfficeClassroom, ErpOfficeCenter
from models.m_online_exam import ErpOnlineQuestion, ErpOnlineQuestionOption, ErpOnlineStuPaper, \
    ErpOnlinePaper, ErpOnlineStuScore, ErpOnlinePaperQuestion
from models.m_order import ErpOrderStudent, ErpOrder
from models.m_student import ErpStudent, ErpStudentWechat
from models.m_teacher import ErpAccountTeacher
from models.m_workflow import <PERSON>rp<PERSON>eceipt, ErpWorkflowInstance, ErpWorkflowInstanceReviewer
from models.models import ErpAccount, ErpAccountDepartment, ErpDepartment
from models.old_models.old_class import RbClass, RbCourse, RbCourseSubject, RbCourseCategory, RbCourseConfig, RbTeacher, \
    RbStudentTempinvitation, RbClassPlan, RbClassLog
from models.old_models.old_student import RbAccount, RbStudent
from utils.enum.enum_account import EmployeeStatus
from utils.enum.enum_approval import AuditState, ClassAuditStatus
from utils.enum.enum_class import ClassStatus
from utils.enum.enum_order import OrderType, StudentState
from utils.db.account_handler import crud_factory
from sqlalchemy.orm import aliased

CreateBy = aliased(ErpAccount)
UpdateBy = aliased(ErpAccount)


async def get_questions(db, page, page_size, keyword):
    selects = [
        ErpOnlineQuestion.id,
        ErpOnlineQuestion.content,
        ErpOnlineQuestion.difficulty_level,
        ErpOnlineQuestion.score,
        ErpOnlineQuestion.creator,
        ErpOnlineQuestion.knowledge_point,
    ]
    conditions = [
        ErpOnlineQuestion.disable == 0
    ]
    if keyword:
        conditions.append(
            or_(ErpOnlineQuestion.content.ilike(f"%{keyword}%"),
                ErpOnlineQuestion.series.ilike(f"%{keyword}%"),
                )
        )
    stmt = (
        select(*selects)
        # .select_from(ErpOnlineQuestion)
        # .outerjoin(ErpOnlineQuestionOption)
        .where(and_(*conditions))
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    return result.fetchall()


async def get_stu_info(uat_db, account_id):
    selects = [
        RbAccount.Id,
        RbAccount.AccountType,
        RbStudent.StuId,
        RbStudent.StuName,
    ]
    conditions = [
        RbAccount.Id == account_id,
        RbAccount.AccountType == 4,
    ]
    stmt = (
        select(*selects)
        .select_from(RbAccount)
        .outerjoin(RbStudent, RbStudent.StuId == RbAccount.AccountId)
        .where(and_(*conditions))
    )
    result = await uat_db.execute(stmt)
    return result.fetchone()


async def get_stu_paper(db, stu_id, grade_id, subject_id):
    selects = [
        ErpOnlineStuPaper.id,
        ErpOnlineStuPaper.paper_id,
        ErpOnlineStuPaper.erp_stu_id,
        ErpOnlineStuPaper.all_process,
        ErpOnlineStuPaper.current_process,
    ]
    conditions = [
        ErpOnlineStuPaper.disable == 0,
        ErpOnlinePaper.disable == 0,
        ErpOnlinePaper.is_active == 1,
        ErpOnlinePaper.subject_id == subject_id,
        ErpOnlinePaper.grade_id == grade_id,
        ErpOnlineStuPaper.erp_stu_id == stu_id,
    ]
    stmt = (
        select(*selects)
        .select_from(ErpOnlineStuPaper)
        .outerjoin(ErpOnlinePaper, ErpOnlineStuPaper.paper_id == ErpOnlinePaper.id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchone()


async def get_exam_detail(db, paper_stu_id):
    selects = [
        ErpOnlineStuScore.is_correct,
        ErpOnlineStuScore.stu_score,
        ErpOnlineQuestion.difficulty_level,
        ErpOnlineQuestion.score,
        ErpOnlineQuestion.id,
        ErpOnlineQuestion.knowledge_point,
    ]
    conditions = [
        ErpOnlineStuScore.paper_stu_id == paper_stu_id
    ]
    stmt = (
        select(*selects)
        .select_from(ErpOnlineStuScore)
        .outerjoin(ErpOnlinePaperQuestion, ErpOnlineStuScore.paper_question_id == ErpOnlinePaperQuestion.id)
        .outerjoin(ErpOnlineQuestion, ErpOnlineQuestion.id == ErpOnlinePaperQuestion.question_id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()


async def query_course(db, page, page_size,
                       subject_id=None,
                       category_config_id=None,
                       shift_term_id=None,
                       class_name=None,
                       teacher_name=None,
                       shift_grade_id=None,
                       yyyy=None,
                       teacher_id=None,
                       shift_class_type=None,
                       ):
    """在售课程"""
    # 条件
    conditions = [
        RbClass.CreateTime >= datetime(2023, 1, 1),  # 2023年以来的开课
    ]
    if shift_grade_id and shift_grade_id > 0:
        conditions.append(RbCourse.ShiftGrade == shift_grade_id)
    if subject_id and subject_id > 0:
        conditions.append(RbCourseSubject.Id == subject_id)
    if category_config_id and category_config_id > 0:
        conditions.append(RbCourseCategory.CateId == category_config_id)
    if shift_term_id and shift_term_id > 0:
        conditions.append(RbCourseConfig.Id == shift_term_id)
    if class_name:
        conditions.append(RbClass.ClassName.ilike(f"%{class_name}%"))
    if teacher_name:
        conditions.append(RbTeacher.TeacherName.ilike(f"%{teacher_name}%"))
    if yyyy and yyyy > 0:
        conditions.append(func.year(RbClass.OpenTime) == yyyy)
    if teacher_id and teacher_id > 0:
        conditions.append(RbClass.Teacher_Id == teacher_id)
    if shift_class_type and shift_class_type > 0:
        conditions.append(RbCourse.ShiftClassType == shift_class_type)
    # 选择项
    selects = [
        RbClass.ClassId,
        RbClass.ClassName,
        RbCourse.ShiftParentGrade,
        RbClass.Teacher_Id,
        RbTeacher.TeacherName,
        RbTeacher.TeacherHead,
        RbClass.Assist_Id,
        RbCourseSubject.SubjectName,
        RbCourse.ShiftClassType,
        RbCourseCategory.CateName,
        RbClass.CourseTimes,
        RbClass.OpenTime,
        RbClass.ClassPersion,
        RbClass.EndClassDate,
        RbClass.DefaultTimeJson,
        RbClass.DateJson,
        RbClass.Mall_Is_Show
    ]
    stmt = (
        select(*selects)
        .select_from(RbClass)
        .outerjoin(RbCourse, RbClass.CouseId == RbCourse.CourseId)
        .outerjoin(RbTeacher, RbClass.Teacher_Id == RbTeacher.TId)
        .outerjoin(RbCourseCategory, RbCourseCategory.CateId == RbCourse.CateId)
        .outerjoin(RbCourseConfig, RbCourseConfig.Id == RbCourse.ShiftTerm)
        .outerjoin(RbCourseSubject, RbCourseSubject.Id == RbCourse.CourseSubject)
        .where(and_(*conditions))
        .order_by(RbClass.CreateTime.desc())  # 排序
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)

    # compiled_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compiled_sql)
    result = await db.execute(stmt)
    return [dict(i) for i in result.fetchall()]


async def course_configuration(db, school_id=0):
    conditions = [RbCourseConfig.Status == 0,
                  RbCourseConfig.School_Id == school_id,
                  RbCourseConfig.Group_Id == 100001
                  ]
    stmt = select(RbCourseConfig).where(and_(*conditions))
    result = await db.execute(stmt)
    return [i.to_dict() for i in result.scalars()]


async def course_subject_configuration(db, school_id=0):
    conditions = [RbCourseSubject.Status == 0, RbCourseSubject.School_Id == school_id]
    stmt = select(RbCourseSubject).where(and_(*conditions))
    result = await db.execute(stmt)
    return [i.to_dict() for i in result.scalars()]


async def class_map(db, school_id=1):
    conditions = [RbClass.Status == 0, RbClass.School_Id == school_id]
    stmt = select(RbClass).where(and_(*conditions))
    result = await db.execute(stmt)
    return [i.to_dict() for i in result.scalars()]


async def category_map(db, school_id=0):
    selects = [
        RbCourseCategory.CateId,
        RbCourseCategory.CateName
    ]
    conditions = [RbCourseCategory.Status == 0, RbCourseCategory.School_Id == school_id]
    stmt = select(*selects).where(and_(*conditions))
    result = await db.execute(stmt)
    return result.fetchall()


async def query_class_with_page(db, page, page_size, condition=None, count=False):
    """
    分页查询班级信息
    
    Args:
        db: 数据库会话
        page: 页码
        page_size: 每页大小
        condition: 查询条件
        count: 是否只返回总数
        condition: 查询条件
        
    Returns:
        如果count为True，返回总记录数；否则返回班级列表
    """
    # 首先查询每个班级的第一个有效课程计划的开始时间
    first_class_plan_subquery = (
        select(
            ErpClassPlan.class_id,
            func.min(ErpClassPlan.start_time).label("first_class_start_time")
        )
        .where(ErpClassPlan.disable == 0)
        .group_by(ErpClassPlan.class_id)
        .subquery()
    )

    # 班级课程进度子查询， 查询finish为1的数量
    class_finish_subquery = (
        select(
            ErpClassPlan.class_id,
            func.count(ErpClassPlan.id).label("class_finish_count")
        )
        .where(ErpClassPlan.disable == 0, ErpClassPlan.finish == 1)
        .group_by(ErpClassPlan.class_id)
        .subquery()
    )

    # 查询未付款学生子查询
    unpaid_stu_subquery = (
        select(
            ErpOrderStudent.class_id,
            func.count(ErpOrderStudent.id).label("unpaid_stu_count")
        )
        .where(ErpOrderStudent.disable == 0, ErpOrderStudent.student_state == StudentState.WAIT_PAY.value)
        .group_by(ErpOrderStudent.class_id)
        .subquery()
    )
    # 已付款学生子查询
    paid_stu_subquery = (
        select(
            ErpOrderStudent.class_id,
            func.count(ErpOrderStudent.id).label("paid_stu_count")
        )
        .where(ErpOrderStudent.disable == 0, ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value]))
        .group_by(ErpOrderStudent.class_id)
        .subquery()
    )


    # 构建查询条件, 首先是默认条件
    conditions = [
        ErpClass.disable == 0,
        # ErpClass.audit_status.in_([ClassAuditStatus.PASS.value, ClassAuditStatus.WAITING.value]),
        # ErpClass.class_status == ClassStatus.NORMAL.value
    ]
    # 添加其他条件
    if condition.get("class_name"):
        pass

    if condition.get("class_names"):
        # print(condition.get("class_names"))
        class_name_conditions = []
        for name in condition.get("class_names"):
            if name:
                class_name_conditions.append(ErpClass.class_name.ilike(f"%{name}%"))
        if class_name_conditions:
            conditions.append(and_(*class_name_conditions))
    
    if condition.get("teacher_id") is not None:
        conditions.append(or_(
            ErpClass.teacher_id == condition.get("teacher_id"),
            ErpClass.assistant_teacher_id == condition.get("teacher_id")
        ))
    
    if condition.get("term_ids"):
        conditions.append(ErpCourse.term_id.in_(condition.get("term_ids")))
    
    if condition.get("grade_ids"):
        conditions.append(ErpCourse.grade_id.in_(condition.get("grade_ids")))
    
    if condition.get("subject_ids"):
        conditions.append(ErpCourse.subject_id.in_(condition.get("subject_ids")))
    
    if condition.get("category_ids"):
        conditions.append(ErpCourse.category_id.in_(condition.get("category_ids")))
    
    if condition.get("course_type_ids"):
        conditions.append(ErpCourse.type_id.in_(condition.get("course_type_ids")))
    
    if condition.get("class_status") is not None:
        conditions.append(ErpClass.class_status == condition.get("class_status"))
    
    if condition.get("course_names") is not None:
        course_name_conditions = []
        for name in condition.get("course_names"):
            if name:
                course_name_conditions.append(ErpCourse.course_name.ilike(f"%{name}%"))
        if course_name_conditions:
            conditions.append(and_(*course_name_conditions))
    
    if condition.get("class_start_time"):
        conditions.append(ErpClass.start_date >= condition.get("class_start_time"))
    
    if condition.get("class_end_time"):
        conditions.append(ErpClass.start_date <= condition.get("class_end_time"))
    if condition.get("audit_status") in (ClassAuditStatus.PASS.value, ClassAuditStatus.WAITING.value, ClassAuditStatus.REJECT.value ):
        conditions.append(ErpClass.audit_status == condition.get("audit_status"))
    
    if condition.get("is_shelf_miniprogram") is not None:
        conditions.append(ErpClass.is_shelf_miniprogram == condition.get("is_shelf_miniprogram"))

    
    # 定义表连接
    joins = [
        (ErpCourse, ErpClass.course_id == ErpCourse.id),
        (ErpOfficeClassroom, ErpClass.classroom_id == ErpOfficeClassroom.id),
        (ErpCourseCategory, ErpCourse.category_id == ErpCourseCategory.id),
        (ErpCourseTextbook, ErpCourse.bound_textbook_id == ErpCourseTextbook.id),
        (ErpCourseTerm, ErpCourse.term_id == ErpCourseTerm.id),
        (ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id),
        (ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id),
        (UpdateBy, ErpClass.update_by == UpdateBy.id),
        (CreateBy, ErpClass.create_by == CreateBy.id),
        (first_class_plan_subquery, ErpClass.id == first_class_plan_subquery.c.class_id),
        (class_finish_subquery, ErpClass.id == class_finish_subquery.c.class_id),
        (unpaid_stu_subquery, ErpClass.id == unpaid_stu_subquery.c.class_id),
        (paid_stu_subquery, ErpClass.id == paid_stu_subquery.c.class_id)
    ]
    
    # 如果只需要统计总数
    if count:
        count_stmt = select(func.count(ErpClass.id)).select_from(ErpClass)
        
        # 添加所有连接
        for table, condition in joins:
            count_stmt = count_stmt.outerjoin(table, condition)
            
        count_stmt = count_stmt.where(and_(*conditions))
        result = await db.execute(count_stmt)
        return result.scalar()
    
    # 定义查询字段
    selects = [
        ErpClass.id,
        ErpClass.course_id,
        ErpClass.class_name,
        ErpClass.class_capacity,
        ErpClass.pre_enrollment,
        ErpClass.teacher_id,
        ErpAccount.employee_name.label("teacher_name"),
        ErpClass.start_date,
        ErpClass.classroom_id,
        ErpOfficeClassroom.room_name,
        ErpOfficeClassroom.stu_cap_max,
        ErpOfficeClassroom.stu_cap_comfort,
        ErpClass.use_standard_full_rate,
        ErpClass.scheduling_method,
        ErpClass.is_shelf_miniprogram,
        ErpClass.miniprogram_start_enrollment_time,
        ErpClass.miniprogram_end_enrollment_time,
        ErpClass.enrollment_conditions,
        ErpClass.classin_sync,
        ErpClass.hourly_tuition_ratio,
        ErpClass.create_time.label("class_create_time"),
        ErpClass.update_time.label("class_update_time"),
        ErpClass.audit_status,
        UpdateBy.employee_name.label("update_by_name"),
        CreateBy.employee_name.label("create_by_name"),
        ErpClass.class_status,
        ErpCourse.course_name,
        ErpCourse.original_price,
        ErpCourse.sale_price,
        ErpCourse.subject_id,
        ErpCourse.type_id,
        ErpCourse.term_id,
        ErpCourse.grade_id,
        ErpCourse.number_of_lessons,
        ErpCourseCategory.category_name,
        ErpCourseTerm.term_name,
        ErpCourseTextbook,
        first_class_plan_subquery.c.first_class_start_time,
        func.coalesce(class_finish_subquery.c.class_finish_count, 0).label("class_finish_count"),
        func.coalesce(unpaid_stu_subquery.c.unpaid_stu_count, 0).label("unpaid_stu_count"),
        func.coalesce(paid_stu_subquery.c.paid_stu_count, 0).label("paid_stu_count")
    ]
    
    # 构建完整查询
    stmt = select(*selects).select_from(ErpClass)
    
    # 添加所有连接
    for table, condition in joins:
        stmt = stmt.outerjoin(table, condition)
    
    # 添加条件和排序
    stmt = (
        stmt
        .where(and_(*conditions))
        .order_by(ErpClass.create_time.desc())
    )
    
    # 添加分页
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    # compiled_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compiled_sql)
    result = await db.execute(stmt)
    return result.fetchall()


async def query_class_with_page_optimized(db, page, page_size, condition=None, count=False):
    """
    优化版分页查询班级信息
    
    Args:
        db: 数据库会话
        page: 页码
        page_size: 每页大小
        condition: 查询条件
        count: 是否只返回总数
        
    Returns:
        如果count为True，返回总记录数；否则返回班级列表
    """
    AssistantTeacher = aliased(ErpAccountTeacher, name="AssistantTeacher")
    AssistantAccount = aliased(ErpAccount, name="AssistantAccount")

    # 构建查询条件, 首先是默认条件
    conditions = [
        ErpClass.disable == 0,
    ]
    
    # 添加其他条件
    if condition and condition.get("class_names"):
        class_name_conditions = []
        for name in condition.get("class_names"):
            if name:
                class_name_conditions.append(ErpClass.class_name.ilike(f"%{name}%"))
        if class_name_conditions:
            conditions.append(and_(*class_name_conditions))
    
    if condition and condition.get("teacher_id") is not None:
        conditions.append(ErpClass.teacher_id == condition.get("teacher_id"))
    
    if condition and condition.get("term_ids"):
        conditions.append(ErpCourse.term_id.in_(condition.get("term_ids")))
    
    if condition and condition.get("grade_ids"):
        conditions.append(ErpCourse.grade_id.in_(condition.get("grade_ids")))
    
    if condition and condition.get("subject_ids"):
        conditions.append(ErpCourse.subject_id.in_(condition.get("subject_ids")))
    
    if condition and condition.get("category_ids"):
        conditions.append(ErpCourse.category_id.in_(condition.get("category_ids")))
    
    if condition and condition.get("course_type_ids"):
        conditions.append(ErpCourse.type_id.in_(condition.get("course_type_ids")))
    
    if condition and condition.get("class_status") is not None:
        conditions.append(ErpClass.class_status == condition.get("class_status"))
    
    if condition and condition.get("course_names") is not None:
        course_name_conditions = []
        for name in condition.get("course_names"):
            if name:
                course_name_conditions.append(ErpCourse.course_name.ilike(f"%{name}%"))
        if course_name_conditions:
            conditions.append(and_(*course_name_conditions))
    
    if condition and condition.get("class_start_time"):
        conditions.append(ErpClass.start_date >= condition.get("class_start_time"))
    
    if condition and condition.get("class_end_time"):
        conditions.append(ErpClass.start_date <= condition.get("class_end_time"))
        
    if condition and condition.get("audit_status") in (ClassAuditStatus.PASS.value, ClassAuditStatus.WAITING.value, ClassAuditStatus.REJECT.value):
        conditions.append(ErpClass.audit_status == condition.get("audit_status"))
    
    if condition and condition.get("is_shelf_miniprogram") is not None:
        conditions.append(ErpClass.is_shelf_miniprogram == condition.get("is_shelf_miniprogram"))

    # 如果只需要统计总数
    if count:
        count_stmt = (
            select(func.count(ErpClass.id))
            .select_from(ErpClass)
            .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
            .where(and_(*conditions))
        )
        result = await db.execute(count_stmt)
        return result.scalar()
    
    # 定义查询字段 - 只查询必要字段
    selects = [
        ErpClass.id,
        ErpClass.course_id,
        ErpClass.class_name,
        ErpClass.class_capacity,
        ErpClass.pre_enrollment,
        ErpClass.teacher_id,
        ErpClass.start_date,
        ErpClass.classroom_id,
        ErpClass.use_standard_full_rate,
        ErpClass.scheduling_method,
        ErpClass.is_shelf_miniprogram,
        ErpClass.miniprogram_start_enrollment_time,
        ErpClass.miniprogram_end_enrollment_time,
        ErpClass.enrollment_conditions,
        ErpClass.classin_sync,
        ErpClass.hourly_tuition_ratio,
        ErpClass.create_time.label("class_create_time"),
        ErpClass.update_time.label("class_update_time"),
        ErpClass.audit_status,
        ErpClass.class_status,
        ErpClass.create_by,
        ErpClass.update_by,
        ErpClass.comments,
        ErpClass.qr_code,
        # 课程信息
        ErpCourse.course_name,
        ErpCourse.original_price,
        ErpCourse.sale_price,
        ErpCourse.subject_id,
        ErpCourse.type_id,
        ErpCourse.term_id,
        ErpCourse.grade_id,
        ErpCourse.number_of_lessons,
        # 教室信息
        ErpOfficeClassroom.room_name,
        ErpOfficeClassroom.stu_cap_max,
        ErpOfficeClassroom.stu_cap_comfort,
        # 分类信息
        ErpCourseCategory.category_name,
        # 学期信息
        ErpCourseTerm.term_name,
        # 教师信息
        ErpAccount.employee_name.label("teacher_name"),
        AssistantAccount.employee_name.label("assistant_teacher_name"),
        # 教材信息
        ErpCourseTextbook,


    ]
    
    # 构建基础查询 - 减少不必要的连接
    stmt = (
        select(*selects)
        .select_from(ErpClass)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpOfficeClassroom, ErpClass.classroom_id == ErpOfficeClassroom.id)
        .outerjoin(ErpCourseCategory, ErpCourse.category_id == ErpCourseCategory.id)
        .outerjoin(ErpCourseTextbook, ErpCourse.bound_textbook_id == ErpCourseTextbook.id)
        .outerjoin(ErpCourseTerm, ErpCourse.term_id == ErpCourseTerm.id)
        .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .outerjoin(AssistantTeacher, ErpClass.assistant_teacher_id == AssistantTeacher.id)
        .outerjoin(AssistantAccount, AssistantTeacher.account_id == AssistantAccount.id)
        .where(and_(*conditions))
        .order_by(ErpClass.create_time.desc())
    )
    
    # 添加分页
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    
    result = await db.execute(stmt)
    class_data = result.fetchall()
    
    if not class_data:
        return []
    
    # 获取班级ID列表，用于后续批量查询统计信息
    class_ids = [row.id for row in class_data]
    
    # 批量查询第一个课程计划开始时间
    first_plan_stmt = (
        select(
            ErpClassPlan.class_id,
            func.min(ErpClassPlan.start_time).label("first_class_start_time")
        )
        .where(
            ErpClassPlan.disable == 0,
            ErpClassPlan.class_id.in_(class_ids)
        )
        .group_by(ErpClassPlan.class_id)
    )
    first_plan_result = await db.execute(first_plan_stmt)
    first_plan_map = {row.class_id: row.first_class_start_time for row in first_plan_result.fetchall()}
    
    # 批量查询已完成课程计划数量
    finish_count_stmt = (
        select(
            ErpClassPlan.class_id,
            func.count(ErpClassPlan.id).label("class_finish_count")
        )
        .where(
            ErpClassPlan.disable == 0,
            ErpClassPlan.finish == 1,
            ErpClassPlan.class_id.in_(class_ids)
        )
        .group_by(ErpClassPlan.class_id)
    )
    finish_count_result = await db.execute(finish_count_stmt)
    finish_count_map = {row.class_id: row.class_finish_count for row in finish_count_result.fetchall()}
    
    # 批量查询学生统计信息
    student_count_stmt = (
        select(
            ErpOrderStudent.class_id,
            ErpOrderStudent.student_state,
            func.count(ErpOrderStudent.id).label("stu_count")
        )
        .where(
            ErpOrderStudent.disable == 0,
            ErpOrderStudent.class_id.in_(class_ids),
            ErpOrderStudent.student_state.in_([StudentState.WAIT_PAY.value, StudentState.NORMAL.value, StudentState.TRANSFER_IN.value])
        )
        .group_by(ErpOrderStudent.class_id, ErpOrderStudent.student_state)
    )
    # compile_sql = student_count_stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    student_count_result = await db.execute(student_count_stmt)
    
    # 构建学生统计映射
    unpaid_stu_map = {}
    paid_stu_map = {}
    for row in student_count_result.fetchall():
        if row.student_state == StudentState.WAIT_PAY.value:
            unpaid_stu_map[row.class_id] = row.stu_count
        elif row.student_state in [StudentState.NORMAL.value, StudentState.TRANSFER_IN.value]:
            paid_stu_map[row.class_id] = paid_stu_map.get(row.class_id, 0) + row.stu_count
    
    # # 获取需要查询审核人信息的工作流实例ID
    # workflow_instance_ids = [row.workflow_instance_id for row in class_data if row.workflow_instance_id and row.workflow_instance_id > 0]
    
    # # 批量查询当前审核人信息
    # current_reviewer_map = {}
    # if workflow_instance_ids:
    #     current_reviewer_stmt = (
    #         select(
    #             ErpWorkflowInstanceReviewer.instance_id,
    #             ErpWorkflowInstanceReviewer.reviewer_id,
    #             ErpWorkflowInstanceReviewer.reviewer_name,
    #             ErpAccount.employee_name.label("reviewer_employee_name"),
    #             ErpAccount.avatar.label("reviewer_avatar")
    #         )
    #         .select_from(ErpWorkflowInstanceReviewer)
    #         .outerjoin(ErpAccount, ErpWorkflowInstanceReviewer.reviewer_id == ErpAccount.id)
    #         .where(
    #             ErpWorkflowInstanceReviewer.instance_id.in_(workflow_instance_ids),
    #             ErpWorkflowInstanceReviewer.status == 0,  # 待审批状态
    #             ErpWorkflowInstanceReviewer.disable == 0
    #         )
    #     )
    #     current_reviewer_result = await db.execute(current_reviewer_stmt)
        
    #     # 构建审核人映射，一个工作流实例可能有多个审核人
    #     for row in current_reviewer_result.fetchall():
    #         if row.instance_id not in current_reviewer_map:
    #             current_reviewer_map[row.instance_id] = []
    #         current_reviewer_map[row.instance_id].append({
    #             "reviewer_id": row.reviewer_id,
    #             "reviewer_name": row.reviewer_name or row.reviewer_employee_name,
    #             "reviewer_avatar": row.reviewer_avatar
    #         })
    
    # 组装最终结果
    result_data = []
    for row in class_data:
        row_dict = dict(row)
        row_dict['first_class_start_time'] = first_plan_map.get(row.id)
        row_dict['class_finish_count'] = finish_count_map.get(row.id, 0)
        row_dict['unpaid_stu_count'] = unpaid_stu_map.get(row.id, 0)
        row_dict['paid_stu_count'] = paid_stu_map.get(row.id, 0)
        
        # # 添加审核流程相关信息
        # row_dict['workflow_info'] = {
        #     "receipt_id": row.receipt_id,
        #     "apply_reason": row.apply_reason,
        #     "receipt_audit_state": row.receipt_audit_state,
        #     "workflow_instance_id": row.workflow_instance_id,
        #     "current_node_id": row.current_node_id,
        #     "current_node_name": row.current_node_name,
        #     "workflow_status": row.workflow_status,
        #     "current_reviewers": current_reviewer_map.get(row.workflow_instance_id, []) if row.workflow_instance_id else []
        # }
        
        result_data.append(row_dict)
    
    return result_data


# 查询教师的模块
async def get_teacher_module(db, page=None, page_size=None, teacher_id=None, sync=None, employee_status=None, teacher_name=None, teacher_subject=None, teacher_grade=None, teacher_tag=None, is_show=None, phone=None, count=False):
    if count:
        selects = [
            func.count(ErpAccount.id)
        ]
    else:
        selects = [
            ErpAccount.id,
            ErpAccount.username,
            ErpAccount.employee_name,
            ErpAccount.employee_number,
            ErpAccount.employee_birth,
            ErpAccount.employee_hire_date,
            ErpAccount.qy_wechat_position,
            ErpAccount.employee_status,
            ErpAccount.employee_type,
            ErpAccount.employee_major,
            ErpAccount.employee_education,
            ErpAccount.qy_wechat_userid,
            ErpAccountTeacher.id.label("teacher_id"),
            ErpAccountTeacher.teacher_grade,
            ErpAccountTeacher.teacher_subject,
            ErpAccountTeacher.teacher_certification,
            ErpAccountTeacher.teacher_fee,
            ErpAccountTeacher.teacher_avatar,
            ErpAccountTeacher.teacher_image,
            ErpAccountTeacher.teacher_qr_img,
            ErpAccountTeacher.teacher_desc,
            ErpAccountTeacher.teacher_tag,
            ErpAccountTeacher.is_show,
            ErpDepartment.id.label("dept_id"),
            ErpDepartment.dept_name,

        ]
    conditions = [
        ErpAccount.is_teacher == 1,
        ErpAccount.employee_status.in_([EmployeeStatus.EMPLOYED.value, EmployeeStatus.PROBATION.value])  # 正式， 试用
    ]
    if sync:
        conditions.append(ErpAccountTeacher.id.is_(None))
    

    if employee_status:
        conditions.append(ErpAccount.employee_status == employee_status)
    if teacher_name:
        conditions.append(ErpAccount.employee_name.ilike(f"%{teacher_name}%"))
    if teacher_subject:
        conditions.append(ErpAccountTeacher.teacher_subject.ilike(f"%{teacher_subject}%"))
    if teacher_grade:
        conditions.append(ErpAccountTeacher.teacher_grade.ilike(f"%{teacher_grade}%"))
    if teacher_tag:
        conditions.append(ErpAccountTeacher.teacher_tag.ilike(f"%{teacher_tag}%"))
    if is_show is not None:
        conditions.append(ErpAccountTeacher.is_show == is_show)
    if phone:
        conditions.append(ErpAccount.employee_phone == phone)
    if teacher_id:
        conditions.append(ErpAccountTeacher.id == teacher_id)

    stmt = (
        select(*selects)
        .select_from(ErpAccount)
        .outerjoin(ErpAccountTeacher, ErpAccount.id == ErpAccountTeacher.account_id)
        .outerjoin(ErpAccountDepartment, and_(ErpAccount.id == ErpAccountDepartment.account_id, ErpAccountDepartment.disable == 0))
        .outerjoin(ErpDepartment, and_(ErpAccountDepartment.dept_id == ErpDepartment.id, ErpDepartment.disable == 0))
        .where(and_(*conditions))
        .order_by(ErpAccount.id)
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    if count:
        return result.scalar()
    if teacher_id:
        return result.fetchone()
    else:
        return result.fetchall()


# 根据class_id查询课程计划
async def get_classplan_by_classid(db, class_id):
    CreateAccount = aliased(ErpAccount, name="CreateAccount")
    UpdateAccount = aliased(ErpAccount, name="UpdateAccount")
    selects = [
        ErpClassPlan.id,
        ErpClassPlan.class_id,
        ErpClassPlan.start_time,
        ErpClassPlan.end_time,
        ErpClassPlan.create_by,
        ErpClassPlan.update_by,
        ErpClassPlan.time_duration,
        ErpClassPlan.finish,
        ErpClass.class_name,
        ErpCourse.id.label("course_id"),
        ErpClassPlan.room_id,
        ErpOfficeClassroom.id.label("room_id2"),
        ErpOfficeClassroom.room_name,
        ErpOfficeCenter.center_name,
        ErpAccount.employee_name,
        ErpAccount.id.label("account_id"),
        ErpAccount.avatar,
        ErpAccountTeacher.id.label("teacher_id"),
        CreateAccount.employee_name.label("create_by_name"),
        UpdateAccount.employee_name.label("update_by_name"),

    ]
    conditions = [
        ErpClassPlan.class_id == class_id,
        ErpClassPlan.disable == 0
    ]
    stmt = (
        select(*selects)
        .select_from(ErpClassPlan)
        .outerjoin(ErpClass, ErpClassPlan.class_id == ErpClass.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpAccountTeacher, ErpClassPlan.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .outerjoin(ErpOfficeClassroom, ErpClassPlan.room_id == ErpOfficeClassroom.id)
        .outerjoin(ErpOfficeCenter, ErpOfficeClassroom.center_id == ErpOfficeCenter.id)
        .outerjoin(CreateAccount, ErpClassPlan.create_by == CreateAccount.id)
        .outerjoin(UpdateAccount, ErpClassPlan.update_by == UpdateAccount.id)
        .where(and_(*conditions))
        .order_by(ErpClassPlan.start_time)
    )
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()


async def get_transfer_record(db, page=None, page_size=None,
                              stu_name=None,
                              old_class_name=None,
                              new_class_name=None,
                              transfer_date=None,
                              is_cancel=None,
                              ):
    # Aliases for tables that are used multiple times
    OldClass = aliased(RbClass)
    NewClass = aliased(RbClass)
    OldTeacher = aliased(RbTeacher)
    NewTeacher = aliased(RbTeacher)
    OldPlan = aliased(RbClassPlan)
    NewPlan = aliased(RbClassPlan)
    # 操作人
    OperateAccount = aliased(RbAccount)
    # 更新人
    UpdateAccount = aliased(RbAccount)

    selects = [
        RbStudent.StuName.label("stu_name"),
        RbStudent.StuRealMobile.label("stu_mobile"),
        OldClass.ClassName.label("old_class_name"),
        OldClass.ClassId.label("old_class_id"),
        RbStudentTempinvitation.ClassPlanId.label("old_plan_id"),
        # OldPlan.ClassDate.label("old_plan"),
        OldTeacher.TeacherName.label("old_teacher_name"),
        NewClass.ClassName.label("new_class_name"),
        NewClass.ClassId.label("new_class_id"),
        RbStudentTempinvitation.SourcePlanId.label("new_plan_id"),
        # NewPlan.ClassDate.label("new_plan"),
        NewTeacher.TeacherName.label("new_teacher_name"),
        RbStudentTempinvitation.CreateTime.label("transfer_time"),
        UpdateAccount.Account.label("update_by"),
        OperateAccount.Account.label("create_by"),
        RbStudentTempinvitation.Status.label("is_cancel"),
        RbStudentTempinvitation.UpdateTime.label("update_time"),
        RbStudentTempinvitation.Id.label("transfer_id"),
    ]

    conditions = []
    if stu_name:
        conditions.append(RbStudent.StuName.ilike(f"%{stu_name}%"))
    if old_class_name:
        conditions.append(OldClass.ClassName.ilike(f"%{old_class_name}%"))
    if new_class_name:
        conditions.append(NewClass.ClassName.ilike(f"%{new_class_name}%"))
    if transfer_date:
        # 只比较当天日期，不比较时间
        conditions.append(func.date(RbStudentTempinvitation.CreateTime) == transfer_date)
    if is_cancel is not None:
        conditions.append(RbStudentTempinvitation.Status == is_cancel)

    stmt = (
        select(*selects)
        .select_from(RbStudentTempinvitation)
        .outerjoin(OldClass, RbStudentTempinvitation.ClassId == OldClass.ClassId)
        .outerjoin(NewClass, RbStudentTempinvitation.SourceClassId == NewClass.ClassId)
        .outerjoin(RbStudent, RbStudentTempinvitation.StuId == RbStudent.StuId)
        .outerjoin(OldTeacher, OldClass.Teacher_Id == OldTeacher.TId)
        .outerjoin(NewTeacher, NewClass.Teacher_Id == NewTeacher.TId)
        # .outerjoin(OldPlan, OldPlan.ClassId == OldClass.ClassId)
        # .outerjoin(NewPlan, NewPlan.ClassId == NewClass.ClassId)
        .outerjoin(UpdateAccount, UpdateAccount.Id == RbStudentTempinvitation.UpdateBy)
        .outerjoin(OperateAccount, OperateAccount.Id == RbStudentTempinvitation.CreateBy)
        .where(and_(*conditions))
        .order_by(RbStudentTempinvitation.CreateTime.desc())
    )

    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)

    result = await db.execute(stmt)
    return result.fetchall()


async def get_class_change_record(db, page=None, page_size=None, keyword=None):
    selects = [
        RbClassLog.LogContent.label("log_content"),
        RbAccount.Account.label("create_by"),
        RbClassLog.CreateTime.label("create_time"),
    ]
    conditions = [
        RbClassLog.School_Id == 1,
        RbClassLog.Group_Id == 100001,
        RbClassLog.LogType == 4,
        RbClassLog.LogContent.ilike("学员%"),
    ]
    if keyword:
        conditions.append(RbClassLog.LogContent.ilike(f"%{keyword}%"))
    stmt = (
        select(*selects)
        .select_from(RbClassLog)
        .outerjoin(RbAccount, RbAccount.Id == RbClassLog.CreateBy)
        .where(and_(*conditions))
        .order_by(RbClassLog.CreateTime.desc())
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    return result.fetchall()


async def query_class_plan(db, teacher_id=None, year=None, month=None, start_time=None, end_time=None, class_room_ids=None, subject_id=None, category_id=None, grade_ids=None):
    selects = [
        ErpClassPlan.start_time.label("start_time"),
        ErpClassPlan.end_time.label("end_time"),
        ErpClassPlan.finish,
        ErpClassPlan.id.label("class_plan_id"),
        ErpClass.class_name,
        ErpClass.id.label("class_id"),
        ErpOfficeClassroom.room_name,
        ErpOfficeCenter.center_name,
        ErpAccount.employee_name.label("teacher_name"),
        ErpAccount.avatar.label("teacher_avatar"),

    ]
    conditions = [
        ErpClass.disable == 0,
        ErpClassPlan.disable == 0,
        
    ]
    if teacher_id:
        conditions.append(ErpClassPlan.teacher_id == teacher_id)
    if year and month:
        conditions.append(func.year(ErpClassPlan.start_time) == year)
        conditions.append(func.month(ErpClassPlan.start_time) == month)
    if start_time:
        conditions.append(ErpClassPlan.start_time >= start_time)
    if end_time:
        conditions.append(ErpClassPlan.start_time <= end_time)
    if class_room_ids:
        conditions.append(ErpClassPlan.room_id.in_(class_room_ids))
    if subject_id:
        conditions.append(ErpCourse.subject_id == subject_id)
    if category_id:
        conditions.append(ErpCourse.category_id == category_id)
    if grade_ids:
        conditions.append(ErpClass.grade_id.in_(grade_ids))
    stmt = (
        select(*selects)
        .select_from(ErpClassPlan)
        .outerjoin(ErpClass, ErpClassPlan.class_id == ErpClass.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpOfficeClassroom, ErpClassPlan.room_id == ErpOfficeClassroom.id)
        .outerjoin(ErpOfficeCenter, ErpOfficeClassroom.center_id == ErpOfficeCenter.id)
        .outerjoin(ErpAccountTeacher, ErpClassPlan.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .where(and_(*conditions))
    )
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()



async def query_teacher_class_times(db, teacher_id=None, start_time=None, end_time=None, type_ids=None, term_ids=None, grade_ids=None, subject_ids=None, category_ids=None, page=1, page_size=10, count=False):
    """
    查询教师的上课总节数
    :param db:
    :param teacher_id:
    :param start_time: 开始时间
    :param end_time: 结束时间
    :param type_ids: 课程类型ID列表
    :param term_ids: 学期ID列表
    :param grade_ids: 年级ID列表
    :param subject_ids: 科目ID列表
    :param category_ids: 分类ID列表
    :param page: 页码
    :param page_size: 每页大小
    :param count: 是否只返回总数
    :return:
    """
    conditions = [
    ]
    if start_time:
        conditions.append(ErpClass.start_date >= start_time)
    if end_time:
        conditions.append(ErpClass.start_date <= end_time)
    if teacher_id and teacher_id > 0:
        conditions.append(ErpAccountTeacher.id == teacher_id)
    if type_ids:
        conditions.append(ErpCourse.type_id.in_(type_ids))
    if term_ids:
        conditions.append(ErpCourse.term_id.in_(term_ids))
    if grade_ids:
        conditions.append(ErpCourse.grade_id.in_(grade_ids))
    if subject_ids:
        conditions.append(ErpCourse.subject_id.in_(subject_ids))
    if category_ids:
        conditions.append(ErpCourse.category_id.in_(category_ids))
    # 如果只需要获取总数
    if count:
        count_stmt = (
            select(func.count(ErpClass.id))
            .select_from(ErpClass)
            .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
            .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
            .where(and_(*conditions))
        )
        result = await db.execute(count_stmt)
        return result.scalar()
    
    # 获取详细数据
    selects = [
        ErpClass.id.label("class_id"),
        ErpClass.class_name,
        ErpClass.planning_class_times,
        ErpAccount.employee_name.label("teacher_name"),
        ErpAccount.avatar,
        ErpCourse.type_id,
        ErpCourse.term_id,
        ErpCourse.grade_id,
        ErpCourse.subject_id,
        ErpCourse.category_id,
        ErpCourse.id.label("course_id")
    ]
    
    stmt = (
        select(*selects)
        .select_from(ErpClass)
        .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(and_(*conditions))
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    return result.fetchall()


async def get_stu_score_history(db, paper_stu_id=None):
    selects = [
        ErpOnlineStuScore.id.label("score_id"),
        ErpOnlineStuScore.paper_stu_id,
        ErpOnlineStuScore.paper_question_id,
        ErpOnlineStuScore.choice_option_id,
        ErpOnlineStuScore.answer,
        ErpOnlineQuestionOption.id.label("option_id"),
        ErpOnlineQuestionOption.option_content,
        ErpOnlineQuestion.question_type,
        ErpOnlineQuestion.content

    ]
    conditions = []
    if paper_stu_id:
        conditions.append(ErpOnlineStuScore.paper_stu_id == paper_stu_id)
    stmt = (
        select(*selects)
        .select_from(ErpOnlineStuScore)
        .outerjoin(ErpOnlineQuestionOption, ErpOnlineStuScore.choice_option_id == ErpOnlineQuestionOption.id)
        .outerjoin(ErpOnlineQuestion, ErpOnlineQuestionOption.question_id == ErpOnlineQuestion.id)
        .where(and_(*conditions))
        # .order_by()
    )
    # if page and page_size:
    #     stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()


async def get_student_list_crud(db, page=None, page_size=None, keyword=None):
    selects = [
        ErpStudent.id.label('stu_id'),
        ErpStudent.stu_username,
        ErpStudent.stu_name,
        ErpStudent.stu_gender,
        ErpStudent.stu_avatar,
        ErpStudent.stu_birth,
        ErpStudent.stu_area,
        ErpStudent.stu_address,
        ErpStudent.stu_grade,
        ErpStudent.stu_idcard,
        ErpStudent.stu_school_name,
        ErpStudent.stu_wallet_amount,
        ErpStudent.stu_serial,
        ErpStudent.classin_sync,
        ErpStudent.how_known,
        ErpStudent.create_time,
        ErpStudent.is_blacklist

        # # 使用exists子查询检查是否存在优惠券
        # exists().where(and_(
        #     ErpStudentDiscountCoupon.stu_id == ErpStudent.id,
        #     ErpStudentDiscountCoupon.disable == 0
        # )).label('has_coupon'),
        # # 使用exists子查询检查是否存在固定折扣
        # exists().where(and_(
        #     ErpStudentDiscountFixed.stu_id == ErpStudent.id,
        #     ErpStudentDiscountFixed.disable == 0
        # )).label('has_fixed_discount')
    ]
    conditions = [
        ErpStudent.disable == 0
    ]
    if keyword:
        conditions.append(or_(
            ErpStudent.stu_name.ilike(f"%{keyword}%"),
            ErpStudent.stu_username.ilike(f"%{keyword}%"),
        ))
    stmt = (
        select(*selects)
        .select_from(ErpStudent)
        # .outerjoin(
        #     ErpStudentDiscountCoupon,
        #     and_(
        #         ErpStudentDiscountCoupon.stu_id == ErpStudent.id,
        #         ErpStudentDiscountCoupon.disable == 0
        #     )
        # )
        # .outerjoin(
        #     ErpStudentDiscountFixed,
        #     and_(
        #         ErpStudentDiscountFixed.stu_id == ErpStudent.id,
        #         ErpStudentDiscountFixed.disable == 0
        #     )
        # )
        .where(and_(*conditions))
        .order_by(ErpStudent.create_time.desc())
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    return result.fetchall()


async def get_student_count(db, keyword=None):
    conditions = [
        ErpStudent.disable == 0
    ]
    if keyword:
        conditions.append(ErpStudent.stu_name.ilike(f"%{keyword}%"))

    stmt = (
        select(func.count(ErpStudent.id))
        .select_from(ErpStudent)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.scalar()


async def get_student_discount_and_orders(db, student_ids: list):
    """获取学生的优惠和订单信息
    
    Args:
        db: 数据库会话
        student_ids: 学生ID列表
    
    Returns:
        tuple: (优惠券学生集合, 固定折扣学生集合, 学生订单字典)
    """
    # 查询优惠券信息
    coupon_stmt = select(ErpStudentDiscountCoupon.stu_id).where(
        and_(
            ErpStudentDiscountCoupon.stu_id.in_(student_ids),
            ErpStudentDiscountCoupon.disable == 0
        )
    )
    coupon_result = await db.execute(coupon_stmt)
    students_with_coupons = {row[0] for row in coupon_result.fetchall()}

    # 查询固定折扣信息
    fixed_stmt = select(ErpStudentDiscountFixed.stu_id).where(
        and_(
            ErpStudentDiscountFixed.stu_id.in_(student_ids),
            ErpStudentDiscountFixed.disable == 0
        )
    )
    fixed_result = await db.execute(fixed_stmt)
    students_with_fixed = {row[0] for row in fixed_result.fetchall()}

    # 查询订单信息
    order_stmt = select(
        ErpOrderStudent.stu_id,
        ErpOrder.id.label('order_id'),
        ErpOrder.total_receivable,
        ErpOrder.total_income,
        ErpOrder.order_state,
        ErpOrder.create_time,

    ).join(
        ErpOrder,
        and_(
            ErpOrderStudent.id == ErpOrder.order_student_id,
            ErpOrder.disable == 0,
            ErpOrderStudent.class_id > 0
        )
    ).where(
        and_(
            ErpOrderStudent.stu_id.in_(student_ids),
            ErpOrderStudent.disable == 0
        )
    )
    order_result = await db.execute(order_stmt)
    order_data = order_result.fetchall()

    # 将订单信息组织成以学生ID为键的字典
    student_orders = {}
    for order in order_data:
        order_dict = {
            'order_id': order.order_id,
            'total_receivable': float(order.total_receivable),
            'total_income': float(order.total_income),
            'order_state': order.order_state,
            'create_time': order.create_time
        }
        if order.stu_id not in student_orders:
            student_orders[order.stu_id] = []
        student_orders[order.stu_id].append(order_dict)

    return students_with_coupons, students_with_fixed, student_orders


async def get_order_courses(db: AsyncSession, order_ids: list[int]):
    """获取订单对应的课程信息
    
    Args:
        db: 数据库会话
        order_ids: 订单ID列表
        
    Returns:
        list: 课程信息列表
    """
    selects = [
        ErpOrderStudent.order_id,
        ErpOrderStudent.class_id,
        ErpOrderStudent.create_time,
        ErpClass.class_name,
    ]

    stmt = (
        select(*selects)
        .select_from(ErpOrderStudent)
        .outerjoin(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(and_(
            ErpOrderStudent.order_id.in_(order_ids),
            ErpOrderStudent.disable == 0,
            ErpClass.disable == 0
        ))
    )

    result = await db.execute(stmt)
    return result.fetchall()


async def get_student_by_osi(db: AsyncSession, osi_ids: list[int]):
    selects = [
        ErpOrderStudent.id.label('order_student_id'),
        ErpOrderStudent.stu_id,
        ErpOrderStudent.class_id,
        ErpOrderStudent.create_time,
        ErpOrderStudent.student_state,
        ErpOrderStudent.total_hours,
        ErpOrderStudent.complete_hours,
        ErpStudent.stu_username,
        ErpStudent.stu_name,
        ErpStudent.stu_avatar,
        ErpStudent.stu_gender,
        ErpStudent.stu_birth,
        ErpStudent.stu_grade,
        ErpStudent.stu_school_name,
        ErpStudent.classin_sync,
        ErpStudent.classin_uid,
        ErpStudent.stu_serial
        
    ]
    conditions = [
        ErpOrderStudent.id.in_(osi_ids),
        ErpOrderStudent.disable == 0,
    ]
    stmt = (
        select(*selects)
        .select_from(
            ErpOrderStudent
        )
        .outerjoin(
            ErpStudent,
            ErpOrderStudent.stu_id == ErpStudent.id
        )
        .where(and_(*conditions))
        .order_by(ErpOrderStudent.create_time.desc())
    )
    result = await db.execute(stmt)
    return result.fetchall()



async def get_student_by_class_id(db: AsyncSession, class_id: int):
    # 创建子查询，获取学生关联的微信数据
    wechat_subquery = (
        select(
            ErpStudentWechat.stu_id,
            func.json_object(
                'wechat_data', func.json_arrayagg(
                    func.json_object(
                        'id', ErpStudentWechat.id,
                        'wechat_open_id', ErpStudentWechat.wechat_open_id
                    )
                )
            ).label('wechat_json')
        )
        .where(ErpStudentWechat.disable == 0)
        .group_by(ErpStudentWechat.stu_id)
        .subquery()
    )

    selects = [
        ErpOrderStudent.id.label('order_student_id'),
        ErpOrderStudent.stu_id,
        ErpOrderStudent.class_id,
        ErpOrderStudent.create_time,
        ErpOrderStudent.student_state,
        ErpOrderStudent.total_hours,
        ErpOrderStudent.complete_hours,
        ErpOrderStudent.p_id,
        ErpOrderStudent.is_online,
        ErpStudent.stu_username,
        ErpStudent.stu_name,
        ErpStudent.stu_avatar,
        ErpStudent.stu_gender,
        ErpStudent.stu_birth,
        ErpStudent.stu_grade,
        ErpStudent.stu_school_name,
        ErpStudent.classin_sync,
        ErpStudent.classin_uid,
        ErpStudent.stu_serial,
        # wechat_subquery.c.wechat_json
    ]
    conditions = [
        ErpOrderStudent.class_id == class_id,
        ErpOrderStudent.disable == 0,
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value]),
        ErpOrderStudent.order_class_type == OrderType.COURSE.value
    ]
    stmt = (
        select(*selects)
        .select_from(
            ErpOrderStudent
        )
        .outerjoin(
            ErpStudent,
            ErpOrderStudent.stu_id == ErpStudent.id
        )
        .outerjoin(
            wechat_subquery,
            ErpStudent.id == wechat_subquery.c.stu_id
        )
        .where(and_(*conditions))
        .order_by(ErpOrderStudent.create_time.desc())
    )
    result = await db.execute(stmt)
    students = result.fetchall()
    new_students = []
     # 单独查询每个学生的订单
    if students:
        order_student_ids = [student.order_student_id for student in students]
        order_stmt = (
            select(
                ErpOrder.order_student_id,
                ErpOrder.id.label('order_id'),
                ErpOrder.order_state,
                ErpOrder.total_income,
                ErpOrder.buy_num,
            )
            .where(
                ErpOrder.order_student_id.in_(order_student_ids),
                ErpOrder.disable == 0,
            )
        )
        order_result = await db.execute(order_stmt)
        orders = order_result.fetchall()
        
        # 将订单信息组织成以order_student_id为键的字典
        student_orders = {}
        for order in orders:
            if order.order_student_id not in student_orders:
                student_orders[order.order_student_id] = []
            student_orders[order.order_student_id].append({
                'order_id': order.order_id,
                'order_state': order.order_state,
                'total_income': order.total_income,
                'buy_num': order.buy_num,
            })
        
        # 直接查询每个学生的微信绑定数据
        student_ids = [student.stu_id for student in students]
        wechat_stmt = (
            select(
                ErpStudentWechat.stu_id,
                ErpStudentWechat.id,
                ErpStudentWechat.wechat_open_id
            )
            .where(
                ErpStudentWechat.stu_id.in_(student_ids),
                ErpStudentWechat.disable == 0
            )
        )
        wechat_result = await db.execute(wechat_stmt)
        wechat_data = wechat_result.fetchall()
        
        # 将微信数据组织成以stu_id为键的字典
        student_wechat = {}
        for wechat in wechat_data:
            if wechat.stu_id not in student_wechat:
                student_wechat[wechat.stu_id] = []
            student_wechat[wechat.stu_id].append({
                'id': wechat.id,
                'wechat_open_id': wechat.wechat_open_id
            })
        
        # 将订单信息和微信数据添加到学生信息中
        new_students = []
        for student in students:
            student_dict = dict(student)
            student_dict['orders'] = student_orders.get(student.order_student_id, [])
            student_dict['wechat_list'] = student_wechat.get(student.stu_id, [])
            new_students.append(student_dict)
            
    return new_students



async def class_waiting_approval_receipts(db):
    """
    获取开班审核单据， 审核通过但是班级状态为待审核的单据
    """
    selects = [
        ErpReceipt.id,
        ErpReceipt.apply_reason,
        ErpReceipt.class_id,
        ErpReceipt.audit_state,
    ]
    conditions = [
        ErpClass.audit_status == ClassAuditStatus.WAITING.value,   # 待审核
        ErpReceipt.class_id > 0,   # 是开班单
        ErpReceipt.audit_state.in_([AuditState.PASS.value])   # 审核通过
    ]
    stmt = (
        select(*selects)
        .select_from(ErpReceipt)
        .outerjoin(ErpClass, ErpReceipt.class_id == ErpClass.id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()


async def query_class_record(db, page=None, page_size=None, teacher_id=None, room_id=None, start_time=None, end_time=None, count=False):
    """
    查询上课记录
    """
    # 如果只需要统计数量，执行更简单的查询以提高性能
    if count:
        count_stmt = select(func.count(ErpClassPlan.id)).select_from(ErpClassPlan).where(
            and_(
                ErpClassPlan.disable == 0,
                ErpClassPlan.finish == 1,  # 只统计已完成的课程
                ErpClass.disable == 0
            )
        ).outerjoin(ErpClass, ErpClassPlan.class_id == ErpClass.id)
        result = await db.execute(count_stmt)
        return result.scalar()
    
    # 首先查询课程计划数据
    selects = [
        ErpClassPlan.id,
        ErpClassPlan.class_id,
        ErpClassPlan.create_time,
        ErpClassPlan.start_time,
        ErpClassPlan.end_time,
        ErpClassPlan.room_id,
        ErpOfficeClassroom.room_name,
        ErpAccountTeacher.id.label('teacher_id'),
        ErpAccount.employee_name.label('teacher_name'),
        ErpClass.class_name,
        ErpCourse.course_name,
    ]
    conditions = [
        ErpClassPlan.disable == 0,
        ErpClassPlan.finish == 1,  # 只获取已完成的课程计划
        ErpClass.disable == 0
    ]
    if teacher_id:
        conditions.append(ErpClassPlan.teacher_id == teacher_id)
    if room_id:
        conditions.append(ErpClassPlan.room_id == room_id)
    if start_time:
        conditions.append(ErpClassPlan.start_time >= start_time)
    if end_time:
        conditions.append(ErpClassPlan.end_time <= end_time)
        
    stmt = (
        select(*selects)
        .select_from(ErpClassPlan)
        .outerjoin(ErpClass, ErpClassPlan.class_id == ErpClass.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpOfficeClassroom, ErpClassPlan.room_id == ErpOfficeClassroom.id)
        .outerjoin(ErpAccountTeacher, ErpClassPlan.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .where(and_(*conditions))
        .order_by(ErpClassPlan.create_time.desc())
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    class_plans = result.fetchall()
    
    # 如果没有课程计划数据，直接返回空列表
    if not class_plans:
        return []
    
    # 获取所有相关的班级ID和计划ID
    class_ids = [plan.class_id for plan in class_plans]
    plan_ids = [plan.id for plan in class_plans]
    
    # 查询每个班级的课程计划总数和顺序
    class_progress = {}
    for class_id in class_ids:
        # 查询该班级的所有课程计划并按开始时间排序
        progress_selects = [
            ErpClassPlan.id,
            ErpClassPlan.start_time
        ]
        progress_conditions = [
            ErpClassPlan.class_id == class_id,
            ErpClassPlan.disable == 0,
            ErpClassPlan.finish == 1  # 只统计已完成的课程
        ]
        progress_stmt = (
            select(*progress_selects)
            .select_from(ErpClassPlan)
            .where(and_(*progress_conditions))
            .order_by(ErpClassPlan.start_time)  # 按开始时间排序
        )
        progress_result = await db.execute(progress_stmt)
        plans = progress_result.fetchall()
        
        # 获取总课程数
        total_plans = len(plans)
        
        # 创建计划ID到序号的映射
        plan_order = {plan.id: idx + 1 for idx, plan in enumerate(plans)}
        
        class_progress[class_id] = {
            "total": total_plans,
            "plan_order": plan_order
        }
    
    # 查询这些班级的学生信息（一次性查询所有相关班级的学生）
    student_selects = [
        ErpOrderStudent.id.label('order_student_id'),  # 添加order_student_id
        ErpOrderStudent.class_id,
        ErpStudent.id.label('stu_id'),
        ErpStudent.stu_name,
        ErpStudent.stu_avatar,
        ErpOrderStudent.student_state
    ]
    student_conditions = [
        ErpOrderStudent.disable == 0,
        ErpOrderStudent.class_id.in_(class_ids),
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value])
    ]
    student_stmt = (
        select(*student_selects)
        .select_from(ErpOrderStudent)
        .outerjoin(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
        .where(and_(*student_conditions))
    )
    student_result = await db.execute(student_stmt)
    students = student_result.fetchall()
    
    # 构建班级ID到学生列表的映射，同时保存order_student_id对应的学生信息用于后续关联
    class_students = {}
    osi_student_map = {}  # 用于存储order_student_id到学生信息的映射
    
    for student in students:
        # 班级学生映射
        if student.class_id not in class_students:
            class_students[student.class_id] = []
        
        student_info = {
            'order_student_id': student.order_student_id,
            'stu_id': student.stu_id,
            'stu_name': student.stu_name,
            'stu_avatar': student.stu_avatar,
            'stu_state': student.student_state
        }
        
        class_students[student.class_id].append(student_info)
        
        # 保存order_student_id映射
        osi_student_map[student.order_student_id] = student_info
    
    # 查询签到记录信息
    checking_selects = [
        ErpClassChecking.class_plan_id,
        ErpClassChecking.order_student_id,  # 修改为order_student_id
        ErpClassChecking.check_status,
    ]
    checking_conditions = [
        ErpClassChecking.disable == 0,
        ErpClassChecking.class_plan_id.in_(plan_ids)
    ]
    checking_stmt = (
        select(*checking_selects)
        .select_from(ErpClassChecking)
        .where(and_(*checking_conditions))
    )
    checking_result = await db.execute(checking_stmt)
    checking_records = checking_result.fetchall()
    
    # 构建计划ID到签到学生列表的映射
    plan_checking = {}
    for record in checking_records:
        if record.class_plan_id not in plan_checking:
            plan_checking[record.class_plan_id] = []
        
        # 获取学生信息（通过order_student_id）
        student_info = osi_student_map.get(record.order_student_id, {})
        
        checking_info = {
            'order_student_id': record.order_student_id,
            'stu_id': student_info.get('stu_id'),
            'stu_name': student_info.get('stu_name'),
            'stu_avatar': student_info.get('stu_avatar'),
            'check_status': record.check_status,
        }
        
        plan_checking[record.class_plan_id].append(checking_info)
    
    # 将学生信息和签到信息添加到课程计划数据中
    result_data = []
    for plan in class_plans:
        plan_dict = dict(plan)
        plan_dict['plan_list'] = class_students.get(plan.class_id, [])  # 重命名为plan_list
        plan_dict['checking_list'] = plan_checking.get(plan.id, [])  # 添加checking_list
        
        # 添加课程进度信息
        if plan.class_id in class_progress:
            progress_info = class_progress[plan.class_id]
            current_order = progress_info["plan_order"].get(plan.id, 0)
            total_plans = progress_info["total"]
            plan_dict['current_index'] = current_order  # 当前是第几节课
            plan_dict['total_plans'] = total_plans  # 总课程数
        
        result_data.append(plan_dict)
    
    return result_data


async def query_open_class_progress(db, page=None, page_size=None, class_name=None, teacher_id=None, approval_status=None):
    """
    查询班级开班进度
    
    参数:
        db: 数据库会话
        page: 页码
        page_size: 每页大小
        class_name: 班级名称关键字
        teacher_id: 教师ID
        approval_status: 审批状态
        
    返回:
        班级开班进度信息列表和总数
    """
    # 选择查询字段
    selects = [
        ErpClass.id.label('class_id'),
        ErpClass.class_name,
        ErpClass.teacher_id,
        ErpClass.audit_status,
        ErpCourse.id.label('course_id'),
        ErpCourse.course_name,
        ErpAccountTeacher.id.label('teacher_id_obj'),
        ErpAccount.employee_name.label('teacher_name'),
        ErpAccount.avatar.label('teacher_avatar'),
        ErpReceipt.id.label('receipt_id'),
        ErpReceipt.create_by.label('creator_id'),
        ErpReceipt.workflow_instance_id,
        CreateBy.employee_name.label('creator_name'),
        CreateBy.avatar.label('creator_avatar'),
        ErpWorkflowInstance.current_node_id,
        ErpWorkflowInstance.current_node_name,
        ErpWorkflowInstance.status.label('workflow_status')
    ]
    
    # 查询条件
    conditions = [
        ErpClass.disable == 0,
        ErpReceipt.class_id > 0,  # 只查找与班级关联的单据
        ErpReceipt.disable == 0
    ]
    
    # 添加过滤条件
    if class_name:
        conditions.append(ErpClass.class_name.like(f'%{class_name}%'))
    if teacher_id:
        conditions.append(ErpClass.teacher_id == teacher_id)
    if approval_status is not None:
        conditions.append(ErpClass.audit_status == approval_status)
    
    # 构建查询语句
    stmt = (
        select(*selects)
        .select_from(ErpReceipt)
        .outerjoin(ErpClass, ErpReceipt.class_id == ErpClass.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .outerjoin(CreateBy, ErpReceipt.create_by == CreateBy.id)
        .outerjoin(ErpWorkflowInstance, ErpReceipt.id == ErpWorkflowInstance.business_id)
        .where(and_(*conditions))
        .order_by(ErpReceipt.create_time.desc())
    )
    
    # 统计总数
    count_stmt = select(func.count()).select_from(stmt.subquery())
    count_result = await db.execute(count_stmt)
    total_count = count_result.scalar()
    
    # 分页查询
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    
    # 执行查询
    result = await db.execute(stmt)
    data = result.fetchall()
    
    return data, total_count


async def query_teacher_class_times_statistics(db, start_time=None, end_time=None, term_ids=None):
    """
    按教师聚合，统计时段内每个教师的总上课节数，以及各班型（长短期）分别的上课节数
    
    参数:
        db: 数据库会话
        start_time: 开始时间 
        end_time: 结束时间
        term_ids: 学期ID列表
    返回:
        按教师ID分组的统计结果
    """
    # 查询条件
    conditions = [
        ErpClassPlan.disable == 0,
        ErpClass.disable == 0,
        # 只统计已完成的课程
        ErpClassPlan.finish == 1
    ]
    
    if start_time:
        conditions.append(ErpClassPlan.start_time >= start_time)
    if end_time:
        conditions.append(ErpClassPlan.start_time <= end_time)
    if term_ids:
        conditions.append(ErpCourse.term_id.in_(term_ids))
    
    # 查询各教师上课总时数
    selects = [
        ErpClassPlan.teacher_id.label("teacher_id"),
        ErpAccount.employee_name.label("teacher_name"),
        ErpAccount.avatar.label("teacher_avatar"),
        func.count(ErpClassPlan.id).label("total_classes"),
        func.sum(ErpClassPlan.time_duration).label("total_duration")
    ]
    
    # 查询教师课时统计
    stmt = (
        select(*selects)
        .select_from(ErpClassPlan)
        .outerjoin(ErpClass, ErpClassPlan.class_id == ErpClass.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpAccountTeacher, ErpClassPlan.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .where(and_(*conditions))
        .group_by(ErpClassPlan.teacher_id, ErpAccount.employee_name, ErpAccount.avatar)
        .order_by(func.count(ErpClassPlan.id).desc())
    )
    
    result = await db.execute(stmt)
    teacher_stats = result.fetchall()
    
    # 获取所有教师的ID
    teacher_ids = [teacher.teacher_id for teacher in teacher_stats if teacher.teacher_id]
    
    # 如果没有数据，直接返回空列表
    if not teacher_ids:
        return []
    
    # 查询各班型的课时统计
    type_selects = [
        ErpClassPlan.teacher_id.label("teacher_id"),
        ErpCourse.type_id.label("course_type_id"),
        func.count(ErpClassPlan.id).label("type_classes"),
        func.sum(ErpClassPlan.time_duration).label("type_duration")
    ]
    
    type_stmt = (
        select(*type_selects)
        .select_from(ErpClassPlan)
        .outerjoin(ErpClass, ErpClassPlan.class_id == ErpClass.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(and_(ErpClassPlan.teacher_id.in_(teacher_ids), *conditions))
        .group_by(ErpClassPlan.teacher_id, ErpCourse.type_id)
    )
    
    type_result = await db.execute(type_stmt)
    type_stats = type_result.fetchall()
    
    # 将班型统计数据按教师ID分组
    teacher_type_stats = {}
    for stat in type_stats:
        teacher_id = stat.teacher_id
        if teacher_id not in teacher_type_stats:
            teacher_type_stats[teacher_id] = {}
        
        teacher_type_stats[teacher_id][stat.course_type_id] = {
            "classes": stat.type_classes,
            "duration": stat.type_duration
        }
    
    # 查询每个教师的详细课程计划记录
    detail_selects = [
        ErpClassPlan.id.label("plan_id"),
        ErpClassPlan.teacher_id.label("teacher_id"),
        ErpClassPlan.class_id.label("class_id"),
        ErpClassPlan.start_time.label("start_time"),
        ErpClassPlan.end_time.label("end_time"),
        ErpClassPlan.time_duration.label("time_duration"),
        ErpClass.class_name.label("class_name"),
        ErpCourse.course_name.label("course_name"),
        ErpCourse.type_id.label("course_type_id")
    ]
    
    detail_stmt = (
        select(*detail_selects)
        .select_from(ErpClassPlan)
        .outerjoin(ErpClass, ErpClassPlan.class_id == ErpClass.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(and_(ErpClassPlan.teacher_id.in_(teacher_ids), *conditions))
        .order_by(ErpClassPlan.start_time.desc())
    )
    
    detail_result = await db.execute(detail_stmt)
    detail_records = detail_result.fetchall()
    
    # 将课程计划记录按教师ID分组
    teacher_detail_records = {}
    for record in detail_records:
        teacher_id = record.teacher_id
        if teacher_id not in teacher_detail_records:
            teacher_detail_records[teacher_id] = []
        
        teacher_detail_records[teacher_id].append(dict(record))
    
    # 组合结果
    final_results = []
    for teacher in teacher_stats:
        teacher_dict = dict(teacher)
        teacher_dict["course_types"] = teacher_type_stats.get(teacher.teacher_id, {})
        teacher_dict["detail_records"] = teacher_detail_records.get(teacher.teacher_id, [])
        final_results.append(teacher_dict)
    
    return final_results

async def query_class_info(db, term_id=None, grade_id=None, p_grade_id=None, type_id=None, subject_id=None, category_id=None):
    """
    查询班级信息
    支持单个ID或ID列表
    """
    # 查询条件
    conditions = [
        ErpClass.disable == 0,
        ErpClass.audit_status == ClassAuditStatus.PASS.value,
        ErpClass.class_status.in_([ClassStatus.NotStart.value, ClassStatus.Started.value]),
    ]
    
    selects = [
        ErpClass.id,
        ErpClass.class_name,
        ErpClass.class_status,
        ErpClass.teacher_id,
        ErpAccount.employee_name.label('teacher_name'),
        
        # ErpClass.class_type,
        # ErpClass.class_grade,
        ErpClass.use_standard_full_rate,
        ErpClass.class_capacity,
        ErpClass.pre_enrollment,
        
        ErpCourse.term_id,
        ErpCourse.grade_id,
        ErpCourse.p_grade_id,
        ErpCourse.type_id,
        ErpCourse.subject_id,
        ErpCourse.category_id,
    ]
    
    # 支持单个ID或ID列表
    if term_id:
        if isinstance(term_id, list):
            conditions.append(ErpCourse.term_id.in_(term_id))
        else:
            conditions.append(ErpCourse.term_id == term_id)
    if grade_id:
        if isinstance(grade_id, list):
            conditions.append(ErpCourse.grade_id.in_(grade_id))
        else:
            conditions.append(ErpCourse.grade_id == grade_id)
    if p_grade_id:
        if isinstance(p_grade_id, list):
            conditions.append(ErpCourse.p_grade_id.in_(p_grade_id))
        else:
            conditions.append(ErpCourse.p_grade_id == p_grade_id)
    if type_id:
        if isinstance(type_id, list):
            conditions.append(ErpCourse.type_id.in_(type_id))
        else:
            conditions.append(ErpCourse.type_id == type_id)
    if subject_id:
        if isinstance(subject_id, list):
            conditions.append(ErpCourse.subject_id.in_(subject_id))
        else:
            conditions.append(ErpCourse.subject_id == subject_id)
    if category_id:
        if isinstance(category_id, list):
            conditions.append(ErpCourse.category_id.in_(category_id))
        else:
            conditions.append(ErpCourse.category_id == category_id)
    
    stmt = (
        select(*selects)
        .select_from(ErpClass)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .where(and_(*conditions))
    )
    
    result = await db.execute(stmt)
    return result.fetchall()

# async def get_teacher_info_by_id(db, teacher_id):
#     """
#     通过教师ID获取教师信息
    
#     参数:
#         db: 数据库会话
#         teacher_id: 教师ID
        
#     返回:
#         教师信息对象，包含账户信息
#     """
#     from models.m_teacher import ErpAccountTeacher
#     from models.models import ErpAccount
    
#     # 查询教师账号关联信息
#     stmt = (
#         select(ErpAccountTeacher, ErpAccount)
#         .join(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
#         .where(ErpAccountTeacher.id == teacher_id)
#     )
    
#     result = await db.execute(stmt)
#     teacher_info = result.first()
    
#     return teacher_info

async def get_class_teacher_info(db, class_ids):
    """
    获取班级教师信息
    
    参数:
        db: 数据库会话
        class_ids: 班级ID列表
        
    返回:
        班级ID与教师信息的映射表
    """
    from models.m_class import ErpClass
    from models.m_teacher import ErpAccountTeacher
    from models.models import ErpAccount
    
    # 查询班级教师关联信息
    stmt = (
        select(ErpClass.id, ErpAccountTeacher, ErpAccount)
        .join(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
        .join(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .where(ErpClass.id.in_(class_ids))
    )
    
    result = await db.execute(stmt)
    class_teacher_info = result.fetchall()
    
    # 构建班级ID与教师信息的映射
    class_teacher_map = {}
    for class_id, teacher, account in class_teacher_info:
        teacher_info = {
            "id": teacher.id,
            "account_id": teacher.account_id,
            "name": account.employee_name,
            "avatar": account.avatar,
            "qy_wechat_userid": account.qy_wechat_userid
        }
        class_teacher_map[class_id] = teacher_info
    
    return class_teacher_map

async def get_non_renewal_student_info(db, start_class_ids, end_class_ids, expected_renewal_student_ids):
    """
    获取未续报学生信息
    
    参数:
        db: 数据库会话
        start_class_ids: 上期班级ID列表
        end_class_ids: 本期班级ID列表
        expected_renewal_student_ids: 应续报学生ID列表
        
    返回:
        未续报学生信息列表 [{"stu_name": "学生名称", "phone": "联系电话", "last_class_name": "上期在读", "last_teacher_name": "授课教师"}]
    """
    from sqlalchemy import select, and_, not_
    from models.m_order import ErpOrderStudent
    from models.m_class import ErpClass
    from models.m_student import ErpStudent
    from models.m_teacher import ErpAccountTeacher
    from models.models import ErpAccount
    from utils.enum.enum_order import StudentState
    
    # 查询续报学生ID
    renewal_student_query = select(ErpOrderStudent.stu_id).where(
        and_(
            ErpOrderStudent.class_id.in_(end_class_ids),
            ErpOrderStudent.stu_id.in_(expected_renewal_student_ids),
            ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value])
        )
    )
    result = await db.execute(renewal_student_query)
    renewal_student_ids = [row[0] for row in result.fetchall()]
    
    # 确定未续报学生ID
    non_renewal_student_ids = [sid for sid in expected_renewal_student_ids if sid not in renewal_student_ids]
    
    # 如果没有未续报学生，直接返回空列表
    if not non_renewal_student_ids:
        return []
    
    # 查询未续报学生的详细信息
    non_renewal_list = []
    
    # 获取上期班级和学生关联信息
    class_student_query = (
        select(
            ErpOrderStudent.stu_id,
            ErpOrderStudent.class_id,
            ErpStudent.stu_name,
            ErpStudent.stu_username,
            ErpClass.class_name,
            ErpClass.teacher_id,
            ErpAccount.employee_name.label('teacher_name')
        )
        .select_from(ErpOrderStudent)
        .join(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
        .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .where(
            and_(
                ErpOrderStudent.stu_id.in_(non_renewal_student_ids),
                ErpOrderStudent.class_id.in_(start_class_ids),
                ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value])
            )
        )
    )
    
    result = await db.execute(class_student_query)
    student_info = result.fetchall()
    
    # 格式化未续报学生信息
    for student in student_info:
        non_renewal_list.append({
            "stu_name": student.stu_name,
            "stu_username": student.stu_username,
            "last_class_name": student.class_name,
            "last_teacher_name": student.teacher_name if student.teacher_name else "未分配"
        })
    
    return non_renewal_list


async def get_non_renewal_student_detail_info(db, start_class_ids, non_renewal_student_ids):
    """
    获取未续报学生详细信息（不重复查询续报学生）
    
    参数:
        db: 数据库会话
        start_class_ids: 上期班级ID列表
        non_renewal_student_ids: 未续报学生ID列表（已确定的）
        
    返回:
        未续报学生信息列表 [{"stu_name": "学生名称", "stu_username": "联系电话", "last_class_name": "上期在读", "last_teacher_name": "授课教师"}]
    """
    from sqlalchemy import select, and_
    from models.m_order import ErpOrderStudent
    from models.m_class import ErpClass
    from models.m_student import ErpStudent
    from models.m_teacher import ErpAccountTeacher
    from models.models import ErpAccount
    from utils.enum.enum_order import StudentState
    
    # 如果没有未续报学生，直接返回空列表
    if not non_renewal_student_ids:
        return []
    
    # 查询未续报学生的详细信息
    non_renewal_list = []
    
    # 获取上期班级和学生关联信息
    class_student_query = (
        select(
            ErpOrderStudent.stu_id,
            ErpOrderStudent.class_id,
            ErpStudent.stu_name,
            ErpStudent.stu_username,
            ErpClass.class_name,
            ErpClass.teacher_id,
            ErpAccount.employee_name.label('teacher_name')
        )
        .select_from(ErpOrderStudent)
        .join(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
        .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .where(
            and_(
                ErpOrderStudent.stu_id.in_(non_renewal_student_ids),
                ErpOrderStudent.class_id.in_(start_class_ids),
                ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value])
            )
        )
    )
    
    result = await db.execute(class_student_query)
    student_info = result.fetchall()
    
    # 格式化未续报学生信息
    for student in student_info:
        non_renewal_list.append({
            "stu_name": student.stu_name,
            "stu_username": student.stu_username,
            "last_class_name": student.class_name,
            "last_teacher_name": student.teacher_name if student.teacher_name else "未分配"
        })
    
    return non_renewal_list
    
    
async def get_class_info_by_conditions(db, term_id=None, grade_id=None, type_id=None, subject_id=None, category_id=None, start_time=None, teacher_id=None):
    """
    获取符合条件的班级信息
    """
    from sqlalchemy import select, and_
    from models.m_class import ErpClass, ErpCourse
    from models.m_teacher import ErpAccountTeacher
    from models.models import ErpAccount
    from utils.enum.enum_approval import ClassAuditStatus

    # 构建查询条件
    conditions = [
        ErpClass.disable == 0,
        ErpClass.audit_status == ClassAuditStatus.PASS.value,  # 已审核通过的班级
    ]
    
    if term_id:
        conditions.append(ErpCourse.term_id == term_id)
    if grade_id:
        conditions.append(ErpCourse.grade_id == grade_id)
    if type_id:
        conditions.append(ErpCourse.type_id == type_id)
    if subject_id:
        conditions.append(ErpCourse.subject_id == subject_id)
    if category_id:
        conditions.append(ErpCourse.category_id == category_id)
    if start_time:
        conditions.append(ErpClass.start_date >= start_time)
    if teacher_id:
        conditions.append(ErpClass.teacher_id == teacher_id)
    
    # 查询班级信息
    selects = [
        ErpClass.id.label('class_id'),
        ErpClass.class_name,
        ErpClass.teacher_id,
        ErpClass.use_standard_full_rate,
        ErpAccount.id.label('account_id'),
        ErpAccount.employee_name.label('teacher_name'),
        ErpAccount.avatar.label('teacher_avatar'),
    ]
    
    stmt = (
        select(*selects)
        .select_from(ErpClass)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .where(and_(*conditions))
    )
    
    result = await db.execute(stmt)
    return result.fetchall()


async def get_class_plan_data(db, class_ids):
    """
    获取班级已上课节数（已结束的课程计划数量）
    """
    from sqlalchemy import select, and_, func
    from models.m_class import ErpClassPlan
    
    class_plan_stmt = (
        select(
            ErpClassPlan.class_id,
            func.count(ErpClassPlan.id).label('finished_class_count')
        )
        .where(
            and_(
                ErpClassPlan.class_id.in_(class_ids),
                ErpClassPlan.finish == 1,
                ErpClassPlan.disable == 0
            )
        )
        .group_by(ErpClassPlan.class_id)
    )
    
    result = await db.execute(class_plan_stmt)
    return {row.class_id: row.finished_class_count for row in result.fetchall()}


async def get_student_counts(db, class_ids):
    """
    获取班级的学生数量（用于计算满班系数）
    """
    from sqlalchemy import select, and_, func
    from models.m_order import ErpOrderStudent
    from utils.enum.enum_order import StudentState
    
    student_stmt = (
        select(
            ErpOrderStudent.class_id,
            func.count(ErpOrderStudent.id).label('student_count')
        )
        .where(
            and_(
                ErpOrderStudent.class_id.in_(class_ids),
                ErpOrderStudent.disable == 0,
                ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value])
            )
        )
        .group_by(ErpOrderStudent.class_id)
    )
    
    result = await db.execute(student_stmt)
    return {row.class_id: row.student_count for row in result.fetchall()}


async def get_checking_durations(db, class_ids):
    """
    获取各班级签到记录数据（用于计算平均课消）
    
    返回:
        返回包含每个班级签到信息的字典：
        {
            class_id: {
                "count": 签到总次数,
                "duration": 签到总时长
            }
        }
    """
    from sqlalchemy import select, and_, func
    from models.m_class import ErpClassChecking
    
    checking_stmt = (
        select(
            ErpClassChecking.class_id,
            func.count(ErpClassChecking.id).label('checking_count'),
            func.sum(ErpClassChecking.time_duration).label('total_duration')
        )
        .where(
            and_(
                ErpClassChecking.class_id.in_(class_ids),
                ErpClassChecking.disable == 0
            )
        )
        .group_by(ErpClassChecking.class_id)
    )
    
    result = await db.execute(checking_stmt)
    checking_data = {}
    for row in result.fetchall():
        checking_data[row.class_id] = {
            "count": row.checking_count,
            "duration": row.total_duration or 0
        }
    
    return checking_data
    
# 查询续报规则
async def class_renew_rule_module(db):
    """
    查询续报规则
    """
    CurrentClass = aliased(ErpClass)
    NextClass = aliased(ErpClass)
    NextClass2 = aliased(ErpClass)
    CreatedBy = aliased(ErpAccount, name="CreatedBy")
    UpdatedBy = aliased(ErpAccount, name="UpdatedBy")

    selects = [
        ErpClassRenewRules.id,
        ErpClassRenewRules.current_class_id,
        ErpClassRenewRules.current_teacher_id,
        ErpClassRenewRules.term_id,
        ErpClassRenewRules.next_class_id,
        ErpClassRenewRules.next2_class_id,
        ErpClassRenewRules.start_time,
        ErpClassRenewRules.end_time,
        ErpClassRenewRules.signup_start,
        ErpClassRenewRules.created_order,
        ErpClassRenewRules.create_by,
        ErpClassRenewRules.update_by,
        ErpClassRenewRules.create_time,
        ErpClassRenewRules.update_time,
        ErpClassRenewRules.run_status,
        ErpClassRenewRules.new_msg,
        CreatedBy.employee_name.label('create_by_name'),
        UpdatedBy.employee_name.label('update_by_name'),
        ErpAccountTeacher.id.label('current_teacher_id'),
        ErpAccount.employee_name.label('current_teacher_name'),
        ErpAccount.avatar.label('current_teacher_avatar'),
        CurrentClass.class_name.label('current_class_name'),
        NextClass.class_name.label('next_class_name'),
        NextClass2.class_name.label('next2_class_name'),
        ErpCourseTerm.term_name,

    ]
    stmt = (
        select(selects)
        .select_from(ErpClassRenewRules)
        .outerjoin(CurrentClass, ErpClassRenewRules.current_class_id == CurrentClass.id)
        .outerjoin(NextClass, ErpClassRenewRules.next_class_id == NextClass.id)
        .outerjoin(NextClass2, ErpClassRenewRules.next2_class_id == NextClass2.id)
        .outerjoin(ErpAccountTeacher, ErpClassRenewRules.current_teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .outerjoin(ErpCourseTerm, ErpClassRenewRules.term_id == ErpCourseTerm.id)
        .outerjoin(CreatedBy, ErpClassRenewRules.create_by == CreatedBy.id)
        .outerjoin(UpdatedBy, ErpClassRenewRules.update_by == UpdatedBy.id)
        .where(ErpClassRenewRules.disable == 0)
        .order_by(ErpClassRenewRules.id.desc())
    )
    result = await db.execute(stmt)
    return result.fetchall()





async def get_current_class(db, stu_id, paid=True):
    """
    查询学生当前所有正常班级， 包含已转入班级
    """
    selects = [
        ErpOrderStudent.class_id,
        ErpOrderStudent.id.label('order_student_id'),
        ErpOrderStudent.self_change_class,
        ErpOrderStudent.student_state,
        ErpClass.class_name,
        ErpClass.planning_class_times,
        ErpClass.start_date,
        ErpClass.pre_enrollment,
        ErpCourse.type_id,
        ErpCourse.grade_id,
        ErpCourse.subject_id,
        ErpCourse.category_id,
        ErpCourse.p_grade_id,
        ErpCourse.original_price,
        ErpCourse.sale_price,
        ErpCourse.number_of_lessons,
        ErpCourseTerm.term_name,
        ErpOfficeClassroom.room_name,
        ErpAccountTeacher.id.label('teacher_id'),
        ErpAccountTeacher.teacher_avatar,
        ErpAccount.id.label('teacher_account_id'),
        ErpAccount.employee_name.label('teacher_name'),
        ErpCourseCategory.category_name,
        ErpOrderStudent.complete_hours,
        ErpOrderStudent.total_hours
    ]
    conditions = [
        ErpOrderStudent.stu_id == stu_id,
        ErpOrderStudent.order_class_type == 1,    # 必须是课程单
        ErpClass.disable == 0,

    ]
    if paid:
        conditions.append(ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value]))
    else:
        conditions.append(ErpOrderStudent.student_state == StudentState.WAIT_PAY.value)
    stmt = (select(*selects)
            .select_from(ErpOrderStudent)
            .outerjoin(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
            .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
            .outerjoin(ErpOfficeClassroom, ErpClass.classroom_id == ErpOfficeClassroom.id)
            .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
            .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
            .outerjoin(ErpCourseCategory, ErpCourse.category_id == ErpCourseCategory.id)
            .outerjoin(ErpCourseTerm, ErpCourse.term_id == ErpCourseTerm.id)
            .where(and_(*conditions))
            .order_by(ErpOrderStudent.create_time.desc())
            )
    
    result = await db.execute(stmt)
    return result.fetchall()




async def get_class_by_class_id(db, class_id):
    """
    根据班级id查询班级信息
    """
    selects = [
        ErpClass.id.label('class_id'),
        ErpClass.class_name,
        ErpClass.planning_class_times,
        ErpClass.start_date,
        ErpClass.classin_id,
        ErpClass.class_capacity,
        ErpCourse.type_id,
        ErpCourse.grade_id,
        ErpCourse.p_grade_id,
        ErpCourse.subject_id,
        ErpCourse.category_id,
        ErpCourse.course_name,
        ErpCourse.term_id,
        ErpOfficeClassroom.room_name,
        ErpAccountTeacher.id.label('teacher_id'),
        ErpAccountTeacher.teacher_avatar,
        ErpAccount.id.label('teacher_account_id'),
        ErpAccount.employee_name.label('teacher_name'),
        ErpCourseCategory.category_name,
        ErpCourseTerm.term_name,
    ]
    conditions = [
        ErpClass.id == class_id,
    ]
    stmt = (select(*selects)
            .select_from(ErpClass)
            .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
            .outerjoin(ErpOfficeClassroom, ErpClass.classroom_id == ErpOfficeClassroom.id)
            .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
            .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
            .outerjoin(ErpCourseCategory, ErpCourse.category_id == ErpCourseCategory.id)
            .outerjoin(ErpCourseTerm, ErpCourse.term_id == ErpCourseTerm.id)
            .where(and_(*conditions)))
    result = await db.execute(stmt)
    return result.fetchone()


async def get_target_class(db, page, page_size, current_class_id, cate_target, subject_target, grade_target):
    """
    获取可转入的目标班级
    """
    selects = [
        ErpClass.id.label('class_id'),
        ErpClass.class_name,
        ErpClass.planning_class_times,
        ErpClass.start_date,
        ErpClass.class_capacity,
        ErpCourse.type_id,
        ErpCourse.grade_id,
        ErpCourse.p_grade_id,
        ErpCourse.subject_id,
        ErpCourse.category_id,
        ErpOfficeClassroom.room_name,
        ErpAccountTeacher.id.label('teacher_id'),
        ErpAccountTeacher.teacher_avatar,
        ErpAccount.id.label('teacher_account_id'),
        ErpAccount.employee_name.label('teacher_name'),
        ErpCourseCategory.category_name,
        ErpCourseTerm.term_name,
    ]
    conditions = [
        ErpCourse.category_id.in_(cate_target),
        ErpCourse.subject_id.in_(subject_target),
        ErpCourse.grade_id.in_(grade_target),
        ErpClass.id != current_class_id,
    ]
    if cate_target:
        conditions.append(ErpCourse.category_id.in_(cate_target))
    if subject_target:
        conditions.append(ErpCourse.subject_id.in_(subject_target))
    if grade_target:
        conditions.append(ErpCourse.grade_id.in_(grade_target))
    stmt = (
        select(*selects)
        .select_from(ErpClass)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpOfficeClassroom, ErpClass.classroom_id == ErpOfficeClassroom.id)
        .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .outerjoin(ErpCourseCategory, ErpCourse.category_id == ErpCourseCategory.id)
        .outerjoin(ErpCourseTerm, ErpCourse.term_id == ErpCourseTerm.id)
        .where(and_(*conditions))
        .order_by(ErpClass.create_time.desc())
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    return result.fetchall()


async def get_valid_class(db, class_id):
    conditions = [
        ErpClassPlan.class_id == class_id,
        ErpClassPlan.finish == 0,
        ErpClassPlan.disable == 0
    ]
    stmt = (
        select([func.count(ErpClassPlan.id)])
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.scalar()


async def get_class_planning_times(db, class_id):
    """
    获取班级计划课节数，直接从erp_class.planning_class_times读取
    """
    stmt = select(ErpClass.planning_class_times).where(ErpClass.id == class_id)
    result = await db.execute(stmt)
    planning_class_times = result.scalar()
    return planning_class_times if planning_class_times is not None else 0


async def get_current_plan_info(db, class_id, stu_id):
    """
    获取学生当前排课信息
    """
    NewClassPlan = aliased(ErpClassPlan, name="NewClassPlan")
    NewClass = aliased(ErpClass, name="NewClass")
    NewClassTeacher = aliased(ErpAccountTeacher, name="NewClassTeacher")
    NewClassAccount = aliased(ErpAccount, name="NewClassAccount")
    NewClassOfficeClassroom = aliased(ErpOfficeClassroom, name="NewClassOfficeClassroom")

    selects = [
        ErpOrderStudent.stu_id,
        ErpOrderStudent.class_id,
        ErpClass.class_name,
        ErpClass.course_id,
        ErpClassPlan.id.label('plan_id'),
        ErpClassPlan.start_time,
        ErpClassPlan.end_time,
        ErpClassPlan.finish,

        ErpOfficeClassroom.room_name,

        ErpAccountTeacher.id.label('teacher_id'),
        ErpAccount.id.label('teacher_account_id'),
        ErpAccount.employee_name.label('teacher_name'),

        ErpClassRescheduling.id.label('reschedule_id'),
        ErpClassRescheduling.new_plan_id,
        ErpClassRescheduling.old_plan_id,
        ErpClassRescheduling.reschedule_reason,
        ErpClassRescheduling.is_inner,

        NewClass.id.label('new_class_id'),
        NewClass.class_name.label('new_class_name'),
        NewClassPlan.id.label('new_plan_id'),
        NewClassPlan.start_time.label('new_start_time'),
        NewClassPlan.end_time.label('new_end_time'),
        NewClassPlan.finish.label('new_finish'),
        NewClassOfficeClassroom.room_name.label('new_room_name'),
        NewClassTeacher.id.label('new_teacher_id'),
        NewClassAccount.id.label('new_teacher_account_id'),
        NewClassAccount.employee_name.label('new_teacher_name'),
        NewClassAccount.avatar.label('new_teacher_avatar'),

    ]
    conditions = [
        ErpOrderStudent.stu_id == stu_id,
        ErpOrderStudent.class_id == class_id,
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value]),
    ]
    stmt = (select(*selects)
            .select_from(ErpOrderStudent)
            .outerjoin(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
            .outerjoin(ErpOfficeClassroom, ErpClass.classroom_id==ErpOfficeClassroom.id)
            .outerjoin(ErpClassPlan, ErpClass.id == ErpClassPlan.class_id)
            .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
            .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
            .outerjoin(ErpClassRescheduling, and_(ErpClassPlan.id == ErpClassRescheduling.old_plan_id, ErpOrderStudent.id == ErpClassRescheduling.order_student_id, ErpClassRescheduling.disable == 0))  # 当前排课中是否有调出信息
            .outerjoin(NewClassPlan, and_(NewClassPlan.id == ErpClassRescheduling.new_plan_id, ErpClassRescheduling.disable == 0))
            .outerjoin(NewClass, and_(NewClass.id == NewClassPlan.class_id))
            .outerjoin(NewClassTeacher, NewClassPlan.teacher_id == NewClassTeacher.id)
            .outerjoin(NewClassAccount, NewClassTeacher.account_id == NewClassAccount.id)
            .outerjoin(NewClassOfficeClassroom, NewClassPlan.room_id == NewClassOfficeClassroom.id)
            .where(and_(*conditions))
            .order_by(ErpClassPlan.start_time)
            )
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()


async def get_target_class_plan(db, class_id, stu_id):
    """
    获取目标班级的排课
    """
    selects = [
        # ErpOrderStudent.stu_id,
        # ErpOrderStudent.class_id,
        ErpClass.id.label('class_id'),
        ErpClass.class_name,
        ErpClassPlan.id.label('plan_id'),
        ErpClassPlan.start_time,
        ErpClassPlan.end_time,
        ErpClassPlan.finish,

        ErpOfficeClassroom.room_name,

        ErpAccountTeacher.id.label('teacher_id'),
        ErpAccount.id.label('teacher_account_id'),
        ErpAccount.employee_name.label('teacher_name'),

        ErpClassRescheduling.id.label('reschedule_id'),
        ErpClassRescheduling.new_plan_id,
        ErpClassRescheduling.old_plan_id,
        ErpClassRescheduling.reschedule_reason,
        ErpClassRescheduling.is_inner,

    ]
    conditions = [
        ErpClass.id == class_id,
    ]
    stmt = (select(*selects)
            .select_from(ErpClass)
            .outerjoin(ErpOfficeClassroom, ErpClass.classroom_id==ErpOfficeClassroom.id)
            .outerjoin(ErpClassPlan, ErpClass.id == ErpClassPlan.class_id)
            .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
            .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
            # 获取学生当前是否有调课记录
            .outerjoin(ErpOrderStudent, and_(ErpClass.id == ErpOrderStudent.class_id, ErpOrderStudent.stu_id == stu_id))
            .outerjoin(ErpClassRescheduling, and_(ErpClassPlan.id == ErpClassRescheduling.new_plan_id, ErpOrderStudent.id == ErpClassRescheduling.order_student_id, ErpClassRescheduling.disable == 0))  # 当前排课中是否有调入信息
            .where(and_(*conditions))
            .order_by(ErpClassPlan.start_time)
            )
    # compile_sql = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_sql)
    result = await db.execute(stmt)
    return result.fetchall()



async def get_class_plan_by_id(db, class_plan_id):
    selects = [
        ErpClassPlan.id.label('plan_id'),
        ErpClassPlan.class_id,
        ErpClassPlan.room_id,
        ErpClassPlan.teacher_id,
        ErpClassPlan.start_time,
        ErpClassPlan.end_time,
        ErpClassPlan.finish,
        ErpClassPlan.create_time,
        ErpClassPlan.update_time,
        ErpClassPlan.disable,
        ErpClassPlan.time_duration,
        ErpClassPlan.classin_id,
        ErpOfficeClassroom.room_name,
        ErpOfficeCenter.center_name,
        ErpAccount.employee_name.label('teacher_name'),
        ErpClass.class_name,
        ErpClass.class_capacity,
        ErpClass.pre_enrollment,
        ErpClass.planning_class_times,
        ErpClass.scheduling_method,
        ErpClass.miniprogram_start_enrollment_time,
    ]
    conditions = [
        ErpClassPlan.id == class_plan_id,
        ErpClassPlan.disable == 0,
    ]
    stmt = (
        select(*selects)
        .select_from(ErpClassPlan)
        .outerjoin(ErpOfficeClassroom, ErpClassPlan.room_id == ErpOfficeClassroom.id)
        .outerjoin(ErpOfficeCenter, ErpOfficeClassroom.center_id == ErpOfficeCenter.id)
        .outerjoin(ErpAccountTeacher, ErpClassPlan.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .outerjoin(ErpClass, ErpClassPlan.class_id == ErpClass.id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchone()


async def get_un_end_class_modules(db, stu_id):
    """
    获取学生未结课班级
    """
    # 查询学生未结课班级信息
    selects = [
        ErpStudent.id.label('stu_id'),
        ErpStudent.stu_name,
        ErpStudent.stu_username,
        ErpOrderStudent.class_id,
        ErpClass.class_name,
        ErpOrderStudent.complete_hours,
        ErpOrderStudent.total_hours,
        ErpOrderStudent.id.label('order_student_id'),
    ]
    
    conditions = [
        ErpOrderStudent.stu_id == stu_id,
        ErpOrderStudent.disable == 0,
        ErpOrderStudent.order_class_type == 1,  # 必须是课程单
        ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value]),  # 正常和转入状态
        ErpClass.class_status != ClassStatus.Closed.value,  # 班级状态不等于3（未结课）
        ErpClass.disable == 0,
    ]
    
    stmt = (
        select(*selects)
        .select_from(ErpOrderStudent)
        .join(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
        .join(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .where(and_(*conditions))
        .order_by(ErpOrderStudent.create_time.desc())
    )
    
    result = await db.execute(stmt)
    return result.fetchall()


async def get_student_list_with_conditions(db, page, page_size, course_id=None, class_id=None, stu_name=None, stu_username=None, start_time=None, end_time=None, student_state=None):
    """
    根据条件查询学员名单
    """
    from models.m_office import ErpOfficeClassroom, ErpOfficeCenter
    from models.m_teacher import ErpAccountTeacher
    from models.models import ErpAccount
    from models.m_class import ErpCourse
    from datetime import datetime as dt
    
    # 构建查询条件
    conditions = [
        ErpOrderStudent.disable == 0,
    ]
    
    # 课程ID过滤
    if course_id:
        conditions.append(ErpCourse.id == course_id)
    
    # 班级ID过滤
    if class_id:
        conditions.append(ErpOrderStudent.class_id == class_id)
    
    # 学生姓名模糊搜索
    if stu_name:
        conditions.append(ErpStudent.stu_name.like(f'%{stu_name}%'))
    
    # 手机号精确搜索
    if stu_username:
        conditions.append(ErpStudent.stu_username == stu_username)
    
    # 时间范围过滤
    if start_time:
        try:
            start_dt = dt.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            conditions.append(ErpOrderStudent.create_time >= start_dt)
        except ValueError:
            try:
                start_dt = dt.strptime(start_time, '%Y-%m-%d')
                conditions.append(ErpOrderStudent.create_time >= start_dt)
            except ValueError:
                pass  # 忽略格式错误
    
    if end_time:
        try:
            end_dt = dt.strptime(end_time, '%Y-%m-%d %H:%M:%S')
            conditions.append(ErpOrderStudent.create_time <= end_dt)
        except ValueError:
            try:
                end_dt = dt.strptime(end_time, '%Y-%m-%d')
                conditions.append(ErpOrderStudent.create_time <= end_dt)
            except ValueError:
                pass  # 忽略格式错误
    
    # 学生状态过滤
    if student_state is not None:
        conditions.append(ErpOrderStudent.student_state == student_state)
    
    # 构建查询字段
    selects = [
        ErpOrderStudent.id.label('order_student_id'),
        ErpOrderStudent.class_id,
        ErpOrderStudent.stu_id,
        ErpOrderStudent.student_state,
        ErpOrderStudent.total_hours,
        ErpOrderStudent.complete_hours,
        ErpOrderStudent.create_time.label('enrollment_time'),
        ErpOrderStudent.p_id.label('parent_order_id'),  # 用于判断是否转班
        
        # 学生信息
        ErpStudent.stu_name,
        ErpStudent.stu_username,
        
        # 班级信息
        ErpClass.class_name,
        ErpClass.teacher_id,
        ErpClass.classroom_id,
        
        # 课程信息
        ErpCourse.id.label('course_id'),
        ErpCourse.course_name,
        
        # 教师信息
        ErpAccount.employee_name.label('teacher_name'),
        ErpAccount.avatar.label('teacher_avatar'),
        
        # 教室信息
        ErpOfficeClassroom.room_name.label('classroom_name'),
        ErpOfficeCenter.center_name.label('center_name'),
    ]
    
    # 构建主查询
    stmt = (
        select(*selects)
        .select_from(ErpOrderStudent)
        .outerjoin(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
        .outerjoin(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .outerjoin(ErpOfficeClassroom, ErpClass.classroom_id == ErpOfficeClassroom.id)
        .outerjoin(ErpOfficeCenter, ErpOfficeClassroom.center_id == ErpOfficeCenter.id)
        .where(and_(*conditions))
        .order_by(ErpOrderStudent.create_time.desc())
    )
    
    # 获取总数
    count_stmt = (
        select(func.count(ErpOrderStudent.id))
        .select_from(ErpOrderStudent)
        .outerjoin(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
        .outerjoin(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(and_(*conditions))
    )
    
    count_result = await db.execute(count_stmt)
    total_count = count_result.scalar()
    
    # 分页查询
    paginated_stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(paginated_stmt)
    student_data = result.fetchall()
    
    return student_data, total_count


async def get_order_data(db, order_student_ids):
    """
    获取订单信息
    """
    order_stmt = (
        select(
            ErpOrder.order_student_id,
            func.sum(ErpOrder.total_receivable).label('total_receivable'),
            func.sum(ErpOrder.total_income).label('total_income'),
            func.sum(ErpOrder.refund).label('total_refund')
        )
        .where(
            and_(
                ErpOrder.order_student_id.in_(order_student_ids),
                ErpOrder.disable == 0
            )
        )
        .group_by(ErpOrder.order_student_id)
    )
    
    result = await db.execute(order_stmt)
    return {row.order_student_id: row for row in result.fetchall()}


async def get_refund_details(db, order_student_ids):
    """
    获取退款详情
    """
    from models.m_order import ErpOrderRefundDetail
    
    refund_stmt = (
        select(
            ErpOrderRefundDetail.order_student_id,
            ErpOrderRefundDetail.refund_money,
            ErpOrderRefundDetail.refund_state,
            ErpOrderRefundDetail.create_time.label('refund_time'),
            ErpOrderRefundDetail.refund_type
        )
        .where(
            and_(
                ErpOrderRefundDetail.order_student_id.in_(order_student_ids),
                ErpOrderRefundDetail.disable == 0
            )
        )
        .order_by(ErpOrderRefundDetail.create_time.desc())
    )
    
    result = await db.execute(refund_stmt)
    refund_data = {}
    for row in result.fetchall():
        if row.order_student_id not in refund_data:
            refund_data[row.order_student_id] = []
        refund_data[row.order_student_id].append({
            'refund_money': float(row.refund_money) if row.refund_money else 0.0,
            'refund_state': row.refund_state,
            'refund_time': row.refund_time.strftime('%Y-%m-%d %H:%M:%S') if row.refund_time else None,
            'refund_type': row.refund_type
        })
    
    return refund_data


async def get_transfer_info(db, order_student_ids):
    """
    获取转班信息
    """
    transfer_stmt = (
        select(
            ErpClassTransfor.new_order_student_id,
            ErpClassTransfor.old_order_student_id,
            ErpClassTransfor.transfor_reason,
            ErpClassTransfor.create_time.label('transfer_time')
        )
        .where(
            and_(
                or_(
                    ErpClassTransfor.new_order_student_id.in_(order_student_ids),
                    ErpClassTransfor.old_order_student_id.in_(order_student_ids)
                ),
                ErpClassTransfor.disable == 0
            )
        )
    )
    
    result = await db.execute(transfer_stmt)
    transfer_data = {}
    for row in result.fetchall():
        # 新订单为转入
        if row.new_order_student_id in order_student_ids:
            transfer_data[row.new_order_student_id] = {
                'type': 'transfer_in',
                'reason': row.transfor_reason,
                'transfer_time': row.transfer_time.strftime('%Y-%m-%d %H:%M:%S') if row.transfer_time else None
            }
        # 旧订单为转出
        if row.old_order_student_id in order_student_ids:
            transfer_data[row.old_order_student_id] = {
                'type': 'transfer_out',
                'reason': row.transfor_reason,
                'transfer_time': row.transfer_time.strftime('%Y-%m-%d %H:%M:%S') if row.transfer_time else None
            }
    
    return transfer_data
    
async def get_order_financial_data(db, order_student_ids):
    """
    获取订单财务信息
    """
    order_stmt = (
        select(
            ErpOrder.order_student_id,
            func.sum(ErpOrder.total_receivable).label('total_receivable'),
            func.sum(ErpOrder.total_income).label('total_income'),
            func.sum(ErpOrder.refund).label('total_refund')
        )
        .where(
            and_(
                ErpOrder.order_student_id.in_(order_student_ids),
                ErpOrder.disable == 0
            )
        )
        .group_by(ErpOrder.order_student_id)
    )
    
    result = await db.execute(order_stmt)
    return {row.order_student_id: row for row in result.fetchall()}


async def get_order_data(db, order_student_ids):
    """
    获取订单详细列表
    """
    order_stmt = (
        select(
            ErpOrder.id.label('order_id'),
            ErpOrder.order_student_id,
            ErpOrder.total_receivable,
            ErpOrder.total_income,
            ErpOrder.refund,
            ErpOrder.discount,
            ErpOrder.order_state,
            ErpOrder.trade_way,
            ErpOrder.join_type,
            ErpOrder.create_time.label('order_create_time'),
            ErpOrder.lesson_price,
            ErpOrder.class_price,
            ErpOrder.buy_num,
            ErpOrder.unit
        )
        .where(
            and_(
                ErpOrder.order_student_id.in_(order_student_ids),
                ErpOrder.disable == 0
            )
        )
        .order_by(ErpOrder.create_time.desc())
    )
    
    result = await db.execute(order_stmt)
    order_data = {}
    for row in result.fetchall():
        if row.order_student_id not in order_data:
            order_data[row.order_student_id] = []
        order_data[row.order_student_id].append({
            'order_id': row.order_id,
            'total_receivable': float(row.total_receivable) if row.total_receivable else 0.0,
            'total_income': float(row.total_income) if row.total_income else 0.0,
            'refund': float(row.refund) if row.refund else 0.0,
            'discount': float(row.discount) if row.discount else 0.0,
            'order_state': row.order_state,
            'trade_way': row.trade_way,
            'join_type': row.join_type,
            'order_create_time': row.order_create_time.strftime('%Y-%m-%d %H:%M:%S') if row.order_create_time else None,
            'lesson_price': float(row.lesson_price) if row.lesson_price else 0.0,
            'class_price': float(row.class_price) if row.class_price else 0.0,
            'buy_num': row.buy_num,
            'unit': row.unit
        })
    
    return order_data
    
# 优化版本的学员名单查询 - 性能优化
async def get_student_list_optimized(db, page, page_size, course_id=None, class_id=None, stu_name=None, stu_username=None, start_time=None, end_time=None, student_state=None):
    """
    优化版本的学员名单查询 - 一次查询获取所有数据
    性能优化：将多个查询合并为一个复杂查询，减少数据库往返次数
    """
    from models.m_office import ErpOfficeClassroom, ErpOfficeCenter
    from models.m_teacher import ErpAccountTeacher
    from models.models import ErpAccount
    from models.m_class import ErpCourse
    from models.m_order import ErpOrderRefundDetail
    from datetime import datetime as dt
    from sqlalchemy import case
    
    # 构建查询条件
    conditions = [
        ErpOrderStudent.disable == 0,
    ]
    
    # 课程ID过滤
    if course_id:
        conditions.append(ErpCourse.id == course_id)
    
    # 班级ID过滤
    if class_id:
        conditions.append(ErpOrderStudent.class_id == class_id)
    
    # 学生姓名模糊搜索
    if stu_name:
        conditions.append(ErpStudent.stu_name.like(f'%{stu_name}%'))
    
    # 手机号精确搜索
    if stu_username:
        conditions.append(ErpStudent.stu_username == stu_username)
    
    # 时间范围过滤
    if start_time:
        try:
            start_dt = dt.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            conditions.append(ErpOrderStudent.create_time >= start_dt)
        except ValueError:
            try:
                start_dt = dt.strptime(start_time, '%Y-%m-%d')
                conditions.append(ErpOrderStudent.create_time >= start_dt)
            except ValueError:
                pass
    
    if end_time:
        try:
            end_dt = dt.strptime(end_time, '%Y-%m-%d %H:%M:%S')
            conditions.append(ErpOrderStudent.create_time <= end_dt)
        except ValueError:
            try:
                end_dt = dt.strptime(end_time, '%Y-%m-%d')
                conditions.append(ErpOrderStudent.create_time <= end_dt)
            except ValueError:
                pass
    
    # 学生状态过滤
    if student_state is not None:
        conditions.append(ErpOrderStudent.student_state == student_state)
    
    # 构建主查询 - 一次性获取所有需要的数据
    selects = [
        # 基础学员信息
        ErpOrderStudent.id.label('order_student_id'),
        ErpOrderStudent.class_id,
        ErpOrderStudent.stu_id,
        ErpOrderStudent.student_state,
        ErpOrderStudent.total_hours,
        ErpOrderStudent.complete_hours,
        ErpOrderStudent.create_time.label('enrollment_time'),
        ErpOrderStudent.p_id.label('parent_order_id'),
        
        # 学生信息
        ErpStudent.stu_name,
        ErpStudent.stu_username,
        
        # 班级信息
        ErpClass.class_name,
        ErpClass.teacher_id,
        ErpClass.classroom_id,
        
        # 课程信息
        ErpCourse.id.label('course_id'),
        ErpCourse.course_name,
        
        # 教师信息
        ErpAccount.employee_name.label('teacher_name'),
        ErpAccount.avatar.label('teacher_avatar'),
        
        # 教室信息
        ErpOfficeClassroom.room_name.label('classroom_name'),
        ErpOfficeCenter.center_name.label('center_name'),
        
        # 财务汇总信息 - 使用子查询
        (
            select(func.coalesce(func.sum(ErpOrder.total_receivable), 0))
            .where(
                and_(
                    ErpOrder.order_student_id == ErpOrderStudent.id,
                    ErpOrder.disable == 0
                )
            )
            .scalar_subquery()
        ).label('total_receivable'),
        
        (
            select(func.coalesce(func.sum(ErpOrder.total_income), 0))
            .where(
                and_(
                    ErpOrder.order_student_id == ErpOrderStudent.id,
                    ErpOrder.disable == 0
                )
            )
            .scalar_subquery()
        ).label('total_income'),
        
        (
            select(func.coalesce(func.sum(ErpOrder.refund), 0))
            .where(
                and_(
                    ErpOrder.order_student_id == ErpOrderStudent.id,
                    ErpOrder.disable == 0
                )
            )
            .scalar_subquery()
        ).label('total_refund'),
        
        # 转班信息检查
        (
            select(func.count(ErpClassTransfor.id))
            .where(
                and_(
                    ErpClassTransfor.new_order_student_id == ErpOrderStudent.id,
                    ErpClassTransfor.disable == 0
                )
            )
            .scalar_subquery()
        ).label('is_transfer_in'),
        
        (
            select(func.count(ErpClassTransfor.id))
            .where(
                and_(
                    ErpClassTransfor.old_order_student_id == ErpOrderStudent.id,
                    ErpClassTransfor.disable == 0
                )
            )
            .scalar_subquery()
        ).label('is_transfer_out'),
    ]
    
    # 构建主查询
    stmt = (
        select(*selects)
        .select_from(ErpOrderStudent)
        .outerjoin(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
        .outerjoin(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .outerjoin(ErpAccountTeacher, ErpClass.teacher_id == ErpAccountTeacher.id)
        .outerjoin(ErpAccount, ErpAccountTeacher.account_id == ErpAccount.id)
        .outerjoin(ErpOfficeClassroom, ErpClass.classroom_id == ErpOfficeClassroom.id)
        .outerjoin(ErpOfficeCenter, ErpOfficeClassroom.center_id == ErpOfficeCenter.id)
        .where(and_(*conditions))
        .order_by(ErpOrderStudent.create_time.desc())
    )
    
    # 获取总数
    count_stmt = (
        select(func.count(ErpOrderStudent.id))
        .select_from(ErpOrderStudent)
        .outerjoin(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
        .outerjoin(ErpClass, ErpOrderStudent.class_id == ErpClass.id)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(and_(*conditions))
    )
    
    count_result = await db.execute(count_stmt)
    total_count = count_result.scalar()
    
    # 分页查询
    paginated_stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(paginated_stmt)
    student_data = result.fetchall()
    
    return student_data, total_count


async def get_additional_data_optimized(db, order_student_ids):
    """
    优化版本：一次查询获取退款详情和订单详情
    """
    from models.m_order import ErpOrderRefundDetail
    from sqlalchemy import case
    
    # 一次性获取退款详情和订单详情
    additional_data_stmt = (
        select(
            # 退款详情
            ErpOrderRefundDetail.order_student_id,
            ErpOrderRefundDetail.refund_money,
            ErpOrderRefundDetail.refund_state,
            ErpOrderRefundDetail.create_time.label('refund_time'),
            ErpOrderRefundDetail.refund_type,
            
            # 订单详情
            ErpOrder.id.label('order_id'),
            ErpOrder.total_receivable.label('order_total_receivable'),
            ErpOrder.total_income.label('order_total_income'),
            ErpOrder.refund.label('order_refund'),
            ErpOrder.discount,
            ErpOrder.order_state,
            ErpOrder.trade_way,
            ErpOrder.join_type,
            ErpOrder.create_time.label('order_create_time'),
            ErpOrder.lesson_price,
            ErpOrder.class_price,
            ErpOrder.buy_num,
            ErpOrder.unit,
            
            # 转班详细信息
            ErpClassTransfor.transfor_reason,
            ErpClassTransfor.create_time.label('transfer_time'),
            case(
                (ErpClassTransfor.new_order_student_id.in_(order_student_ids), 'transfer_in'),
                (ErpClassTransfor.old_order_student_id.in_(order_student_ids), 'transfer_out'),
                else_=None
            ).label('transfer_type')
        )
        .select_from(ErpOrderStudent)
        .outerjoin(
            ErpOrderRefundDetail,
            and_(
                ErpOrderRefundDetail.order_student_id == ErpOrderStudent.id,
                ErpOrderRefundDetail.disable == 0
            )
        )
        .outerjoin(
            ErpOrder,
            and_(
                ErpOrder.order_student_id == ErpOrderStudent.id,
                ErpOrder.disable == 0
            )
        )
        .outerjoin(
            ErpClassTransfor,
            and_(
                or_(
                    ErpClassTransfor.new_order_student_id == ErpOrderStudent.id,
                    ErpClassTransfor.old_order_student_id == ErpOrderStudent.id
                ),
                ErpClassTransfor.disable == 0
            )
        )
        .where(ErpOrderStudent.id.in_(order_student_ids))
        .order_by(
            ErpOrderRefundDetail.create_time.desc(),
            ErpOrder.create_time.desc()
        )
    )
    
    result = await db.execute(additional_data_stmt)
    rows = result.fetchall()
    
    # 组织数据
    refund_data = {}
    order_data = {}
    transfer_data = {}
    
    for row in rows:
        order_student_id = row.order_student_id
        
        # 处理退款数据
        if row.refund_money is not None:
            if order_student_id not in refund_data:
                refund_data[order_student_id] = []
            refund_data[order_student_id].append({
                'refund_money': float(row.refund_money) if row.refund_money else 0.0,
                'refund_state': row.refund_state,
                'refund_time': row.refund_time.strftime('%Y-%m-%d %H:%M:%S') if row.refund_time else None,
                'refund_type': row.refund_type
            })
        
        # 处理订单数据
        if row.order_id is not None:
            if order_student_id not in order_data:
                order_data[order_student_id] = []
            order_data[order_student_id].append({
                'order_id': row.order_id,
                'total_receivable': float(row.order_total_receivable) if row.order_total_receivable else 0.0,
                'total_income': float(row.order_total_income) if row.order_total_income else 0.0,
                'refund': float(row.order_refund) if row.order_refund else 0.0,
                'discount': float(row.discount) if row.discount else 0.0,
                'order_state': row.order_state,
                'trade_way': row.trade_way,
                'join_type': row.join_type,
                'order_create_time': row.order_create_time.strftime('%Y-%m-%d %H:%M:%S') if row.order_create_time else None,
                'lesson_price': float(row.lesson_price) if row.lesson_price else 0.0,
                'class_price': float(row.class_price) if row.class_price else 0.0,
                'buy_num': row.buy_num,
                'unit': row.unit
            })
        
        # 处理转班数据
        if row.transfer_type is not None and order_student_id not in transfer_data:
            transfer_data[order_student_id] = {
                'type': row.transfer_type,
                'reason': row.transfor_reason,
                'transfer_time': row.transfer_time.strftime('%Y-%m-%d %H:%M:%S') if row.transfer_time else None
            }
    
    return refund_data, order_data, transfer_data


async def process_student_list_data_optimized(student_data, refund_data, order_data, transfer_data):
    """
    优化版本的数据处理函数
    """
    result_data = []
    for row in student_data:
        # 获取退款信息
        refunds = refund_data.get(row.order_student_id, [])
        
        # 获取转班信息
        transfer_info = transfer_data.get(row.order_student_id)
        
        # 获取订单列表
        orders = order_data.get(row.order_student_id, [])
        
        # 判断类型（转班/正常报入）
        if row.parent_order_id and row.parent_order_id > 0:
            join_type = "转班报入"
        elif row.is_transfer_in > 0:
            join_type = "转班报入"
        elif row.is_transfer_out > 0:
            join_type = "转班转出"
        else:
            join_type = "正常报入"
        
        student_info = {
            'stu_id': row.stu_id,
            'order_student_id': row.order_student_id,
            'stu_name': row.stu_name,
            'stu_username': row.stu_username,  # 手机号
            'class_id': row.class_id,
            'class_name': row.class_name,
            'course_id': row.course_id,
            'course_name': row.course_name,
            'teacher_name': row.teacher_name,
            'teacher_avatar': row.teacher_avatar,
            'classroom_name': row.classroom_name,
            'center_name': row.center_name,
            'enrollment_time': row.enrollment_time.strftime('%Y-%m-%d %H:%M:%S') if row.enrollment_time else None,
            'total_receivable': float(row.total_receivable) if row.total_receivable else 0.0,
            'total_income': float(row.total_income) if row.total_income else 0.0,
            'total_refund': float(row.total_refund) if row.total_refund else 0.0,
            'refund_details': refunds,
            'order_list': orders,
            'complete_hours': float(row.complete_hours) if row.complete_hours else 0.0,
            'total_hours': float(row.total_hours) if row.total_hours else 0.0,
            'student_state': row.student_state,
            'join_type': join_type,
            'transfer_info': transfer_info
        }
        result_data.append(student_info)
    
    return result_data
    

# 查询调课记录
async def get_reschedule_record_list(db, page, page_size, stu_name=None, class_name=None):
    """
    查询调课记录 分页
    关联表： erp_class_rescheduling
    返回字段：学生姓名，电话，调课原因，原班级id和名称，原课节start_time和end_time,授课教师，现班级id和名称，现课节start_time和end_time,授课教师，创建时间，操作人，是否删除，更新人，课节序号
    """
    from sqlalchemy import text
    
    # 为多次使用的表创建别名
    OldClassPlan = aliased(ErpClassPlan)
    NewClassPlan = aliased(ErpClassPlan)
    OldClass = aliased(ErpClass)
    NewClass = aliased(ErpClass)
    OldTeacher = aliased(ErpAccountTeacher)
    NewTeacher = aliased(ErpAccountTeacher)
    OldTeacherAccount = aliased(ErpAccount)
    NewTeacherAccount = aliased(ErpAccount)
    CreateByAccount = aliased(ErpAccount)
    UpdateByAccount = aliased(ErpAccount)
    
    # 为了兼容 MySQL 5.7，我们需要在应用层计算序号
    # 先获取所有相关的课节信息，然后在Python中计算序号
    
    # 构建查询字段
    selects = [
        ErpClassRescheduling.id.label('reschedule_id'),
        ErpClassRescheduling.order_student_id,
        ErpClassRescheduling.old_plan_id,
        ErpClassRescheduling.new_plan_id,
        ErpClassRescheduling.reschedule_reason,
        ErpClassRescheduling.create_time,
        ErpClassRescheduling.update_time,
        ErpClassRescheduling.disable,
        ErpClassRescheduling.is_inner,
        
        # 学生信息
        ErpStudent.id.label('stu_id'),
        ErpStudent.stu_name,
        ErpStudent.stu_username.label('stu_phone'),
        
        # 原班级信息
        OldClass.id.label('old_class_id'),
        OldClass.class_name.label('old_class_name'),
        
        # 新班级信息
        NewClass.id.label('new_class_id'),
        NewClass.class_name.label('new_class_name'),
        
        # 原课节信息
        OldClassPlan.start_time.label('old_start_time'),
        OldClassPlan.end_time.label('old_end_time'),
        
        # 新课节信息
        NewClassPlan.start_time.label('new_start_time'),
        NewClassPlan.end_time.label('new_end_time'),
        
        # 原授课教师
        OldTeacherAccount.employee_name.label('old_teacher_name'),
        
        # 新授课教师
        NewTeacherAccount.employee_name.label('new_teacher_name'),
        
        # 操作人
        CreateByAccount.employee_name.label('create_by_name'),
        
        # 更新人
        UpdateByAccount.employee_name.label('update_by_name'),
    ]
    
    # 构建查询条件
    conditions = [
        ErpClassRescheduling.disable == 0,
    ]
    if stu_name:
        conditions.append(ErpStudent.stu_name.like(f'%{stu_name}%'))
    if class_name:
        conditions.append(ErpClass.class_name.like(f'%{class_name}%'))
    
    # 构建主查询
    stmt = (
        select(*selects)
        .select_from(ErpClassRescheduling)
        .outerjoin(ErpOrderStudent, ErpClassRescheduling.order_student_id == ErpOrderStudent.id)
        .outerjoin(ErpStudent, ErpOrderStudent.stu_id == ErpStudent.id)
        .outerjoin(OldClassPlan, ErpClassRescheduling.old_plan_id == OldClassPlan.id)
        .outerjoin(NewClassPlan, ErpClassRescheduling.new_plan_id == NewClassPlan.id)
        .outerjoin(OldClass, OldClassPlan.class_id == OldClass.id)
        .outerjoin(NewClass, NewClassPlan.class_id == NewClass.id)
        .outerjoin(OldTeacher, OldClass.teacher_id == OldTeacher.id)
        .outerjoin(NewTeacher, NewClass.teacher_id == NewTeacher.id)
        .outerjoin(OldTeacherAccount, OldTeacher.account_id == OldTeacherAccount.id)
        .outerjoin(NewTeacherAccount, NewTeacher.account_id == NewTeacherAccount.id)
        .outerjoin(CreateByAccount, ErpClassRescheduling.create_by == CreateByAccount.id)
        .outerjoin(UpdateByAccount, ErpClassRescheduling.update_by == UpdateByAccount.id)
        .where(and_(*conditions))
        .order_by(ErpClassRescheduling.create_time.desc())
    )
    
    # 获取总数
    count_stmt = (
        select(func.count(ErpClassRescheduling.id))
        .select_from(ErpClassRescheduling)
        .where(and_(*conditions))
    )
    
    count_result = await db.execute(count_stmt)
    total_count = count_result.scalar()
    
    # 分页查询
    paginated_stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(paginated_stmt)
    reschedule_data = result.fetchall()
    
    # 为了兼容 MySQL 5.7，在应用层计算课节序号
    if reschedule_data:
        # 获取所有涉及的班级ID
        class_ids = set()
        for row in reschedule_data:
            if hasattr(row, 'old_class_id') and row.old_class_id:
                class_ids.add(row.old_class_id)
            if hasattr(row, 'new_class_id') and row.new_class_id:
                class_ids.add(row.new_class_id)
        
        # 获取所有班级的课节信息并按ID排序
        class_plan_index_map = {}
        if class_ids:
            class_plan_query = (
                select(ErpClassPlan.id, ErpClassPlan.class_id)
                .where(and_(
                    ErpClassPlan.class_id.in_(list(class_ids)),
                    ErpClassPlan.disable == 0
                ))
                .order_by(ErpClassPlan.class_id, ErpClassPlan.id)
            )
            class_plan_result = await db.execute(class_plan_query)
            class_plans = class_plan_result.fetchall()
            
            # 为每个班级的课节生成序号
            current_class_id = None
            current_index = 0
            for plan in class_plans:
                if plan.class_id != current_class_id:
                    current_class_id = plan.class_id
                    current_index = 1
                else:
                    current_index += 1
                class_plan_index_map[plan.id] = current_index
        
        # 为每条调课记录添加课节序号
        enhanced_reschedule_data = []
        for row in reschedule_data:
            # 将 Row 对象转换为字典
            row_dict = dict(row._mapping)
            
            # 添加课节序号
            row_dict['old_class_plan_index'] = class_plan_index_map.get(row.old_plan_id, None)
            row_dict['new_class_plan_index'] = class_plan_index_map.get(row.new_plan_id, None)
            
            # 创建一个新的对象来保持兼容性
            from types import SimpleNamespace
            enhanced_row = SimpleNamespace(**row_dict)
            enhanced_reschedule_data.append(enhanced_row)
        
        return enhanced_reschedule_data, total_count
    
    return reschedule_data, total_count


# 查询转班记录
async def get_transfer_record_list(db, page, page_size, stu_name=None, class_name=None):
    """
    查询转班记录 分页
    关联表：erp_class_transfor
    返回字段：学生姓名，电话，转班原因，原班级id和名称，old_erp_order_student_id，现班级id和名称，现erp_order_student_id，创建时间，操作人，更新人
    """
    # 为多次使用的表创建别名
    OldOrderStudent = aliased(ErpOrderStudent)
    NewOrderStudent = aliased(ErpOrderStudent)
    OldClass = aliased(ErpClass)
    NewClass = aliased(ErpClass)
    CreateByAccount = aliased(ErpAccount)
    UpdateByAccount = aliased(ErpAccount)
    OldTeacher = aliased(ErpAccountTeacher)
    NewTeacher = aliased(ErpAccountTeacher)
    OldTeacherAccount = aliased(ErpAccount)
    NewTeacherAccount = aliased(ErpAccount)
    
    
    # 构建查询字段
    selects = [
        ErpClassTransfor.id.label('transfer_id'),
        ErpClassTransfor.old_order_student_id,
        ErpClassTransfor.new_order_student_id,
        ErpClassTransfor.old_class_id,
        ErpClassTransfor.new_class_id,
        ErpClassTransfor.transfor_reason,
        ErpClassTransfor.transfor_num,
        ErpClassTransfor.create_time,
        ErpClassTransfor.update_time,
        ErpClassTransfor.is_inner,
        
        # 学生信息（从新订单获取，因为是同一个学生）
        ErpStudent.stu_name,
        ErpStudent.stu_username.label('stu_phone'),
        
        # 原班级信息
        OldClass.class_name.label('old_class_name'),
        
        # 新班级信息
        NewClass.class_name.label('new_class_name'),

         # 原授课教师
        OldTeacherAccount.employee_name.label('old_teacher_name'),
        
        # 新授课教师
        NewTeacherAccount.employee_name.label('new_teacher_name'),
        
        # 操作人
        CreateByAccount.employee_name.label('create_by_name'),
        
        # 更新人
        UpdateByAccount.employee_name.label('update_by_name'),
    ]
    
    # 构建查询条件
    conditions = [
        ErpClassTransfor.disable == 0,
    ]
    if stu_name:
        conditions.append(ErpStudent.stu_name.like(f'%{stu_name}%'))
    if class_name:
        conditions.append(ErpClass.class_name.like(f'%{class_name}%'))
    
    # 构建主查询
    stmt = (
        select(*selects)
        .select_from(ErpClassTransfor)
        .outerjoin(OldOrderStudent, ErpClassTransfor.old_order_student_id == OldOrderStudent.id)
        .outerjoin(NewOrderStudent, ErpClassTransfor.new_order_student_id == NewOrderStudent.id)
        .outerjoin(ErpStudent, NewOrderStudent.stu_id == ErpStudent.id)  # 使用新订单获取学生信息
        .outerjoin(OldClass, ErpClassTransfor.old_class_id == OldClass.id)
        .outerjoin(NewClass, ErpClassTransfor.new_class_id == NewClass.id)
        .outerjoin(OldTeacher, OldClass.teacher_id == OldTeacher.id)
        .outerjoin(NewTeacher, NewClass.teacher_id == NewTeacher.id)
        .outerjoin(OldTeacherAccount, OldTeacher.account_id == OldTeacherAccount.id)
        .outerjoin(NewTeacherAccount, NewTeacher.account_id == NewTeacherAccount.id)
        .outerjoin(CreateByAccount, ErpClassTransfor.create_by == CreateByAccount.id)
        .outerjoin(UpdateByAccount, ErpClassTransfor.update_by == UpdateByAccount.id)
        .where(and_(*conditions))
        .order_by(ErpClassTransfor.create_time.desc())
    )
    
    # 获取总数
    count_stmt = (
        select(func.count(ErpClassTransfor.id))
        .select_from(ErpClassTransfor)
        .where(and_(*conditions))
    )
    
    count_result = await db.execute(count_stmt)
    total_count = count_result.scalar()
    
    # 分页查询
    paginated_stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(paginated_stmt)
    transfer_data = result.fetchall()
    
    return transfer_data, total_count


async def get_child_teacher_id_list(db: AsyncSession, teacher_id: int):
    """
    根据教师teacher_id查询下属老师teacher_id列表
    
    Args:
        db: 数据库会话
        teacher_id: 教师ID
        
    Returns:
        list: 下属教师的teacher_id列表
    """
    from app_human_resources.modules import all_children_node
    
    # 1. 根据teacher_id查询对应的account_id
    teacher_stmt = (
        select(ErpAccountTeacher.account_id)
        .where(and_(
            ErpAccountTeacher.id == teacher_id,
            ErpAccountTeacher.disable == 0
        ))
    )
    teacher_result = await db.execute(teacher_stmt)
    teacher_account = teacher_result.fetchone()
    
    if not teacher_account:
        return []
    
    account_id = teacher_account.account_id
    
    # 2. 通过account_id查询该教师所在的部门
    dept_stmt = (
        select(ErpAccountDepartment.dept_id)
        .where(and_(
            ErpAccountDepartment.account_id == account_id,
            ErpAccountDepartment.disable == 0
        ))
    )
    dept_result = await db.execute(dept_stmt)
    dept_info = dept_result.fetchone()
    
    if not dept_info:
        return []
    
    dept_id = dept_info.dept_id
    
    # 3. 递归查询该部门的所有子部门
    child_depts = await all_children_node(db, ErpDepartment, dept_id)
    all_dept_ids = [dept_id]  # 包含当前部门
    for child_dept in child_depts:
        all_dept_ids.append(child_dept.get('id'))
    
    # 4. 查询所有子部门下的员工account_id
    account_dept_stmt = (
        select(ErpAccountDepartment.account_id)
        .where(and_(
            ErpAccountDepartment.dept_id.in_(all_dept_ids),
            ErpAccountDepartment.disable == 0
        ))
    )
    account_dept_result = await db.execute(account_dept_stmt)
    account_ids = [row.account_id for row in account_dept_result.fetchall()]
    
    if not account_ids:
        return []
    
    # 5. 筛选出是教师的员工，返回其teacher_id列表
    teacher_stmt = (
        select(ErpAccountTeacher.id)
        .where(and_(
            ErpAccountTeacher.account_id.in_(account_ids),
            ErpAccountTeacher.disable == 0
        ))
    )
    teacher_result = await db.execute(teacher_stmt)
    child_teacher_ids = [row.id for row in teacher_result.fetchall()]
    
    return child_teacher_ids
    
async def query_class_statistics_summary(db, condition=None):
    """
    查询班级统计汇总信息
    
    Args:
        db: 数据库会话
        condition: 查询条件
        
    Returns:
        包含paid_stu_count、unpaid_stu_count、pre_enrollment总数的字典
    """
    # 构建查询条件, 首先是默认条件
    conditions = [
        ErpClass.disable == 0,
    ]
    
    # 添加其他条件
    if condition and condition.get("class_names"):
        class_name_conditions = []
        for name in condition.get("class_names"):
            if name:
                class_name_conditions.append(ErpClass.class_name.ilike(f"%{name}%"))
        if class_name_conditions:
            conditions.append(and_(*class_name_conditions))
    
    if condition and condition.get("teacher_id") is not None:
        conditions.append(ErpClass.teacher_id == condition.get("teacher_id"))
    
    if condition and condition.get("term_ids"):
        conditions.append(ErpCourse.term_id.in_(condition.get("term_ids")))
    
    if condition and condition.get("grade_ids"):
        conditions.append(ErpCourse.grade_id.in_(condition.get("grade_ids")))
    
    if condition and condition.get("subject_ids"):
        conditions.append(ErpCourse.subject_id.in_(condition.get("subject_ids")))
    
    if condition and condition.get("category_ids"):
        conditions.append(ErpCourse.category_id.in_(condition.get("category_ids")))
    
    if condition and condition.get("course_type_ids"):
        conditions.append(ErpCourse.type_id.in_(condition.get("course_type_ids")))
    
    if condition and condition.get("class_status") is not None:
        conditions.append(ErpClass.class_status == condition.get("class_status"))
    
    if condition and condition.get("course_names") is not None:
        course_name_conditions = []
        for name in condition.get("course_names"):
            if name:
                course_name_conditions.append(ErpCourse.course_name.ilike(f"%{name}%"))
        if course_name_conditions:
            conditions.append(and_(*course_name_conditions))
    
    if condition and condition.get("class_start_time"):
        conditions.append(ErpClass.start_date >= condition.get("class_start_time"))
        
    if condition and condition.get("class_end_time"):
        conditions.append(ErpClass.start_date <= condition.get("class_end_time"))
        
    if condition and condition.get("audit_status") in (ClassAuditStatus.PASS.value, ClassAuditStatus.WAITING.value, ClassAuditStatus.REJECT.value):
        conditions.append(ErpClass.audit_status == condition.get("audit_status"))
    
    if condition and condition.get("is_shelf_miniprogram") is not None:
        conditions.append(ErpClass.is_shelf_miniprogram == condition.get("is_shelf_miniprogram"))

    # 首先查询符合条件的班级ID列表
    class_ids_stmt = (
        select(ErpClass.id)
        .select_from(ErpClass)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(and_(*conditions))
    )
    
    class_ids_result = await db.execute(class_ids_stmt)
    class_ids = [row.id for row in class_ids_result.fetchall()]
    
    if not class_ids:
        return {
            "total_paid_stu_count": 0,
            "total_unpaid_stu_count": 0,
            "total_pre_enrollment": 0
        }
    
    # 统计已付费学生总数
    paid_stu_stmt = (
        select(func.count(ErpOrderStudent.id))
        .where(
            ErpOrderStudent.disable == 0,
            ErpOrderStudent.class_id.in_(class_ids),
            ErpOrderStudent.student_state.in_([StudentState.NORMAL.value, StudentState.TRANSFER_IN.value])
        )
    )
    paid_stu_result = await db.execute(paid_stu_stmt)
    total_paid_stu_count = paid_stu_result.scalar() or 0
    
    # 统计未付费学生总数
    unpaid_stu_stmt = (
        select(func.count(ErpOrderStudent.id))
        .where(
            ErpOrderStudent.disable == 0,
            ErpOrderStudent.class_id.in_(class_ids),
            ErpOrderStudent.student_state == StudentState.WAIT_PAY.value
        )
    )
    unpaid_stu_result = await db.execute(unpaid_stu_stmt)
    total_unpaid_stu_count = unpaid_stu_result.scalar() or 0
    
    # 统计预报名总数 (pre_enrollment字段的总和)
    pre_enrollment_stmt = (
        select(func.coalesce(func.sum(ErpClass.pre_enrollment), 0))
        .select_from(ErpClass)
        .outerjoin(ErpCourse, ErpClass.course_id == ErpCourse.id)
        .where(and_(*conditions))
    )
    pre_enrollment_result = await db.execute(pre_enrollment_stmt)
    total_pre_enrollment = pre_enrollment_result.scalar() or 0
    
    return {
        "total_paid_stu_count": total_paid_stu_count,
        "total_unpaid_stu_count": total_unpaid_stu_count,
        "total_pre_enrollment": total_pre_enrollment
    }
    