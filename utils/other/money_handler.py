from decimal import Decimal, ROUND_HALF_UP
from typing import Union, Optional


class MoneyHandler:
    """金额处理工具类"""
    
    @staticmethod
    def to_decimal(amount: Union[int, float, str, Decimal], precision: int = 2) -> Decimal:
        """
        将金额转换为Decimal类型
        :param amount: 金额值
        :param precision: 精度，默认2位小数
        :return: Decimal类型的金额
        """
        if amount is None:
            return Decimal('0.00')
        
        if isinstance(amount, Decimal):
            return amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        
        # 转换为字符串避免浮点数精度问题
        amount_str = str(amount)
        decimal_amount = Decimal(amount_str)
        
        # 设置精度
        precision_str = '0.' + '0' * precision
        return decimal_amount.quantize(Decimal(precision_str), rounding=ROUND_HALF_UP)
    
    @staticmethod
    def to_float(amount: Union[Decimal, int, float, str], precision: int = 2) -> float:
        """
        将金额转换为float类型，主要用于API返回
        :param amount: 金额值
        :param precision: 精度，默认2位小数
        :return: float类型的金额
        """
        if amount is None:
            return 0.0
        
        decimal_amount = MoneyHandler.to_decimal(amount, precision)
        return float(decimal_amount)
    
    @staticmethod
    def add(*amounts: Union[int, float, str, Decimal]) -> Decimal:
        """
        金额相加
        :param amounts: 多个金额值
        :return: Decimal类型的总和
        """
        total = Decimal('0.00')
        for amount in amounts:
            if amount is not None:
                total += MoneyHandler.to_decimal(amount)
        return total
    
    @staticmethod
    def subtract(minuend: Union[int, float, str, Decimal], 
                 subtrahend: Union[int, float, str, Decimal]) -> Decimal:
        """
        金额相减
        :param minuend: 被减数
        :param subtrahend: 减数
        :return: Decimal类型的差值
        """
        minuend_decimal = MoneyHandler.to_decimal(minuend)
        subtrahend_decimal = MoneyHandler.to_decimal(subtrahend)
        return minuend_decimal - subtrahend_decimal
    
    @staticmethod
    def multiply(amount: Union[int, float, str, Decimal], 
                 multiplier: Union[int, float, str, Decimal]) -> Decimal:
        """
        金额相乘
        :param amount: 金额
        :param multiplier: 乘数
        :return: Decimal类型的乘积
        """
        amount_decimal = MoneyHandler.to_decimal(amount)
        multiplier_decimal = MoneyHandler.to_decimal(multiplier)
        result = amount_decimal * multiplier_decimal
        return result.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    
    @staticmethod
    def divide(dividend: Union[int, float, str, Decimal], 
               divisor: Union[int, float, str, Decimal]) -> Decimal:
        """
        金额相除
        :param dividend: 被除数
        :param divisor: 除数
        :return: Decimal类型的商
        """
        if MoneyHandler.to_decimal(divisor) == Decimal('0'):
            raise ValueError("除数不能为0")
        
        dividend_decimal = MoneyHandler.to_decimal(dividend)
        divisor_decimal = MoneyHandler.to_decimal(divisor)
        result = dividend_decimal / divisor_decimal
        return result.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    
    @staticmethod
    def compare(amount1: Union[int, float, str, Decimal], 
                amount2: Union[int, float, str, Decimal]) -> int:
        """
        比较两个金额
        :param amount1: 金额1
        :param amount2: 金额2
        :return: -1表示amount1<amount2, 0表示相等, 1表示amount1>amount2
        """
        decimal1 = MoneyHandler.to_decimal(amount1)
        decimal2 = MoneyHandler.to_decimal(amount2)
        
        if decimal1 < decimal2:
            return -1
        elif decimal1 > decimal2:
            return 1
        else:
            return 0
    
    @staticmethod
    def is_greater_than(amount1: Union[int, float, str, Decimal], 
                        amount2: Union[int, float, str, Decimal]) -> bool:
        """判断amount1是否大于amount2"""
        return MoneyHandler.compare(amount1, amount2) == 1
    
    @staticmethod
    def is_less_than(amount1: Union[int, float, str, Decimal], 
                     amount2: Union[int, float, str, Decimal]) -> bool:
        """判断amount1是否小于amount2"""
        return MoneyHandler.compare(amount1, amount2) == -1
    
    @staticmethod
    def is_equal(amount1: Union[int, float, str, Decimal], 
                 amount2: Union[int, float, str, Decimal]) -> bool:
        """判断两个金额是否相等"""
        return MoneyHandler.compare(amount1, amount2) == 0
    
    @staticmethod
    def sum_list(amounts: list) -> Decimal:
        """
        计算金额列表的总和
        :param amounts: 金额列表
        :return: Decimal类型的总和
        """
        total = Decimal('0.00')
        for amount in amounts:
            if amount is not None:
                total += MoneyHandler.to_decimal(amount)
        return total 