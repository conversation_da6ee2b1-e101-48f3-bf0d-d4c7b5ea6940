from datetime import datetime, timed<PERSON>ta
from typing import Optional, List, Union
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError
import logging
from models.m_common import ErpLog

logger = logging.getLogger(__name__)


class LogHandler:
    def __init__(self, db: AsyncSession):
        """初始化日志处理器

        参数:
            db (AsyncSession): SQLAlchemy异步数据库会话
        """
        self.db = db

    async def create_log(
            self,
            log_type: int,
            action: str,
            content: Optional[list],  # 列表或者字典,
            create_time: Optional[datetime] = None,
            account_id: Optional[int] = None,
            obj_id: Optional[int] = None
    ) -> Optional[ErpLog]:
        """创建新的日志记录

        参数:
            log_type: 日志类型
            action: 执行的操作
            content: 日志内容
            create_time: 创建时间（默认为当前时间）
            account_id: 关联的账户ID
            obj_id: 关联的对象ID

        返回:
            成功返回ErpLog对象，失败返回None

        异常:
            ValueError: 必填字段为空时抛出
        """
        try:
            # 验证必填字段
            if not all([log_type, action, content]):
                raise ValueError("log_type, action 和 content 为必填字段")

            # 创建日志对象
            obj = ErpLog(
                log_type=log_type,
                action=action,
                content=content,
                create_time=create_time or datetime.now(),
                account_id=account_id,
                obj_id=obj_id
            )

            self.db.add(obj)
            await self.db.flush()
            await self.db.refresh(obj)

            logger.info(
                f"日志创建成功 - 类型: {log_type}, "
                f"操作: {action}, 账户ID: {account_id}"
            )
            return obj

        except ValueError as e:
            logger.error(f"create_log 数据验证错误: {str(e)}")
            raise
        except SQLAlchemyError as e:
            logger.error(f"create_log 数据库错误: {str(e)}")
            await self.db.rollback()
            raise
        except Exception as e:
            logger.error(f"create_log 未预期的错误: {str(e)}")
            await self.db.rollback()
            raise

    async def get_logs(
            self,
            log_id: Optional[int] = None,
            account_id: Optional[int] = None,
            obj_id: Optional[int] = None,
            log_type: int = None,
            limit: int = 100
    ) -> List[ErpLog]:
        """根据条件检索日志

        参数:
            log_id: 特定的日志ID
            account_id: 按账户ID筛选
            obj_id: 按对象ID筛选
            limit: 返回记录的最大数量

        返回:
            ErpLog对象列表

        异常:
            SQLAlchemyError: 数据库查询失败时抛出
        """
        try:
            # 构建查询条件
            conditions = []
            if log_id is not None:
                conditions.append(ErpLog.id == log_id)
            if account_id is not None:
                conditions.append(ErpLog.account_id == account_id)
            if obj_id is not None:
                conditions.append(ErpLog.obj_id == obj_id)
            if action_type is not None:
                conditions.append(ErpLog.log_type == log_type)

            # 创建查询
            query = select(ErpLog).where(and_(*conditions))

            # 添加限制和排序
            query = query.order_by(ErpLog.create_time.desc()).limit(limit)

            # 执行查询
            result = await self.db.execute(query)
            return result.fetchall()

        except SQLAlchemyError as e:
            logger.error(f"get_logs 数据库错误: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"get_logs 未预期的错误: {str(e)}")
            raise
