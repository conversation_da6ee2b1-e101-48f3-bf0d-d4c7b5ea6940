class SalaryCalculator:
    def __init__(self, taxable_salary, insurance_salary):
        self.taxable_salary = taxable_salary
        self.insurance_salary = insurance_salary
        self.threshold = 5000
        self.insurance_rates = {
            'pension': (0.08, 0.19),  # 养老金 (个人, 公司)
            'medical': (0.02, 0.10),  # 医疗保险 (个人, 公司)
            'unemployment': (0.005, 0.015),  # 失业保险 (个人, 公司)
            'work_injury': (0, 0.005),  # 工伤保险 (个人, 公司)
            'maternity': (0, 0.01)  # 生育保险 (个人, 公司)
        }
        self.housing_fund_rate = 0.12  # 公积金比例 (个人和公司相同)
        self.min_base = 5360  # 最低基数（根据实际情况调整）
        self.max_base = 28221  # 最高基数（根据实际情况调整）

    def calculate_tax(self):
        taxable_income = self.taxable_salary - self.threshold
        if taxable_income <= 0:
            return 0

        tax_brackets = [
            (3000, 0.03),
            (12000, 0.1),
            (25000, 0.2),
            (35000, 0.25),
            (55000, 0.3),
            (80000, 0.35),
            (float('inf'), 0.45)
        ]

        tax = 0
        for bracket, rate in tax_brackets:
            if taxable_income > bracket:
                tax += bracket * rate
                taxable_income -= bracket
            else:
                tax += taxable_income * rate
                break

        return tax

    def calculate_social_insurance_and_housing_fund(self):
        base = max(self.min_base, min(self.insurance_salary, self.max_base))

        individual_total = 0
        company_total = 0
        for rate in self.insurance_rates.values():
            individual_total += base * rate[0]
            company_total += base * rate[1]

        housing_fund_individual = base * self.housing_fund_rate
        housing_fund_company = base * self.housing_fund_rate

        individual_total += housing_fund_individual
        company_total += housing_fund_company

        return individual_total, company_total

    def calculate_all(self):
        tax = self.calculate_tax()
        individual_si, company_si = self.calculate_social_insurance_and_housing_fund()
        return {
            "individual_tax": tax,
            "individual_social_insurance": individual_si,
            "company_social_insurance": company_si
        }


# 示例
monthly_taxable_salary = 10000
monthly_insurance_salary = 3000
calculator = SalaryCalculator(monthly_taxable_salary, monthly_insurance_salary)
results = calculator.calculate_all()
print(f"月应纳税收入 {monthly_taxable_salary} 元的个税为 {results['individual_tax']} 元")
print(f"月收入基数 {monthly_insurance_salary} 元的个人五险一金缴费为 {results['individual_social_insurance']} 元")
print(f"月收入基数 {monthly_insurance_salary} 元的公司五险一金缴费为 {results['company_social_insurance']} 元")
