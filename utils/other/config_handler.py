from aiocache import cached, Cache
from aiocache.serializers import JsonSerializer

from models.models import ErpPublicSettings
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db


@cached(ttl=60, cache=Cache.MEMORY, key="config_cache", serializer=JsonSerializer())
async def get_config():
    print(' -- config cache expired -- ')
    async for db in get_default_db():
        setting = CRUD(ErpPublicSettings)
        configs = await setting.get_many(db)
        return {str(config.dict_key): config.dict_value for config in configs}
