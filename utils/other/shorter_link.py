# import random
# import string
# from datetime import datetime, timedelta
#
#
# class ShortLinkGenerator:
#     crud_factory = CRUDFactory()
#
#     # 获取 MyModel1 的 CRUD 实例
#     ERP_SHORT_URL = crud_factory.get_crud(ErpShortUrl)
#     # mall_short_url = CRUD(MallShortUrl)
#
#     @classmethod
#     async def _generate_random(cls, db, length=6):
#         """生成一个无碰撞的短链。"""
#         from app_user.crud import exists_db
#
#         characters = string.ascii_letters + string.digits
#         short_link = ''.join(random.choice(characters) for _ in range(length))
#         exists = await exists_db(db, ErpShortUrl, 'hashed_str', short_link)
#         if exists:
#             return await cls._generate_random(db, length)
#         return short_link
#
#     @classmethod
#     async def generate_short_link(cls, db, base_url, url, length=6):
#         """
#         """
#         short_link = await cls._generate_random(db, length)
#         # 存储短链和原始URL的映射
#         await cls.ERP_SHORT_URL.create(db, **{
#             "base_url": base_url,
#             "hashed_str": short_link,
#             "long_url": url,
#             "expired_time": datetime.now() + timedelta(days=2)
#         })
#         return short_link
#
#     @classmethod
#     async def get_long_link(cls, db, hashed_str):
#         """根据短链查询正常网址
#         """
#         long_link = await cls.ERP_SHORT_URL.get_objs_by_field(db, [
#             MallShortUrl.expired_time > datetime.now(),
#             MallShortUrl.disable == 0,
#             MallShortUrl.hashed_str == hashed_str
#         ])
#         # 存储短链和原始URL的映射
#         if not long_link:
#             return
#         return long_link[0]
