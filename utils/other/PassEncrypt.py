from Crypto.Cipher import DES
from Crypto.Util.Padding import pad
import base64

_webapikey = bytes([0xF1, 0x12, 0xA3, 0xD1, 0xBA, 0x54, 0x2A, 0x88])
_webapiiv = bytes([0x04, 0xAE, 0x57, 0x83, 0x56, 0x28, 0x66, 0xA7])


def encrypt(encrypt_string: str) -> str:
    """
    密码生成的入口，原ERP的密码加密逻辑，返回加密后的字符串
    """
    return encrypt_with_key_iv(encrypt_string, _webapikey, _webapiiv)


def encrypt_with_key_iv(encrypt_string: str, key: bytes, iv: bytes) -> str:
    des = DES.new(key, DES.MODE_CBC, iv)
    data = encrypt_string.encode('utf-8')
    padded_data = pad(data, DES.block_size)
    encrypted_data = des.encrypt(padded_data)
    return base64.b64encode(encrypted_data).decode('utf-8')
