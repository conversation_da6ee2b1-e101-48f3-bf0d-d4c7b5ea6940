class AmountConverter:
    num_to_char = {
        '0': '零', '1': '壹', '2': '贰', '3': '叁', '4': '肆', '5': '伍', '6': '陆', '7': '柒', '8': '捌', '9': '玖'
    }
    unit = ['', '拾', '佰', '仟']
    small_unit = ['角', '分']
    big_unit = ['', '万', '亿', '兆']

    @classmethod
    def convert(cls, amount):
        try:
            if isinstance(amount, (int, float)):
                amount = f"{amount:.2f}"
            integer_part, decimal_part = cls._split_amount(amount)
            integer_result = cls._convert_integer_part(integer_part)
            decimal_result = cls._convert_decimal_part(decimal_part)
            return cls._combine_results(integer_result, decimal_result)
        except Exception as e:
            raise ValueError(f"Invalid input for conversion: {amount}") from e

    @classmethod
    def _split_amount(cls, amount):
        if '.' in amount:
            integer_part, decimal_part = amount.split('.')
            if len(decimal_part) > 2:
                decimal_part = decimal_part[:2]
        else:
            integer_part, decimal_part = amount, '00'
        return integer_part, decimal_part

    @classmethod
    def _convert_integer_part(cls, integer_part):
        result = ''
        length = len(integer_part)
        zero_flag = False
        for i, num in enumerate(integer_part):
            if num != '0':
                result += (cls.num_to_char[num] + cls.unit[(length - 1 - i) % 4])
                zero_flag = False
            else:
                if not zero_flag:
                    result += '零'
                    zero_flag = True
            if (length - 1 - i) % 4 == 0 and (length - 1 - i) != 0:
                result += cls.big_unit[(length - 1 - i) // 4]
        result = result.rstrip('零')
        result += '元' if result else ''
        return result

    @classmethod
    def _convert_decimal_part(cls, decimal_part):
        result = ''
        for i, num in enumerate(decimal_part):
            if num != '0':
                result += cls.num_to_char[num] + cls.small_unit[i]
        return result if result else '整'

    @classmethod
    def _combine_results(cls, integer_result, decimal_result):
        if integer_result and decimal_result != '整':
            return integer_result + decimal_result
        elif integer_result:
            return integer_result + '整'
        else:
            return decimal_result
