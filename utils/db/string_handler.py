import random
from datetime import datetime

import pytz


class StringHandler:

    @classmethod
    def generate_unique_number(cls, prefix):
        # 获取当前日期，格式为 YYYYMMDD
        date_str = datetime.now().strftime("%Y%m%d%H%M%S")
        # 生成一个随机数用作唯一标识
        random_number = random.randint(1000, 9999)
        # 组合成最终的订单数字串
        order_number = f"{prefix}{date_str}{random_number}"
        return order_number

    @classmethod
    def convert_datetime_to_db_format(cls, iso_datetime_str):
        # 解析 ISO 8601 格式的字符串
        iso_datetime = datetime.fromisoformat(iso_datetime_str.rstrip("Z"))
        # 转换为 UTC 时间
        utc_datetime = iso_datetime.replace(tzinfo=pytz.UTC)
        # 格式化为数据库接受的格式
        return utc_datetime.strftime('%Y-%m-%d %H:%M:%S')

    @classmethod
    def find_equal_element(cls, a, b):
        """
        使用集合来找出两个列表中的相同元素
        """
        common_elements = set(a) & set(b)

        # 如果有相同元素，返回这些元素，否则返回False
        if common_elements:
            return list(common_elements)
        else:
            return False

