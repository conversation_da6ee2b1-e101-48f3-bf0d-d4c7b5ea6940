from datetime import datetime, timedelta


class DateHandler:

    @classmethod
    def generate_date_range(cls, start_date, end_date):
        """
        生成两个日期之间的日期范围
        """
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        date_list = []
        current_date = start
        while current_date <= end:
            date_list.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)
        return date_list
