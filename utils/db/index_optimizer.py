"""
数据库索引优化管理脚本
用于创建和管理教室利用率查询相关的索引
"""

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from utils.db.db_handler import get_default_db
import settings


class IndexOptimizer:
    """数据库索引优化器"""
    
    def __init__(self):
        self.indexes = [
            {
                "name": "idx_erp_class_plan_room_time_disable",
                "table": "erp_class_plan",
                "columns": "(room_id, start_time, disable)",
                "description": "教室利用率查询优化索引 - 按教室ID、时间、禁用状态"
            },
            {
                "name": "idx_erp_class_plan_time_disable",
                "table": "erp_class_plan", 
                "columns": "(start_time, disable)",
                "description": "时间范围查询优化索引"
            },
            {
                "name": "idx_erp_class_plan_disable_room_time",
                "table": "erp_class_plan",
                "columns": "(disable, room_id, start_time)",
                "description": "禁用状态优先的复合索引"
            },
            {
                "name": "idx_erp_office_classroom_center_disable",
                "table": "erp_office_classroom",
                "columns": "(center_id, disable)",
                "description": "教室表校区查询优化索引"
            },
            {
                "name": "idx_erp_office_classroom_disable",
                "table": "erp_office_classroom",
                "columns": "(disable)",
                "description": "教室表禁用状态索引"
            },
            # 课消统计相关索引
            {
                "name": "idx_erp_order_student_class_disable",
                "table": "erp_order_student",
                "columns": "(class_id, disable)",
                "description": "订单学生表 - 班级和禁用状态索引"
            },
            {
                "name": "idx_erp_order_student_stu_disable",
                "table": "erp_order_student",
                "columns": "(stu_id, disable)",
                "description": "订单学生表 - 学生和禁用状态索引"
            },
            {
                "name": "idx_erp_order_student_state_disable",
                "table": "erp_order_student",
                "columns": "(student_state, disable)",
                "description": "订单学生表 - 学生状态和禁用状态索引"
            },
            {
                "name": "idx_erp_order_order_student_type_disable",
                "table": "erp_order",
                "columns": "(order_student_id, order_class_type, disable)",
                "description": "订单表 - 订单学生ID、类型和禁用状态索引"
            },
            {
                "name": "idx_erp_order_create_time_disable",
                "table": "erp_order",
                "columns": "(create_time, disable)",
                "description": "订单表 - 创建时间和禁用状态索引"
            },
            {
                "name": "idx_erp_order_type_disable_create_time",
                "table": "erp_order",
                "columns": "(order_class_type, disable, create_time)",
                "description": "订单表 - 类型、禁用状态、创建时间复合索引"
            },
            {
                "name": "idx_erp_class_checking_order_student_disable",
                "table": "erp_class_checking",
                "columns": "(order_student_id, disable)",
                "description": "签到表 - 订单学生ID和禁用状态索引"
            },
            {
                "name": "idx_erp_class_checking_status_disable",
                "table": "erp_class_checking",
                "columns": "(check_status, disable)",
                "description": "签到表 - 签到状态和禁用状态索引"
            },
            {
                "name": "idx_erp_class_checking_create_time_disable",
                "table": "erp_class_checking",
                "columns": "(create_time, disable)",
                "description": "签到表 - 创建时间和禁用状态索引"
            },
            {
                "name": "idx_erp_class_checking_order_status_disable",
                "table": "erp_class_checking",
                "columns": "(order_student_id, check_status, disable)",
                "description": "签到表 - 订单学生ID、签到状态、禁用状态复合索引"
            },
            {
                "name": "idx_erp_class_classroom_disable",
                "table": "erp_class",
                "columns": "(classroom_id, disable)",
                "description": "班级表 - 教室ID和禁用状态索引"
            },
            {
                "name": "idx_erp_class_course_disable",
                "table": "erp_class",
                "columns": "(course_id, disable)",
                "description": "班级表 - 课程ID和禁用状态索引"
            },
            {
                "name": "idx_erp_student_name_disable",
                "table": "erp_student",
                "columns": "(stu_name, disable)",
                "description": "学生表 - 学生姓名和禁用状态索引"
            },
            {
                "name": "idx_erp_course_name_disable",
                "table": "erp_course",
                "columns": "(course_name, disable)",
                "description": "课程表 - 课程名称和禁用状态索引"
            },
            {
                "name": "idx_erp_order_refund_detail_order_student_state",
                "table": "erp_order_refund_detail",
                "columns": "(order_student_id, refund_state, disable)",
                "description": "退款详情表 - 订单学生ID、退款状态、禁用状态索引"
            }
        ]
    
    async def check_index_exists(self, db: AsyncSession, table_name: str, index_name: str) -> bool:
        """检查索引是否存在"""
        query = """
        SELECT COUNT(*) as count
        FROM information_schema.statistics 
        WHERE table_schema = DATABASE() 
        AND table_name = :table_name 
        AND index_name = :index_name
        """
        result = await db.execute(query, {"table_name": table_name, "index_name": index_name})
        row = result.fetchone()
        return row.count > 0 if row else False
    
    async def create_index(self, db: AsyncSession, index_info: dict) -> bool:
        """创建单个索引"""
        try:
            # 检查索引是否已存在
            exists = await self.check_index_exists(db, index_info["table"], index_info["name"])
            if exists:
                settings.logger.info(f"索引 {index_info['name']} 已存在，跳过创建")
                return True
            
            # 创建索引
            create_sql = f"CREATE INDEX {index_info['name']} ON {index_info['table']} {index_info['columns']}"
            await db.execute(create_sql)
            await db.commit()
            
            settings.logger.info(f"成功创建索引: {index_info['name']} - {index_info['description']}")
            return True
            
        except Exception as e:
            settings.logger.error(f"创建索引 {index_info['name']} 失败: {str(e)}")
            await db.rollback()
            return False
    
    async def create_all_indexes(self, db: AsyncSession) -> dict:
        """创建所有索引"""
        results = {
            "success": [],
            "failed": [],
            "skipped": []
        }
        
        for index_info in self.indexes:
            try:
                # 检查索引是否已存在
                exists = await self.check_index_exists(db, index_info["table"], index_info["name"])
                if exists:
                    results["skipped"].append(index_info["name"])
                    settings.logger.info(f"索引 {index_info['name']} 已存在，跳过创建")
                    continue
                
                # 创建索引
                create_sql = f"CREATE INDEX {index_info['name']} ON {index_info['table']} {index_info['columns']}"
                await db.execute(create_sql)
                
                results["success"].append(index_info["name"])
                settings.logger.info(f"成功创建索引: {index_info['name']} - {index_info['description']}")
                
            except Exception as e:
                results["failed"].append({
                    "name": index_info["name"],
                    "error": str(e)
                })
                settings.logger.error(f"创建索引 {index_info['name']} 失败: {str(e)}")
        
        # 提交所有成功的索引创建
        try:
            await db.commit()
        except Exception as e:
            settings.logger.error(f"提交索引创建失败: {str(e)}")
            await db.rollback()
        
        return results
    
    async def list_existing_indexes(self, db: AsyncSession) -> list:
        """列出现有的相关索引"""
        query = """
        SELECT 
            table_name,
            index_name,
            column_name,
            seq_in_index
        FROM information_schema.statistics 
        WHERE table_schema = DATABASE() 
        AND table_name IN ('erp_class_plan', 'erp_office_classroom')
        AND index_name LIKE 'idx_%'
        ORDER BY table_name, index_name, seq_in_index
        """
        result = await db.execute(query)
        return result.fetchall()
    
    async def drop_index(self, db: AsyncSession, table_name: str, index_name: str) -> bool:
        """删除指定索引"""
        try:
            # 检查索引是否存在
            exists = await self.check_index_exists(db, table_name, index_name)
            if not exists:
                settings.logger.info(f"索引 {index_name} 不存在，无需删除")
                return True
            
            # 删除索引
            drop_sql = f"DROP INDEX {index_name} ON {table_name}"
            await db.execute(drop_sql)
            await db.commit()
            
            settings.logger.info(f"成功删除索引: {index_name}")
            return True
            
        except Exception as e:
            settings.logger.error(f"删除索引 {index_name} 失败: {str(e)}")
            await db.rollback()
            return False


async def optimize_classroom_utilization_indexes():
    """优化教室利用率查询索引的主函数"""
    optimizer = IndexOptimizer()
    
    async for db in get_default_db():
        try:
            settings.logger.info("开始优化教室利用率查询索引...")
            
            # 列出现有索引
            existing_indexes = await optimizer.list_existing_indexes(db)
            settings.logger.info(f"现有相关索引数量: {len(existing_indexes)}")
            
            # 创建所有需要的索引
            results = await optimizer.create_all_indexes(db)
            
            # 输出结果统计
            settings.logger.info(f"索引优化完成:")
            settings.logger.info(f"  - 成功创建: {len(results['success'])} 个")
            settings.logger.info(f"  - 已存在跳过: {len(results['skipped'])} 个") 
            settings.logger.info(f"  - 创建失败: {len(results['failed'])} 个")
            
            if results['failed']:
                for failed in results['failed']:
                    settings.logger.error(f"    失败索引: {failed['name']} - {failed['error']}")
            
            return results
            
        except Exception as e:
            settings.logger.error(f"索引优化过程出错: {str(e)}")
            return None
        finally:
            await db.close()


async def optimize_class_consumption_indexes():
    """
    专门优化课消统计查询的数据库索引
    
    课消统计涉及的主要表和查询模式：
    1. erp_order_student - 主要查询表，按class_id、stu_id分组
    2. erp_order - 按order_student_id关联，筛选order_class_type=1
    3. erp_class_checking - 按order_student_id关联，统计课消数据
    4. erp_order_refund_detail - 按order_student_id关联，计算退款
    5. 其他关联表用于展示信息
    """
    optimizer = IndexOptimizer()
    
    # 课消统计专用索引
    class_consumption_indexes = [
        {
            "name": "idx_erp_order_student_class_disable",
            "table": "erp_order_student",
            "columns": "(class_id, disable)",
            "description": "订单学生表 - 班级和禁用状态索引"
        },
        {
            "name": "idx_erp_order_student_stu_disable",
            "table": "erp_order_student",
            "columns": "(stu_id, disable)",
            "description": "订单学生表 - 学生和禁用状态索引"
        },
        {
            "name": "idx_erp_order_order_student_type_disable",
            "table": "erp_order",
            "columns": "(order_student_id, order_class_type, disable)",
            "description": "订单表 - 订单学生ID、类型和禁用状态索引"
        },
        {
            "name": "idx_erp_order_create_time_disable",
            "table": "erp_order",
            "columns": "(create_time, disable)",
            "description": "订单表 - 创建时间和禁用状态索引"
        },
        {
            "name": "idx_erp_class_checking_order_status_disable",
            "table": "erp_class_checking",
            "columns": "(order_student_id, check_status, disable)",
            "description": "签到表 - 订单学生ID、签到状态、禁用状态复合索引"
        },
        {
            "name": "idx_erp_class_checking_create_time_disable",
            "table": "erp_class_checking",
            "columns": "(create_time, disable)",
            "description": "签到表 - 创建时间和禁用状态索引"
        },
        {
            "name": "idx_erp_order_refund_detail_order_student_state",
            "table": "erp_order_refund_detail",
            "columns": "(order_student_id, refund_state, disable)",
            "description": "退款详情表 - 订单学生ID、退款状态、禁用状态索引"
        },
        {
            "name": "idx_erp_student_name_disable",
            "table": "erp_student",
            "columns": "(stu_name, disable)",
            "description": "学生表 - 学生姓名和禁用状态索引（支持模糊查询）"
        },
        {
            "name": "idx_erp_class_name_disable",
            "table": "erp_class",
            "columns": "(class_name, disable)",
            "description": "班级表 - 班级名称和禁用状态索引（支持模糊查询）"
        },
        {
            "name": "idx_erp_course_name_disable",
            "table": "erp_course",
            "columns": "(course_name, disable)",
            "description": "课程表 - 课程名称和禁用状态索引（支持模糊查询）"
        }
    ]
    
    async for db in get_default_db():
        try:
            settings.logger.info("开始优化课消统计查询索引...")
            
            results = {
                "success": [],
                "failed": [],
                "skipped": []
            }
            
            for index_info in class_consumption_indexes:
                try:
                    # 检查索引是否已存在
                    exists = await optimizer.check_index_exists(db, index_info["table"], index_info["name"])
                    if exists:
                        results["skipped"].append(index_info["name"])
                        settings.logger.info(f"索引 {index_info['name']} 已存在，跳过创建")
                        continue
                    
                    # 创建索引
                    create_sql = f"CREATE INDEX {index_info['name']} ON {index_info['table']} {index_info['columns']}"
                    await db.execute(create_sql)
                    
                    results["success"].append(index_info["name"])
                    settings.logger.info(f"成功创建索引: {index_info['name']} - {index_info['description']}")
                    
                except Exception as e:
                    results["failed"].append({
                        "name": index_info["name"],
                        "error": str(e)
                    })
                    settings.logger.error(f"创建索引 {index_info['name']} 失败: {str(e)}")
            
            # 提交所有成功的索引创建
            try:
                await db.commit()
                settings.logger.info("所有索引创建操作已提交")
            except Exception as e:
                settings.logger.error(f"提交索引创建失败: {str(e)}")
                await db.rollback()
            
            # 输出结果统计
            settings.logger.info(f"课消统计索引优化完成:")
            settings.logger.info(f"  - 成功创建: {len(results['success'])} 个")
            settings.logger.info(f"  - 已存在跳过: {len(results['skipped'])} 个") 
            settings.logger.info(f"  - 创建失败: {len(results['failed'])} 个")
            
            if results['failed']:
                for failed in results['failed']:
                    settings.logger.error(f"    失败索引: {failed['name']} - {failed['error']}")
            
            return results
            
        except Exception as e:
            settings.logger.error(f"课消统计索引优化过程出错: {str(e)}")
            return None
        finally:
            await db.close()


if __name__ == "__main__":
    # 直接运行此脚本来优化索引
    asyncio.run(optimize_classroom_utilization_indexes()) 