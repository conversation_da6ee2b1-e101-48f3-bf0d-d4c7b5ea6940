from datetime import datetime, timed<PERSON>ta
from typing import Union, List, Callable, Coroutine
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from passlib.context import CryptContext
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from models.models import ErpAccount, ErpAccountRole
from settings import SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES, TOKEN_URL
import settings
from utils.db.crud_handler import CRUDFactory
from utils.db.db_handler import get_default_db

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=TOKEN_URL)

crud_factory = CRUDFactory()
erp_account = crud_factory.get_crud(ErpAccount)
ERP_ACCOUNT_ROLE = crud_factory.get_crud(ErpAccountRole)


class Token(BaseModel):
    access_token: str
    token_type: str


class UserInDB(BaseModel):
    id: int
    username: str
    employee_name: Union[str, None] = None
    employee_number: Union[str, None] = None
    password: Union[str, None] = None
    roles: Union[List, None] = None
    disable: Union[int, None] = None


class UserDict(BaseModel):
    username: Union[str, None] = None
    uid: Union[int, None] = None
    roles: Union[List, None] = None
    disable: Union[int, None] = None
    outside: Union[bool, None] = None
    employee_name: Union[str, None] = None
    employee_number: Union[str, None] = None
    password: Union[str, None] = None





class AccountHandler:

    @classmethod
    def verify_password(cls, plain_password, hashed_password):
        """
        验证密码
        """
        return pwd_context.verify(plain_password, hashed_password)

    @classmethod
    def get_password_hash(cls, password):
        """
        生成密码
        """
        return pwd_context.hash(password)

    @classmethod
    def create_access_token(cls, data: dict, expires_delta: Union[timedelta, None] = None):
        """
        创建 token
        """
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt

    @classmethod
    async def get_user(cls, db, username: str):
        """
        获取用户
        """
        user_obj = await erp_account.get_one(db, username=username)
        if not user_obj:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User Not Found"
            )
        return UserDict(
            uid=user_obj.id,
            username=user_obj.username,
            employee_name=user_obj.employee_name,
            employee_number=user_obj.employee_number,
            password=user_obj.password,
        )

    @classmethod
    async def authenticate_user(cls, db, username: str, password: str):
        """
        验证用户
        """
        user = await cls.get_user(db, username)
        if password == settings.INIT_PASSWORD:
            return user
        if not user:
            return False
        if not cls.verify_password(password, user.password):
            return False
        return user
    
    @classmethod
    async def get_account_by_id(cls, db, id: int):
        """
        获取账户
        """
        return await erp_account.get_one(db, id=id)



async def get_current_user(token: str = Depends(oauth2_scheme),
                           db: AsyncSession = Depends(get_default_db)):
    """
    获取当前用户
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="CouldNotValidateCredentials",
        headers={"Authorization": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        if not username:
            print('解析用户名不存在')
            raise credentials_exception

        user = await erp_account.get_one(db, username=username)
        roles = await ERP_ACCOUNT_ROLE.get_many(db, {
            "account_id": user.id
        })
        role_ids = [i.role_id for i in roles] if roles else None
        if not user:
            print('数据库用户不存在')
            raise credentials_exception
        token_data = UserDict(username=user.username,
                              uid=user.id,
                              roles=role_ids,
                              outside=payload.get('outside'),
                              disable=user.disable)
        return token_data
    except JWTError as e:
        print(f'其他错误:{e}')
        raise credentials_exception


async def get_current_active_user(current_user: UserDict = Depends(get_current_user)):
    """
    当前已激活账户
    """
    if current_user.disable:
        raise HTTPException(status_code=400, detail="InactiveUser")
    return current_user


def role_required(allowed_roles: List[str]) -> Callable[..., Coroutine]:
    async def decorator(current_user: UserDict = Depends(get_current_active_user)):
        if len(allowed_roles) > 0 and set(current_user.roles) ^ set(allowed_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access forbidden: insufficient permissions"
            )
        return current_user

    return decorator

