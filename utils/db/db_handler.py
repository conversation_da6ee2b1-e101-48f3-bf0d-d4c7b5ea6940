import asyncio
from settings import logger
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from typing import AsyncGenerator
import aioredis
from settings import DB_CONFIGS, redis_url

DB_ERP2 = DB_CONFIGS['default']
DB_UAT = DB_CONFIGS['uat_reborn_think']
DATABASE_URL_ERP2 = f"mysql+aiomysql://{DB_ERP2['USER']}:{DB_ERP2['PASSWD']}@{DB_ERP2['HOST']}:{DB_ERP2['PORT']}/{DB_ERP2['DB']}"
DATABASE_URL_UAT = f"mysql+aiomysql://{DB_UAT['USER']}:{DB_UAT['PASSWD']}@{DB_UAT['HOST']}:{DB_UAT['PORT']}/{DB_UAT['DB']}"


def choice_db(db_url):
    engine = create_async_engine(
        db_url,
        pool_size=20,
        max_overflow=10,
        pool_recycle=3600,  # 每隔3600秒回收一次连接
        pool_pre_ping=True,  # 在使用连接前检查其是否有效
        pool_timeout=30,  # 获取连接的超时时间
    )
    return sessionmaker(
        bind=engine,
        class_=AsyncSession,
        autocommit=False,
        autoflush=False,
    )


async def get_default_db() -> AsyncGenerator[AsyncSession, None]:
    async_session_factory = choice_db(DATABASE_URL_ERP2)
    async with async_session_factory() as session:
        try:
            yield session
        finally:
            await session.close()


async def get_uat_db() -> AsyncGenerator[AsyncSession, None]:
    async_session_factory = choice_db(DATABASE_URL_UAT)
    async with async_session_factory() as session:
        try:
            yield session
        finally:
            await session.close()


async def get_redis() -> AsyncGenerator[aioredis.Redis, None]:
    retries = 5
    for attempt in range(retries):
        try:
            redis = await aioredis.from_url(redis_url)
            try:
                yield redis
            finally:
                await redis.close()
                # logger.info("Redis connection closed")
            break
        except aioredis.exceptions.ConnectionError as e:
            if attempt < retries - 1:
                logger.warning(f"Redis connection failed, retrying ({attempt + 1}/{retries})...")
                await asyncio.sleep(1)  # 等待一段时间再重试
            else:
                logger.error("Redis connection failed after multiple attempts")
                raise e
