import json
from typing import Optional, Dict, Any, List, Type

from sqlalchemy import select, desc, and_, update, insert, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from tenacity import retry, wait_fixed, stop_after_attempt
from sqlalchemy.exc import MultipleResultsFound
import logging
from datetime import datetime


class CRUD:
    def __init__(self, model: Type):
        self.model = model

    async def create(self, db: AsyncSession, commit: bool = True, **kwargs) -> Any:
        obj = self.model(**kwargs)
        db.add(obj)
        if commit:
            await self._commit(db)
        else:
            await self._flush(db)
        await self._refresh(db, obj)
        return obj

    async def create_many(self, db: AsyncSession, objects: List[Dict[str, Any]], commit: bool = True) -> List[Any]:
        objs = [self.model(**obj_data) for obj_data in objects]
        db.add_all(objs)
        if commit:
            await self._commit(db)
        else:
            await self._flush(db)

    async def get_by_id(self, db: AsyncSession, obj_id: Any) -> Optional[Any]:
        return await self._get(db, self.model, obj_id)

    async def get_one(self, db: AsyncSession, **kwargs) -> Optional[Any]:
        # Check if 'disable' attribute exists in the model
        if hasattr(self.model, 'disable'):
            disable_filter = getattr(self.model, 'disable') == 0
        else:
            disable_filter = True  # No filter if 'disable' does not exist

        # Create the query statement
        stmt = select(self.model).where(
            and_(
                *(getattr(self.model, key) == value for key, value in kwargs.items()),
                disable_filter
            )
        )

        # Execute the query
        result = await self._execute(db, stmt)
        return result.scalar()

    async def get_many(self, db: AsyncSession, condition: Optional[Dict[str, Any]] = None,
                       raw: Optional[List[Any]] = None, reverse=True, orderby: Optional[List[Any]] = None) -> List[Any]:
        stmt = self._build_select_stmt(condition, raw, reverse=reverse, orderby=orderby)
        result = await self._execute(db, stmt)
        return result.scalars().all()
    
    async def get_count(self, db: AsyncSession, condition: Optional[Dict[str, Any]] = None,
                       raw: Optional[List[Any]] = None) -> int:
        conditions = [getattr(self.model, key) == value for key, value in condition.items()] if condition else []
        if raw:
            conditions.extend(raw)
        if hasattr(self.model, 'disable'):
            conditions.append(self.model.disable == 0)
        if hasattr(self.model, 'Status'):
            conditions.append(self.model.Status == 0)
            
        stmt = select(func.count()).select_from(self.model).where(and_(*conditions))
        result = await self._execute(db, stmt)
        return result.scalar()

    async def get_many_with_pagination(self, db: AsyncSession, page: int, page_size: int,
                                       condition: Optional[Dict[str, Any]] = None,
                                       raw: Optional[List[Any]] = None, reverse=True, orderby: Optional[List[Any]] = None) -> List[Any]:
        stmt = self._build_select_stmt(condition, raw, reverse=reverse, orderby=orderby).offset((page - 1) * page_size).limit(page_size)
        result = await self._execute(db, stmt)
        return result.scalars().all()

    async def update_many(self, db: AsyncSession, condition: Dict[str, Any], new_values: Dict[str, Any],
                          commit: bool = True) -> None:
        stmt = self._build_update_stmt(condition, new_values)
        await self._execute(db, stmt)
        if commit:
            await self._commit(db)

    
    # 增加一个存在则更新，不存在则创建
    async def upsert(self, db: AsyncSession, condition: Dict[str, Any], 
                     values: Dict[str, Any], commit: bool = True) -> Any: 
        """
        # 参数说明
        condition: 条件，用于唯一确定对象
        values: 需要更新的字段和值
        commit: 是否立即提交事务，默认为True

        # 返回值
        obj: 返回一个对象
        """
        # 首先尝试获取对象
        stmt = self._build_select_stmt(condition, None)
        result = await self._execute(db, stmt)
        
        try:
            obj = result.scalar_one_or_none()
        except MultipleResultsFound as e:
            # 如果找到多条记录，记录详细信息并处理
            table_name = self.model.__tablename__
            logging.error(f"Multiple records found in table {table_name} with condition: {condition}")
            
            # 重新执行查询获取所有匹配的记录
            stmt_all = self._build_select_stmt(condition, None)
            result_all = await self._execute(db, stmt_all)
            objs = result_all.scalars().all()
            
            logging.error(f"Found {len(objs)} records in table {table_name}")
            
            # 记录每条记录的详细信息用于调试
            for i, record in enumerate(objs):
                record_info = {
                    'id': getattr(record, 'id', None),
                    'created_at': getattr(record, 'created_at', None),
                    'updated_at': getattr(record, 'updated_at', None)
                }
                logging.error(f"Record {i+1}: {record_info}")
            
            # 选择最新的记录（优先使用updated_at，其次created_at，最后使用id）
            if hasattr(self.model, 'updated_at'):
                obj = max(objs, key=lambda x: getattr(x, 'updated_at', datetime.min))
                logging.warning(f"Selected record with updated_at: {getattr(obj, 'updated_at', None)}")
            elif hasattr(self.model, 'created_at'):
                obj = max(objs, key=lambda x: getattr(x, 'created_at', datetime.min))
                logging.warning(f"Selected record with created_at: {getattr(obj, 'created_at', None)}")
            else:
                # 如果没有时间字段，选择ID最大的
                obj = max(objs, key=lambda x: getattr(x, 'id', 0))
                logging.warning(f"Selected record with id: {getattr(obj, 'id', None)}")
        
        if obj:
            # 如果对象存在，更新它
            non_empty_values = {key: value for key, value in values.items() if value is not None}
            updated_fields = []
            for key, value in non_empty_values.items():
                old_value = getattr(obj, key, None)
                if old_value != value:
                    setattr(obj, key, value)
                    updated_fields.append(f"{key}: {old_value} -> {value}")
            
            if updated_fields:
                logging.info(f"Updating record in {self.model.__tablename__} with id {getattr(obj, 'id', None)}: {updated_fields}")
            
            db.add(obj)
        else:
            # 如果对象不存在，创建它
            # 合并条件和值
            create_data = {**condition, **values}
            obj = self.model(**create_data)
            db.add(obj)
            logging.info(f"Creating new record in {self.model.__tablename__} with condition: {condition}")
            
        if commit:
            await self._commit(db)
        else:
            await self._flush(db)
        await self._refresh(db, obj)
        return obj

    # 批量存在更新不存在创建
    async def bulk_upsert(self, db: AsyncSession, items: List[Dict[str, Any]], 
                          key_fields: List[str], commit: bool = True) -> List[Any]:
        """
        # 参数说明
        items: 需要批量处理的列表，每个元素是一个字典，表示一个对象
        key_fields: 作为条件的关键字段列表，这些字段将用于唯一确定对象
        commit: 是否立即提交事务，默认为True

        # 返回值
        result_objects: 返回一个列表，包含所有处理后的对象
        """
        result_objects = []
        
        for item in items:
            # 从item中提取key_fields作为条件
            condition = {field: item.get(field) for field in key_fields if field in item}
            # 使用单个upsert操作处理
            obj = await self.upsert(db, condition, item, commit=False)
            result_objects.append(obj)
            
        if commit:
            await self._commit(db)
            
        return result_objects

    async def update_one(self, db: AsyncSession,
                         obj_id: Any,
                         new_values: Dict[str, Any],
                         commit: bool = True) -> Optional[Any]:
        obj = await self._get(db, self.model, obj_id)
        if obj:
            non_empty_new_values = {key: value for key, value in new_values.items() if value is not None}
            for key, value in non_empty_new_values.items():
                setattr(obj, key, value)
            db.add(obj)
            if commit:
                await self._commit(db)
            else:
                await self._flush(db)
            await self._refresh(db, obj)
        return obj

    async def delete_many(self, db: AsyncSession, condition: Optional[Dict[str, Any]] = None,
                          commit: bool = True) -> None:
        stmt = self._build_delete_stmt(condition)
        await self._execute(db, stmt)
        if commit:
            await self._commit(db)

    async def delete_one(self, db: AsyncSession, obj_id: Any, commit: bool = True) -> Optional[Any]:
        obj = await self._get(db, self.model, obj_id)
        if obj:
            obj.disable = 1
            db.add(obj)
            if commit:
                await self._commit(db)
            else:
                await self._flush(db)
        return obj

    async def count(self, db: AsyncSession, condition: Optional[Dict[str, Any]] = None,
                    raw: Optional[List[Any]] = None) -> int:
        selects = [
            func.count(1)
        ]
        conditions = [getattr(self.model, key) == value for key, value in condition.items()] if condition else []
        if raw:
            conditions.extend(raw)
        conditions.append(self.model.disable == 0)
        stmt = select(*selects).where(and_(*conditions))
        result = await self._execute(db, stmt)
        return result.scalar()

    def _build_select_stmt(self, condition: Optional[Dict[str, Any]], raw: Optional[List[Any]],
                           reverse: bool = True, orderby: Optional[List[Any]] = None) -> Any:
        conditions = [getattr(self.model, key) == value for key, value in condition.items()] if condition else []
        if raw:
            conditions.extend(raw)
        if hasattr(self.model, 'disable'):
            conditions.append(self.model.disable == 0)
        if hasattr(self.model, 'Status'):
            conditions.append(self.model.Status == 0)
        
        stmt = select(self.model).where(and_(*conditions))
        if orderby:
            stmt = stmt.order_by(*orderby)
        else:
            if hasattr(self.model, 'create_time'):
                if reverse:
                    stmt = stmt.order_by(desc(self.model.create_time))
                else:
                    stmt = stmt.order_by(self.model.create_time)
        return stmt

    def _build_update_stmt(self, condition: Dict[str, Any], new_values: Dict[str, Any]) -> Any:
        conditions = [getattr(self.model, key) == value for key, value in condition.items() if value is not None]
        stmt = update(self.model).where(and_(*conditions)).values(**new_values)
        return stmt

    def _build_delete_stmt(self, condition: Optional[Dict[str, Any]]) -> Any:
        conditions = [getattr(self.model, key) == value for key, value in condition.items() if
                      value is not None] if condition else []
        stmt = update(self.model).where(and_(*conditions)).values(disable=1)
        return stmt

    # @retry(wait=wait_fixed(2), stop=stop_after_attempt(5))
    async def _commit(self, db: AsyncSession):
        await db.commit()

    # @retry(wait=wait_fixed(2), stop=stop_after_attempt(5))
    async def _flush(self, db: AsyncSession):
        await db.flush()

    # @retry(wait=wait_fixed(2), stop=stop_after_attempt(5))
    async def _refresh(self, db: AsyncSession, obj: Any):
        await db.refresh(obj)

    @retry(wait=wait_fixed(2), stop=stop_after_attempt(5))
    async def _get(self, db: AsyncSession, model: Type, obj_id: Any) -> Optional[Any]:
        return await db.get(model, obj_id)

    # @retry(wait=wait_fixed(2), stop=stop_after_attempt(5))
    async def _execute(self, db: AsyncSession, stmt: Any):
        return await db.execute(stmt)


class CRUDFactory:
    def __init__(self):
        self._instances = {}

    def get_crud(self, model: Type) -> CRUD:
        if model not in self._instances:
            self._instances[model] = CRUD(model)
        return self._instances[model]
