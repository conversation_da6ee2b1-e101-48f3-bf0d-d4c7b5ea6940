from datetime import datetime
from typing import Type, List
from fastapi import APIRouter, Depends
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from utils.db.account_handler import role_required, erp_account
from utils.db.crud_handler import CRUD
from utils.response.response_handler import ApiFailedResponse, ApiSuccessResponse


def generate_crud_routes(
        db_model: Type,
        schema: Type[BaseModel],
        default_db: AsyncSession,
        prefix: str,
        title: str = "表",
        action=None,
        AuthModel: BaseModel = None,
) -> APIRouter:
    router = APIRouter()
    crud_instance = CRUD(db_model)

    if not action or 'post' in action:
        @router.post(f"/{prefix}", description=f"# 新增{title}")
        async def create_item(item: schema, db: AsyncSession = Depends(default_db),
                              user: AuthModel = Depends(role_required([]))):
            i = item.dict()
            if hasattr(db_model, 'create_by'):
                i.update({"create_by": user.uid})
            if hasattr(db_model, 'update_by'):
                i.update({"update_by": user.uid})
            obj = await crud_instance.create(db, **i)
            return await ApiSuccessResponse(obj)

    if not action or 'one' in action:
        @router.get(f"/{prefix}" + "/{pk_id}", description=f"# 查询{title}")
        async def read_item(pk_id: int, db: AsyncSession = Depends(default_db),
                            user: AuthModel = Depends(role_required([]))):
            obj = await crud_instance.get_by_id(db, pk_id)
            if not obj:
                return await ApiFailedResponse("[get]Item not found")
            account_map = {}
            if hasattr(obj, 'create_by') or hasattr(obj, 'update_by') or hasattr(obj, 'account_id'):
                account_objs = await erp_account.get_many(db)
                account_map = {i.id: i.employee_name for i in account_objs}
            if hasattr(obj, 'create_by'):
                obj.create_by_name = account_map.get(obj.create_by)
            if hasattr(obj, 'update_by'):
                obj.update_by_name = account_map.get(obj.update_by)
            if hasattr(obj, 'account_id'):
                obj.account_name = account_map.get(obj.account_id)

            return await ApiSuccessResponse(obj)
    if not action or 'all' in action:
        @router.get(f"/{prefix}", description=f"# 分页查询{title}")
        async def read_items(page: int = None, page_size: int = None, db: AsyncSession = Depends(default_db),
                             user: AuthModel = Depends(role_required([]))):
            if page:
                items = await crud_instance.get_many_with_pagination(db, page, page_size)
                count_items = await crud_instance.get_many(db)
                total_count = len(count_items)
            else:
                items = await crud_instance.get_many(db)
                total_count = None

            # Map account information if needed
            if items and (hasattr(items[0], 'create_by') or hasattr(items[0], 'update_by') or hasattr(items[0],
                                                                                                      'account_id')):
                account_objs = await erp_account.get_many(db)
                account_map = {i.id: i.employee_name for i in account_objs}

                for item in items:
                    if hasattr(item, 'create_by'):
                        item.create_by_name = account_map.get(item.create_by)
                    if hasattr(item, 'update_by'):
                        item.update_by_name = account_map.get(item.update_by)
                    if hasattr(item, 'account_id'):
                        item.account_name = account_map.get(item.account_id)

            # Return appropriate response structure
            response_data = {"data": items}
            if total_count is not None:
                response_data["count"] = total_count

            return await ApiSuccessResponse(response_data)

    if not action or 'put' in action:
        @router.put(f"/{prefix}" + "/{pk_id}", description=f"# 更新{title}")
        async def update_item(pk_id: int, item: schema, db: AsyncSession = Depends(default_db),
                              user: AuthModel = Depends(role_required([]))):
            item = item.dict()
            item.update({"update_time": datetime.now()})
            if hasattr(db_model, 'creator_by'):
                item.update({"update_by": user.uid})
            obj = await crud_instance.update_one(db, pk_id, item)
            if not obj:
                return await ApiFailedResponse("[put]Item not found")
            return await ApiSuccessResponse(obj)
    if not action or 'delete' in action:
        @router.delete(f"/{prefix}" + "/{pk_id}", description=f"# 删除{title}")
        async def delete_item(pk_id: int, db: AsyncSession = Depends(default_db),
                              user: AuthModel = Depends(role_required([]))):
            await crud_instance.delete_one(db, pk_id)
            return await ApiSuccessResponse(True)

        return router
