from collections import defaultdict
from datetime import date, datetime
from decimal import Decimal
from typing import List, Dict, Any, Union
from sqlalchemy import inspect, select, func


class ModelDataHelper:
    @classmethod
    def model_to_dict(cls, model):
        """将 SQLAlchemy ORM 对象转换为字典，同时处理日期格式"""
        data = {}
        for c in inspect(model).mapper.column_attrs:
            value = getattr(model, c.key)
            if isinstance(value, date):
                data[c.key] = value.isoformat()
            elif isinstance(value, datetime):
                data[c.key] = value.isoformat()
            elif isinstance(value, Decimal):
                data[c.key] = float(value)
            else:
                data[c.key] = value

        for rel in inspect(model).mapper.relationships:
            related_value = getattr(model, rel.key)
            if related_value is None:
                data[rel.key] = None
            elif rel.uselist:
                data[rel.key] = [cls.model_to_dict(item) for item in related_value]
            else:
                data[rel.key] = cls.model_to_dict(related_value)

        return data

    @classmethod
    def list_to_tree(
            cls,
            root_pid: Union[int, str],
            data: List[Union[Dict[str, Any], Any]],
            child_field: str = "children",
            parent_field: str = "parent_id",
            id_field: str = "id",
            sort_children: bool = False
    ) -> List[Dict[str, Any]]:
        """
        将列表转换为树结构
        :param root_pid: 根节点的parent_id
        :param data: 数据列表: [{}]
        :param child_field: 子节点列表的键名
        :param parent_field: 父节点ID的键名
        :param id_field: 节点ID的键名
        :param sort_children: 是否对子节点进行排序
        :return: 树形结构的列表
        """

        if not isinstance(root_pid, (int, str)):
            raise ValueError("root_pid should be an integer or string")
        if not isinstance(data, list):
            raise ValueError("data should be a list of dictionaries")
        if not data:
            return []
        if not all(isinstance(item, dict) or hasattr(item, 'as_dict') for item in data):
            raise ValueError("Each item in data should be a dictionary or have an as_dict method")

        # Convert all items to dictionaries if they are not already
        processed_data = [item if isinstance(item, dict) else item.as_dict() for item in data]

        # Create a map of id to item and a map of parent_id to children
        item_map = {}
        children_map = defaultdict(list)
        for item in processed_data:
            item_id = item[id_field]
            parent_id = item.get(parent_field)
            item_map[item_id] = item
            children_map[parent_id].append(item)

            # Check for circular reference
            current_parent = parent_id
            visited = set()
            while current_parent is not None and current_parent != root_pid:
                if current_parent in visited:
                    raise ValueError(f"Circular reference detected for item with id {item_id}")
                visited.add(current_parent)
                current_parent = item_map.get(current_parent, {}).get(parent_field)

        # Build the tree structure
        tree = children_map[root_pid]
        to_process = tree.copy()
        while to_process:
            current = to_process.pop(0)
            current_id = current[id_field]
            children = children_map[current_id]
            if children:
                current[child_field] = children
                if sort_children:
                    current[child_field].sort(key=lambda x: x[id_field])
                to_process.extend(children)

        return tree

    # @classmethod
    # def list_to_tree(
    #         cls,
    #         root_pid: Union[int, str],
    #         data: List[Union[Dict[str, Any], Any]],
    #         child_field: str = "children",
    #         parent_field: str = "parent_id",
    #         id_field: str = "id"
    # ) -> List[Dict[str, Any]]:
    #     """
    #     将列表转换为树结构
    #     :param root_pid: 根节点的parent_id
    #     :param data: 数据列表: [{}]
    #     :param child_field: 子节点列表的键名
    #     :param parent_field: 父节点ID的键名
    #     :param id_field: 节点ID的键名
    #     :return: 树形结构的列表
    #     """
    #
    #     if not isinstance(root_pid, (int, str)):
    #         raise ValueError("root_pid should be an integer or string")
    #     if not isinstance(data, list):
    #         raise ValueError("data should be a list of dictionaries")
    #     if not all(isinstance(item, dict) or hasattr(item, 'as_dict') for item in data):
    #         raise ValueError("Each item in data should be a dictionary or have an as_dict method")
    #
    #     # Convert all items to dictionaries if they are not already
    #
    #     processed_data = [item if isinstance(item, dict) else item.as_dict() for item in data]
    #
    #     # Create a map of id to item
    #     item_map = {item[id_field]: item for item in processed_data}
    #
    #     # Initialize the result list for the root level
    #     tree = []
    #
    #     # Iterate over the data to build the tree structure
    #     for item in processed_data:
    #         parent_id = item.get(parent_field)
    #         if parent_id == root_pid:
    #             tree.append(item)
    #         elif parent_id in item_map:
    #             parent_item = item_map[parent_id]
    #             if child_field not in parent_item:
    #                 parent_item[child_field] = []
    #             parent_item[child_field].append(item)
    #
    #     return tree

    @classmethod
    async def create_increase_num(cls, db, model, field, prefix):
        """
        生成编号
        """
        stmt = (
            select(func.count(getattr(model, field)))
        )

        result = await db.execute(stmt)
        count = result.scalar_one_or_none()
        if count is None:
            count = 0
        return f"{prefix}{str(count + 1).zfill(4)}"

    @classmethod
    def filter_field_module(cls, objs, fields):
        if type(objs) is list:
            new_list = []
            for obj in objs:
                filtered_obj = {field: getattr(obj, field) for field in fields if hasattr(obj, field)}
                new_list.append(filtered_obj)
            return new_list
        else:
            filtered_obj = {field: getattr(objs, field) for field in fields if hasattr(objs, field)}
            return filtered_obj
