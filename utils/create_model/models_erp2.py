# coding: utf-8
from sqlalchemy import Column, DECIMAL, Date, DateTime, Float, JSON, String, TIMESTAMP, Table, Text, text
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, LONGTEXT, MEDIUMTEXT, TINYINT, VARCHAR, YEAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class ErpAccount(Base):
    __tablename__ = 'erp_account'

    id = Column(INTEGER(11), primary_key=True)
    username = Column(String(125))
    password = Column(String(255))
    qy_wechat_userid = Column(String(125), comment='企微userid')
    employee_number = Column(String(11), comment='员工号')
    employee_name = Column(String(255))
    employee_idcard = Column(String(125), comment='身份证')
    employee_gender = Column(INTEGER(11), comment='性别，1 男 2 女')
    employee_education = Column(INTEGER(11), comment='教育程度')
    employee_hire_date = Column(Date, comment='受雇日期')
    employee_leave_date = Column(Date)
    employee_birth = Column(Date, comment='生日')
    employee_city = Column(String(45), comment='所在城市')
    employee_address = Column(String(255), comment='住址')
    employee_emergency = Column(String(125), comment='紧急联系电话')
    employee_status = Column(INTEGER(4), comment='1 在职 2 离职交接中 3 离职 4 试用')
    employee_type = Column(INTEGER(4), comment='1 全职 2 兼职')
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    qy_wechat_openid = Column(String(125), comment='微信公众号openid')
    qy_wechat_nickname = Column(String(125))
    qy_wechat_department = Column(JSON)
    qy_wechat_direct_leader = Column(JSON)
    qy_wechat_position = Column(String(45))
    disable = Column(INTEGER(4), server_default=text("'0'"))
    sync_status = Column(INTEGER(4), server_default=text("'1'"), comment='1 等待同步 2 同步成功 3 同步失败')
    sync_msg = Column(String(255), comment='同步消息')
    is_teacher = Column(INTEGER(11), comment='是否是老师')
    level_id = Column(INTEGER(4), comment='职级')
    employee_major = Column(String(45), comment='专业')
    leave_reason = Column(INTEGER(4))
    leave_reason_detail = Column(String(255))
    successor_account_id = Column(INTEGER(11))
    attachment = Column(JSON)
    avatar = Column(String(255))
    old_erp_id = Column(INTEGER(11))


class ErpAccountCopy1(Base):
    __tablename__ = 'erp_account_copy1'

    id = Column(INTEGER(11), primary_key=True)
    username = Column(String(125))
    password = Column(String(255))
    qy_wechat_userid = Column(String(125), comment='企微userid')
    employee_number = Column(String(11), comment='员工号')
    employee_name = Column(String(255))
    employee_idcard = Column(String(125), comment='身份证')
    employee_gender = Column(INTEGER(11), comment='性别，1 男 2 女')
    employee_education = Column(INTEGER(11), comment='教育程度')
    employee_hire_date = Column(Date, comment='受雇日期')
    employee_leave_date = Column(Date)
    employee_birth = Column(Date, comment='生日')
    employee_city = Column(String(45), comment='所在城市')
    employee_address = Column(String(255), comment='住址')
    employee_emergency = Column(String(125), comment='紧急联系电话')
    employee_status = Column(INTEGER(4), comment='1 在职 2 离职交接中 3 离职 4 试用')
    employee_type = Column(INTEGER(4), comment='1 全职 2 兼职')
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    qy_wechat_openid = Column(String(125), comment='微信公众号openid')
    qy_wechat_nickname = Column(String(125))
    qy_wechat_department = Column(JSON)
    qy_wechat_direct_leader = Column(JSON)
    qy_wechat_position = Column(String(45))
    disable = Column(INTEGER(4), server_default=text("'0'"))
    sync_status = Column(INTEGER(4), server_default=text("'1'"), comment='1 等待同步 2 同步成功 3 同步失败')
    sync_msg = Column(String(255), comment='同步消息')
    is_teacher = Column(INTEGER(11), comment='是否是老师')
    level_id = Column(INTEGER(4), comment='职级')
    employee_major = Column(String(45), comment='专业')
    leave_reason = Column(INTEGER(4))
    leave_reason_detail = Column(String(255))
    successor_account_id = Column(INTEGER(11))
    attachment = Column(JSON)
    avatar = Column(String(255))
    old_erp_id = Column(INTEGER(11))


class ErpAccountDepartment(Base):
    __tablename__ = 'erp_account_department'

    id = Column(INTEGER(11), primary_key=True, comment='用户部门表主键Id')
    account_id = Column(INTEGER(11), server_default=text("'0'"), comment='用户Id(RB_Account表Id)')
    account_type = Column(INTEGER(11), server_default=text("'1'"), comment='账号类型')
    dept_id = Column(INTEGER(11), server_default=text("'0'"), comment='部门Id')
    create_by = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_by = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    disable = Column(INTEGER(11), server_default=text("'0'"), comment='状态')
    is_main_dept = Column(INTEGER(11), server_default=text("'0'"), comment='是否为主部门(1-是,0不是)')


class ErpAccountLevel(Base):
    __tablename__ = 'erp_account_level'

    id = Column(INTEGER(11), primary_key=True)
    level_id = Column(INTEGER(11))
    account_id = Column(INTEGER(11))
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpAccountMapping(Base):
    __tablename__ = 'erp_account_mapping'
    __table_args__ = {'comment': '旧系统账号与新系统账号映射表'}

    id = Column(INTEGER(11), primary_key=True)
    old_account_id = Column(INTEGER(11), nullable=False, comment='旧系统rb_account表的Id')
    new_account_id = Column(INTEGER(11), nullable=False, comment='新系统erp_account表的id')
    account_type = Column(INTEGER(11), comment='账号类型(1-管理端，2-教师端，3-助教，4-学生)')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))


class ErpAccountRole(Base):
    __tablename__ = 'erp_account_role'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    role_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpAccountTeacher(Base):
    __tablename__ = 'erp_account_teacher'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    teacher_grade = Column(INTEGER(4))
    teacher_subject = Column(INTEGER(4))
    teacher_certification = Column(String(125))
    teacher_fee = Column(Float(10))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    teacher_avatar = Column(String(255))
    teacher_image = Column(String(255))
    teacher_qr_img = Column(String(255))
    teacher_desc = Column(LONGTEXT)
    teacher_tag = Column(JSON)
    class_fee = Column(Float(10), comment='课时费')
    class_fee_type = Column(INTEGER(4), comment='课时费类型 1 标准值 2 比例值')
    is_show = Column(INTEGER(4), comment='是否展示')
    classin_sync = Column(INTEGER(4))
    classin_uid = Column(INTEGER(11))


class ErpAccountTeacherLog(Base):
    __tablename__ = 'erp_account_teacher_log'

    id = Column(INTEGER(11), primary_key=True)
    teacher_id = Column(INTEGER(11))
    content = Column(String(500))
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    update_time = Column(DateTime)
    create_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpApprovalAccountRole(Base):
    __tablename__ = 'erp_approval_account_role'

    id = Column(INTEGER(11), primary_key=True)
    role_id = Column(INTEGER(11))
    account_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpApprovalCc(Base):
    __tablename__ = 'erp_approval_cc'

    id = Column(INTEGER(11), primary_key=True)
    process_id = Column(INTEGER(11))
    cc_type = Column(INTEGER(11), comment='抄送类型：1 直接上级、2 连续多级上级、3 指定成员、4 指定岗位')
    cc_value = Column(String(255), comment='抄送人账号，存储连续多级上级的级数或指定成员ID、岗位的ID')
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))


class ErpApprovalNode(Base):
    __tablename__ = 'erp_approval_node'

    id = Column(INTEGER(11), primary_key=True)
    process_id = Column(INTEGER(11), comment='关联的流程Id')
    node_desc = Column(String(255), comment='节点名称或描述')
    order = Column(INTEGER(4), comment='节点顺序')
    action = Column(INTEGER(4), comment='节点动作: 1  常规 2 财务')
    approval_type = Column(INTEGER(4), comment='审批人类型：1 连续多级上级、2 指定成员、3 指定岗位')
    approval_value = Column(String(255), comment='审批人账号，存储连续多级上级的级数或指定成员ID、岗位的ID')
    approval_mode = Column(String(255), comment='审批模式：1 或签 2 会签')
    involve_accounting = Column(INTEGER(4), comment='是否涉及出入账')
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))


class ErpApprovalProcess(Base):
    __tablename__ = 'erp_approval_process'

    id = Column(INTEGER(11), primary_key=True)
    process_name = Column(String(255))
    process_type = Column(INTEGER(4), comment='1 收入 2 支出 3 收支相抵 4 不入账')
    process_desc = Column(String(1000))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    disable = Column(INTEGER(4))
    cost_type_ids = Column(JSON)


class ErpApprovalProcessCampus(Base):
    __tablename__ = 'erp_approval_process_campus'

    id = Column(INTEGER(11), primary_key=True)
    campus_id = Column(INTEGER(4))
    process_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpApprovalRole(Base):
    __tablename__ = 'erp_approval_role'

    id = Column(INTEGER(11), primary_key=True)
    role_name = Column(String(255))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpBankAccount(Base):
    __tablename__ = 'erp_bank_account'

    id = Column(INTEGER(4), primary_key=True)
    campus_id = Column(INTEGER(4))
    account_alias = Column(String(125))
    account_type = Column(INTEGER(4), comment='关联类型表')
    account_number = Column(String(125), comment='账户号 | 学生号')
    bank_name = Column(String(45), comment='银行')
    bank_sub_name = Column(String(125), comment='开户支行')
    account_holder = Column(String(45), comment='持有人')
    balance = Column(Float(10), comment='余额')
    currency_type = Column(INTEGER(4), comment='1 人民币')
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    is_active = Column(INTEGER(4), comment='是否激活')
    cmb_merchant_id = Column(String(125), comment='招行商户号')
    default_account_type = Column(INTEGER(4), comment='1 银行账户 2 现金 3 虚拟账户')


class ErpBankAccountLog(Base):
    __tablename__ = 'erp_bank_account_log'

    id = Column(INTEGER(11), primary_key=True)
    bill_account_id = Column(INTEGER(11))
    change_type = Column(INTEGER(4), comment='1 增加 2 减少')
    amount = Column(Float(10), comment='数量')
    desc = Column(String(1000), comment='描述')
    create_by = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    rest_balance = Column(Float(10), comment='剩余金额')
    receipt_id = Column(INTEGER(11), comment='关联单据')


class ErpBankAccountReport(Base):
    __tablename__ = 'erp_bank_account_report'

    id = Column(INTEGER(11), primary_key=True)
    bill_account_id = Column(INTEGER(11))
    statistic_date = Column(Date)
    last_balance = Column(DECIMAL(10, 2))
    current_balance = Column(String(255))
    pay_money = Column(DECIMAL(10, 2))
    received_money = Column(DECIMAL(10, 2))
    wait_pay = Column(DECIMAL(10, 2))
    pay_num = Column(INTEGER(4))
    received_num = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpBankAccountType(Base):
    __tablename__ = 'erp_bank_account_type'

    id = Column(INTEGER(11), primary_key=True)
    account_type_name = Column(String(45))
    is_public = Column(INTEGER(4))
    is_virtual = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpCampus(Base):
    __tablename__ = 'erp_campus'

    id = Column(INTEGER(11), primary_key=True)
    campus_name = Column(String(45))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpChecking(Base):
    __tablename__ = 'erp_checking'

    id = Column(INTEGER(11), primary_key=True)
    title = Column(String(255))
    ym = Column(INTEGER(4))
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpCheckingRecords(Base):
    __tablename__ = 'erp_checking_records'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    employee_number = Column(String(45))
    employee_name = Column(String(255), comment='员工姓名')
    check_date = Column(Date, comment='考勤日期')
    day_period = Column(INTEGER(4), comment='1 上午 2 下午')
    check_value = Column(String(45), comment='考勤结果')
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    ym = Column(INTEGER(6), comment='年月')


class ErpCheckingReport(Base):
    __tablename__ = 'erp_checking_report'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11), nullable=False)
    ym = Column(INTEGER(11), nullable=False)
    employee_name = Column(String(255), nullable=False)
    employee_number = Column(String(50), nullable=False)
    salary_base = Column(DECIMAL(10, 2), nullable=False)
    salary_performance = Column(DECIMAL(10, 2), nullable=False)
    late_within_10_min = Column(INTEGER(11), nullable=False)
    late_10_to_30_min = Column(INTEGER(11), nullable=False)
    absent_without_notice = Column(INTEGER(11), nullable=False)
    early_leave = Column(INTEGER(11))
    forgot_to_check_in = Column(INTEGER(11))
    sick_leave = Column(INTEGER(11))
    personal_leave = Column(INTEGER(11))
    overtime = Column(INTEGER(11))
    other_paid_leave = Column(INTEGER(11))
    enterprise_name = Column(String(255))
    forgot_to_check_in_deduction = Column(DECIMAL(10, 2))
    late_deduction = Column(DECIMAL(10, 2))
    early_leave_deduction = Column(DECIMAL(10, 2))
    absent_without_notice_deduction = Column(DECIMAL(10, 2))
    personal_leave_deduction = Column(DECIMAL(10, 2))
    sick_leave_deduction = Column(DECIMAL(10, 2))
    sick_leave_addition = Column(DECIMAL(10, 2))
    actual_attendance_days = Column(DECIMAL(5, 2))
    actual_salary = Column(DECIMAL(10, 2))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpClass(Base):
    __tablename__ = 'erp_class'

    id = Column(INTEGER(11), primary_key=True)
    course_id = Column(INTEGER(11), nullable=False)
    class_name = Column(String(255), nullable=False)
    class_capacity = Column(INTEGER(11), nullable=False)
    pre_enrollment = Column(INTEGER(11), nullable=False)
    teacher_id = Column(INTEGER(11), nullable=False)
    start_date = Column(DateTime, nullable=False)
    classroom_id = Column(INTEGER(11), nullable=False)
    use_standard_full_rate = Column(INTEGER(1), nullable=False, server_default=text("'1'"))
    planning_class_times = Column(INTEGER(11), nullable=False)
    scheduling_method = Column(INTEGER(1), nullable=False, server_default=text("'1'"))
    is_shelf_miniprogram = Column(INTEGER(1), nullable=False, server_default=text("'1'"))
    miniprogram_start_enrollment_time = Column(DateTime)
    miniprogram_end_enrollment_time = Column(DateTime)
    qwechat_id = Column(String(255))
    classin_id = Column(String(255))
    enrollment_conditions = Column(Text)
    classin_sync = Column(INTEGER(1), nullable=False, server_default=text("'0'"))
    hourly_tuition_ratio = Column(DECIMAL(5, 2), nullable=False, server_default=text("'0.00'"))
    create_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"))
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))
    disable = Column(INTEGER(4), nullable=False, server_default=text("'0'"))
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    class_status = Column(INTEGER(4), comment='班级状态0 未上架 1 未开始 2 上课中 3 已结课')
    audit_status = Column(INTEGER(4), comment='0 未审核 1 已通过 2 已驳回')
    comments = Column(LONGTEXT, comment='备注')
    qr_code = Column(String(255))
    qwechat_name = Column(String(125))
    qwechat_config_id = Column(String(255))
    assistant_teacher_id = Column(INTEGER(11))


class ErpClassChangeRules(Base):
    __tablename__ = 'erp_class_change_rules'
    __table_args__ = {'comment': '未付费转班规则'}

    id = Column(INTEGER(11), primary_key=True, comment='主键ID')
    p_grade_id = Column(INTEGER(11), nullable=False, comment='年级大类ID')
    grade_id = Column(INTEGER(11), nullable=False, comment='源年级ID')
    subject_id = Column(INTEGER(11), nullable=False, comment='源学科ID')
    cate_id = Column(INTEGER(11), nullable=False, comment='源班型ID')
    target_cate_ids = Column(String(255), nullable=False, comment='目标班型IDs,逗号分隔')
    target_subject_ids = Column(String(255), nullable=False, comment='目标学科IDs,逗号分隔')
    target_grade_ids = Column(String(255), nullable=False, comment='目标年级IDs,逗号分隔')
    description = Column(String(255), comment='规则描述')
    is_enabled = Column(TINYINT(1), nullable=False, server_default=text("'1'"), comment='是否启用:1启用,0禁用')
    create_by = Column(INTEGER(11), comment='创建者ID')
    update_by = Column(INTEGER(11), comment='更新者ID')
    create_time = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='是否删除')


class ErpClassChangeRulesPaid(Base):
    __tablename__ = 'erp_class_change_rules_paid'
    __table_args__ = {'comment': '已付费转班规则'}

    id = Column(INTEGER(11), primary_key=True, comment='主键ID')
    p_grade_id = Column(INTEGER(11), nullable=False, comment='年级大类ID')
    grade_id = Column(INTEGER(11), nullable=False, comment='源年级ID')
    subject_id = Column(INTEGER(11), nullable=False, comment='源学科ID')
    cate_id = Column(INTEGER(11), nullable=False, comment='源班型ID')
    target_cate_ids = Column(String(255), nullable=False, comment='目标班型IDs,逗号分隔')
    target_subject_ids = Column(String(255), nullable=False, comment='目标学科IDs,逗号分隔')
    target_grade_ids = Column(String(255), nullable=False, comment='目标年级IDs,逗号分隔')
    description = Column(String(255), comment='规则描述')
    is_enabled = Column(TINYINT(1), nullable=False, server_default=text("'1'"), comment='是否启用:1启用,0禁用')
    create_by = Column(INTEGER(11), comment='创建者ID')
    update_by = Column(INTEGER(11), comment='更新者ID')
    create_time = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='是否删除')


class ErpClassChecking(Base):
    __tablename__ = 'erp_class_checking'

    id = Column(INTEGER(11), primary_key=True)
    class_id = Column(INTEGER(11))
    class_plan_id = Column(INTEGER(11))
    check_status = Column(INTEGER(4))
    order_student_id = Column(INTEGER(11))
    teacher_id = Column(INTEGER(11))
    class_room_id = Column(INTEGER(11))
    stu_type = Column(INTEGER(4), comment='1 正常 2 转班')
    is_online = Column(INTEGER(4), comment='1 线上')
    time_duration = Column(Float(10))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(5))


class ErpClassCopy1(Base):
    __tablename__ = 'erp_class_copy1'

    id = Column(INTEGER(11), primary_key=True)
    course_id = Column(INTEGER(11), nullable=False)
    class_name = Column(String(255), nullable=False)
    class_capacity = Column(INTEGER(11), nullable=False)
    pre_enrollment = Column(INTEGER(11), nullable=False)
    teacher_id = Column(INTEGER(11), nullable=False)
    start_date = Column(DateTime, nullable=False)
    classroom_id = Column(INTEGER(11), nullable=False)
    use_standard_full_rate = Column(INTEGER(1), nullable=False, server_default=text("'1'"))
    planning_class_times = Column(INTEGER(11), nullable=False)
    scheduling_method = Column(INTEGER(1), nullable=False, server_default=text("'1'"))
    is_shelf_miniprogram = Column(INTEGER(1), nullable=False, server_default=text("'1'"))
    miniprogram_start_enrollment_time = Column(DateTime)
    miniprogram_end_enrollment_time = Column(DateTime)
    qwechat_id = Column(String(255))
    classin_id = Column(String(255))
    enrollment_conditions = Column(Text)
    classin_sync = Column(INTEGER(1), nullable=False, server_default=text("'0'"))
    hourly_tuition_ratio = Column(DECIMAL(5, 2), nullable=False, server_default=text("'0.00'"))
    create_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"))
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))
    disable = Column(INTEGER(4), nullable=False, server_default=text("'0'"))
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    class_status = Column(INTEGER(4), comment='班级状态0 未上架 1 未开始 2 上课中 3 已结课')
    audit_status = Column(INTEGER(4), comment='0 未审核 1 已通过 2 已驳回')
    comments = Column(LONGTEXT, comment='备注')


class ErpClassLog(Base):
    __tablename__ = 'erp_class_log'

    id = Column(INTEGER(11), primary_key=True)
    objective_id = Column(INTEGER(11))
    log_content = Column(String(500))
    log_type = Column(INTEGER(4))
    log_name = Column(String(125))
    create_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpClassPlan(Base):
    __tablename__ = 'erp_class_plan'

    id = Column(INTEGER(11), primary_key=True)
    class_id = Column(INTEGER(11))
    room_id = Column(INTEGER(11))
    teacher_id = Column(INTEGER(11))
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    finish = Column(INTEGER(4))
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    time_duration = Column(Float(4))
    classin_id = Column(String(11))
    classin_activity_id = Column(INTEGER(11))
    classin_name = Column(String(125))
    classin_live_url = Column(String(1000))
    classin_live_info = Column(JSON)
    classin_unit_id = Column(INTEGER(11))


class ErpClassRenewRules(Base):
    __tablename__ = 'erp_class_renew_rules'
    __table_args__ = {'comment': '续报规则表'}

    id = Column(INTEGER(11), primary_key=True, comment='主键ID')
    create_by = Column(INTEGER(11), comment='创建者ID')
    update_by = Column(INTEGER(11), comment='更新者ID')
    create_time = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='是否删除')
    current_class_id = Column(INTEGER(11))
    current_teacher_id = Column(INTEGER(11))
    term_id = Column(INTEGER(11))
    next_class_id = Column(INTEGER(11))
    next2_class_id = Column(INTEGER(11))
    start_time = Column(DateTime, comment='规则生效时间，也是自动生成订单的时间')
    end_time = Column(DateTime, comment='规则截止时间，报名也截止')
    signup_start = Column(DateTime, comment='可报名开始时间')
    created_order = Column(INTEGER(4), comment='是否已生成订单')
    new_msg = Column(String(500))
    run_status = Column(INTEGER(4), comment='0 未运行 1 成功 2 失败')


class ErpClassRescheduleRules(Base):
    __tablename__ = 'erp_class_reschedule_rules'
    __table_args__ = {'comment': '调课规则'}

    id = Column(INTEGER(11), primary_key=True, comment='主键ID')
    p_grade_id = Column(INTEGER(11), nullable=False, comment='年级大类ID')
    grade_id = Column(INTEGER(11), nullable=False, comment='源年级ID')
    subject_id = Column(INTEGER(11), nullable=False, comment='源学科ID')
    cate_id = Column(INTEGER(11), nullable=False, comment='源班型ID')
    target_cate_ids = Column(String(255), nullable=False, comment='目标班型IDs,逗号分隔')
    target_subject_ids = Column(String(255), nullable=False, comment='目标学科IDs,逗号分隔')
    target_grade_ids = Column(String(255), nullable=False, comment='目标年级IDs,逗号分隔')
    description = Column(String(255), comment='规则描述')
    is_enabled = Column(TINYINT(1), nullable=False, server_default=text("'1'"), comment='是否启用:1启用,0禁用')
    create_by = Column(INTEGER(11), comment='创建者ID')
    update_by = Column(INTEGER(11), comment='更新者ID')
    create_time = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='是否删除')


class ErpClassRescheduling(Base):
    __tablename__ = 'erp_class_rescheduling'

    id = Column(INTEGER(11), primary_key=True)
    order_student_id = Column(INTEGER(11))
    old_plan_id = Column(INTEGER(11))
    new_plan_id = Column(INTEGER(11))
    reschedule_reason = Column(String(255))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))
    is_inner = Column(INTEGER(4))


class ErpClassTransfor(Base):
    __tablename__ = 'erp_class_transfor'

    id = Column(INTEGER(11), primary_key=True)
    old_order_student_id = Column(INTEGER(11))
    new_order_student_id = Column(INTEGER(11))
    old_class_id = Column(INTEGER(11))
    new_class_id = Column(INTEGER(11))
    transfor_reason = Column(String(255))
    transfor_num = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))
    is_inner = Column(INTEGER(4))


class ErpClassinConfig(Base):
    __tablename__ = 'erp_classin_config'

    Id = Column(INTEGER(11), primary_key=True)
    SID = Column(String(255), comment='classIn API_ID')
    SECRET = Column(String(255), comment='秘钥')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='归属集团ID')
    ClassroomSettingId = Column(INTEGER(11), server_default=text("'0'"), comment='教室设置 ID （课程使用）')
    AllowAddFriend = Column(INTEGER(11), server_default=text("'0'"), comment='是否允许班级成员在群里互相添加好友，0=不允许，1=允许   (课程使用)')
    AllowStudentModifyNickname = Column(INTEGER(11), server_default=text("'0'"), comment='是否允许学生在群里修改其班级昵称，0=不允许，1=允许  (课程使用)')
    TeachMode = Column(INTEGER(11), server_default=text("'1'"), comment='教学模式，1=在线教室，2=智慧教室')
    IsAutoOnstage = Column(INTEGER(11), server_default=text("'0'"), comment='0=自动，1=不自动，默认为0，所有非1的数字,都会当成0处理\t学生进入教室是否自动上台')
    SeatNum = Column(INTEGER(11), server_default=text("'6'"), comment='默认为6，最大上限值调整为12\t学生上台数')
    IsHd = Column(INTEGER(11), server_default=text("'0'"), comment='0=非高清，1=高清，2=全高清，默认为0，除了1和2，所有非0的数字,都会当成0处理\t是否高清\t目前仅支持 1V1 或 1V6 高清、全高清')
    IsDc = Column(INTEGER(11), server_default=text("'0'"), comment='双摄模式，是否开启副摄像头，0=不开启，3=开启全高清副摄像头')
    Record = Column(INTEGER(11), server_default=text("'1'"), comment='录课(0 关闭，1 开启)')
    RecordScene = Column(INTEGER(11), server_default=text("'0'"), comment='录制现场(0 关闭，1 开启)')
    Live = Column(INTEGER(11), server_default=text("'0'"), comment='网页直播(0 关闭，1 开启)')
    Replay = Column(INTEGER(11), server_default=text("'0'"), comment='网页回放(0 关闭，1 开启)')
    WatchByLogin = Column(INTEGER(11), server_default=text("'0'"), comment='只允许登录ClassIn账号后才可观看，未登录不可观看，0=未开启，1=开启')
    AllowUnloggedChat = Column(INTEGER(11), server_default=text("'0'"), comment='允许未登录用户参与直播聊天和点赞，0=不允许，1=允许')
    Enable = Column(INTEGER(11), server_default=text("'2'"), comment='是否启用  1是  其他否')


class ErpCompetitionCenterBooking(Base):
    __tablename__ = 'erp_competition_center_booking'

    id = Column(INTEGER(11), primary_key=True)
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    stu_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    booking_date = Column(Date)


class ErpCompetitionCenterChecking(Base):
    __tablename__ = 'erp_competition_center_checking'

    id = Column(INTEGER(11), primary_key=True)
    booking_id = Column(INTEGER(11))
    check_status = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpCompetitionCenterStu(Base):
    __tablename__ = 'erp_competition_center_stu'

    id = Column(INTEGER(11), primary_key=True)
    stu_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))


class ErpCompetitionCenterStuTarget(Base):
    __tablename__ = 'erp_competition_center_stu_target'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    stu_name = Column(String(45))
    stu_id = Column(INTEGER(11), primary_key=True, nullable=False)
    stu_tel = Column(String(125))
    target_date = Column(DateTime)
    plan_period = Column(String(125))
    target_index = Column(INTEGER(4))
    target_value = Column(String(500))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))
    comments = Column(String(500))


class ErpCompetitionLog(Base):
    __tablename__ = 'erp_competition_log'

    id = Column(INTEGER(11), primary_key=True)
    competition_stu_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    content = Column(String(500))


class ErpCompetitionSituation(Base):
    __tablename__ = 'erp_competition_situation'

    id = Column(INTEGER(11), primary_key=True)
    competition_stu_id = Column(INTEGER(11))
    part_type = Column(INTEGER(4))
    score = Column(Float(10))
    comments = Column(String(1000))
    suggestion = Column(String(1000))
    book_detail = Column(JSON)
    update_time = Column(DateTime)
    create_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpCompetitionStu(Base):
    __tablename__ = 'erp_competition_stu'

    id = Column(INTEGER(11), primary_key=True)
    stu_name = Column(String(45))
    stu_tel = Column(String(125))
    stu_grade = Column(String(45))
    stu_target = Column(String(125))
    update_time = Column(DateTime)
    create_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpCostTypeBind(Base):
    __tablename__ = 'erp_cost_type_bind'

    id = Column(INTEGER(4), primary_key=True)
    receipt_category_name = Column(String(255))
    workflow_id = Column(INTEGER(4))
    default_cost_type_id = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpCourse(Base):
    __tablename__ = 'erp_course'

    id = Column(INTEGER(11), primary_key=True)
    course_name = Column(String(255))
    year = Column(YEAR(4))
    term_id = Column(INTEGER(11))
    subject_id = Column(INTEGER(11))
    grade_id = Column(INTEGER(11))
    type_id = Column(INTEGER(11))
    category_id = Column(INTEGER(11))
    course_cover = Column(String(255))
    course_introduction_page = Column(String(255))
    allow_refund = Column(INTEGER(4), server_default=text("'0'"))
    allow_repeated_purchase = Column(INTEGER(4), server_default=text("'0'"))
    cost_calculation_plan = Column(INTEGER(4), comment='1 固定金额 2 标准课时费（倍数*教师课时费）')
    course_coefficient = Column(DECIMAL(10, 2), comment='金额或倍数')
    pricing_plan = Column(INTEGER(4))
    original_price = Column(DECIMAL(10, 2))
    sale_price = Column(DECIMAL(10, 2))
    number_of_lessons = Column(INTEGER(11))
    bound_textbook_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))
    disable = Column(INTEGER(4), server_default=text("'0'"))
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    is_term_plan = Column(INTEGER(4))
    is_enable = Column(INTEGER(4))
    p_grade_id = Column(INTEGER(11))


class ErpCourseCategory(Base):
    __tablename__ = 'erp_course_category'

    id = Column(INTEGER(11), primary_key=True)
    category_name = Column(String(125))
    category_desc = Column(String(255))
    category_objective = Column(String(255))
    stu_type_desc = Column(String(255))
    grade_id = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    is_enable = Column(INTEGER(4))


class ErpCourseCopy1(Base):
    __tablename__ = 'erp_course_copy1'

    id = Column(INTEGER(11), primary_key=True)
    course_name = Column(String(255))
    year = Column(YEAR(4))
    term_id = Column(INTEGER(11))
    subject_id = Column(INTEGER(11))
    grade_id = Column(INTEGER(11))
    type_id = Column(INTEGER(11))
    category_id = Column(INTEGER(11))
    course_cover = Column(String(255))
    course_introduction_page = Column(String(255))
    allow_refund = Column(INTEGER(4), server_default=text("'0'"))
    allow_repeated_purchase = Column(INTEGER(4), server_default=text("'0'"))
    cost_calculation_plan = Column(INTEGER(4), comment='1 固定金额 2 标准课时费（倍数*教师课时费）')
    course_coefficient = Column(DECIMAL(10, 2), comment='金额或倍数')
    pricing_plan = Column(INTEGER(4))
    original_price = Column(DECIMAL(10, 2))
    sale_price = Column(DECIMAL(10, 2))
    number_of_lessons = Column(INTEGER(11))
    bound_textbook_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))
    disable = Column(INTEGER(4), server_default=text("'0'"))
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    is_term_plan = Column(INTEGER(4))
    is_enable = Column(INTEGER(4))
    p_grade_id = Column(INTEGER(11))


class ErpCourseLabel(Base):
    __tablename__ = 'erp_course_label'

    id = Column(INTEGER(11), primary_key=True)
    label_id = Column(INTEGER(4))
    course_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))


class ErpCourseLog(Base):
    __tablename__ = 'erp_course_log'

    id = Column(INTEGER(11), primary_key=True)
    objective_id = Column(INTEGER(11))
    log_content = Column(String(500))
    log_type = Column(INTEGER(4))
    log_name = Column(String(125))
    create_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpCourseOutline(Base):
    __tablename__ = 'erp_course_outline'

    id = Column(INTEGER(11), primary_key=True)
    outline_name = Column(String(45))
    outline_desc = Column(String(255))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    course_id = Column(INTEGER(11))


class ErpCourseTerm(Base):
    __tablename__ = 'erp_course_term'

    id = Column(INTEGER(11), primary_key=True)
    term_name = Column(String(255))
    year = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    term_type = Column(INTEGER(4))


class ErpCourseTextbook(Base):
    __tablename__ = 'erp_course_textbook'

    id = Column(INTEGER(11), primary_key=True)
    name = Column(VARCHAR(125))
    unit = Column(VARCHAR(45))
    origin_price = Column(DECIMAL(10, 2))
    sale_price = Column(DECIMAL(10, 2))
    disable = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    category_id = Column(INTEGER(11))


class ErpDepartment(Base):
    __tablename__ = 'erp_department'

    id = Column(INTEGER(11), primary_key=True, comment='部门编号(主键)')
    dept_name = Column(VARCHAR(100), server_default=text("''"), comment='部门名称')
    dept_tel = Column(VARCHAR(50), server_default=text("''"), comment='联系电话')
    parent_id = Column(INTEGER(11), server_default=text("'0'"), comment='上级部门编号')
    disable = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态(1-删除)')
    create_by = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_by = Column(INTEGER(11), server_default=text("'0'"), comment='更新人')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    dept_level = Column(INTEGER(11), server_default=text("'0'"), comment='部门层级')
    dept_sort = Column(INTEGER(11), server_default=text("'0'"), comment='部门排序')
    is_company = Column(INTEGER(11), server_default=text("'0'"), comment='是否问公司(0-不是,1-是)')
    qy_wechat_dept_id = Column(INTEGER(11), server_default=text("'0'"), comment='企业微信部门id')
    manager_id = Column(INTEGER(11), comment='部门负责人')


class ErpEeoAnswerSheetScores(Base):
    __tablename__ = 'erp_eeo_answer_sheet_scores'
    __table_args__ = {'comment': '答题卡成绩表'}

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(INTEGER(11), comment='机构ID')
    course_id = Column(INTEGER(11), comment='课程ID')
    course_name = Column(String(128), comment='课程名称')
    activity_id = Column(INTEGER(11), comment='活动ID')
    activity_name = Column(String(128), comment='活动名称')
    unit_id = Column(INTEGER(11), comment='单元ID')
    unit_name = Column(String(128), comment='单元名称')
    class_id = Column(INTEGER(11), comment='班级ID')
    student_uid = Column(INTEGER(11), comment='学生UID')
    student_name = Column(String(64), comment='学生姓名')
    student_account = Column(String(64), comment='学生账号')
    teacher_uid = Column(INTEGER(11), comment='教师UID')
    teacher_name = Column(String(64), comment='教师姓名')
    teacher_account = Column(String(64), comment='教师账号')
    maximum_score = Column(Float, comment='最大分值')
    score = Column(Float, comment='得分')
    student_scoring_rate = Column(DECIMAL(10, 4), comment='得分率')
    answer_duration = Column(INTEGER(11), comment='答题时长(秒)')
    submission_time = Column(INTEGER(11), comment='提交时间')
    correction_time = Column(INTEGER(11), comment='批改时间')
    disable = Column(TINYINT(1), nullable=False, server_default=text("'0'"), comment='删除状态：0正常，1已删除')
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class ErpEeoClassInfoChange(Base):
    __tablename__ = 'erp_eeo_class_info_change'
    __table_args__ = {'comment': 'EEO课节信息改动记录表'}

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(32), nullable=False, comment='机构ID')
    op_type = Column(INTEGER(11), comment='操作类型 1:增加，2:删除，3:修改')
    op_time = Column(BIGINT(20), comment='操作时间')
    uid = Column(String(32), comment='操作人UID')
    course_id = Column(String(32), comment='课程ID')
    class_id = Column(String(32), comment='课节ID')
    op_source = Column(INTEGER(11), comment='操作来源 1:EEO后台，2:classIn客户端，4:机构API')
    class_name = Column(String(255), comment='课节名称')
    course_name = Column(String(255), comment='课程名称')
    begin_time = Column(BIGINT(20), comment='课节开始时间戳')
    end_time = Column(BIGINT(20), comment='课节结束时间戳')
    class_type = Column(INTEGER(11), comment='课节类型，1=标准课 2=公开课 3=双师课')
    class_status = Column(INTEGER(11), comment='课节状态，1=还没开课 2=上课中 3=上课结束 4=已取消')
    seat_num = Column(INTEGER(11), comment='上台人数')
    is_auto_onstage = Column(INTEGER(11), comment='是否自动上台，0=自动，1=不自动')
    teacher_name = Column(String(64), comment='授课教师姓名')
    teacher_uid = Column(String(32), comment='授课教师UID')
    teacher_no = Column(String(64), comment='授课教师工号')
    assistant_teacher_infos = Column(JSON, comment='联席教师信息')
    label_infos = Column(JSON, comment='标签信息')
    processed = Column(TINYINT(1), server_default=text("'0'"), comment='处理状态：0未处理，1已处理')
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class ErpEeoClassStudentChange(Base):
    __tablename__ = 'erp_eeo_class_student_change'
    __table_args__ = {'comment': 'EEO课节学生信息改动记录表'}

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(32), nullable=False, comment='机构ID')
    op_type = Column(INTEGER(11), comment='操作类型 1:增加，2:删除，3:修改')
    op_time = Column(BIGINT(20), comment='操作时间')
    uid = Column(String(32), comment='操作人UID')
    course_id = Column(String(32), comment='课程ID')
    class_id = Column(String(32), comment='课节ID')
    op_source = Column(INTEGER(11), comment='操作来源 1:EEO后台，2:classIn客户端，4:机构API')
    class_name = Column(String(255), comment='课节名称')
    student_infos = Column(JSON, comment='学生信息')
    processed = Column(TINYINT(1), server_default=text("'0'"), comment='处理状态：0未处理，1已处理')
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class ErpEeoCourseClass(Base):
    __tablename__ = 'erp_eeo_course_class'

    id = Column(INTEGER(11), primary_key=True)
    eeo_course_id = Column(INTEGER(11))
    eeo_class_id = Column(INTEGER(11))
    eeo_class_name = Column(String(125))
    class_begin_time = Column(DateTime)
    class_end_time = Column(DateTime)
    lesson_key = Column(String(45))
    eeo_course_name = Column(String(125))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    is_get_vod = Column(INTEGER(4))


class ErpEeoCourseInfoChange(Base):
    __tablename__ = 'erp_eeo_course_info_change'
    __table_args__ = {'comment': 'EEO课程信息改动记录表'}

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(32), nullable=False, comment='机构ID')
    op_type = Column(INTEGER(11), comment='操作类型 1:增加，2:删除，3:修改')
    op_time = Column(BIGINT(20), comment='操作时间')
    uid = Column(String(32), comment='操作人UID')
    course_id = Column(String(32), comment='课程ID')
    op_source = Column(INTEGER(11), comment='操作来源 1:EEO后台，2:classIn客户端，4:机构API')
    course_name = Column(String(255), comment='课程名称')
    expiry_time = Column(BIGINT(20), comment='课程过期时间戳，0：为永久有效')
    course_type = Column(INTEGER(11), comment='课程类型，1=标准课、2=公开课、3=双师课')
    course_status = Column(INTEGER(11), comment='课程状态，1=还没开课、2=开课中、3=已结课、4=已删除')
    category = Column(String(255), comment='组织架构名称')
    category_id = Column(String(32), comment='组织架构ID')
    subject_id = Column(INTEGER(11), comment='课程学科')
    main_teacher_name = Column(String(64), comment='班主任姓名')
    main_teacher_uid = Column(String(32), comment='班主任UID')
    main_teacher_no = Column(String(64), comment='班主任工号')
    label_infos = Column(JSON, comment='标签信息')
    processed = Column(TINYINT(1), server_default=text("'0'"), comment='处理状态：0未处理，1已处理')
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class ErpEeoCourseRecordings(Base):
    __tablename__ = 'erp_eeo_course_recordings'
    __table_args__ = {'comment': '录课文件表'}

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(20), comment='机构ID')
    course_id = Column(String(20), comment='课程ID')
    file_id = Column(String(50), comment='文件ID')
    vurl = Column(String(255), comment='视频URL')
    vst = Column(BIGINT(20), comment='视频开始时间')
    vet = Column(BIGINT(20), comment='视频结束时间')
    duration = Column(INTEGER(11), comment='视频时长')
    size = Column(INTEGER(11), comment='文件大小')
    cid_ext = Column(String(50), comment='扩展课堂ID')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    processed = Column(INTEGER(4))
    class_id = Column(String(20))
    action_time = Column(BIGINT(20))
    disable = Column(TINYINT(1), nullable=False, server_default=text("'0'"), comment='删除状态：0正常，1已删除')


class ErpEeoCourseStudentChange(Base):
    __tablename__ = 'erp_eeo_course_student_change'
    __table_args__ = {'comment': 'EEO课程学生信息改动记录表'}

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(32), nullable=False, comment='机构ID')
    op_type = Column(INTEGER(11), nullable=False, comment='操作类型 1:增加，2:删除，3:修改')
    op_time = Column(BIGINT(20), nullable=False, comment='操作时间')
    uid = Column(String(32), nullable=False, comment='操作人UID')
    course_id = Column(String(32), nullable=False, comment='课程ID')
    op_source = Column(INTEGER(11), nullable=False, comment='操作来源 1:EEO后台，2:classIn客户端，4:机构API')
    course_name = Column(String(255), comment='课程名称')
    student_infos = Column(JSON, comment='学生信息')
    processed = Column(TINYINT(1), server_default=text("'0'"), comment='处理状态：0未处理，1已处理')
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class ErpEeoExamScores(Base):
    __tablename__ = 'erp_eeo_exam_scores'

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(20), nullable=False)
    course_id = Column(String(20))
    course_name = Column(String(100))
    activity_id = Column(String(20))
    activity_name = Column(String(100))
    unit_id = Column(String(20))
    unit_name = Column(String(100))
    class_id = Column(String(20))
    student_uid = Column(String(20))
    student_name = Column(String(50))
    student_account = Column(String(50))
    teacher_uid = Column(String(20))
    teacher_name = Column(String(50))
    teacher_account = Column(String(50))
    score = Column(INTEGER(11))
    student_scoring_rate = Column(DECIMAL(10, 4))
    answer_duration = Column(INTEGER(11))
    submission_time = Column(BIGINT(20))
    correction_time = Column(BIGINT(20))
    disable = Column(TINYINT(1), nullable=False, server_default=text("'0'"), comment='删除状态：0正常，1已删除')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))


class ErpEeoHomeworkScores(Base):
    __tablename__ = 'erp_eeo_homework_scores'

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(20), nullable=False)
    course_id = Column(String(20))
    course_name = Column(String(100))
    activity_id = Column(String(20))
    activity_name = Column(String(100))
    unit_id = Column(String(20))
    unit_name = Column(String(100))
    student_uid = Column(String(20))
    student_name = Column(String(50))
    student_account = Column(String(50))
    teacher_uid = Column(String(20))
    teacher_name = Column(String(50))
    teacher_account = Column(String(50))
    score = Column(INTEGER(11))
    student_score = Column(String(20))
    student_scoring_rate = Column(DECIMAL(10, 4))
    review_details = Column(JSON)
    grading_plan = Column(String(20))
    submission_time = Column(BIGINT(20))
    correction_time = Column(BIGINT(20))
    disable = Column(TINYINT(1), nullable=False, server_default=text("'0'"), comment='删除状态：0正常，1已删除')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))


class ErpEeoHomeworkSubmit(Base):
    __tablename__ = 'erp_eeo_homework_submit'

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(20), nullable=False)
    course_id = Column(String(20))
    course_name = Column(String(100))
    activity_id = Column(String(20))
    activity_name = Column(String(100))
    unit_id = Column(String(20))
    unit_name = Column(String(100))
    student_uid = Column(String(20))
    student_name = Column(String(50))
    student_account = Column(String(50))
    teacher_uid = Column(String(20))
    teacher_name = Column(String(50))
    teacher_account = Column(String(50))
    files = Column(JSON)
    content = Column(Text)
    is_submit_late = Column(TINYINT(1), server_default=text("'0'"))
    is_revision = Column(TINYINT(1), server_default=text("'0'"))
    student_total = Column(INTEGER(11))
    submit_total = Column(INTEGER(11))
    submission_time = Column(BIGINT(20))
    disable = Column(TINYINT(1), nullable=False, server_default=text("'0'"), comment='删除状态：0正常，1已删除')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))


class ErpEeoMessageLogs(Base):
    __tablename__ = 'erp_eeo_message_logs'

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(20))
    cmd = Column(String(50), nullable=False)
    msg = Column(Text)
    safe_key = Column(String(100))
    time_stamp = Column(BIGINT(20))
    course_id = Column(String(20))
    course_name = Column(String(100))
    data = Column(JSON)
    processed = Column(TINYINT(1), server_default=text("'0'"))
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"))


class ErpEeoPhoneNumberChange(Base):
    __tablename__ = 'erp_eeo_phone_number_change'
    __table_args__ = {'comment': 'EEO手机号码更换记录表'}

    id = Column(INTEGER(11), primary_key=True)
    sid = Column(String(32), nullable=False, comment='机构ID')
    uid = Column(String(32), nullable=False, comment='用户UID')
    telephone = Column(String(32), nullable=False, comment='新手机号码')
    replace_time = Column(BIGINT(20), comment='替换时间')
    processed = Column(TINYINT(1), server_default=text("'0'"), comment='处理状态：0未处理，1已处理')
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    msg = Column(String(255))


class ErpEeoVodPlaylist(Base):
    __tablename__ = 'erp_eeo_vod_playlist'

    id = Column(INTEGER(11), primary_key=True)
    eeo_course_class_id = Column(INTEGER(11))
    eeo_file_id = Column(String(45))
    eeo_size = Column(String(45))
    eeo_update_time = Column(DateTime)
    eeo_end_time = Column(DateTime)
    eeo_play_set = Column(JSON)
    is_download = Column(INTEGER(4))
    is_check = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    download_status = Column(INTEGER(4))
    duration = Column(INTEGER(11))
    index = Column(INTEGER(4))


class ErpEnterprise(Base):
    __tablename__ = 'erp_enterprise'

    id = Column(INTEGER(11), primary_key=True)
    enterprise_name = Column(String(125))
    enterprise_short_name = Column(String(45))
    enterprise_bank_account = Column(String(255))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpExam(Base):
    __tablename__ = 'erp_exam'

    id = Column(INTEGER(11), primary_key=True)
    exam_name = Column(String(255), comment='考试名称')
    class_id = Column(INTEGER(11), server_default=text("'0'"), comment='关联class_id')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='删除状态')
    create_by = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    create_time = Column(DateTime)
    update_by = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    type = Column(INTEGER(11), server_default=text("'1'"), comment='类型  1正常考试   2分组试题   3统计分析')
    analysis_type = Column(INTEGER(11), server_default=text("'1'"), comment='是否生成统计表的1-8题统计分析   1是  其他否')
    upload_type = Column(INTEGER(4), comment='1 诊断 其他待定')
    grade_type = Column(String(45))


class ErpExamQuestions(Base):
    __tablename__ = 'erp_exam_questions'

    id = Column(INTEGER(11), primary_key=True)
    exam_id = Column(INTEGER(11), server_default=text("'0'"), comment='考试ID')
    sort = Column(INTEGER(11), server_default=text("'0'"), comment='题号')
    difficulty = Column(String(255), comment='难度')
    knowledge_point = Column(String(255), comment='知识点')
    score = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='得分')
    group_name = Column(String(255), comment='分组名称')
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpExamScore(Base):
    __tablename__ = 'erp_exam_score'

    id = Column(INTEGER(11), primary_key=True)
    exam_id = Column(INTEGER(11), server_default=text("'0'"), comment='考试ID')
    stu_name = Column(String(255), comment='导入的名称')
    stu_id = Column(INTEGER(11), server_default=text("'0'"), comment='学生StuID')
    content = Column(MEDIUMTEXT, comment='得分明细  json格式')
    t_score = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='总分')
    rank = Column(INTEGER(11), server_default=text("'0'"), comment='排名')
    rank_rate = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='百分比排名')
    exam_score = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='考试总分')
    match_class = Column(String(125))
    teacher_comments = Column(String(555))
    update_time = Column(DateTime)
    match_system = Column(String(45))
    disable = Column(INTEGER(4))
    create_time = Column(DateTime)


class ErpFinance(Base):
    __tablename__ = 'erp_finance'

    id = Column(INTEGER(11), primary_key=True)
    order_no = Column(String(125))
    merchant_id = Column(INTEGER(11), comment='对象id')
    merchant_name = Column(String(125), comment='对象名')
    ie_type = Column(INTEGER(4), comment='1 收入 2 支出')
    trade_money = Column(DECIMAL(10, 2))
    trade_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    audit_state = Column(INTEGER(4), comment='1 已审核')
    approval_record_id = Column(INTEGER(11), comment='关联审核记录表')
    desc = Column(String(255))
    template_id = Column(INTEGER(11))
    is_public = Column(INTEGER(4), comment='1 私账 2 公账')
    pre_pay_time = Column(DateTime, comment='预计付款日期')
    cashier_id = Column(INTEGER(11), comment='出纳状态 0 未审核 >0 已审核（id为谁审核的）')
    create_by_name = Column(String(45))
    dept_id = Column(INTEGER(11))
    dept_name = Column(String(45))
    related_obj_id = Column(INTEGER(11), comment='关联对象id')
    disable = Column(INTEGER(4))
    is_auto = Column(INTEGER(4), comment='是否自动通过')
    cost_type_id = Column(INTEGER(11), comment='费用类型id')
    attachment = Column(JSON, comment='附件')
    invoice_type = Column(INTEGER(4), comment='发票类型 1 专票 2 普票')
    invoice_money = Column(DECIMAL(10, 2), comment='发票金额')
    invoice_remark = Column(String(255), comment='发票备注')
    related_obj_type = Column(INTEGER(4), comment='关联对象类型')
    current_auditors = Column(JSON, comment='当前审批人s')
    application_reason = Column(String(500), comment='申请原因')


class ErpFinanceBudget(Base):
    __tablename__ = 'erp_finance_budget'

    id = Column(INTEGER(11), primary_key=True)
    yyyy = Column(INTEGER(4))
    month = Column(INTEGER(4))
    presale = Column(DECIMAL(12, 2), comment='预收')
    class_fee = Column(DECIMAL(12, 2), comment='课时费，预期课时费')
    estimated_profit = Column(DECIMAL(12, 2), comment='预计利润=预收-课时费')
    expenses = Column(DECIMAL(12, 2), comment='编辑费用支出')
    actual_profit = Column(DECIMAL(12, 2), comment='实际利润=预计利润-费用支出')
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpFinanceCostType(Base):
    __tablename__ = 'erp_finance_cost_type'

    id = Column(INTEGER(11), primary_key=True)
    name = Column(String(255))
    type = Column(INTEGER(4), comment='1 收入 2 支出 3 收支相抵 4 其他')
    parent_id = Column(INTEGER(11))
    is_admin = Column(INTEGER(4), comment='是否是管理型节点')
    add_finance = Column(INTEGER(4), comment='是否计入营收')
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    tag = Column(INTEGER(4), comment='标签大类')


class ErpFinanceDetail(Base):
    __tablename__ = 'erp_finance_detail'

    id = Column(INTEGER(11), primary_key=True)
    finance_id = Column(INTEGER(11))
    cost_type_id = Column(INTEGER(11))
    total_price = Column(DECIMAL(10, 2))
    unit_price = Column(DECIMAL(10, 2))
    num = Column(INTEGER(11))
    original_price = Column(DECIMAL(10, 2))
    comments = Column(String(255))
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    order_id = Column(INTEGER(11))
    item_name = Column(String(255))


class ErpFinanceRole(Base):
    __tablename__ = 'erp_finance_role'

    id = Column(INTEGER(4), primary_key=True)
    type = Column(INTEGER(4))
    name = Column(String(45))
    account_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpFinanceTradePayment(Base):
    __tablename__ = 'erp_finance_trade_payment'

    id = Column(INTEGER(11), primary_key=True)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    payment_order_no = Column(String(125), comment='支付生成单号')
    trade_type = Column(INTEGER(4), comment='1 微信 2 支付宝')
    money_pay = Column(Float(10), comment='发起金额')
    money_income = Column(Float(10), comment='实际收入金额')
    openid = Column(String(125), comment='用户openid')
    trade_status = Column(INTEGER(4), comment='1 挂起 2 成功 3 失败 4 已关闭')
    merchant_id = Column(String(125), comment='商户编号')
    cmb_order_id = Column(String(225), comment='招行订单号')
    cmb_pay_time = Column(DateTime, comment='招行交易时间')
    cmb_trade_type = Column(String(255), comment='招行交易类型 JSAPI')
    third_order_id = Column(String(255), comment='三方流水号')
    stu_id = Column(INTEGER(11))
    offer_id = Column(INTEGER(11))
    campus_id = Column(INTEGER(11))
    receipt_id = Column(INTEGER(11))


class ErpFinanceTradeRefund(Base):
    __tablename__ = 'erp_finance_trade_refund'

    id = Column(INTEGER(11), primary_key=True)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    payment_order_no = Column(String(125), comment='支付生成单号')
    refund_order_no = Column(String(125), comment='退款生成单号')
    trade_type = Column(INTEGER(4), comment='1 微信 2 支付宝')
    money = Column(Float(10), comment='发起金额')
    money_refund = Column(Float(10), comment='实际退款金额')
    openid = Column(String(125), comment='用户openid')
    trade_status = Column(INTEGER(4), comment='1 挂起 2 成功 3 失败 4 已关闭')
    merchant_id = Column(INTEGER(11), comment='商户编号')
    cmb_order_id = Column(String(225), comment='招行订单号')
    cmb_pay_time = Column(DateTime, comment='招行交易时间')
    cmb_trade_type = Column(String(255), comment='招行交易类型 JSAPI')
    third_order_id = Column(String(255), comment='三方流水号')
    comments = Column(String(255))
    stu_id = Column(INTEGER(11), comment='关联学生')
    offer_id = Column(INTEGER(11), comment='报价单号')
    campus_id = Column(INTEGER(11), comment='关联校区')
    receipt_id = Column(INTEGER(11), comment='关联单据')
    retry_times = Column(INTEGER(4))
    order_student_id = Column(INTEGER(11))


class ErpGuest(Base):
    __tablename__ = 'erp_guest'

    id = Column(INTEGER(11), primary_key=True)
    guest_name = Column(String(45))
    username = Column(String(45))
    password = Column(String(255))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))


class ErpHrAccount(Base):
    __tablename__ = 'erp_hr_account'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    hr_plan_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    introducer = Column(INTEGER(4))
    source = Column(String(45))
    comments = Column(String(255))


class ErpHrPlan(Base):
    __tablename__ = 'erp_hr_plan'

    id = Column(INTEGER(11), primary_key=True)
    title = Column(String(255))
    desired_num = Column(INTEGER(11))
    entry_num = Column(INTEGER(11))
    employee_type = Column(INTEGER(4))
    push_date = Column(Date)
    desc = Column(String(1000))
    end_date = Column(Date)
    create_by = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpHrTalentPool(Base):
    __tablename__ = 'erp_hr_talent_pool'

    id = Column(INTEGER(11), primary_key=True)
    name = Column(String(45))
    interview_time = Column(DateTime)
    hr_plan_id = Column(INTEGER(11))
    is_accept = Column(INTEGER(4))
    comments = Column(String(255))
    attachment = Column(JSON)
    update_by = Column(INTEGER(11))
    create_by = Column(INTEGER(11))
    update_time = Column(DateTime)
    create_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpJobLevel(Base):
    __tablename__ = 'erp_job_level'

    id = Column(INTEGER(11), primary_key=True)
    level_name = Column(String(45))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    level_desc = Column(String(1000))
    level_type = Column(INTEGER(4))
    level_item = Column(String(1000))


class ErpLabel(Base):
    __tablename__ = 'erp_label'

    id = Column(INTEGER(11), primary_key=True)
    title = Column(String(45))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))


class ErpLog(Base):
    __tablename__ = 'erp_log'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    action = Column(String(45))
    content = Column(JSON)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    log_type = Column(INTEGER(4))
    obj_id = Column(INTEGER(11))


class ErpOfficeCenter(Base):
    __tablename__ = 'erp_office_center'

    id = Column(INTEGER(11), primary_key=True)
    center_name = Column(String(125))
    center_cover_img = Column(String(255))
    address = Column(String(125))
    phone = Column(String(45))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    latitude = Column(DECIMAL(10, 6))
    longitude = Column(DECIMAL(10, 6))
    school_spell = Column(String(45))
    qrcode = Column(String(255))
    sort = Column(INTEGER(4))
    center_type = Column(INTEGER(4))


class ErpOfficeClassroom(Base):
    __tablename__ = 'erp_office_classroom'

    id = Column(INTEGER(11), primary_key=True)
    center_id = Column(INTEGER(11))
    room_name = Column(String(255))
    room_square = Column(Float(6))
    room_length = Column(Float(6))
    room_width = Column(Float(6))
    stu_cap_max = Column(INTEGER(4))
    stu_cap_comfort = Column(INTEGER(4))
    window_to = Column(String(45))
    quiet_level = Column(String(45))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    room_cover_img = Column(String(255))
    inner_type = Column(INTEGER(4))


class ErpOfficeClassroomProblem(Base):
    __tablename__ = 'erp_office_classroom_problem'

    id = Column(INTEGER(11), primary_key=True)
    room_id = Column(INTEGER(11))
    content = Column(String(255))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpOfficeConsumable(Base):
    __tablename__ = 'erp_office_consumable'

    id = Column(INTEGER(11), primary_key=True)
    title = Column(String(125))
    center_id = Column(INTEGER(11))
    num = Column(INTEGER(11))
    receive_datetime = Column(DateTime)
    pages = Column(INTEGER(4))
    size = Column(String(45))
    color = Column(String(45))
    binding = Column(String(45))
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    stock_status = Column(INTEGER(4), comment='入库状态 0 未入库 1 已入库')
    comments = Column(String(255))
    parent_id = Column(INTEGER(11))
    is_supplement = Column(INTEGER(11))


class ErpOfficeConsumableSupplement(Base):
    __tablename__ = 'erp_office_consumable_supplement'

    id = Column(INTEGER(11), primary_key=True)
    consumable_id = Column(INTEGER(11))
    consumable_type = Column(INTEGER(4), comment='加印类型 1 缺 2 需')
    num = Column(INTEGER(4), comment='补充数量')
    supplement_status = Column(INTEGER(4), comment='加印状态 0 忽略 1 显示')
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpOfficeFixedAssets(Base):
    __tablename__ = 'erp_office_fixed_assets'

    id = Column(INTEGER(11), primary_key=True)
    type = Column(INTEGER(4))
    name = Column(String(45))
    amount = Column(INTEGER(6))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    room_id = Column(INTEGER(11))
    serial_no = Column(String(45))
    status = Column(INTEGER(4), comment='1 使用中 2 异常 3 报废 4 遗失')
    unit = Column(String(20))


class ErpOfficePurchasingList(Base):
    __tablename__ = 'erp_office_purchasing_list'

    id = Column(INTEGER(11), primary_key=True)
    room_id = Column(INTEGER(11))
    type = Column(INTEGER(4))
    name = Column(String(45))
    num = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    unit = Column(String(45))
    receipt_detail_id = Column(INTEGER(11))


class ErpOfficeWarehouse(Base):
    __tablename__ = 'erp_office_warehouse'

    id = Column(INTEGER(11), primary_key=True)
    type = Column(INTEGER(4))
    name = Column(String(45))
    amount = Column(INTEGER(6))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    center_id = Column(INTEGER(11))
    serial_no = Column(String(45))
    status = Column(INTEGER(4), comment='1 使用中 2 报废 3 异常 4 遗失')
    unit = Column(String(20))


class ErpOfficeWarehouseLog(Base):
    __tablename__ = 'erp_office_warehouse_log'

    id = Column(INTEGER(11), primary_key=True)
    warehouse_id = Column(INTEGER(11))
    content = Column(String(500))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))


class ErpOfflineExam(Base):
    __tablename__ = 'erp_offline_exam'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    appointment_time = Column(DateTime)
    appointment_center_id = Column(INTEGER(4), primary_key=True, nullable=False)
    paper_name = Column(String(255))
    stu_score = Column(Float(10))
    paper_score = Column(Float(10))
    exam_result = Column(String(255))
    plus_id = Column(INTEGER(11))
    attachment = Column(JSON)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))


class ErpOnlinePaper(Base):
    __tablename__ = 'erp_online_paper'

    id = Column(INTEGER(11), primary_key=True)
    paper_name = Column(String(255))
    create_by = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    is_active = Column(INTEGER(4))
    grade_id = Column(INTEGER(4))
    subject_id = Column(INTEGER(4))
    is_lock = Column(INTEGER(4))
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    passing_score = Column(Float(10))
    total_score = Column(Float(10))
    p_grade_id = Column(INTEGER(11))


class ErpOnlinePaperCourse(Base):
    __tablename__ = 'erp_online_paper_course'

    id = Column(INTEGER(11), primary_key=True)
    paper_id = Column(INTEGER(11))
    min_score = Column(INTEGER(4))
    max_score = Column(INTEGER(4))
    course_name = Column(String(255))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    teacher_msg = Column(String(255))


class ErpOnlinePaperQuestion(Base):
    __tablename__ = 'erp_online_paper_question'

    id = Column(INTEGER(11), primary_key=True)
    paper_id = Column(INTEGER(11))
    question_id = Column(INTEGER(11))
    sort = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpOnlineQuestion(Base):
    __tablename__ = 'erp_online_question'

    id = Column(INTEGER(11), primary_key=True)
    content = Column(LONGTEXT, comment='题目内容')
    difficulty_level = Column(INTEGER(4), comment='难度')
    score = Column(Float(10), comment='分值')
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4), server_default=text("'0'"))
    create_by = Column(INTEGER(4))
    knowledge_point = Column(String(255), comment='知识点')
    question_type = Column(INTEGER(4))
    analysis = Column(LONGTEXT)


class ErpOnlineQuestionOption(Base):
    __tablename__ = 'erp_online_question_option'

    id = Column(INTEGER(11), primary_key=True)
    question_id = Column(INTEGER(11), comment='试题id')
    option_content = Column(LONGTEXT)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    correct = Column(INTEGER(4))
    answer = Column(Text)
    option_score = Column(Float(10))


class ErpOnlineStuPaper(Base):
    __tablename__ = 'erp_online_stu_paper'

    id = Column(INTEGER(11), primary_key=True)
    paper_id = Column(INTEGER(11))
    erp_stu_id = Column(INTEGER(11), comment='erp的学生id')
    current_process = Column(INTEGER(4), comment='第几道')
    all_process = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpOnlineStuScore(Base):
    __tablename__ = 'erp_online_stu_score'

    id = Column(INTEGER(11), primary_key=True)
    paper_stu_id = Column(INTEGER(11))
    paper_question_id = Column(INTEGER(11))
    choice_option_id = Column(INTEGER(4))
    is_correct = Column(INTEGER(4))
    stu_score = Column(String(255))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    answer = Column(String(255))


class ErpOrder(Base):
    __tablename__ = 'erp_order'
    __table_args__ = {'comment': 'ERP 订单表'}

    id = Column(INTEGER(11), primary_key=True)
    offer_id = Column(INTEGER(11), server_default=text("'0'"), comment='报价单id')
    trade_way = Column(INTEGER(11), server_default=text("'0'"), comment='交易方式 1线上交易 2现金付款')
    class_price = Column(DECIMAL(12, 2), comment='课程单价')
    lesson_price = Column(DECIMAL(12, 3), comment='课节单价')
    total_receivable = Column(DECIMAL(12, 2), comment='总应收')
    total_income = Column(DECIMAL(12, 2), comment='总实收')
    refund = Column(DECIMAL(12, 2), server_default=text("'0.00'"), comment='退款金额')
    discount = Column(DECIMAL(12, 2), server_default=text("'0.00'"), comment='优惠金额')
    platform_tax = Column(DECIMAL(12, 2), server_default=text("'0.00'"), comment='平台税金')
    order_state = Column(INTEGER(11), server_default=text("'0'"), comment='订单状态 1正常 2退学 3其他')
    campus_id = Column(INTEGER(11), server_default=text("'1'"), comment='校区')
    renew_order_id = Column(INTEGER(11), server_default=text("'0'"), comment='续费订单编号')
    order_class_type = Column(INTEGER(11), server_default=text("'0'"), comment='订单类型 1课程 3 讲义')
    insert_plan_index = Column(INTEGER(11), server_default=text("'0'"), comment='插班开始课节 0正常单 x 插班单')
    join_type = Column(INTEGER(11), server_default=text("'1'"), comment='订单报入类型(1-正常报入，2-插班报入，3-续费订单，4-转班订单，5-分拆订单)')
    is_first_buy = Column(INTEGER(11), server_default=text("'0'"), comment='是否首次下单 0否 1是')
    create_by = Column(INTEGER(11), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_by = Column(INTEGER(11), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='是否删除')
    unit = Column(INTEGER(4), comment='1 按期收费 2 按次收费')
    buy_num = Column(INTEGER(4), comment='计量次数')
    discount_id = Column(INTEGER(11))
    order_student_id = Column(INTEGER(11), comment='学生订单id')
    ewallet_money = Column(DECIMAL(10, 2))


class ErpOrderCopy1(Base):
    __tablename__ = 'erp_order_copy1'
    __table_args__ = {'comment': 'ERP 订单表'}

    id = Column(INTEGER(11), primary_key=True)
    offer_id = Column(INTEGER(11), server_default=text("'0'"), comment='报价单id')
    trade_way = Column(INTEGER(11), server_default=text("'0'"), comment='交易方式 1线上交易 2现金付款')
    class_price = Column(DECIMAL(12, 2), comment='课程单价')
    lesson_price = Column(DECIMAL(12, 3), comment='课节单价')
    total_receivable = Column(DECIMAL(12, 2), comment='总应收')
    total_income = Column(DECIMAL(12, 2), comment='总实收')
    refund = Column(DECIMAL(12, 2), server_default=text("'0.00'"), comment='退款金额')
    discount = Column(DECIMAL(12, 2), server_default=text("'0.00'"), comment='优惠金额')
    platform_tax = Column(DECIMAL(12, 2), server_default=text("'0.00'"), comment='平台税金')
    order_state = Column(INTEGER(11), server_default=text("'0'"), comment='订单状态 1正常 2退学 3其他')
    campus_id = Column(INTEGER(11), server_default=text("'1'"), comment='校区')
    renew_order_id = Column(INTEGER(11), server_default=text("'0'"), comment='续费订单编号')
    order_class_type = Column(INTEGER(11), server_default=text("'0'"), comment='订单类型 1课程 3 讲义')
    insert_plan_index = Column(INTEGER(11), server_default=text("'0'"), comment='插班开始课节 0正常单 x 插班单')
    join_type = Column(INTEGER(11), server_default=text("'1'"), comment='订单报入类型(1-正常报入，2-插班报入，3-续费订单，4-转班订单，5-分拆订单)')
    is_first_buy = Column(INTEGER(11), server_default=text("'0'"), comment='是否首次下单 0否 1是')
    create_by = Column(INTEGER(11), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_by = Column(INTEGER(11), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='是否删除')
    unit = Column(INTEGER(4), comment='1 按期收费 2 按次收费')
    buy_num = Column(INTEGER(4), comment='计量次数')
    discount_id = Column(INTEGER(11))
    order_student_id = Column(INTEGER(11), comment='学生订单id')
    ewallet_money = Column(DECIMAL(10, 2))


class ErpOrderOffer(Base):
    __tablename__ = 'erp_order_offer'

    id = Column(INTEGER(11), primary_key=True, comment='主键编号')
    name = Column(String(255), server_default=text("''"), comment='报价单名称')
    effective_start = Column(DateTime, comment='有效期开始时间')
    effective_end = Column(DateTime, comment='有效期结束时间')
    total_original_price = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总原价')
    total_sale_price = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总金额')
    total_discount_price = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='优惠券总额')
    create_by = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_by = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    campus_id = Column(INTEGER(11), server_default=text("'0'"), comment='校区')
    order_no = Column(String(200), server_default=text("''"), comment='订单编号')
    offer_type = Column(INTEGER(11), server_default=text("'0'"), comment='报价单类型(1-新购订单，2-续费订单)')
    stu_id = Column(INTEGER(11), server_default=text("'0'"), comment='学员编号')
    offer_state = Column(INTEGER(11), server_default=text("'0'"), comment='付款状态(0-未推送，1-已推送，2-未付款，3-已付款，4-已关闭，5-已失效)')
    integral_money = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='积分兑换金额')
    ewallet_money = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='使用电子钱包金额')
    is_push = Column(INTEGER(10), server_default=text("'0'"), comment='是否推送(0-未推送，1-已推送)')
    cash_voucher = Column(String(2000), comment='现金付款凭证')
    cash_pay_remark = Column(String(255), comment='现金付款备注')
    inner_remark = Column(String(500), server_default=text("''"), comment='对内备注(内部显示)')
    out_remark = Column(String(500), server_default=text("''"), comment='对外备注(公众号H5支付显示)')
    order_from = Column(INTEGER(4), server_default=text("'1'"), comment='订单来源 1 教务制单 2 新版小课堂自行下单')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='是否禁用')
    rule_id = Column(INTEGER(11))
    cash_receive_way = Column(INTEGER(4))


class ErpOrderOfferCopy1(Base):
    __tablename__ = 'erp_order_offer_copy1'

    id = Column(INTEGER(11), primary_key=True, comment='主键编号')
    name = Column(String(255), server_default=text("''"), comment='报价单名称')
    effective_start = Column(DateTime, comment='有效期开始时间')
    effective_end = Column(DateTime, comment='有效期结束时间')
    total_original_price = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总原价')
    total_sale_price = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总金额')
    total_discount_price = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='优惠券总额')
    create_by = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_by = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    campus_id = Column(INTEGER(11), server_default=text("'0'"), comment='校区')
    order_no = Column(String(200), server_default=text("''"), comment='订单编号')
    offer_type = Column(INTEGER(11), server_default=text("'0'"), comment='报价单类型(1-新购订单，2-续费订单)')
    stu_id = Column(INTEGER(11), server_default=text("'0'"), comment='学员编号')
    offer_state = Column(INTEGER(11), server_default=text("'0'"), comment='付款状态(0-未推送，1-已推送，2-未付款，3-已付款，4-已关闭，5-已失效)')
    integral_money = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='积分兑换金额')
    ewallet_money = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='使用电子钱包金额')
    is_push = Column(INTEGER(10), server_default=text("'0'"), comment='是否推送(0-未推送，1-已推送)')
    cash_voucher = Column(String(2000), comment='现金付款凭证')
    cash_pay_remark = Column(String(255), comment='现金付款备注')
    inner_remark = Column(String(500), server_default=text("''"), comment='对内备注(内部显示)')
    out_remark = Column(String(500), server_default=text("''"), comment='对外备注(公众号H5支付显示)')
    order_from = Column(INTEGER(4), server_default=text("'1'"), comment='订单来源 1 教务制单 2 新版小课堂自行下单')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='是否禁用')
    rule_id = Column(INTEGER(11))


class ErpOrderRefund(Base):
    __tablename__ = 'erp_order_refund'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    refund_reason = Column(String(255))
    stu_id = Column(INTEGER(11), primary_key=True, nullable=False)
    refund_remark = Column(String(255))
    audit_state = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    apply_date = Column(Date)
    apply_money = Column(DECIMAL(10, 2))
    refund_money = Column(DECIMAL(10, 2))
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    refund_type = Column(String(255), comment='1 退费 2 结转')
    receipt_id = Column(INTEGER(11), comment='绑定流程单')
    attachment = Column(JSON, comment='附件')


class ErpOrderRefundDetail(Base):
    __tablename__ = 'erp_order_refund_detail'

    id = Column(INTEGER(11), primary_key=True)
    refund_id = Column(INTEGER(11))
    order_no = Column(String(125))
    refund_order_no = Column(String(125))
    order_student_id = Column(INTEGER(11))
    order_id = Column(INTEGER(11))
    unit_price = Column(DECIMAL(10, 2))
    refund_num = Column(INTEGER(4))
    total_money = Column(DECIMAL(10, 2))
    unit = Column(String(45))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    apply_money = Column(DECIMAL(10, 2))
    refund_money = Column(DECIMAL(10, 2))
    pay_time = Column(DateTime)
    disable = Column(INTEGER(4))
    refund_state = Column(INTEGER(4))
    refund_type = Column(INTEGER(4), comment='1退费 2 结转')
    refund_ewallet = Column(DECIMAL(10, 2), comment='退电子钱包金额')
    refund_way = Column(INTEGER(4), comment='退费返还方式 1 原路 2 现金 3 转账')
    cost_type_id = Column(INTEGER(11))
    cost_type_name = Column(String(125))
    sort_no = Column(INTEGER(4))
    refund_times = Column(INTEGER(4), comment='退款次数')
    act_money = Column(DECIMAL(10, 2), comment='实际到账金额')


class ErpOrderStudent(Base):
    __tablename__ = 'erp_order_student'

    id = Column(INTEGER(11), primary_key=True)
    class_id = Column(INTEGER(11), server_default=text("'0'"), comment='班级id')
    stu_id = Column(INTEGER(11))
    student_state = Column(INTEGER(11), server_default=text("'0'"), comment='状态   1正常   2退学')
    create_by = Column(INTEGER(11), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_by = Column(INTEGER(11), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    total_hours = Column(DECIMAL(10, 1), server_default=text("'0.0'"), comment='总课时')
    complete_hours = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='完成课时')
    contract_id = Column(INTEGER(11), server_default=text("'0'"), comment='合同编号')
    is_renew = Column(INTEGER(11), server_default=text("'0'"), comment='是否是续费订单')
    self_change_class = Column(INTEGER(4), server_default=text("'0'"), comment='自主调课次数')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='是否禁用')
    order_class_type = Column(INTEGER(4))
    p_id = Column(INTEGER(11), comment='父学生订单')
    class_sync = Column(INTEGER(11), comment='0 未同步 >0已同步')
    classin_course_id = Column(INTEGER(11))
    classin_stu_uid = Column(INTEGER(11))
    is_online = Column(INTEGER(4), comment='是否线上')


class ErpOrderStudentCopy1(Base):
    __tablename__ = 'erp_order_student_copy1'

    id = Column(INTEGER(11), primary_key=True)
    class_id = Column(INTEGER(11), server_default=text("'0'"), comment='班级id')
    stu_id = Column(INTEGER(11))
    student_state = Column(INTEGER(11), server_default=text("'0'"), comment='状态   1正常   2退学')
    create_by = Column(INTEGER(11), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_by = Column(INTEGER(11), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    total_hours = Column(DECIMAL(10, 1), server_default=text("'0.0'"), comment='总课时')
    complete_hours = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='完成课时')
    contract_id = Column(INTEGER(11), server_default=text("'0'"), comment='合同编号')
    is_renew = Column(INTEGER(11), server_default=text("'0'"), comment='是否是续费订单')
    self_change_class = Column(INTEGER(4), server_default=text("'0'"), comment='自主调课次数')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='是否禁用')
    order_class_type = Column(INTEGER(4))
    p_id = Column(INTEGER(11), comment='父学生订单')
    class_sync = Column(INTEGER(11), comment='0 未同步 >0已同步')
    classin_course_id = Column(INTEGER(11))
    classin_stu_uid = Column(INTEGER(11))


class ErpOrderStudentCopy2(Base):
    __tablename__ = 'erp_order_student_copy2'

    id = Column(INTEGER(11), primary_key=True)
    class_id = Column(INTEGER(11), server_default=text("'0'"), comment='班级id')
    stu_id = Column(INTEGER(11))
    student_state = Column(INTEGER(11), server_default=text("'0'"), comment='状态   1正常   2退学')
    create_by = Column(INTEGER(11), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_by = Column(INTEGER(11), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    total_hours = Column(DECIMAL(10, 1), server_default=text("'0.0'"), comment='总课时')
    complete_hours = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='完成课时')
    contract_id = Column(INTEGER(11), server_default=text("'0'"), comment='合同编号')
    is_renew = Column(INTEGER(11), server_default=text("'0'"), comment='是否是续费订单')
    self_change_class = Column(INTEGER(4), server_default=text("'0'"), comment='自主调课次数')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='是否禁用')
    order_class_type = Column(INTEGER(4))
    p_id = Column(INTEGER(11), comment='父学生订单')
    class_sync = Column(INTEGER(11), comment='0 未同步 >0已同步')
    classin_course_id = Column(INTEGER(11))
    classin_stu_uid = Column(INTEGER(11))


class ErpPaymentObj(Base):
    __tablename__ = 'erp_payment_obj'

    id = Column(INTEGER(11), primary_key=True)
    obj_name = Column(String(255))
    obj_type = Column(INTEGER(4))
    account_type = Column(INTEGER(4))
    account_no = Column(String(125))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))
    bank_name = Column(String(255))
    remark = Column(String(255))
    obj_related_id = Column(INTEGER(11))
    obj_related_name = Column(String(125))
    phone = Column(String(125))


class ErpPaymentObjType(Base):
    __tablename__ = 'erp_payment_obj_type'

    id = Column(INTEGER(4), primary_key=True)
    type_name = Column(String(45))
    update_by = Column(INTEGER(4))
    create_by = Column(INTEGER(4))
    update_time = Column(DateTime)
    create_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpPerformanceRecords(Base):
    __tablename__ = 'erp_performance_records'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    yyyy = Column(INTEGER(4))
    quarter = Column(INTEGER(4))
    rating = Column(String(45))
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpPublicSettings(Base):
    __tablename__ = 'erp_public_settings'

    id = Column(INTEGER(11), primary_key=True)
    dict_key = Column(String(125))
    dict_value = Column(JSON)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpPublicSettingsCopy1(Base):
    __tablename__ = 'erp_public_settings_copy1'

    id = Column(INTEGER(11), primary_key=True)
    dict_key = Column(String(125))
    dict_value = Column(JSON)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpPublicSettingsCopy2(Base):
    __tablename__ = 'erp_public_settings_copy2'

    id = Column(INTEGER(11), primary_key=True)
    dict_key = Column(String(125))
    dict_value = Column(JSON)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpQbAccountPermission(Base):
    __tablename__ = 'erp_qb_account_permission'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    account_type = Column(INTEGER(4))
    subject_ids = Column(JSON)
    grade_ids = Column(JSON)
    permission_ids = Column(JSON)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))


class ErpQbPermission(Base):
    __tablename__ = 'erp_qb_permission'

    id = Column(INTEGER(11), primary_key=True)
    permission_name = Column(String(45), comment='题库权限名')
    create_time = Column(DateTime, comment='1 内部')
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))


class ErpQwechatContact(Base):
    __tablename__ = 'erp_qwechat_contact'

    id = Column(INTEGER(11), primary_key=True)
    teacher_account_id = Column(INTEGER(11))
    teacher_id = Column(INTEGER(11))
    account_userid = Column(String(45))
    remark = Column(String(45))
    follow_create_time = Column(DateTime, comment='关注时间')
    add_way = Column(INTEGER(4))
    external_userid = Column(String(125))
    external_name = Column(String(45))
    external_type = Column(INTEGER(4))
    external_avatar = Column(String(255))
    external_gender = Column(INTEGER(4))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    stu_id = Column(INTEGER(11))


class ErpQwechatContactConfig(Base):
    __tablename__ = 'erp_qwechat_contact_config'

    id = Column(INTEGER(11), primary_key=True)
    teacher_id = Column(INTEGER(11))
    teacher_account_id = Column(INTEGER(11))
    account_userid = Column(String(125))
    qr_code = Column(String(255))
    config_id = Column(String(125))
    stu_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpReceipt(Base):
    __tablename__ = 'erp_receipt'
    __table_args__ = {'comment': '单据表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    apply_reason = Column(String(255), comment='申请原因')
    related_obj_id = Column(BIGINT(20), comment='关联对象ID')
    related_obj_type = Column(INTEGER(4), comment='关联对象类型')
    apply_remark = Column(String(500), comment='申请备注')
    audit_state = Column(TINYINT(1), server_default=text("'0'"), comment='审核状态：0暂存 1待审 2通过 3驳回 4取消')
    workflow_instance_id = Column(BIGINT(20), comment='绑定流程实例')
    dept_id = Column(BIGINT(20), comment='部门ID')
    dept_name = Column(String(100), comment='部门名称')
    is_auto = Column(TINYINT(1), server_default=text("'0'"), comment='是否自动审核通过')
    attachment = Column(JSON, comment='附件')
    relate_finance_id = Column(BIGINT(20), comment='关联财务单号')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    create_by = Column(INTEGER(11), comment='创建人')
    update_by = Column(INTEGER(11), comment='更新人')
    disable = Column(TINYINT(1), server_default=text("'0'"), comment='是否禁用')
    workflow_id = Column(BIGINT(20))
    related_obj_name = Column(String(255), comment='关联对象')
    refund_id = Column(INTEGER(11), comment='是否是退费单， 有则为退费单号')
    payment_id = Column(String(125), comment='是否是付款单')
    class_id = Column(INTEGER(11), comment='如果是开班单，则存在class_id>0')
    ewallet_stu_id = Column(INTEGER(11))
    ewallet_amount = Column(DECIMAL(10, 2))
    is_transfer = Column(INTEGER(4), comment='是否调拨单')
    expect_time = Column(DateTime, comment='期望日期')


class ErpReceiptDetail(Base):
    __tablename__ = 'erp_receipt_detail'
    __table_args__ = {'comment': '通用明细表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    receipt_id = Column(BIGINT(20), comment='关联单据ID')
    item_type = Column(TINYINT(1), comment='明细类型:1单据明细 2财务明细')
    item_name = Column(String(100), comment='项目名称')
    item_num = Column(INTEGER(11), comment='数量')
    item_unit_price = Column(DECIMAL(10, 2), comment='单价')
    item_total_price = Column(DECIMAL(10, 2), comment='总价')
    cost_type_id = Column(BIGINT(20), comment='费用类型ID')
    cost_type_name = Column(String(50), comment='费用类型名称')
    amount = Column(DECIMAL(10, 2), comment='金额')
    tax_rate = Column(DECIMAL(5, 2), comment='税率')
    tax_amount = Column(DECIMAL(10, 2), comment='税额')
    remark = Column(String(255), comment='备注/描述')
    sort_no = Column(INTEGER(11), server_default=text("'0'"), comment='排序号')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    create_by = Column(BIGINT(20), comment='创建人')
    update_by = Column(BIGINT(20), comment='更新人')
    attachment = Column(JSON, comment='附件')
    order_id = Column(INTEGER(11), comment='订单号')
    item_source = Column(String(255), comment='来源')
    purchasing_id = Column(INTEGER(11), comment='>0即教学点采购项')
    transfer_type = Column(INTEGER(4), comment='1 付款方 2 收款方')
    transfer_income = Column(INTEGER(4), comment='是否汇兑收益/损失')
    transfer_comments = Column(String(255), comment='调拨备注')
    transfer_date = Column(DateTime, comment='调拨日期')
    transfer_account_id = Column(INTEGER(11), comment='付款/收款对象')
    transfer_account_type = Column(INTEGER(4), comment='账户类型')
    transfer_account_number = Column(String(255))
    transfer_way_id = Column(INTEGER(4))
    disable = Column(TINYINT(1), nullable=False, server_default=text("'0'"), comment='是否删除：0否 1是')


class ErpReceiptFinance(Base):
    __tablename__ = 'erp_receipt_finance'
    __table_args__ = {'comment': '财务单表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    receipt_id = Column(BIGINT(20), comment='关联单据ID')
    order_no = Column(String(50), comment='订单号')
    bank_account_id = Column(BIGINT(20), comment='关联账户ID')
    bank_account_name = Column(String(100), comment='关联账户名')
    apply_money = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='申请金额')
    trade_money = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='实际金额')
    ie_type = Column(TINYINT(1), comment='收支类型：1收入 2支出')
    desc = Column(String(500), comment='描述')
    pre_pay_time = Column(Date, comment='预计付款日期')
    cashier_id = Column(BIGINT(20), server_default=text("'0'"), comment='出纳状态：0未审核 >0已审核（审核人ID）')
    invoice_type = Column(TINYINT(1), comment='发票类型：1专票 2普票')
    invoice_money = Column(DECIMAL(10, 2), comment='发票金额')
    invoice_remark = Column(String(255), comment='发票备注')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    create_by = Column(BIGINT(20), comment='创建人')
    update_by = Column(BIGINT(20), comment='更新人')
    disable = Column(TINYINT(1), server_default=text("'0'"), comment='是否禁用')
    financer_id = Column(INTEGER(4))


class ErpRole(Base):
    __tablename__ = 'erp_role'

    id = Column(INTEGER(11), primary_key=True)
    role_name = Column(String(255))
    role_desc = Column(String(255))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpRoleRoute(Base):
    __tablename__ = 'erp_role_route'

    id = Column(INTEGER(11), primary_key=True)
    role_id = Column(INTEGER(11))
    route_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpRoute(Base):
    __tablename__ = 'erp_route'

    id = Column(INTEGER(11), primary_key=True)
    route_name = Column(String(45))
    route_path = Column(String(255))
    identification = Column(String(32))
    parent_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    meta = Column(JSON)
    component = Column(String(255))


class ErpSalary(Base):
    __tablename__ = 'erp_salary'

    id = Column(INTEGER(11), primary_key=True)
    ym = Column(INTEGER(6))
    title = Column(String(125))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    salary_status = Column(INTEGER(4), comment='薪资状态 0 待生成 1 待发放 2 已发放')
    comments = Column(String(255), comment='备注')
    admin_certified = Column(INTEGER(4), comment='管理员最终确认 发放按钮')


class ErpSalaryBase(Base):
    __tablename__ = 'erp_salary_base'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    enterprise_id = Column(INTEGER(4))
    salary_base = Column(Float(10))
    salary_performance = Column(Float(10))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    bank_card_number = Column(String(125), comment='银行卡号')
    bank_sub_name = Column(String(125), comment='支行')
    bank_city = Column(String(45), comment='开户地')
    type = Column(INTEGER(4), server_default=text("'1'"), comment='1私账 2 公账')
    to_type = Column(INTEGER(4), server_default=text("'1'"), comment='1 员工 2 供应商 3 兼职 4 学生')
    to_name = Column(String(125), comment='对外付款姓名')


class ErpSalaryChangeLog(Base):
    __tablename__ = 'erp_salary_change_log'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    change_type = Column(INTEGER(4), comment='1 调整基础工资 2 调整绩效工资')
    salary_type = Column(INTEGER(4), comment='1 普通薪资 2 课时费')
    prev_salary = Column(DECIMAL(10, 2))
    current_salary = Column(DECIMAL(10, 2))
    update_time = Column(DateTime)
    create_time = Column(DateTime)
    disable = Column(INTEGER(4))
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))


class ErpSalaryDetail(Base):
    __tablename__ = 'erp_salary_detail'

    id = Column(INTEGER(11), primary_key=True)
    salary_id = Column(INTEGER(11))
    account_id = Column(INTEGER(11))
    enterprise_name = Column(String(125))
    actual_salary = Column(DECIMAL(10, 2))
    issued_salary = Column(DECIMAL(10, 2))
    detail = Column(JSON)
    is_send = Column(INTEGER(4))
    send_error = Column(INTEGER(4))
    send_time = Column(DateTime)
    is_check = Column(INTEGER(4))
    check_time = Column(DateTime)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    send_error_msg = Column(String(255))


class ErpStudent(Base):
    __tablename__ = 'erp_student'

    id = Column(INTEGER(11), primary_key=True)
    stu_username = Column(String(125), comment='手机号')
    stu_name = Column(String(125), comment='学生姓名')
    stu_birth = Column(DateTime, comment='出生日期')
    stu_gender = Column(INTEGER(4), comment='性别 1 男 2 女')
    stu_avatar = Column(String(555), comment='头像或形象照')
    stu_area = Column(String(45), comment='城市')
    stu_address = Column(String(255), comment='地址')
    stu_grade = Column(String(125), comment='账号建立时的年级')
    stu_idcard = Column(String(125), comment='身份证')
    stu_school_name = Column(String(125), comment='就读学校')
    stu_wallet_amount = Column(DECIMAL(10, 2), comment='钱包额度')
    stu_serial = Column(String(125), comment='学号')
    classin_uid = Column(INTEGER(11), comment='classin uid')
    classin_sync = Column(INTEGER(4), comment='是否同步classin')
    update_time = Column(DateTime)
    create_time = Column(DateTime)
    disable = Column(INTEGER(4), comment='数据状态')
    mall_user_id = Column(INTEGER(11))
    how_known = Column(String(125), comment='如何了解进阶')
    wechat_open_id = Column(String(125))
    campus_id = Column(INTEGER(4))
    stu_passwd = Column(String(255))
    is_blacklist = Column(INTEGER(4))


class ErpStudentConsultant(Base):
    __tablename__ = 'erp_student_consultant'

    id = Column(INTEGER(11), primary_key=True)
    account_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))
    current_point = Column(INTEGER(4))
    consultant_qr = Column(String(500))
    nick_name = Column(String(45))
    enterprise_image = Column(String(125))


class ErpStudentCouponCourse(Base):
    __tablename__ = 'erp_student_coupon_course'

    id = Column(INTEGER(11), primary_key=True)
    coupon_id = Column(INTEGER(11))
    course_id = Column(INTEGER(11), nullable=False)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpStudentDiscountCoupon(Base):
    __tablename__ = 'erp_student_discount_coupon'

    id = Column(INTEGER(11), primary_key=True)
    amount = Column(DECIMAL(10, 2))
    expired_time = Column(DateTime)
    limit_money = Column(DECIMAL(10, 2))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))
    status = Column(INTEGER(4), comment='0 未激活 1 可用（未使用） 2 已使用 3已失效')
    is_universal = Column(INTEGER(4), server_default=text("'0'"), comment='是否通用')
    stu_id = Column(INTEGER(11), nullable=False)


class ErpStudentDiscountFixed(Base):
    __tablename__ = 'erp_student_discount_fixed'

    id = Column(INTEGER(11), primary_key=True)
    stu_id = Column(INTEGER(11), nullable=False)
    discount_rate = Column(Float(6))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    create_by = Column(INTEGER(4))
    update_by = Column(INTEGER(4))
    disable = Column(INTEGER(4))


class ErpStudentEwalletLog(Base):
    __tablename__ = 'erp_student_ewallet_log'

    id = Column(INTEGER(11), primary_key=True)
    stu_id = Column(INTEGER(11))
    change_type = Column(INTEGER(4), comment='1 增加 2 减少')
    amount = Column(Float(10), comment='数量')
    desc = Column(String(1000), comment='描述')
    create_by = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    rest_balance = Column(Float(10), comment='剩余金额')
    receipt_id = Column(INTEGER(11), comment='关联单据')
    from_order_id = Column(INTEGER(11), comment='从哪个订单结转')


class ErpStudentPlus(Base):
    __tablename__ = 'erp_student_plus'

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    stu_id = Column(INTEGER(11), primary_key=True, nullable=False)
    training_plan = Column(String(45))
    consultant_status = Column(INTEGER(4))
    consultant_id = Column(INTEGER(11))
    parental_appeal = Column(String(255))
    character = Column(String(45))
    is_multi_child = Column(String(125))
    real_guardian = Column(String(45))
    major_concern_point = Column(String(45))
    reject_point = Column(String(45))
    famous_teacher_effect = Column(String(45))
    other_comments = Column(String(255))
    create_time = Column(DateTime)
    create_by = Column(INTEGER(11))
    update_by = Column(INTEGER(11))
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    complete_exam = Column(INTEGER(4))
    course_labels = Column(JSON)


class ErpStudentWechat(Base):
    __tablename__ = 'erp_student_wechat'

    id = Column(INTEGER(11), primary_key=True)
    stu_id = Column(INTEGER(11))
    wechat_open_id = Column(String(125))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))
    nick_name = Column(String(125))


class ErpStudyClassReport(Base):
    __tablename__ = 'erp_study_class_report'

    id = Column(INTEGER(11), primary_key=True)
    study_report_id = Column(INTEGER(11))
    class_id = Column(INTEGER(11))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpStudyReport(Base):
    __tablename__ = 'erp_study_report'

    id = Column(INTEGER(11), primary_key=True)
    report_name = Column(String(255))
    desc = Column(LONGTEXT)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpWechatApp(Base):
    __tablename__ = 'erp_wechat_app'
    __table_args__ = {'comment': '企微应用表'}

    id = Column(INTEGER(11), primary_key=True, comment='ID')
    parent_id = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='父级ID')
    app_id = Column(String(255), nullable=False, server_default=text("''"), comment='应用ID')
    secret = Column(String(255), nullable=False, server_default=text("''"), comment='密钥')
    code = Column(String(255), nullable=False, server_default=text("''"), comment='功能代码')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='删除状态')


class ErpWechatChannel(Base):
    __tablename__ = 'erp_wechat_channel'
    __table_args__ = {'comment': '企微渠道活码表'}

    id = Column(INTEGER(11), primary_key=True, comment='ID')
    wx_config_id = Column(String(255), server_default=text("''"), comment='企业微信联系我方式配置ID')
    wx_qrcode = Column(String(500), server_default=text("''"), comment='微信二维码')
    channel_group_id = Column(INTEGER(11), server_default=text("'0'"), comment='分组ID')
    name = Column(String(255), server_default=text("''"), comment='活码名称')
    skip_verify = Column(INTEGER(4), server_default=text("'1'"), comment='外部客户添加时是否无需验证 1需要')
    lable_ids = Column(Text, comment='标签IDs 英文逗号分隔')
    type = Column(INTEGER(4), server_default=text("'1'"), comment='类型 1单人 2多人')
    special_date_enable = Column(INTEGER(4), server_default=text("'0'"), comment='特殊日期启用 1是')
    emp_add_limit = Column(INTEGER(4), server_default=text("'0'"), comment='员工添加上限 1开启')
    emp_backup = Column(Text, comment='备用人员ID 英文逗号分隔')
    welcome_enable = Column(INTEGER(4), server_default=text("'0'"), comment='欢迎语启用 1是')
    welcome_week_enable = Column(INTEGER(4), server_default=text("'0'"), comment='欢迎语周期启用 1是')
    welcome_special_enable = Column(INTEGER(4), server_default=text("'0'"), comment='特殊时期欢迎语启用 1是')
    status = Column(INTEGER(4), server_default=text("'0'"), comment='删除状态')
    create_by = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_by = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    remark = Column(String(500), server_default=text("''"), comment='备注')
    teacher_id = Column(INTEGER(11), server_default=text("'0'"), comment='老师ID')
    stu_id = Column(INTEGER(11), server_default=text("'0'"), comment='学生ID')
    class_id = Column(INTEGER(11), server_default=text("'0'"), comment='就读班级ID')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='删除状态')


class ErpWechatChannelrecord(Base):
    __tablename__ = 'erp_wechat_channelrecord'
    __table_args__ = {'comment': '企微渠道活码客户记录表'}

    id = Column(INTEGER(11), primary_key=True, comment='ID')
    channel_id = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='渠道活码ID')
    type = Column(INTEGER(4), nullable=False, server_default=text("'0'"), comment='类型 1新增 2被客户删除 3删除')
    emp_id = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='员工ID')
    work_emp_id = Column(String(255), nullable=False, server_default=text("''"), comment='员工企业微信ID')
    external_user_id = Column(String(255), nullable=False, server_default=text("''"), comment='客户企业微信ID')
    customer_name = Column(String(255), nullable=False, server_default=text("''"), comment='客户名称')
    status = Column(INTEGER(4), nullable=False, server_default=text("'0'"), comment='删除状态')
    create_by = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='创建人')
    create_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_by = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='修改人')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='删除状态')


class ErpWechatChannelweek(Base):
    __tablename__ = 'erp_wechat_channelweek'
    __table_args__ = {'comment': '企微渠道周期表'}

    id = Column(INTEGER(11), primary_key=True, comment='ID')
    channel_id = Column(INTEGER(11), server_default=text("'0'"), comment='渠道活码ID')
    type = Column(INTEGER(4), server_default=text("'1'"), comment='类型 1标准周期 2特殊日期')
    week = Column(INTEGER(4), server_default=text("'0'"), comment='周几')
    start_date = Column(DateTime, comment='特殊开始日期')
    end_date = Column(DateTime, comment='特殊结束日期')
    is_default = Column(INTEGER(4), server_default=text("'0'"), comment='是否默认数据 1是')
    start_hours = Column(String(10), server_default=text("''"), comment='周期开始时间 精确到小时')
    end_hours = Column(String(10), server_default=text("''"), comment='周期结束时间 精确到小时')
    emp_ids = Column(Text, comment='员工IDs 英文逗号分隔')
    dept_ids = Column(Text, comment='部门IDs 英文逗号分隔')
    status = Column(INTEGER(4), server_default=text("'0'"), comment='删除状态')
    create_by = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_by = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    remark = Column(String(500), server_default=text("''"), comment='备注')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='删除状态')


class ErpWechatCheckin(Base):
    __tablename__ = 'erp_wechat_checkin'
    __table_args__ = {'comment': '企微打卡数据表'}

    id = Column(INTEGER(11), primary_key=True, comment='ID')
    account_id = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='账号ID')
    date = Column(Date, nullable=False, comment='日期')
    rule = Column(String(255), nullable=False, server_default=text("''"), comment='打卡规则')
    schedule = Column(String(255), nullable=False, server_default=text("''"), comment='班次')
    first_time = Column(String(10), nullable=False, server_default=text("''"), comment='最早打卡时间 时分')
    last_time = Column(String(10), nullable=False, server_default=text("''"), comment='最晚打卡时间 时分')
    number = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='打卡次数')
    state = Column(INTEGER(4), nullable=False, server_default=text("'0'"), comment='校准状态类型 1迟到 2早退 3缺卡 4旷工 5地点异常 6设备异常')
    create_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    dept_name = Column(String(255), nullable=False, server_default=text("''"), comment='部门名称')
    record_type = Column(INTEGER(4), nullable=False, server_default=text("'1'"), comment='记录类型 1固定上下班 2外出 3按班次上下班 4自由签到 5加班 7无规则')
    day_type = Column(INTEGER(4), nullable=False, server_default=text("'0'"), comment='日报类型 0工作日日报 1休息日日报')
    exception_count = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='当日此异常的次数')
    exception_duration = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='当日此异常的时长(分钟)')
    first_class_time = Column(String(10), nullable=False, server_default=text("''"), comment='第一节课上课时间')
    checkin_state = Column(INTEGER(4), nullable=False, server_default=text("'1'"), comment='考勤状态 1正常 2课前迟到 3课后迟到 4早退 5缺卡 6旷工 7异常')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='删除状态')


class ErpWechatClassuser(Base):
    __tablename__ = 'erp_wechat_classuser'
    __table_args__ = {'comment': '企微班级群关联学生表'}

    id = Column(INTEGER(11), primary_key=True, comment='ID')
    class_id = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='班级ID')
    wechat_id = Column(String(255), nullable=False, server_default=text("''"), comment='企微群ID')
    external_user_id = Column(String(255), nullable=False, server_default=text("''"), comment='企微外部联系人UserId')
    create_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    state = Column(INTEGER(4), nullable=False, server_default=text("'0'"), comment='状态 0在群 1已退群')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='删除状态')


class ErpWechatExternaluser(Base):
    __tablename__ = 'erp_wechat_externaluser'
    __table_args__ = {'comment': '企微外部用户关联表'}

    id = Column(INTEGER(11), primary_key=True, comment='ID')
    teacher_id = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='老师ID')
    stu_id = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='学生ID')
    external_user_id = Column(String(255), nullable=False, server_default=text("''"), comment='企微外部联系人UserId')
    customer_name = Column(String(255), nullable=False, server_default=text("''"), comment='客户微信昵称')
    disable = Column(INTEGER(4), server_default=text("'0'"), comment='删除状态')
    create_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class ErpWorkflowCostType(Base):
    __tablename__ = 'erp_workflow_cost_type'

    id = Column(INTEGER(11), primary_key=True)
    workflow_id = Column(INTEGER(11))
    cost_type_id = Column(INTEGER(11))
    cost_type_name = Column(String(255))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    disable = Column(INTEGER(4))


class ErpWorkflowDef(Base):
    __tablename__ = 'erp_workflow_def'
    __table_args__ = {'comment': '流程定义表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    workflow_desc = Column(String(50), comment='流程编码')
    workflow_name = Column(String(100), comment='流程名称')
    workflow_type = Column(INTEGER(4), comment='流程类型')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='状态：0禁用 1启用')
    remark = Column(String(255), comment='备注')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    create_by = Column(INTEGER(11), comment='创建人')
    update_by = Column(INTEGER(11), comment='更新人')
    disable = Column(INTEGER(4))


class ErpWorkflowInstance(Base):
    __tablename__ = 'erp_workflow_instance'
    __table_args__ = {'comment': '流程实例表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    workflow_id = Column(BIGINT(20), comment='流程定义ID')
    workflow_code = Column(String(50), comment='流程编码')
    workflow_name = Column(String(100), comment='流程名称')
    business_id = Column(BIGINT(20), comment='业务单据ID')
    business_type = Column(String(50), comment='业务单据类型')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='状态：0草稿 1进行中 2已完成 3已取消 4已驳回')
    current_node_id = Column(BIGINT(20), comment='当前节点ID')
    current_node_name = Column(String(100), comment='当前节点名称')
    submit_time = Column(DateTime, comment='提交时间')
    finish_time = Column(DateTime, comment='完成时间')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    create_by = Column(INTEGER(11), comment='创建人')
    update_by = Column(INTEGER(11), comment='更新人')
    disable = Column(INTEGER(4))


class ErpWorkflowInstanceReviewer(Base):
    __tablename__ = 'erp_workflow_instance_reviewer'
    __table_args__ = {'comment': '流程实例审批人表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    instance_id = Column(BIGINT(20), nullable=False, comment='流程实例ID')
    node_id = Column(BIGINT(20), nullable=False, comment='节点ID')
    reviewer_id = Column(BIGINT(20), nullable=False, comment='审批人ID')
    reviewer_name = Column(String(50), comment='审批人姓名')
    status = Column(TINYINT(1), server_default=text("'0'"), comment='状态：0待审批 1已同意 2已驳回')
    review_time = Column(DateTime, comment='审批时间')
    comments = Column(String(500), comment='审批意见')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    disable = Column(INTEGER(4))


class ErpWorkflowNode(Base):
    __tablename__ = 'erp_workflow_node'
    __table_args__ = {'comment': '流程节点表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    workflow_id = Column(BIGINT(20), nullable=False, comment='流程定义ID')
    node_desc = Column(String(50), nullable=False, comment='节点描述')
    node_name = Column(String(100), nullable=False, comment='节点名称')
    node_type = Column(TINYINT(1), server_default=text("'1'"), comment='节点类型：1审批节点 2抄送节点')
    approver_type = Column(TINYINT(1), comment='审批人类型：1指定人 2指定角色 3部门主管 4连续多级上级 5指定岗位')
    approver_ids = Column(String(500), comment='审批人ID列表（逗号分隔）')
    approver_names = Column(String(500), comment='审批人名称列表（逗号分隔）')
    sort_no = Column(INTEGER(11), server_default=text("'0'"), comment='排序号')
    is_countersign = Column(TINYINT(1), server_default=text("'0'"), comment='是否会签：0或签 1是')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    create_by = Column(INTEGER(11), comment='创建人')
    update_by = Column(INTEGER(11), comment='更新人')
    node_action = Column(INTEGER(4), comment='节点动作')
    is_finance_related = Column(TINYINT(1), server_default=text("'0'"), comment='是否涉及出入账：0否 1是')
    is_continuous_approval = Column(TINYINT(1), server_default=text("'0'"), comment='是否连续多级上级：0否 1是')
    disable = Column(INTEGER(4))


class ErpWorkflowRecord(Base):
    __tablename__ = 'erp_workflow_record'
    __table_args__ = {'comment': '审批记录表'}

    id = Column(BIGINT(20), primary_key=True, comment='主键ID')
    instance_id = Column(BIGINT(20), nullable=False, comment='流程实例ID')
    node_id = Column(BIGINT(20), nullable=False, comment='节点ID')
    node_name = Column(String(100), comment='节点名称')
    approver_id = Column(BIGINT(20), comment='审批人ID')
    approver_name = Column(String(50), comment='审批人姓名')
    approve_status = Column(TINYINT(1), server_default=text("'0'"), comment='审批状态：0待审批 1同意 2驳回 3转交 4撤销')
    approve_time = Column(DateTime, comment='审批时间')
    approve_opinion = Column(String(500), comment='审批意见')
    sort_no = Column(INTEGER(11), server_default=text("'0'"), comment='排序号')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    disable = Column(INTEGER(4))


class MallAccount(Base):
    __tablename__ = 'mall_account'

    UserId = Column(INTEGER(11), primary_key=True)
    Uid = Column(INTEGER(11), comment='rb_account表Id')
    StuId = Column(INTEGER(11), comment='学生表')
    OpenId = Column(String(45), comment='微信小程序小课堂openId')
    CreateTime = Column(DateTime)
    DeliveryAddress = Column(String(1000), comment='收货地址')
    AvatarImg = Column(String(255), comment='头像')
    PhoneNumber = Column(String(45), comment='电话')
    RealName = Column(String(45), comment='真实姓名')


class MallComplain(Base):
    __tablename__ = 'mall_complain'

    Id = Column(INTEGER(11), primary_key=True, comment='投诉主键编号')
    ComplainCode = Column(String(50), server_default=text("''"), comment='投诉编号')
    ComplainTypeId = Column(INTEGER(11), server_default=text("'0'"), comment='投诉类型')
    ComplainObject = Column(String(500), server_default=text("''"), comment='投诉对象')
    ComplainContent = Column(String(2000), server_default=text("''"), comment='投诉内容')
    SeverityLevel = Column(INTEGER(11), server_default=text("'0'"), comment='严重程度')
    DisposeStatus = Column(INTEGER(11), server_default=text("'0'"), comment='处理状态')
    NeedResponse = Column(INTEGER(4), comment='是否需要回复')
    ComplainResponse = Column(String(2000), server_default=text("''"), comment='回复')
    NextPersionId = Column(INTEGER(11), server_default=text("'0'"), comment='当前处理人员')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态(0-正常,1-禁用)')


class MallComplainConfig(Base):
    __tablename__ = 'mall_complain_config'

    Id = Column(INTEGER(11), primary_key=True, comment='投诉类型编号')
    Name = Column(String(255), server_default=text("''"), comment='投诉配置名称')
    NextUserId = Column(INTEGER(11), server_default=text("'0'"), comment='初始处理人')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态(0-正常,1-禁用)')
    Viewer = Column(String(255))


class MallMerchantConfig(Base):
    __tablename__ = 'mall_merchant_config'

    Id = Column(INTEGER(11), primary_key=True)
    CmbMerId = Column(String(125))
    CmbUserId = Column(String(125))
    CmdAppId = Column(String(125))
    CmdAppSecret = Column(String(125))
    AppId = Column(String(45))
    Sk = Column(LONGTEXT)
    Pk = Column(LONGTEXT)
    NotifyUrl = Column(String(255))
    CmbBaseUrl = Column(String(255))
    AppSecret = Column(String(255))
    Type = Column(INTEGER(4), comment='1 微信 2 支付宝')
    Status = Column(String(255))
    Enable = Column(INTEGER(4))
    MerchantName = Column(String(45))
    Env = Column(String(45))
    AlipaySk = Column(LONGTEXT)
    AlipayPk = Column(LONGTEXT)


class MallOrder(Base):
    __tablename__ = 'mall_order'

    Id = Column(INTEGER(11), primary_key=True, comment='小课堂订单信息id')
    ShoppingAddressDetail = Column(String(125), comment='收货地址')
    ShoppingMobile = Column(String(45), comment='收货电话')
    ShoppingConsignee = Column(String(45), comment='收货人')
    BuyMsg = Column(String(255), comment='订单备注')
    OfferId = Column(INTEGER(11), comment='报价单Id')
    OrderNo = Column(String(125), comment='订单号')
    MerchantsNo = Column(String(125), comment='商户单号')
    StuId = Column(INTEGER(11))
    CreateTime = Column(DateTime)
    SnapShot = Column(JSON, comment='交易快照')
    ShoppingAddressProvince = Column(String(45))
    ShoppingAddressCity = Column(String(45))
    ShoppingAddressDistrict = Column(String(45))
    Status = Column(INTEGER(11), server_default=text("'0'"))
    PayState = Column(INTEGER(11), server_default=text("'0'"))
    UpdateTime = Column(DateTime)
    CmbOrderId = Column(String(125), comment='招行id')
    CmbTradeState = Column(String(45), comment='定时任务状态')
    PayOpenId = Column(String(45), comment='支付的微信openid')
    PayType = Column(String(45), comment='支付平台')
    thirdOrderId = Column(String(125), comment='三方订单号')
    OrderFrom = Column(INTEGER(4), server_default=text("'2'"))
    MerchantConfigId = Column(INTEGER(11), comment=' 订单支付商户')


class MallProductsBak(Base):
    __tablename__ = 'mall_products_bak'

    ProductId = Column(INTEGER(11), primary_key=True)
    Name = Column(String(255), comment='商品名称')
    CarouselImage = Column(String(4000), comment='轮播图  json 格式  第一张为封面图')
    VideoAddress = Column(String(255), comment='视频地址')
    CustomShareTitles = Column(String(255), comment='自定义分享标题')
    ProductStatus = Column(INTEGER(11), server_default=text("'0'"), comment='商品状态   1销售中  2下架中')
    InventoryNum = Column(INTEGER(11), server_default=text("'0'"), comment='库存数量')
    SellingPrice = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='售价')
    OriginalPrice = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='原价')
    Unit = Column(String(20), comment='单位')
    SalesNum = Column(INTEGER(11), server_default=text("'0'"), comment='已售出数量')
    ProductNumbers = Column(String(50), comment='商品货号')
    IntegralPresentType = Column(INTEGER(11), server_default=text("'0'"), comment='赠送类型  1固定值  2百分比')
    ProductDetails = Column(Text, comment='商品详情')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态')
    TenantId = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='商户号')
    MallBaseId = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='小程序id')
    CreateDate = Column(DateTime)
    UpdateDate = Column(DateTime)
    productType = Column(INTEGER(11), server_default=text("'0'"), comment='商品类型  枚举')
    Advertising = Column(String(2000), comment='广告词')
    SubName = Column(String(255), comment='副标题')
    ShelvesDate = Column(DateTime, comment='自动上架时间')
    DownDate = Column(DateTime, comment='自动下架时间')
    CourseLable = Column(String(1000), comment='课程标签')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    GoodsEduType = Column(INTEGER(11), server_default=text("'0'"), comment='教育商品类型   0 正常商品   1咖啡劵   2耗材类  3教室类')
    MaterialId = Column(INTEGER(11), server_default=text("'0'"), comment='耗材对应的ID')
    EduJsonData = Column(MEDIUMTEXT, comment='教育相关信息JSON对象')
    EduGradeId = Column(INTEGER(11), server_default=text("'0'"), comment='进阶小课堂年级Id')
    EduClassTypeId = Column(INTEGER(11), server_default=text("'0'"), comment='进阶小课堂班型Id')
    EduTeacherId = Column(INTEGER(11), server_default=text("'0'"), comment='进阶小课堂老师Id')
    IsShow = Column(INTEGER(11), server_default=text("'1'"), comment='是否显示商品(1-显示，0-不显示)')


class MallServiceGuarantee(Base):
    __tablename__ = 'mall_service_guarantee'

    GuaranteeId = Column(INTEGER(11), primary_key=True, comment='服务保障表id')
    Title = Column(String(45), comment='标题')
    Desc = Column(LONGTEXT, comment='描述')
    Status = Column(INTEGER(4), server_default=text("'0'"), comment='状态 1-删除')
    CreatTime = Column(DateTime)
    Type = Column(INTEGER(4), comment='1-购课服务；2-退费服务')


class MallSettingsAccount(Base):
    __tablename__ = 'mall_settings_account'

    TenantId = Column(INTEGER(11), primary_key=True, comment='商户号')
    Account = Column(String(20), comment='账号')
    Password = Column(String(50), comment='密码')
    Name = Column(String(255), comment='姓名/企业名称')
    MobilePhone = Column(String(20), comment='手机号码')
    WeChatNum = Column(String(100), comment='微信号')
    ApplyReason = Column(String(2000), comment='申请原因')
    IDCardPositiveImg = Column(String(255), comment='身份证正面图片')
    IDCardReverseImg = Column(String(255), comment='身份证背面图片')
    BusinessLicenseImg = Column(String(255), comment='营业执照图片')
    CreateDate = Column(DateTime)
    IsEffective = Column(INTEGER(255), server_default=text("'0'"), comment='是否永久有效(0-默认，1-永久有效)')
    AccountValidate = Column(DateTime, comment='账号有效期')
    CreateMiniPrograme = Column(INTEGER(11), server_default=text("'0'"), comment='可创建小程序数量')
    AccountStatus = Column(INTEGER(11), server_default=text("'0'"), comment='账号状态(0-默认，1-提交申请，2-审核通过，3-审核拒绝）')


class MallSettingsCampus(Base):
    __tablename__ = 'mall_settings_campus'

    CampusId = Column(INTEGER(11), primary_key=True)
    CampusName = Column(String(45))
    Address = Column(String(255))
    Phone = Column(String(45))
    CampusType = Column(INTEGER(11), comment='图片类型  1 - 校区,2 - 默认客服')
    QrCode = Column(String(255))
    Latitude = Column(Float(10), comment='纬度')
    Longitude = Column(Float(10), comment='经度')
    SchoolPinyin = Column(String(255))


class MallSettingsImg(Base):
    __tablename__ = 'mall_settings_img'

    ImgId = Column(INTEGER(11), primary_key=True)
    ImgType = Column(INTEGER(4), comment='图片类型 1 - 首页封面（师资） ,2 - 首页banner, 3 - 课程列表页banner，')
    ImgUrl = Column(String(255))
    Comment = Column(String(255), comment='备注')
    CreateTime = Column(DateTime)
    PathUrl = Column(String(255), comment='跳转链接')


class MallSettingsMiniprogram(Base):
    __tablename__ = 'mall_settings_miniprogram'

    SettingId = Column(INTEGER(11), primary_key=True, comment='Id')
    ProgramName = Column(String(255), comment='程序名称')
    CreateDate = Column(DateTime, comment='创建时间')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='状态(0-正常，1-禁用)')
    MiniAppId = Column(String(100), comment='小程序AppId')
    MiniAppSecret = Column(String(100), comment='小程序AppSecret')
    WeChatPayMerchants = Column(String(50), comment='微信支付商户号')
    WeChatApiSecret = Column(String(100), comment='微信支付Api密钥')
    WeChatPayCertificateUrl = Column(String(500), comment='支付证书路径')
    WeChatPayCertificate = Column(String(1000), comment='微信支付证书')
    WeChatPayPrivateKey = Column(String(1000), comment='微信支付私钥')
    WechatPublicQr = Column(String(255), comment='公众号二维码')
    ServiceQr = Column(String(255), comment='服务二维码')
    About = Column(LONGTEXT, comment='关于进阶')


class MallShoppingCart(Base):
    __tablename__ = 'mall_shopping_cart'

    Id = Column(INTEGER(11), primary_key=True)
    ClassId = Column(INTEGER(11))
    UserId = Column(INTEGER(11))
    Status = Column(INTEGER(4), comment='转态：1-删除；0-正常')
    CreateTime = Column(DateTime, comment='创建时间')


class RbAccounttype(Base):
    __tablename__ = 'rb_accounttype'
    __table_args__ = {'comment': '账户类型'}

    ID = Column(INTEGER(11), primary_key=True, comment='编号')
    CreateBy = Column(INTEGER(11), comment='创建人')
    CreateDate = Column(DateTime, comment='创建日期')
    RB_Group_Id = Column(INTEGER(11), comment='集团')
    RB_Branch_Id = Column(INTEGER(11), comment='公司')
    Status = Column(INTEGER(11), comment='状态')
    Remark = Column(String(200), comment='备注')
    Name = Column(String(200), comment='类型名称')
    IsPublic = Column(INTEGER(11), comment='是否对公')
    IsVirtual = Column(INTEGER(11), comment='虚拟类型')
    GroupName = Column(String(200))
    BranchName = Column(String(200))
    CreateByName = Column(String(200))


class RbClass(Base):
    __tablename__ = 'rb_class'

    ClassId = Column(INTEGER(11), primary_key=True, comment='主键(班级ID)')
    ClassName = Column(String(50, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='班级名称')
    CouseId = Column(INTEGER(11), server_default=text("'0'"), comment='课程Id')
    Teacher_Id = Column(INTEGER(11), server_default=text("'0'"), comment='讲师Id')
    Assist_Id = Column(INTEGER(11), server_default=text("'0'"), comment='助教Id')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校编号')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态(0-正常,1-禁用)')
    ClassPersion = Column(INTEGER(11), server_default=text("'0'"), comment='招生人数')
    OpenTime = Column(DateTime, comment='开班时间')
    EndOrderTime = Column(DateTime, comment='截止报名时间')
    OriginalPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='原价')
    SellPrice = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='售价')
    IsStepPrice = Column(INTEGER(11), server_default=text("'0'"), comment='是否阶梯价(1-是)')
    ClassRoomId = Column(INTEGER(11), server_default=text("'0'"), comment='教室编号')
    IsOpenCommission = Column(INTEGER(11), server_default=text("'0'"), comment='是否开启提成设置(1-是)')
    CommissionType = Column(INTEGER(11), server_default=text("'0'"), comment='提成类型(1-人头，2-百分比）')
    CommissionValue = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='提成值')
    ClassHours = Column(INTEGER(11), server_default=text("'0'"), comment='课时')
    ClassStyle = Column(INTEGER(11), server_default=text("'0'"), comment='排课方式(1-周，2-月，3-固定日期，4-约课)')
    ClassStatus = Column(INTEGER(11), server_default=text("'0'"), comment='班级状态')
    InnerRemark = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='对内备注')
    OutRemark = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='对外备注')
    CompleteProgress = Column(INTEGER(11), server_default=text("'0'"), comment='完成进度')
    ClassType = Column(INTEGER(11), server_default=text("'0'"), comment='班级类型(1-学生班，2-社会班)')
    DefaultTimeJson = Column(String(4000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='默认时间字符串')
    DateJson = Column(String(4000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='默认选中的日期、周')
    EndClassDate = Column(DateTime, comment='结课日期')
    IsDeduction = Column(INTEGER(11), server_default=text("'0'"), comment='是否可抵扣(1-是)')
    IsSubscribe = Column(INTEGER(11), server_default=text("'0'"), comment='App是否可预约(1-是)')
    Point = Column(INTEGER(11), server_default=text("'0'"), comment='App预约点数')
    CourseClassType = Column(INTEGER(11), server_default=text("'0'"), comment='分类类型')
    StudentNumType = Column(INTEGER(11), server_default=text("'0'"), comment='上课人数')
    ClassHourMinute = Column(INTEGER(11), server_default=text("'45'"), comment='课时分钟数')
    ClassNo = Column(String(20, 'utf8mb4_unicode_ci'), comment='班级编码')
    ClassLetterNum = Column(INTEGER(11), server_default=text("'0'"), comment='字母排序')
    ClassScrollType = Column(INTEGER(11), server_default=text("'0'"), comment='开班类型   1正常班   2滚动班')
    ScrollMonth = Column(String(255, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='滚动开班月份')
    GUIDStr = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='班级GUID编号')
    CourseTimes = Column(INTEGER(11), server_default=text("'0'"), comment='计划排课次数')
    DetailInfo = Column(MEDIUMTEXT, comment='图文')
    CoverImage = Column(String(1000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='封面图')
    AuditStatus = Column(INTEGER(11), server_default=text("'0'"), comment='审核状态')
    WeChatId = Column(String(50, 'utf8mb4_unicode_ci'), comment='企微群ID')
    WeChatName = Column(String(255, 'utf8mb4_unicode_ci'), comment='群聊名称')
    WeChatConfigId = Column(String(50, 'utf8mb4_unicode_ci'), comment='进群配置ID')
    Mall_Goods_Id = Column(INTEGER(11), server_default=text("'0'"), comment='商城表RB_Goods的Id')
    ClassInCourseID = Column(INTEGER(11), server_default=text("'0'"), comment='classIn 课程 ID')
    Mall_Is_Show = Column(INTEGER(11), server_default=text("'1'"), comment='商城是否显示此班级(1-显示，0-不显示)')
    MallImage = Column(String(2000, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='商城图片')
    HourFeeRate = Column(DECIMAL(18, 2), server_default=text("'0.00'"), comment='老师课时费发放比例')
    EnrollPersion = Column(INTEGER(11), server_default=text("'0'"), comment='预招人数')
    StartOrderTime = Column(DateTime, comment='小程序上架时间')
    CourseDescImage = Column(String(255, 'utf8mb4_unicode_ci'))


class RbCourseCategory(Base):
    __tablename__ = 'rb_course_category'

    CateId = Column(INTEGER(11), primary_key=True, comment='主键（课程分类编号）')
    ParentId = Column(INTEGER(11), server_default=text("'0'"), comment='父级编号')
    CateName = Column(String(255, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='课程分类名称')
    SortNum = Column(INTEGER(11), server_default=text("'0'"), comment='排序')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校Id')
    CourseSubject = Column(INTEGER(11), server_default=text("'0'"), comment='所属科目')
    GUIDStr = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='课程分类GUID编号')
    Mall_Product_Category_Id = Column(INTEGER(11), server_default=text("'0'"), comment='商城表RB_Product_Category的Id')


class RbCourseConfig(Base):
    __tablename__ = 'rb_course_config'

    Id = Column(INTEGER(11), primary_key=True, comment='主键编号')
    ConfigType = Column(INTEGER(11), comment='配置类型')
    ConfigName = Column(String(500), server_default=text("''"), comment='配置名称')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校编号')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='删除状态(0-正常,1-禁用)')
    GUIDStr = Column(String(255), server_default=text("''"), comment='GUID编号')
    ConfigDesc = Column(String(500), comment='描述')
    Mall_Product_Category_Id = Column(INTEGER(11), server_default=text("'0'"), comment='商城表RB_Product_Category的Id')
    IsCalcRenew = Column(INTEGER(11), server_default=text("'0'"), comment='是否计入续报(1-统计,0-不统计)')
    IsCalcHourFee = Column(INTEGER(11), server_default=text("'0'"), comment='是否计入课时费(1-统计,0-不统计)')
    ParentId = Column(INTEGER(11), server_default=text("'0'"), comment='父级节点编号')
    IsStaticReport = Column(INTEGER(11), server_default=text("'0'"), comment='是否统计营收报表(1-统计)')


class RbCourseSubject(Base):
    __tablename__ = 'rb_course_subject'

    Id = Column(INTEGER(11), primary_key=True)
    SubjectName = Column(String(255, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='科目名称')
    AliasName = Column(String(255, 'utf8mb4_unicode_ci'), comment='科目别名')
    SubjectIcon = Column(String(255, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='科目图标')
    CreateBy = Column(INTEGER(11), server_default=text("'0'"), comment='创建人')
    CreateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    UpdateBy = Column(INTEGER(11), server_default=text("'0'"), comment='修改人')
    UpdateTime = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    School_Id = Column(INTEGER(11), server_default=text("'0'"), comment='学校Id')
    Group_Id = Column(INTEGER(11), server_default=text("'0'"), comment='集团编号')
    Status = Column(INTEGER(11), server_default=text("'0'"), comment='状态')
    GUIDStr = Column(String(100, 'utf8mb4_unicode_ci'), server_default=text("''"), comment='课程分类GUID编号')
    AliasNameReview = Column(String(255, 'utf8mb4_unicode_ci'), comment='审核用的科目别名')


class TempBank(Base):
    __tablename__ = 'temp_bank'

    id = Column(INTEGER(11), primary_key=True)
    employee_name = Column(String(255))
    bank_account = Column(String(255))
    bank_name = Column(String(255))


t_temp_enterprise = Table(
    'temp_enterprise', metadata,
    Column('employee_name', String(255)),
    Column('enterprise_name', String(255))
)


class TempSalary(Base):
    __tablename__ = 'temp_salary'

    id = Column(INTEGER(11), primary_key=True)
    employee_name = Column(String(45))
    salary_base = Column(Float(10))
    salary_performance = Column(Float(10))


class ErpEeoAnswerSheetScoresDetail(Base):
    __tablename__ = 'erp_eeo_answer_sheet_scores_detail'
    __table_args__ = {'comment': '答题卡成绩详情表'}

    id = Column(INTEGER(11), primary_key=True)
    answer_sheet_score_id = Column(INTEGER(11), comment='答题卡成绩ID')
    topic_id = Column(INTEGER(11), comment='题目ID')
    topic_type = Column(String(32), comment='题目类型')
    topic_max_score = Column(Float, comment='题目最大分值')
    topic_score = Column(Float, comment='题目得分')
    topic_result = Column(Text, comment='题目结果')
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class ErpEeoExamScoresDetail(Base):
    __tablename__ = 'erp_eeo_exam_scores_detail'

    id = Column(INTEGER(11), primary_key=True)
    exam_score_id = Column(INTEGER(11), nullable=False)
    topic_id = Column(INTEGER(11), nullable=False)
    topic_type = Column(String(10))
    topic_max_score = Column(INTEGER(11))
    topic_score = Column(INTEGER(11))
    topic_result = Column(JSON)
    created_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))


class MallClassGuarantee(Base):
    __tablename__ = 'mall_class_guarantee'

    Id = Column(INTEGER(11), primary_key=True)
    GuaranteeId = Column(INTEGER(11))
    ClassId = Column(INTEGER(11))
    CreateTime = Column(DateTime)
    Status = Column(INTEGER(4))


class MallClassImg(Base):
    __tablename__ = 'mall_class_img'

    ImgId = Column(INTEGER(11), primary_key=True)
    ImgType = Column(INTEGER(4), comment='图片类型  1 - 课程详情图片,2 - 大纲图片')
    ImgUrl = Column(String(255))
    Comment = Column(String(255), comment='备注')
    CreateTime = Column(DateTime)
    ClassId = Column(INTEGER(11))


class MallCourseCoverImg(Base):
    __tablename__ = 'mall_course_cover_img'

    Id = Column(INTEGER(11), primary_key=True)
    SubjectId = Column(INTEGER(11))
    CategoryId = Column(INTEGER(11))
    CreateTime = Column(DateTime)
    ImgUrl = Column(String(255))
