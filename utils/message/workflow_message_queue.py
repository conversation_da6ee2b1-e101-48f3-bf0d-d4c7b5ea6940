import json
import asyncio
from typing import List, Dict, Any
from loguru import logger

from modules.queue.redis_queue import RedisQueue
from utils.message.QyWechatMessage import send_markdown_to_users

# 定义队列名称
WORKFLOW_CC_MESSAGE_QUEUE = "workflow_cc_message_queue"

# 定义最大重试次数
MAX_RETRY_COUNT = 3

# 重试延迟（秒）
RETRY_DELAY = 300  # 5分钟后重试


async def add_cc_message_to_queue(cc_user_ids: List[str], markdown_content: str, retry_count: int = 0) -> bool:
    """
    将抄送消息添加到队列
    
    Args:
        cc_user_ids: 企业微信用户ID列表
        markdown_content: Markdown格式的消息内容
        retry_count: 当前重试次数
        
    Returns:
        成功返回True，失败返回False
    """
    try:
        message_data = {
            "cc_user_ids": cc_user_ids,
            "markdown_content": markdown_content,
            "retry_count": retry_count
        }
        
        # 将消息添加到队列
        redis_queue = RedisQueue()
        await redis_queue.produce_message(WORKFLOW_CC_MESSAGE_QUEUE, message_data)
        logger.info(f"抄送消息已成功添加到队列: {cc_user_ids}")
        return True
    except Exception as e:
        logger.error(f"将抄送消息添加到队列失败: {e}")
        return False


async def process_cc_message(message_data: Dict[str, Any]) -> bool:
    """
    处理队列中的抄送消息
    
    Args:
        message_data: 消息数据，包含cc_user_ids、markdown_content和retry_count
        
    Returns:
        成功返回True，失败返回False
    """
    cc_user_ids = message_data.get("cc_user_ids", [])
    markdown_content = message_data.get("markdown_content", "")
    retry_count = message_data.get("retry_count", 0)
    
    if not cc_user_ids or not markdown_content:
        logger.error(f"抄送消息数据不完整: {message_data}")
        return False
    
    try:
        # 发送消息
        logger.info(f"开始发送抄送消息给用户: {cc_user_ids}")
        result = await send_markdown_to_users(cc_user_ids, markdown_content)
        
        # 检查发送结果
        if result.get("errcode") == 0:
            logger.info(f"抄送消息发送成功: {cc_user_ids}")
            return True
        else:
            logger.error(f"抄送消息发送失败: {result}")
            
            # 如果未超过最大重试次数，则重新加入队列
            if retry_count < MAX_RETRY_COUNT:
                # 增加重试计数
                message_data["retry_count"] = retry_count + 1
                
                # 延迟一段时间后重新加入队列
                asyncio.create_task(delay_retry_message(message_data))
                logger.info(f"消息将在{RETRY_DELAY}秒后重试，当前重试次数: {retry_count + 1}")
            else:
                logger.error(f"抄送消息发送失败，已达到最大重试次数: {retry_count}")
            
            return False
    except Exception as e:
        logger.error(f"处理抄送消息时发生异常: {e}")
        
        # 如果未超过最大重试次数，则重新加入队列
        if retry_count < MAX_RETRY_COUNT:
            # 增加重试计数
            message_data["retry_count"] = retry_count + 1
            
            # 延迟一段时间后重新加入队列
            asyncio.create_task(delay_retry_message(message_data))
            logger.info(f"消息将在{RETRY_DELAY}秒后重试，当前重试次数: {retry_count + 1}")
        
        return False


async def delay_retry_message(message_data: Dict[str, Any]):
    """
    延迟重试发送消息
    
    Args:
        message_data: 消息数据
    """
    await asyncio.sleep(RETRY_DELAY)
    redis_queue = RedisQueue()
    await redis_queue.produce_message(WORKFLOW_CC_MESSAGE_QUEUE, message_data)
    logger.info(f"抄送消息已重新加入队列，重试次数: {message_data.get('retry_count')}")


async def start_cc_message_consumer():
    """
    启动抄送消息消费者
    
    注意：此函数应在应用启动时调用，用于持续处理队列中的消息
    """
    # logger.info("开始启动工作流抄送消息消费者")
    redis_queue = RedisQueue()
    
    while True:
        try:
            # 从队列中获取消息
            message = await redis_queue.consume_message(WORKFLOW_CC_MESSAGE_QUEUE)
            
            if message:
                # 处理消息
                asyncio.create_task(process_cc_message(message))
            else:
                # 如果没有消息，等待一段时间再尝试
                await asyncio.sleep(5)
                
        except Exception as e:
            logger.error(f"消费抄送消息时发生异常: {e}")
            await asyncio.sleep(5)  # 发生异常时等待一段时间再继续 