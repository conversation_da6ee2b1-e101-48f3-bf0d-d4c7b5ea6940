import json
import random
import asyncio
from typing import Dict, Any, List, Union
from loguru import logger

from settings import SMS_CONFIG, SMS_EXPIRED_MINUTES, SMS_CODE_LENGTH
from alibabacloud_dysmsapi20170525.client import Client as Dysmsapi20170525Client
from alibabacloud_dysmsapi20170525.models import SendSmsResponse
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysmsapi_20170525_models
from alibabacloud_tea_util import models as util_models


class AliSmsHandler:
    """
    阿里云短信服务处理类
    """
    def __init__(self):
        """
        初始化阿里云短信服务
        """
        config = open_api_models.Config(
            access_key_id=SMS_CONFIG["ALIBABA_CLOUD_ACCESS_KEY_ID"],
            access_key_secret=SMS_CONFIG["ALIBABA_CLOUD_ACCESS_KEY_SECRET"]
        )
        config.endpoint = SMS_CONFIG["ENDPOINT"]
        self.client = Dysmsapi20170525Client(config)
        
    async def send_sms(self, phone_number: str, template_code: str, template_param: Dict[str, Any]) -> SendSmsResponse:
        """
        发送短信
        
        参数:
            phone_number: 手机号码
            template_code: 短信模板代码
            template_param: 短信模板参数
            
        返回:
            SendSmsResponse: 发送结果
        """
        try:
            # 将模板参数转换为JSON字符串
            template_param_str = json.dumps(template_param)
            
            # 创建发送短信请求
            send_sms_request = dysmsapi_20170525_models.SendSmsRequest(
                phone_numbers=phone_number,
                sign_name=SMS_CONFIG['SIGN_NAME'],
                template_code=template_code,
                template_param=template_param_str
            )
            
            # 发送短信并获取响应
            response = await self.client.send_sms_with_options_async(
                send_sms_request, 
                util_models.RuntimeOptions()
            )
            
            # 记录日志
            if response.body.code == "OK":
                logger.info(f"发送短信成功，手机号: {phone_number}")
            else:
                logger.error(f"发送短信失败: {response.body.message}, 手机号: {phone_number}")
            
            return response
        except Exception as e:
            logger.error(f"发送短信异常: {str(e)}")
            raise

    async def send_verification_code(self, phone_number: str) -> tuple[str, SendSmsResponse]:
        """
        发送验证码短信
        
        参数:
            phone_number: 手机号码
            
        返回:
            tuple: (验证码, 发送结果)
        """
        # 生成验证码
        code = generate_code()
        
        # 发送验证码短信
        response = await self.send_sms(
            phone_number=phone_number,
            template_code=SMS_CONFIG["VERIFICATION_TEMPLATE_CODE"],
            template_param={"code": code}
        )
        
        return code, response


def generate_code() -> str:
    """
    生成SMS验证码
    
    返回:
        str: 验证码
    """
    return ''.join(random.choices('0123456789', k=SMS_CODE_LENGTH))


# 实例化一个全局对象，方便直接调用
ali_sms = AliSmsHandler()


async def send_sms(phone_number: str, template_code: str, template_param: Dict[str, Any]) -> SendSmsResponse:
    """
    发送短信的便捷函数
    
    参数:
        phone_number: 手机号码
        template_code: 短信模板代码
        template_param: 短信模板参数
        
    返回:
        SendSmsResponse: 发送结果
    """
    return await ali_sms.send_sms(phone_number, template_code, template_param)


async def send_verification_code(phone_number: str) -> tuple[str, SendSmsResponse]:
    """
    发送验证码短信的便捷函数
    
    参数:
        phone_number: 手机号码
        
    返回:
        tuple: (验证码, 发送结果)
    """
    return await ali_sms.send_verification_code(phone_number)


async def main():
    """
    测试函数
    """
    # 测试发送验证码
    code, resp = await send_verification_code("17729832192")
    print(f"生成的验证码: {code}")
    print(f"发送结果: {resp.body.to_map()}")

    # 测试发送自定义模板短信
    resp = await send_sms("17729832192", 'SMS_463629642', {
        "code": generate_code()
    })
    print(resp.body.to_map())


if __name__ == '__main__':
    asyncio.run(main())
