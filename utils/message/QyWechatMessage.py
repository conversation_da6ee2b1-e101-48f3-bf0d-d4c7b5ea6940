from aiocache import <PERSON>ache, cached
import aiohttp
import json
import time
from typing import List, Dict, Any
from loguru import logger
from settings import REDIS_CONFIG, QY_WECHAT_CONFIG
from aiocache.serializers import JsonSerializer


class QyWechatMessage:
    """
    企业微信应用消息发送类
    """
    def __init__(self):
        self.corpid = QY_WECHAT_CONFIG["CORPID"]  # 企业微信 corpId
        self.corpsecret = QY_WECHAT_CONFIG["JJXZS_SECRET"]  # 进阶小助手 secret
        self.agentid = QY_WECHAT_CONFIG["JJXZS_AGENT_ID"]  # 进阶小助手 AgentId
        self.access_token = None
        self.expires_time = 0
        self.session = None

    async def _ensure_session(self):
        """
        确保aiohttp会话已创建
        """
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session

    @cached(ttl=6000, cache=Cache.REDIS, key="jjxzs_access_token", serializer=JsonSerializer(), 
            namespace="qywechat", endpoint=REDIS_CONFIG["host"], port=REDIS_CONFIG["port"], db=REDIS_CONFIG["db"], password=REDIS_CONFIG["password"])
    async def _get_access_token(self) -> str:
        """
        获取企业微信 access_token
        
        返回:
            str: access_token
        """
        # 如果 token 未过期，直接返回
        if self.access_token and time.time() < self.expires_time:
            return self.access_token
        
        # token 过期或不存在，重新获取
        url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={self.corpid}&corpsecret={self.corpsecret}"
        
        try:
            session = await self._ensure_session()
            async with session.get(url) as response:
                result = await response.json()
                
                if result.get("errcode") == 0:
                    self.access_token = result.get("access_token")
                    # token 有效期为7200秒，这里提前200秒更新
                    self.expires_time = time.time() + 7000
                    return self.access_token
                else:
                    logger.error(f"获取企业微信 access_token 失败: {result}")
                    return None
        except Exception as e:
            logger.error(f"获取企业微信 access_token 异常: {str(e)}")
            return None

    async def send_text_message(self, user_ids: List[str], content: str) -> Dict[str, Any]:
        """
        发送文本消息给指定员工
        
        参数:
            user_ids: 员工ID列表，多个用户用'|'分隔
            content: 消息内容
            
        返回:
            Dict: 发送结果，包含errcode和errmsg
        """
        access_token = await self._get_access_token()
        if not access_token:
            return {"errcode": -1, "errmsg": "获取access_token失败"}
        
        url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}"
        
        # 将用户ID列表转换为字符串，用'|'分隔
        touser = "|".join(user_ids)
        
        data = {
            "touser": touser,
            "msgtype": "text",
            "agentid": self.agentid,
            "text": {
                "content": content
            },
            "safe": 0
        }
        
        try:
            session = await self._ensure_session()
            async with session.post(url, json=data) as response:
                result = await response.json()
                
                if result.get("errcode") == 0:
                    logger.info(f"发送企业微信消息成功，接收人: {touser}")
                else:
                    logger.error(f"发送企业微信消息失败: {result}, 接收人: {touser}")
                
                return result
        except Exception as e:
            logger.error(f"发送企业微信消息异常: {str(e)}")
            return {"errcode": -1, "errmsg": str(e)}

    async def send_markdown_message(self, user_ids: List[str], content: str) -> Dict[str, Any]:
        """
        发送markdown格式消息给指定员工
        
        参数:
            user_ids: 员工ID列表，多个用户用'|'分隔
            content: markdown格式的消息内容
            
        返回:
            Dict: 发送结果，包含errcode和errmsg
        """
        access_token = await self._get_access_token()
        if not access_token:
            return {"errcode": -1, "errmsg": "获取access_token失败"}
        
        url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}"
        
        # 将用户ID列表转换为字符串，用'|'分隔
        touser = "|".join(user_ids)
        
        data = {
            "touser": touser,
            "msgtype": "markdown",
            "agentid": self.agentid,
            "markdown": {
                "content": content
            }
        }
        
        try:
            session = await self._ensure_session()
            async with session.post(url, json=data) as response:
                result = await response.json()
                
                if result.get("errcode") == 0:
                    logger.info(f"发送企业微信markdown消息成功，接收人: {touser}")
                else:
                    logger.error(f"发送企业微信markdown消息失败: {result}, 接收人: {touser}")
                
                return result
        except Exception as e:
            logger.error(f"发送企业微信markdown消息异常: {str(e)}")
            return {"errcode": -1, "errmsg": str(e)}

    async def close(self):
        """
        关闭aiohttp会话
        """
        if self.session and not self.session.closed:
            await self.session.close()


# 实例化一个全局对象，方便直接调用
qy_wechat = QyWechatMessage()


async def send_message_to_users(user_ids: List[str], content: str) -> Dict[str, Any]:
    """
    向指定员工发送文本消息
    
    参数:
        user_ids: 员工ID列表
        content: 消息内容
        
    返回:
        Dict: 发送结果，包含errcode和errmsg
    """
    return await qy_wechat.send_text_message(user_ids, content)


async def send_markdown_to_users(user_ids: List[str], content: str) -> Dict[str, Any]:
    """
    向指定员工发送markdown格式消息
    
    参数:
        user_ids: 员工ID列表
        content: markdown格式的消息内容
        
    返回:
        Dict: 发送结果，包含errcode和errmsg
    """
    return await qy_wechat.send_markdown_message(user_ids, content)



# 向单个员工发送消息
# result = send_message_to_users(["13523_202304240950232615@6855"], "这是一条测试消息")

# # 向多个员工发送消息
# result = send_message_to_users(["13523_202304240950232615@6855", "14295_202306121344156656@1423", "14234_202306081608327905@1103"], "这是一条群发消息")