import time
import hashlib
import aioboto3
from botocore.client import Config
from settings import FILE_SERVER


def generate_md5(string):
    return hashlib.md5(string.encode('utf-8')).hexdigest()


class S3Client:
    """
    文件服务器SDK
    """

    def __init__(self):
        self.session = aioboto3.Session(
            aws_access_key_id=FILE_SERVER['aws_access_key_id'],  # 你的MinIO access key
            aws_secret_access_key=FILE_SERVER['aws_secret_access_key'],  # 你的MinIO secret key
        )
        self.config = Config(signature_version=FILE_SERVER['signature_version'])
        self.endpoint_url = FILE_SERVER['endpoint_url']

    def _generate_new_filename(self, original_filename):
        """
        根据原始文件名生成包含时间戳和哈希值的新文件名
        """
        name, extension = original_filename.rsplit('.', 1)
        timestamp = str(int(time.time()))
        hashed_name = generate_md5(name + timestamp)
        new_filename = f"{hashed_name}.{extension}"
        return new_filename

    async def upload_file(self, bucket_name, original_filename, file_content=None):
        """
        上传文件，并返回可公开下载的链接
        """
        new_filename = self._generate_new_filename(original_filename)

        async with self.session.client('s3', endpoint_url=self.endpoint_url, config=self.config) as client:
            try:
                if file_content is None:
                    with open(original_filename, 'rb') as file:
                        await client.put_object(Bucket=bucket_name, Key=new_filename, Body=file, ACL='public-read')
                else:
                    await client.put_object(Bucket=bucket_name, Key=new_filename, Body=file_content, ACL='public-read')
                return f"{client.meta.endpoint_url}/{bucket_name}/{new_filename}"
            except Exception as e:
                print(f"Failed to upload file: {e}")
                raise

    async def download_file(self, bucket_name, file_key, target_path):
        """
        下载文件
        """
        async with self.session.client('s3', endpoint_url=self.endpoint_url, config=self.config) as client:
            try:
                await client.download_file(Bucket=bucket_name, Key=file_key, Filename=target_path)
            except Exception as e:
                print(f"Failed to download file: {e}")
                raise
