from fastapi import HTTPException
from pydantic import BaseModel
from typing import Generic, TypeVar, Optional
from settings import logger

from starlette import status

DataT = TypeVar("DataT")


class ApiResponse(BaseModel, Generic[DataT]):
    message: str
    status: str
    # count: Optional[int]
    code: int
    data: Optional[DataT]


class ApiResponseWithToken(ApiResponse):
    access_token: Optional[str]


async def ApiSuccessResponse(data: Optional[DataT] = None,
                             message: str = "操作成功",
                             count: int = None,
                             access_token=None, db=None) -> ApiResponse:
    """
    成功返回
    """
    if db:
        await db.close()
    data = {
        "message": message,
        "status": "success",
        "code": 200,
        "data": data,
    }
    if count:
        data['count'] = count
    if access_token:
        data['access_token'] = access_token
        return ApiResponseWithToken(**data)
    else:
        return ApiResponse(**data)


async def ApiFailedResponse(message: str = "操作失败", code=0, db=None):
    """
    失败返回
    """
    if db:
        await db.close()
    logger.warning(f"ApiFailedResponse: {message}")
    raise HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail=message)
