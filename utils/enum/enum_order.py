from enum import Enum


class DiscountStatus(Enum):
    """
    0 未激活 1 可用（未使用） 2 已使用 3已失效
    """
    NOT_ACTIVE = 0
    AVAILABLE = 1
    USED = 2
    EXPIRED = 3

class OfferState(Enum):
    """
    1-未付款，2-已付款，3-已关闭 4 -已失效 5 - 部分退款 6 - 已全部退款
    """
    NOT_PAY = 1
    PAID = 2
    CLOSED = 3
    INVALID = 4
    PARTIAL_REFUND = 5
    FULL_REFUND = 6

class OfferType(Enum):
    """
    1 新购订单 2 续费订单
    """
    NEW_ORDER = 1
    RENEW_ORDER = 2

class OrderState(Enum):
    """
    订单表
    - 0 - 未交费 1 - 已缴费 2 - 已退款 3 - 已取消
    """
    NOT_PAY = 0
    PAID = 1
    REFUND = 2
    CANCEL = 3

class OrderType(Enum):
    """
    订单类型
    - 1 课程订单
    - 3 随材订单
    """
    COURSE = 1
    MATERIAL = 3


class StudentState(Enum):
    """
    # 班级学生状态
    0 等待付款（名额锁定） 1 - 正常上课 2 - 已退课 3 已取消（释放名额） 4 - 已转入 5 - 已转出 6 - 已毕业 7
    - （1， 3， 4视为在读或班级名单学员） （2， 5视为已退课学员） 
    """
    WAIT_PAY = 0
    NORMAL = 1
    REFUND = 2
    CANCEL = 3
    TRANSFER_IN = 4
    TRANSFER_OUT = 5
    GRADUATED = 6


class StudentState(Enum):
    """
    # 班级学生状态
    0 等待付款（名额锁定） 1 - 正常上课 2 - 已退课 3 已取消（释放名额） 4 - 已转入 5 - 已转出 6 - 已毕业
    - （1， 3， 4视为在读或班级名单学员） （2， 5视为已退课学员） 
    """
    WAIT_PAY = 0
    NORMAL = 1
    REFUND = 2
    CANCEL = 3
    TRANSFER_IN = 4
    TRANSFER_OUT = 5
    GRADUATED = 6



class OfferState(Enum):
    """
    1-未付款，2-已付款，3-已关闭 4 -已失效 5 - 部分退款 6 - 已全部退款
    """
    NOT_PAY = 1
    PAID = 2
    CLOSED = 3
    INVALID = 4
    PARTIAL_REFUND = 5
    FULL_REFUND = 6

class OfferType(Enum):
    """
    1 新购订单 2 续费订单
    """
    NEW_ORDER = 1
    RENEW_ORDER = 2


class OrderType(Enum):
    """
    订单类型
    - 1 课程订单
    - 3 随材订单
    """
    COURSE = 1
    MATERIAL = 3

class TradeWay(Enum):
    """
    交易方式
    """
    ONLINE = 1
    OFFLINE = 2


class OrderState(Enum):
    """
    订单表
    - 0 - 未交费 1 - 已缴费 2 - 已退款 3 - 已取消
    """
    NOT_PAY = 0
    PAID = 1
    REFUND = 2
    CANCEL = 3

class TradeStatus(Enum):
    """
    0 本地创建 1 已提交至支付机构 2 成功 3 失败 4 交易关闭 
    绑定erp_finance_trade_payment.trade_status
    """
    LOCAL_CREATED = 0
    PENDING = 1
    SUCCESS = 2
    FAIL = 3
    CLOSED = 4
    REFUNDING = 5
    PAYING = 6

class TradeType(Enum):
    """
    交易类型 0 未支付 1 微信 2 支付宝 绑定erp_finance_trade_payment.trade_type
    """
    NOT_PAY = 0
    WECHAT = 1
    ALIPAY = 2

class FinanceAuditState(Enum):
    """
    退费审核状态 绑定erp_order_refund.audit_state
    """
    PENDING = 0
    WAIT_AUDIT = 1
    PASS = 2
    REJECTED = 3
    CANCEL = 4

class RefundType(Enum):
    """
    退费类型 绑定erp_order_refund.refund_type
    """
    REFUND = 1
    CARRYOVER = 2

class RefundDetailState(Enum):
    """
    退费状态 绑定erp_order_refund_detail.refund_state
    0 未退款 1 已退款（不再进行退费任务检测）
    """
    PENDING = 0
    SUCCESS = 1
    FAIL = 2
    REJECTED = 3


class RefundWay(Enum):
    """
    退费方式 绑定erp_order_refund_detail.refund_way
    1 原路退款 2 现金退款 3 转账退款
    """
    ORIGINAL = 1  # 原路退款
    CASH = 2      # 现金退款
    TRANSFER = 3  # 转账退款


class CheckStatus(Enum):
    """
    签到状态
    """
    ATTEND = 1
    ABSENT = 2
    LEAVE = 3
    LATE = 4
    ONLINE = 5

# class IEType(Enum):
#     """
#     财务单类型
#     """
#     PAYMENT = 1
#     REFUND = 2


class IEType(Enum):
    """
    收支类型 0 收入 1 退费单 2 采购单 3 报销单 4 支出单
    """
    INCOME = 0  # 收入
    REFUND = 1  # 退费单
    PURCHASE = 2  # 采购单
    REIMBURSEMENT = 3  # 报销单
    EXPENSE = 4  # 支出单


class BankAccountType(Enum):
    """
    银行账户类型 1 公账 2 现金 3 虚拟账户 4 学生
    """
    PUBLIC = 1
    CASH = 2
    VIRTUAL = 3
    STUDENT = 4


class ItemType(Enum):
    """
    财务单据类型
    """
    FINANCE = 1
    NORMAL = 2
