from enum import Enum


class EmployeeStatus(Enum):
    INACTIVE = 0  # 未激活
    EMPLOYED = 1  # 正式
    IN_HANOVER = 2  # 离职交接中
    RESIGNED = 3  # 已离职
    PROBATION = 4  # 试用


class EmployeeType(Enum):
    NOT_CHOICE = 0  # 未选择
    FullTimeJob = 1  # 全职
    PartTimeJob = 2  # 兼职
    Internship = 3  # 实习
    ProfessionJob = 4  # 专职


class SyncStatus(Enum):
    WAITING = 1  # 等待同步
    SUCCESS = 2  # 同步成功
    FAILED = 3  # 同步失败


class SalaryChangeType(Enum):
    BASE_SALARY = 1  # 基础薪资
    PERFORMANCE_SALARY = 2  # 绩效薪资


class SalaryType(Enum):
    NORMAL_SALARY = 1  # 普通类型
    COURSE_SALARY = 2  # 课时费类型


class SalaryStatus(Enum):
    WAITING_UPLOAD = 0  # 待生成
    WAITING_SEND = 1  # 待报送
    ADMIN_SEND = 2  # 已报送
    EMPLOYEE_SEND = 3  # 已发放


class Roles(Enum):
    Admin = 1  # 超管
    Normal = 2  # 普通员工


class BillType(Enum):
    PublicAccount = 1  # 公账
    CashAccount = 2  # 现金账户
    VirtualAccount = 3  # 虚拟账户
    StuAccount = 4  # 学生账户
