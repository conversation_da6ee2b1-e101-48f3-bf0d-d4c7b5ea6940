from enum import Enum


# class ProcessStatus(Enum):
#     WaitStart = 0  # 等待开始
#     WaitAudit = 1  # 等待审核
#     Passed = 2  # 通过
#     Rejected = 3  # 已驳回
#     Canceled = 4  # 已取消


# class ReceiptStatus(Enum):
#     Saved = 0  # 暂存
#     Auditing = 1  # 审核中
#     Passed = 2  # 已通过
#     Rejected = 3  # 已驳回


class ApprovalRole(Enum):
    Normal = 0  # 常规
    FinanceAdmin = 1  # 财务管理
    Cashier = 2  # 出纳


class ReceiptTradeType(Enum):
    Received = 1  # 收款
    Paid = 2  # 付款


class ReceiptTradeWay(Enum):
    """
    1 银行 2 平台 3 现金 4 资金池
    """
    Bank = 1
    Platform = 2
    Cash = 3
    FundPool = 4


class ReceiptAccountType(Enum):
    """
    1 公账 2 私账 3 现金账户 4 微信支付宝 5 虚拟账户
    """
    Public = 1
    Private = 2
    Cash = 3
    WechatAlipay = 4
    Virtual = 5


class CostTypeBind(Enum):
    """
    财务费用类型和流程的 -> 绑定类型, 例如
    type: 1 退费单  2 采购单 3 报销单 4 支出单 5 课程收入 6 收入单 7 开班
    8 课程费用现金收入  9 课程费用银行收入 10 教材收入  11 课程费用银行退款 
    12 课程费用现金退款 13 教材退款 14 电子钱包退款 15 调拨单
    """
    # Refund = 1
    Purchase = 2
    Reimbursement = 3
    Expense = 4
    # CourseIncome = 5
    Income = 6
    OpenClass = 7
    CourseCashIncome = 8
    CourseBankIncome = 9
    TextbookIncome = 10
    CourseBankRefund = 11
    CourseCashRefund = 12
    TextbookRefund = 13
    EWalletRefund = 14
    Transfer = 15


class AuditState(Enum):
    """
    审核状态
    """
    DRAFT = 0  # 草稿
    AUDITING = 1  # 审核中
    PASS = 2  # 通过
    REJECT = 3  # 驳回
    CANCEL = 4  # 取消

class ClassAuditStatus(Enum):
    """
    开班审核状态
    """
    WAITING = 0  # 待审核
    PASS = 1  # 通过
    REJECT = 2  # 驳回

class InstanceStatus(Enum):
    """
    流程实例状态 和上方一致
    """
    WAITING = 0  # 未开始
    PROCESSING = 1  # 进行中
    COMPLETED = 2  # 已完成
    REJECTED = 3  # 已驳回
    CANCELLED = 4  # 已取消


class ApproverType(Enum):
    """
    审批人类型 1连续多级上级 2指定成员 3指定岗位 4授课老师
    """
    CONTINUOUS_SUPERIOR = 1  # 连续多级上级
    SPECIFIED_MEMBER = 2  # 指定成员
    SPECIFIED_POSITION = 3  # 指定岗位
    TEACHER = 4  # 授课老师


class NodeType(Enum):
    """
    节点类型 1审批节点 2抄送节点
    """
    APPROVAL = 1  # 审批节点
    CC = 2  # 抄送节点


class NodeAction(Enum):
    """
    节点动作 1同意 2驳回
    """
    APPROVE = 1  # 同意
    REJECT = 2  # 驳回


class RelatedObjType(Enum):
    """
    关联对象类型 1 学生 2 员工 3 供应商  4 内部学生号 5 内部员工号 6 兼职
    """
    STUDENT = 1  # 学生
    EMPLOYEE = 2  # 员工
    SUPPLIER = 3  # 供应商
    INTERNAL_STUDENT = 4  # 内部学生号
    INTERNAL_EMPLOYEE = 5  # 内部员工号
    PART_TIME = 6  # 兼职


class AccountType(Enum):
    """
    账户类型 1 公账 2 现金 3 虚拟账户 4 私账 
    """
    PUBLIC = 1
    CASH = 2
    VIRTUAL = 3
    PRIVATE = 4
