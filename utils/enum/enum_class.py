from enum import Enum


class CourseLogType(Enum):
    ChangeCategoryLog = 1  # 变动课程分类


class ClassLogType(Enum):
    CreateClassLog = 1  # 创建班级日志
    ChangeClassLog = 2  # 变更班级日志

class ClassStatus(Enum):
    """
    0 未激活 1 未上课 2 上课中 3 已结课
    """
    NotActive = 0
    NotStart = 1
    Started = 2
    Closed = 3


# 增加规则类型枚举
class RuleType(Enum):
    PAID_TRANSFER = "paid_transfer"  # 付费转班规则
    UNPAID_TRANSFER = "unpaid_transfer"  # 未付费转班规则
    RESCHEDULE = "reschedule"  # 调课规则