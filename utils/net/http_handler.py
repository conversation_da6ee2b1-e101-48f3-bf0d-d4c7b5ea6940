import aiohttp
from tenacity import retry, wait_fixed, stop_after_attempt


class HttpHandler:
    @classmethod
    @retry(wait=wait_fixed(2), stop=stop_after_attempt(5))
    async def post(cls, url, data=None, headers=None, params=None):
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, headers=headers, params=params) as response:
                return await response.json()

    @classmethod
    @retry(wait=wait_fixed(2), stop=stop_after_attempt(5))
    async def get(cls, url, headers=None, params=None):
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, params=params) as response:
                return await response.json()

    @classmethod
    @retry(wait=wait_fixed(2), stop=stop_after_attempt(5))
    async def download(cls, url, save_path):
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    with open(save_path, 'wb') as f:
                        while True:
                            chunk = await response.content.read(1024)
                            if not chunk:
                                break
                            f.write(chunk)
                    return f"{save_path} downloaded successfully"
                else:
                    return f"Failed to download {url}. Status code: {response.status}"
