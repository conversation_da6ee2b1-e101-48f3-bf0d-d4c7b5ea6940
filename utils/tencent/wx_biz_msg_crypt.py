#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业微信消息解密工具类
基于官方WXBizMsgCrypt Python3版本改写
"""

import base64
import hashlib
import struct
import socket
import time
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import xml.etree.ElementTree as ET


class WXBizMsgCrypt:
    """
    企业微信消息解密类
    """
    
    def __init__(self, token, encoding_aes_key, corp_id):
        """
        初始化
        
        Args:
            token: 企业微信后台设置的Token
            encoding_aes_key: 企业微信后台设置的EncodingAESKey
            corp_id: 企业微信的CorpId
        """
        self.token = token
        self.encoding_aes_key = encoding_aes_key
        self.corp_id = corp_id
        
        # 将EncodingAESKey转换为32字节的密钥
        self.aes_key = base64.b64decode(encoding_aes_key + "=")
        
    def verify_url(self, msg_signature, timestamp, nonce, echostr):
        """
        验证URL
        
        Args:
            msg_signature: 签名
            timestamp: 时间戳
            nonce: 随机数
            echostr: 随机字符串
            
        Returns:
            tuple: (ret_code, reply_echostr)
        """
        # 验证签名
        signature = self._generate_signature(self.token, timestamp, nonce, echostr)
        if signature != msg_signature:
            return -40001, None
            
        # 解密echostr
        try:
            ret_code, reply_echostr = self._decrypt(echostr)
            return ret_code, reply_echostr
        except Exception as e:
            return -40002, None
    
    def decrypt_msg(self, msg_signature, timestamp, nonce, post_data):
        """
        解密消息
        
        Args:
            msg_signature: 签名
            timestamp: 时间戳
            nonce: 随机数
            post_data: 密文数据
            
        Returns:
            tuple: (ret_code, msg)
        """
        try:
            # 解析XML获取Encrypt字段
            xml_tree = ET.fromstring(post_data)
            encrypt = xml_tree.find("Encrypt").text
            
            # 验证签名
            signature = self._generate_signature(self.token, timestamp, nonce, encrypt)
            if signature != msg_signature:
                return -40001, None
                
            # 解密消息
            ret_code, msg = self._decrypt(encrypt)
            return ret_code, msg
        except Exception as e:
            return -40002, None
    
    def encrypt_msg(self, reply_msg, nonce, timestamp=None):
        """
        加密消息
        
        Args:
            reply_msg: 回复消息
            nonce: 随机数
            timestamp: 时间戳
            
        Returns:
            tuple: (ret_code, encrypt_msg)
        """
        try:
            # 加密消息
            ret_code, encrypt = self._encrypt(reply_msg)
            if ret_code != 0:
                return ret_code, None
                
            # 生成签名
            if timestamp is None:
                timestamp = str(int(time.time()))
            signature = self._generate_signature(self.token, timestamp, nonce, encrypt)
            
            # 构造XML回复
            xml_msg = f"""<xml>
<Encrypt><![CDATA[{encrypt}]]></Encrypt>
<MsgSignature><![CDATA[{signature}]]></MsgSignature>
<TimeStamp>{timestamp}</TimeStamp>
<Nonce><![CDATA[{nonce}]]></Nonce>
</xml>"""
            
            return 0, xml_msg
        except Exception as e:
            return -40003, None
    
    def _generate_signature(self, token, timestamp, nonce, msg_encrypt):
        """
        生成签名
        """
        sort_list = [token, timestamp, nonce, msg_encrypt]
        sort_list.sort()
        sha = hashlib.sha1()
        sha.update("".join(sort_list).encode('utf-8'))
        return sha.hexdigest()
    
    def _decrypt(self, text):
        """
        解密
        """
        try:
            # Base64解码
            ciphertext = base64.b64decode(text)
            
            # AES解密
            cipher = AES.new(self.aes_key, AES.MODE_CBC, self.aes_key[:16])
            decrypted = cipher.decrypt(ciphertext)
            
            # 去除填充
            decrypted = unpad(decrypted, AES.block_size)
            
            # 解析内容
            content = decrypted[16:]  # 去掉随机字符串
            xml_len = struct.unpack("!I", content[:4])[0]  # 获取xml长度
            xml_content = content[4:xml_len + 4]  # 获取xml内容
            from_corpid = content[xml_len + 4:]  # 获取corpid
            
            # 验证corpid
            if from_corpid.decode('utf-8') != self.corp_id:
                return -40005, None
                
            return 0, xml_content.decode('utf-8')
        except Exception as e:
            return -40007, None
    
    def _encrypt(self, text):
        """
        加密
        """
        try:
            # 随机字符串
            random_str = self._generate_random_str(16)
            
            # 构造待加密内容
            text_bytes = text.encode('utf-8')
            corpid_bytes = self.corp_id.encode('utf-8')
            
            # 拼接内容：随机字符串 + 消息长度 + 消息内容 + corpid
            content = random_str + struct.pack("!I", len(text_bytes)) + text_bytes + corpid_bytes
            
            # PKCS7填充
            block_size = AES.block_size
            padding_len = block_size - len(content) % block_size
            padding = bytes([padding_len] * padding_len)
            content += padding
            
            # AES加密
            cipher = AES.new(self.aes_key, AES.MODE_CBC, self.aes_key[:16])
            encrypted = cipher.encrypt(content)
            
            # Base64编码
            return 0, base64.b64encode(encrypted).decode('utf-8')
        except Exception as e:
            return -40006, None
    
    def _generate_random_str(self, length):
        """
        生成随机字符串
        """
        import random
        import string
        return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(length)).encode('utf-8') 