import aiohttp
import aioredis
import requests
import time
from aiocache import cached, <PERSON><PERSON> as AioCache
import settings
from aiocache.serializers import JsonSerializer


class WeChatTemplateMessage:
    def __init__(self, app_id=None, app_secret=None):
        self.app_id = app_id
        self.app_secret = app_secret
        self.access_token = None
        self.token_expiry = 0

    async def _get_access_token(self, force_refresh=False):
        """获取access_token，支持强制刷新"""
        if force_refresh or self.access_token is None or time.time() > self.token_expiry:
            url = f"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={self.app_id}&secret={self.app_secret}"
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    result = await response.json()
                    settings.logger.info(f"获取微信access_token响应: {result}")
                    if 'access_token' in result:
                        self.access_token = result['access_token']
                        self.token_expiry = time.time() + result['expires_in'] - 60  # 提前60秒过期
                        # 同时更新Redis缓存
                        async for redis in self._get_token_redis():
                            await redis.set(f"GZH_JJSWToken_Key_{settings.GZH_CONFIG['app_id']}", 
                                           self.access_token, ex=result['expires_in'] - 60)
                            settings.logger.info(f"已更新Redis中的access_token缓存，过期时间: {result['expires_in'] - 60}秒")
                    else:
                        raise Exception("Failed to get access token: {}".format(result))
        return self.access_token

    async def _get_token_redis(self):
        redis_url = settings.redis_url
        redis = await aioredis.from_url(redis_url)
        try:
            yield redis
        finally:
            await redis.close()

    async def _get_token_from_redis(self):
        """从Redis获取token，如果不存在则重新获取"""
        async for redis in self._get_token_redis():
            value = await redis.get(f"GZH_JJSWToken_Key_{settings.GZH_CONFIG['app_id']}")
            if not value:
                settings.logger.info("Redis中无access_token缓存，重新获取")
                access_token = await self._get_access_token()
                return access_token
            token = value.decode('utf-8')
            return token

    async def send_template_message(self, openid, template_id, data, url=None, access_token=None, miniprogram=None, retry_count=0):
        """发送模板消息，支持自动重试"""
        if not access_token:
            access_token = await self._get_token_from_redis()
        
        send_url = f"https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={access_token}"
        payload = {
            "touser": openid,
            "template_id": template_id,
            "data": data,
        }
        if url:
            payload["url"] = url
        if miniprogram:
            payload["miniprogram"] = miniprogram
            
        # settings.logger.info(f"发送模板消息到微信: openid={openid}, template_id={template_id}")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(send_url, json=payload) as response:
                result = await response.json()
                settings.logger.info(f"微信模板消息响应: {result}")
                
                # 如果access_token无效且重试次数小于2次，则刷新token并重试
                if result.get('errcode') == 40001 and retry_count < 2:
                    settings.logger.warning(f"access_token无效，第{retry_count + 1}次重试，强制刷新token")
                    # 强制刷新access_token
                    new_access_token = await self._get_access_token(force_refresh=True)
                    # 递归重试
                    return await self.send_template_message(openid, template_id, data, url, new_access_token, miniprogram, retry_count + 1)
                
                if result.get('errcode') != 0:
                    raise Exception("Failed to send template message: {}".format(result))

        return result


class WeChatMiniProgram:
    """微信小程序工具类"""
    
    def __init__(self, app_id=None, app_secret=None):
        self.app_id = app_id or settings.MINI_PROGRAM_CONFIG.get('app_id')
        self.app_secret = app_secret or settings.MINI_PROGRAM_CONFIG.get('app_secret')
        self.access_token = None
        self.token_expiry = 0

    async def _get_access_token(self, force_refresh=False):
        """获取小程序access_token，支持强制刷新"""
        if not self.app_secret:
            raise Exception("小程序app_secret未配置")
            
        if force_refresh or self.access_token is None or time.time() > self.token_expiry:
            url = f"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={self.app_id}&secret={self.app_secret}"
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    result = await response.json()
                    settings.logger.info(f"获取小程序access_token响应: {result}")
                    if 'access_token' in result:
                        self.access_token = result['access_token']
                        self.token_expiry = time.time() + result['expires_in'] - 60  # 提前60秒过期
                        # 同时更新Redis缓存
                        async for redis in self._get_token_redis():
                            await redis.set(f"MINIPROGRAM_JJSWToken_Key_{self.app_id}", 
                                           self.access_token, ex=result['expires_in'] - 60)
                            settings.logger.info(f"已更新小程序Redis中的access_token缓存，过期时间: {result['expires_in'] - 60}秒")
                    else:
                        error_msg = result.get('errmsg', '未知错误')
                        raise Exception(f"获取小程序access_token失败: {error_msg}")
        return self.access_token

    async def _get_token_redis(self):
        """获取Redis连接"""
        redis_url = settings.redis_url
        redis = await aioredis.from_url(redis_url)
        try:
            yield redis
        finally:
            await redis.close()

    async def _get_token_from_redis(self):
        """从Redis获取token，如果不存在则重新获取"""
        async for redis in self._get_token_redis():
            value = await redis.get(f"MINIPROGRAM_JJSWToken_Key_{self.app_id}")
            if not value:
                settings.logger.info("Redis中无小程序access_token缓存，重新获取")
                access_token = await self._get_access_token()
                return access_token
            token = value.decode('utf-8')
            settings.logger.info("从Redis获取到小程序access_token")
            return token

    async def generate_qr_code(self, path, width=430, auto_color=False, line_color=None, is_hyaline=False):
        """
        生成小程序二维码
        
        Args:
            path: 扫码进入的小程序页面路径，最大长度 128 字节，不能为空
            width: 二维码的宽度，单位 px，最小 280px，最大 1280px
            auto_color: 自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调
            line_color: auto_color 为 false 时生效，使用 rgb 设置颜色 例如 {"r":"xxx","g":"xxx","b":"xxx"}
            is_hyaline: 是否需要透明底色，为 true 时，生成透明底色的小程序码
            
        Returns:
            bytes: 二维码图片的二进制数据
        """
        try:
            access_token = await self._get_token_from_redis()
            url = f'https://api.weixin.qq.com/wxa/getwxacode?access_token={access_token}'
            
            payload = {
                "path": path,
                "width": width,
                "auto_color": auto_color,
                "is_hyaline": is_hyaline
            }
            
            if not auto_color and line_color:
                payload["line_color"] = line_color
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    # 检查响应内容类型
                    content_type = response.headers.get('content-type', '')
                    
                    if 'image' in content_type:
                        # 如果是图片数据，直接返回二进制数据
                        qr_data = await response.read()
                        settings.logger.info(f"成功生成小程序二维码，path: {path}")
                        return qr_data
                    else:
                        # 如果不是图片，可能是错误响应，解析JSON
                        result = await response.json()
                        error_msg = result.get('errmsg', '生成二维码失败')
                        error_code = result.get('errcode', 'unknown')
                        
                        # 如果是access_token无效，尝试刷新token重试
                        if error_code == 40001:
                            settings.logger.warning("小程序access_token无效，刷新token并重试")
                            new_access_token = await self._get_access_token(force_refresh=True)
                            # 重新生成二维码
                            return await self.generate_qr_code(path, width, auto_color, line_color, is_hyaline)
                        
                        settings.logger.error(f"生成小程序二维码失败: {result}")
                        raise Exception(f"生成小程序二维码失败: {error_msg}")
                        
        except Exception as e:
            settings.logger.error(f"生成小程序二维码异常: {str(e)}")
            raise e

    async def generate_unlimited_qr_code(self, scene, page=None, width=430, auto_color=False, 
                                       line_color=None, is_hyaline=False, check_path=True):
        """
        生成不限制数量的小程序码
        
        Args:
            scene: 最大32个可见字符，只支持数字，大小写英文以及部分特殊字符
            page: 必须是已经发布的小程序存在的页面，根路径前不要填加 /
            width: 二维码的宽度，单位 px，最小 280px，最大 1280px
            auto_color: 自动配置线条颜色
            line_color: auto_color 为 false 时生效，使用 rgb 设置颜色
            is_hyaline: 是否需要透明底色
            check_path: 检查page 是否存在，为 true 时 page 必须是已经发布的小程序存在的页面
            
        Returns:
            bytes: 二维码图片的二进制数据
        """
        try:
            access_token = await self._get_token_from_redis()
            url = f'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={access_token}'
            
            payload = {
                "scene": scene,
                "width": width,
                "auto_color": auto_color,
                "is_hyaline": is_hyaline,
                "check_path": check_path
            }
            
            if page:
                payload["page"] = page
                
            if not auto_color and line_color:
                payload["line_color"] = line_color
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    content_type = response.headers.get('content-type', '')
                    
                    if 'image' in content_type:
                        qr_data = await response.read()
                        settings.logger.info(f"成功生成不限制数量的小程序二维码，scene: {scene}")
                        return qr_data
                    else:
                        result = await response.json()
                        error_msg = result.get('errmsg', '生成二维码失败')
                        error_code = result.get('errcode', 'unknown')
                        
                        if error_code == 40001:
                            settings.logger.warning("小程序access_token无效，刷新token并重试")
                            new_access_token = await self._get_access_token(force_refresh=True)
                            return await self.generate_unlimited_qr_code(scene, page, width, auto_color, 
                                                                       line_color, is_hyaline, check_path)
                        
                        settings.logger.error(f"生成不限制数量的小程序二维码失败: {result}")
                        raise Exception(f"生成小程序二维码失败: {error_msg}")
                        
        except Exception as e:
            settings.logger.error(f"生成不限制数量的小程序二维码异常: {str(e)}")
            raise e
