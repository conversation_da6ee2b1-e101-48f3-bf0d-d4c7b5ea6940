from datetime import datetime
from fastapi import APIRouter, Depends
from sqlalchemy import extract, func, select, or_
from sqlalchemy.ext.asyncio import AsyncSession

from app_office.crud import get_fixed_assets
from app_office.serializer import OfficeCenterBase, OfficeClassroomBase, OfficeFixedAssetsBase, OfficePurchasingItemBase, \
    OfficeClassroomProblemBase, OfficeWarehouseBase
from models.m_office import ErpOfficeCenter, ErpOfficeClassroom, ErpOfficeClassroomProblem, ErpOfficeFixedAssets, \
    ErpOfficePurchasingList, ErpOfficeWarehouse, ErpOfficeWarehouseLog
from models.m_class import ErpClassPlan
from models.models import ErpAccount
from utils.db.account_handler import UserDict,  role_required
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db
from utils.response.response_handler import ApiSuccessResponse
from settings import CLASS_ROOM_UTILIZATION_RATE_PARAMS
from datetime import datetime
from sqlalchemy import extract, func, select
from models.m_class import ErpClassPlan
from settings import CLASS_ROOM_UTILIZATION_RATE_PARAMS
    

router = APIRouter(prefix="/teaching_center", tags=["教学点管理"])

erp_office_center = CRUD(ErpOfficeCenter)
erp_office_classroom = CRUD(ErpOfficeClassroom)
erp_office_classroom_problem = CRUD(ErpOfficeClassroomProblem)
erp_office_fixed_assets = CRUD(ErpOfficeFixedAssets)
erp_office_purchasing_list = CRUD(ErpOfficePurchasingList)
erp_office_warehouse = CRUD(ErpOfficeWarehouse)
erp_office_warehouse_log = CRUD(ErpOfficeWarehouseLog)
erp_account = CRUD(ErpAccount)


# 针对教学点的增删改查
@router.post("/office_center")
async def create_office_center(
        center_params: OfficeCenterBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增教学点
    """
    create_item = {
        "center_name": center_params.center_name,
        "center_cover_img": center_params.center_cover_img,
        "address": center_params.address,
        "phone": center_params.phone,
        "latitude": center_params.latitude,
        "longitude": center_params.longitude,
        "school_spell": center_params.school_spell,
        "qrcode": center_params.qrcode,
        "sort": center_params.sort,
        "center_cover_img": center_params.center_cover_img,
        "center_type": center_params.center_type,
    }

    center_obj= await erp_office_center.create(db, commit=False, **create_item)

    # 教学点默认创建一个前台一个卫生间
    await erp_office_classroom.create(db, commit=False, **{
        "room_name": f"前台",
        "inner_type": 1,
        "center_id": center_obj.id,
    })

    await erp_office_classroom.create(db, commit=False, **{
        "room_name": f"卫生间",
        "inner_type": 2,
        "center_id": center_obj.id,
    })

    await db.commit()
    return await ApiSuccessResponse(True)


# 删除教学点
@router.delete("/office_center/{center_id}")
async def delete_office_center(
        center_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除教学点
    """
    await erp_office_center.delete_one(db, center_id)
    return await ApiSuccessResponse(True)


# 修改教学点
@router.put("/office_center/{center_id}")
async def update_office_center(
        center_id: int,
        center_params: OfficeCenterBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改教学点
    """
    update_item = {
        **{k: v for k, v in center_params.dict().items() if v not in (None, 0)}
    }
    await erp_office_center.update_one(db, center_id, update_item)
    return await ApiSuccessResponse(True)


# 查询教学点
@router.get("/office_center")
async def get_office_center(
        page: int = 1,
        page_size: int = 10,
        keyword: str = None,
        center_type: int = 1,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询教学点
    - inner_type  0 教室 1 前台和卫生间
    """
    raw = []
    if keyword:
        raw = [ErpOfficeCenter.center_name.like(f'%{keyword}%')]
    if center_type:
        raw.append(ErpOfficeCenter.center_type == center_type)
    data = await erp_office_center.get_many_with_pagination(db, page, page_size, raw=raw)
    count_data = await erp_office_center.get_many(db, raw=raw)
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 查询教学点详情
@router.get("/office_center/{center_id}")
async def get_office_center_detail(
        center_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询教学点详情
    """
    data = await erp_office_center.get_one(db, id=center_id)
    return await ApiSuccessResponse(data)


# 仿照上面的代码，实现教室的增删改查
@router.post("/office_classroom")
async def create_office_classroom(
        classroom_params: OfficeClassroomBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增教室
    """
    create_item = {
        **classroom_params.dict()
    }

    await erp_office_classroom.create(db, commit=True, **create_item)
    return await ApiSuccessResponse(True)


# 删除教室
@router.delete("/office_classroom/{room_id}")
async def delete_office_classroom(
        room_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除教室
    """
    await erp_office_classroom.delete_one(db, room_id)
    return await ApiSuccessResponse(True)


# 修改教室
@router.put("/office_classroom/{room_id}")
async def update_office_classroom(
        room_id: int,
        classroom_params: OfficeClassroomBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改教室
    """
    update_item = {
        **{k: v for k, v in classroom_params.dict().items() if v not in (None, 0)}
    }
    await erp_office_classroom.update_one(db, room_id, update_item)
    return await ApiSuccessResponse(True)


# 查询教室
@router.get("/office_classroom")
async def get_office_classroom(
        page: int = 1,
        page_size: int = 10,
        keyword: str = None,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询教室
    """
    raw = []
    if keyword:
        raw = [ErpOfficeClassroom.room_name.like(f'%{keyword}%')]
    data = await erp_office_classroom.get_many_with_pagination(db, page, page_size, raw=raw)
    count_data = await erp_office_classroom.get_many(db, raw=raw)
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 查询教室详情
@router.get("/office_classroom/{room_id}")
async def get_office_classroom_detail(
        room_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询教室详情
    """
    data = await erp_office_classroom.get_one(db, id=room_id)
    return await ApiSuccessResponse(data)


# 根据教学点id查询教室
@router.get("/office_classroom_by_center/{center_id}")
async def get_office_classroom_by_center(
        center_id: int,
        page: int = 1,
        page_size: int = 10,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 根据教学点id查询教室详情
    """
    
    # 获取当前日期和上个月的信息
    now = datetime.now()
    last_month = now.month - 1 if now.month > 1 else 12
    year = now.year if now.month > 1 else now.year - 1
    
    # 查询教室数据
    conditions = {"center_id": center_id}
    data = await erp_office_classroom.get_many_with_pagination(db, page, page_size, condition=conditions)
    count_data = await erp_office_classroom.get_many(db, condition=conditions)
    
    # 从配置中获取利用率计算参数
    special_months = CLASS_ROOM_UTILIZATION_RATE_PARAMS.get("special_months", [1, 2, 7, 8])
    special_month_daily_hours = CLASS_ROOM_UTILIZATION_RATE_PARAMS.get("special_month_daily_hours", 8.33)
    weekday_hours = CLASS_ROOM_UTILIZATION_RATE_PARAMS.get("weekday_hours", 0)
    friday_hours = CLASS_ROOM_UTILIZATION_RATE_PARAMS.get("friday_hours", 2.5)
    weekend_hours = CLASS_ROOM_UTILIZATION_RATE_PARAMS.get("weekend_hours", 10)
    
    # 计算上月基础课时
    import calendar
    def get_base_hours_for_month(year, month):
        total_days = calendar.monthrange(year, month)[1]
        total_base_hours = 0
        
        # 特殊月份每日基础课时固定
        if month in special_months:
            return total_days * special_month_daily_hours
        
        # 其他月份根据周几计算基础课时
        for day in range(1, total_days + 1):
            weekday = calendar.weekday(year, month, day)
            if weekday == 4:  # 周五
                total_base_hours += friday_hours
            elif weekday in [5, 6]:  # 周六、周日
                total_base_hours += weekend_hours
            else:  # 周一至周四
                total_base_hours += weekday_hours
        
        return total_base_hours
    
    # 计算上月基础课时
    base_hours = get_base_hours_for_month(year, last_month)
    
    # 获取所有教室ID列表用于一次性查询
    room_ids = [room.id for room in data]
    
    # 一次性查询所有教室的上月课程计划总时长
    if room_ids:
        query = (
            select(ErpClassPlan.room_id, func.sum(ErpClassPlan.time_duration).label("total_duration"))
            .where(
                ErpClassPlan.room_id.in_(room_ids),
                ErpClassPlan.disable == 0,
                extract('year', ErpClassPlan.start_time) == year,
                extract('month', ErpClassPlan.start_time) == last_month
            )
            .group_by(ErpClassPlan.room_id)
        )
        result = await db.execute(query)
        room_durations = {row[0]: row[1] for row in result.all()}
        
        # 为每个教室添加上月利用率数据
        for room in data:
            month_duration = room_durations.get(room.id, 0)
            room_utilization = round(month_duration / base_hours, 2) if base_hours > 0 else 0
            room.room_utilization = room_utilization
    else:
        # 如果没有教室数据，返回空列表
        pass
    
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 仿照上面的代码，实现固定资产的增删改查
@router.post("/office_fixed_assets")
async def create_office_fixed_assets(
        fixed_assets_params: OfficeFixedAssetsBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增固定资产
    """
    create_item = {
        **fixed_assets_params.dict()
    }

    await erp_office_fixed_assets.create(db, commit=True, **create_item)
    return await ApiSuccessResponse(True)


# 删除固定资产
@router.delete("/office_fixed_assets/{assets_id}")
async def delete_office_fixed_assets(
        assets_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除固定资产
    """
    await erp_office_fixed_assets.delete_one(db, assets_id)
    return await ApiSuccessResponse(True)


# 修改固定资产
@router.put("/office_fixed_assets/{assets_id}")
async def update_office_fixed_assets(
        assets_id: int,
        fixed_assets_params: OfficeFixedAssetsBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改固定资产
    """
    update_item = {
        **{k: v for k, v in fixed_assets_params.dict().items() if v not in (None, 0)}
    }
    await erp_office_fixed_assets.update_one(db, assets_id, update_item)
    return await ApiSuccessResponse(True)


# 查询固定资产
@router.get("/office_fixed_assets")
async def get_office_fixed_assets(
        page: int = 1,
        page_size: int = 10,
        center_id: int = None,
        type: int = None,
        classroom_id: int = None,
        status: int = None,
        keyword: str = None,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询固定资产
    """
    data = await get_fixed_assets(db, page, page_size, center_id, type, classroom_id, status, keyword)
    count = await get_fixed_assets(db, page, page_size, center_id, type, classroom_id, status, keyword, count=True)
    return await ApiSuccessResponse({
        "data": data,
        "count": count
    })



# 查询固定资产详情
@router.get("/office_fixed_assets/{assets_id}")
async def get_office_fixed_assets_detail(
        assets_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询固定资产详情
    """
    data = await erp_office_fixed_assets.get_one(db, id=assets_id)
    return await ApiSuccessResponse(data)


# 根据教室id查询固定资产
@router.get("/office_fixed_assets_by_room/{room_id}")
async def get_office_fixed_assets_by_room(
        room_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 根据教室id查询固定资产详情
    """
    data = await erp_office_fixed_assets.get_many(db, raw=[ErpOfficeFixedAssets.room_id == room_id])
    return await ApiSuccessResponse(data)


# 仿照上面的代码，实现采购清单的增删改查
@router.post("/office_purchasing_item")
async def create_office_purchasing_item(
        purchasing_item_params: OfficePurchasingItemBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增采购项
    """
    create_item = {
        **purchasing_item_params.dict()
    }

    await erp_office_purchasing_list.create(db, commit=True, **create_item)
    return await ApiSuccessResponse(True)


# 删除采购项
@router.delete("/office_purchasing_item/{item_id}")
async def delete_office_purchasing_item(
        item_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除采购项
    """
    exist = await erp_office_purchasing_list.get_one(db, id=item_id)
    if not exist:
        return await ApiSuccessResponse(False, "采购项不存在")
    exist.disable = 1
    await db.commit()

    return await ApiSuccessResponse(True)


# 修改采购项
@router.put("/office_purchasing_item/{item_id}")
async def update_office_purchasing_item(
        item_id: int,
        purchasing_item_params: OfficePurchasingItemBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改采购清单
    """
    exist = await erp_office_purchasing_list.get_one(db, id=item_id)
    if not exist:
        return await ApiSuccessResponse(False, "采购项不存在")
    if purchasing_item_params.num and purchasing_item_params.num != exist.num:
        exist.num = purchasing_item_params.num
    if purchasing_item_params.unit and purchasing_item_params.unit != exist.unit:
        exist.unit = purchasing_item_params.unit
    if purchasing_item_params.type and purchasing_item_params.type != exist.type:
        exist.type = purchasing_item_params.type
    if purchasing_item_params.name and purchasing_item_params.name != exist.name:
        exist.name = purchasing_item_params.name
    if purchasing_item_params.room_id and purchasing_item_params.room_id != exist.room_id:
        exist.room_id = purchasing_item_params.room_id
    await db.commit()
    return await ApiSuccessResponse(True)


# 查询采购清单
@router.get("/office_purchasing_list")
async def get_office_purchasing_list(
        page: int = 1,
        page_size: int = 10,
        keyword: str = None,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询采购清单
    - 这里的采购审批： 这里当多选采购项以后，传入/receipt中receipt.purchasing_ids中
    """
    raw = []
    if keyword:
        raw = [ErpOfficePurchasingList.name.like(f'%{keyword}%')]
    data = await erp_office_purchasing_list.get_many_with_pagination(db, page, page_size, raw=raw)
    # 附加教室名称
    room_ids = [item.room_id for item in data]
    rooms = await erp_office_classroom.get_many(db, raw=[ErpOfficeClassroom.id.in_(room_ids)])
    room_map = {room.id: room.room_name for room in rooms}
    for item in data:
        item.room_name = room_map.get(item.room_id, "")
    count_data = await erp_office_purchasing_list.get_many(db, raw=raw)
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# # 查询采购清单详情
# @router.get("/office_purchasing_list/{purchase_id}")
# async def get_office_purchasing_list_detail(
#         purchase_id: int,
#         db: AsyncSession = Depends(get_default_db),
#         # user: UserDict = Depends(role_required([])),
# ):
# ):
#     """
#     # 查询采购清单详情
#     """
#     data = await erp_office_purchasing_list.get_one(db, id=purchase_id)
#     return await ApiSuccessResponse(data)


# 根据教室id查询采购清单
@router.get("/office_purchasing_list_by_room/{room_id}")
async def get_office_purchasing_list_by_room(
        room_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 根据教室id查询采购清单详情
    """
    data = await erp_office_purchasing_list.get_many(db, raw=[ErpOfficePurchasingList.room_id == room_id])
    return await ApiSuccessResponse(data)


# 仿照上面的代码，实现教室问题的增删改查
@router.post("/office_classroom_problem")
async def create_office_classroom_problem(
        classroom_problem_params: OfficeClassroomProblemBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增教室问题
    """
    create_item = {
        **classroom_problem_params.dict()
    }

    await erp_office_classroom_problem.create(db, commit=True, **create_item)
    return await ApiSuccessResponse(True)


# 删除教室问题
@router.delete("/office_classroom_problem/{problem_id}")
async def delete_office_classroom_problem(
        problem_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除教室问题
    """
    exist = await erp_office_classroom_problem.get_one(db, id=problem_id)
    if not exist:
        return await ApiSuccessResponse(False, "教室问题不存在")
    exist.disable = 1
    await db.commit()
    return await ApiSuccessResponse(True)


# 修改教室问题
@router.put("/office_classroom_problem/{problem_id}")
async def update_office_classroom_problem(
        problem_id: int,
        classroom_problem_params: OfficeClassroomProblemBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改教室问题
    """
    exist = await erp_office_classroom_problem.get_one(db, id=problem_id)
    if not exist:
        return await ApiSuccessResponse(False, "教室问题不存在")
    exist.content = classroom_problem_params.content
    await db.commit()
    return await ApiSuccessResponse(True)


# 查询教室问题
@router.get("/office_classroom_problem")
async def get_office_classroom_problem(
        page: int = 1,
        page_size: int = 10,
        keyword: str = None,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询教室问题
    """
    raw = []
    if keyword:
        raw = [ErpOfficeClassroomProblem.content.like(f'%{keyword}%')]
    data = await erp_office_classroom_problem.get_many_with_pagination(db, page, page_size, raw=raw)
    count_data = await erp_office_classroom_problem.get_many(db, raw=raw)
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 查询教室问题详情
@router.get("/office_classroom_problem/{problem_id}")
async def get_office_classroom_problem_detail(
        problem_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询教室问题详情
    """
    data = await erp_office_classroom_problem.get_one(db, id=problem_id)
    return await ApiSuccessResponse(data)


# 根据教室id查询教室问题
@router.get("/office_classroom_problem_by_room/{room_id}")
async def get_office_classroom_problem_by_room(
        room_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 根据教室id查询教室问题详情
    """
    data = await erp_office_classroom_problem.get_many(db, raw=[ErpOfficeClassroomProblem.room_id == room_id])
    return await ApiSuccessResponse(data)


# 仓库管理 - 针对教学点的仓库管理
@router.post("/office_warehouse")
async def create_office_warehouse(
        warehouse_params: OfficeWarehouseBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 新增仓库
    """
    create_item = {
        **warehouse_params.dict()
    }

    # 创建仓库记录
    warehouse_obj = await erp_office_warehouse.create(db, commit=False, **create_item)
    
    # 查询创建者信息
    create_account = await erp_account.get_one(db, id=user.uid)
    
    # 查询教学点名称
    center = await erp_office_center.get_one(db, id=warehouse_params.center_id)
    center_name = center.center_name if center else "未知教学点"
    
    # 物品类型对应表
    type_dict = {1: "硬件", 2: "家具"}
    type_name = type_dict.get(warehouse_params.type, "未知")
    
    # 记录日志
    await erp_office_warehouse_log.create(db, commit=False, **{
        "warehouse_id": warehouse_obj.id,
        "content": f"新增仓库物品：{warehouse_params.name}，类型：{type_name}，数量：{warehouse_params.amount}{warehouse_params.unit or ''}，所属教学点：{center_name}，创建人：{create_account.employee_name}"
    })

    await db.commit()
    return await ApiSuccessResponse(True)


# 删除仓库
@router.delete("/office_warehouse/{warehouse_id}")
async def delete_office_warehouse(
        warehouse_id: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 删除仓库
    """
    exist = await erp_office_warehouse.get_one(db, id=warehouse_id)
    if not exist:
        return await ApiSuccessResponse(False, "仓库不存在")
    
    # 查询操作者信息
    delete_account = await erp_account.get_one(db, id=user.uid)
    
    # 查询教学点名称
    center = await erp_office_center.get_one(db, id=exist.center_id)
    center_name = center.center_name if center else "未知教学点"
    
    # 物品类型对应表
    type_dict = {1: "硬件", 2: "家具"}
    type_name = type_dict.get(exist.type, "未知")
    
    # 记录日志
    await erp_office_warehouse_log.create(db, commit=False, **{
        "warehouse_id": warehouse_id,
        "content": f"删除仓库物品：{exist.name}，类型：{type_name}，数量：{exist.amount}{exist.unit or ''}，所属教学点：{center_name}，删除人：{delete_account.employee_name}"
    })
    
    exist.disable = 1
    await db.commit()
    return await ApiSuccessResponse(True)


# 修改仓库
@router.put("/office_warehouse/{warehouse_id}")
async def update_office_warehouse(
        warehouse_id: int,
        warehouse_params: OfficeWarehouseBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改仓库
    """
    exist = await erp_office_warehouse.get_one(db, id=warehouse_id)
    if not exist:
        return await ApiSuccessResponse(False, "仓库不存在")
    
    # 保存原始值用于日志记录
    original_values = {
        "type": exist.type,
        "name": exist.name,
        "amount": exist.amount,
        "center_id": exist.center_id,
        "serial_no": exist.serial_no,
        "status": exist.status,
        "unit": exist.unit,
    }
    
    changes = []
    # 检查并更新每个字段
    if warehouse_params.type and warehouse_params.type != exist.type:
        # 物品类型对应表
        type_dict = {1: "硬件", 2: "家具",}
        changes.append(f"物品类型: {type_dict.get(original_values['type'], '未知')} → {type_dict.get(warehouse_params.type, '未知')}")
        exist.type = warehouse_params.type
    if warehouse_params.name and warehouse_params.name != exist.name:
        changes.append(f"物品名称: {original_values['name']} → {warehouse_params.name}")
        exist.name = warehouse_params.name
    if warehouse_params.amount and warehouse_params.amount != exist.amount:
        changes.append(f"数量: {original_values['amount']} → {warehouse_params.amount}")
        exist.amount = warehouse_params.amount
    if warehouse_params.center_id and warehouse_params.center_id != exist.center_id:
        # 获取教学点名称
        old_center = await erp_office_center.get_one(db, id=original_values['center_id'])
        new_center = await erp_office_center.get_one(db, id=warehouse_params.center_id)
        old_center_name = old_center.center_name if old_center else "未知教学点"
        new_center_name = new_center.center_name if new_center else "未知教学点"
        changes.append(f"所属教学点: {old_center_name} → {new_center_name}")
        exist.center_id = warehouse_params.center_id
    if warehouse_params.serial_no and warehouse_params.serial_no != exist.serial_no:
        changes.append(f"序列号: {original_values['serial_no'] or '无'} → {warehouse_params.serial_no}")
        exist.serial_no = warehouse_params.serial_no
    if warehouse_params.status and warehouse_params.status != exist.status:
        # 状态对应表
        status_dict = {1: "使用中", 2: "报废", 3: "异常", 4: "遗失"}
        changes.append(f"状态: {status_dict.get(original_values['status'], '未知')} → {status_dict.get(warehouse_params.status, '未知')}")
        exist.status = warehouse_params.status
    if warehouse_params.unit and warehouse_params.unit != exist.unit:
        changes.append(f"单位: {original_values['unit'] or '无'} → {warehouse_params.unit}")
        exist.unit = warehouse_params.unit
    exist.update_by = user.uid
    exist.update_time = datetime.now()

    update_account = await erp_account.get_one(db, id=user.uid)
    
    # 记录日志
    if changes:
        log_content = f"仓库物品'{exist.name}'被修改 - " + "，".join(changes) + f"，修改人：{update_account.employee_name}"
        await erp_office_warehouse_log.create(db, commit=False, **{
            "warehouse_id": warehouse_id,
            "content": log_content,
            "create_by": user.uid,
            "update_by": user.uid,
        })
    
    await db.commit()
    return await ApiSuccessResponse(True)


# 查询仓库
@router.get("/office_warehouse")
async def get_office_warehouse(
        page: int = 1,
        page_size: int = 10,
        keyword: str = None,
        center_id: int = None,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询仓库
    """
    raw = []
    if keyword:
        raw.append(ErpOfficeWarehouse.name.like(f'%{keyword}%'))
    if center_id:
        raw.append(ErpOfficeWarehouse.center_id == center_id)
    data = await erp_office_warehouse.get_many_with_pagination(db, page, page_size, raw=raw)
    count_data = await erp_office_warehouse.get_many(db, raw=raw)
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count_data)
    })


# 查询仓库详情
@router.get("/office_warehouse/{warehouse_id}")
async def get_office_warehouse_detail(
        warehouse_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 查询仓库详情
    """
    data = await erp_office_warehouse.get_one(db, id=warehouse_id)
    return await ApiSuccessResponse(data)


# 根据教学点id查询仓库
@router.get("/office_warehouse_by_center/{center_id}")
async def get_office_warehouse_by_center(
        center_id: int,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    """
    # 根据教学点id查询仓库详情
    """
    data = await erp_office_warehouse.get_many(db, raw=[ErpOfficeWarehouse.center_id == center_id])
    return await ApiSuccessResponse(data)

# 查询仓库日志
@router.get("/office_warehouse_log")
async def get_office_warehouse_log(
        page: int = 1,
        page_size: int = 10,
        warehouse_id: int = None,
        db: AsyncSession = Depends(get_default_db),
        # user: UserDict = Depends(role_required([])),
):
    data = await erp_office_warehouse_log.get_many_with_pagination(db, page, page_size, raw=[ErpOfficeWarehouseLog.warehouse_id == warehouse_id])
    count = await erp_office_warehouse_log.get_count(db, raw=[ErpOfficeWarehouseLog.warehouse_id == warehouse_id])
    return await ApiSuccessResponse({
        "data": data,
        "count": count
    })
