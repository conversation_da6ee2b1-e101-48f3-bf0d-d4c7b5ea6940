from datetime import datetime
from typing import List

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app_office.crud import get_consumable_supplement
from app_office.serializer import OfficeConsumableBase, ConfirmSupplement, SupplementBase
from models.m_office import ErpOfficeConsumable, ErpOfficeConsumableSupplement
from models.models import ErpAccount
from utils.db.account_handler import UserDict, role_required
from utils.db.crud_handler import CRUD
from utils.db.db_handler import get_default_db
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

router = APIRouter(prefix="/consumable", tags=["耗材管理"])

erp_account = CRUD(ErpAccount)
erp_office_consumable = CRUD(ErpOfficeConsumable)
erp_office_consumable_supplement = CRUD(ErpOfficeConsumableSupplement)


# 针对印刷的增删改查
@router.post("/consumable")
async def create_consumable(
        param_list: List[OfficeConsumableBase],
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 批量新增印刷(耗材)
    """
    content = ''
    for params in param_list:
        # 创建耗材库存
        # 先查询该校区是否有相同的同名耗材
        consumable_obj = await erp_office_consumable.get_one(db, title=params.title, center_id=params.center_id)
        if not consumable_obj:  # 不存在则创建耗材
            create_consumable_item = {
                "title": params.title,
                "center_id": params.center_id,
                "num": 0,
                "receive_datetime": params.receive_datetime,
                "pages": params.pages,
                "size": params.size,
                "color": params.color,
                "binding": params.binding,
                "create_by": user.uid,
                "update_by": user.uid,
                "stock_status": 0,  # 入库状态 0 未入库 1 已入库

            }
            consumable_obj = await erp_office_consumable.create(db, commit=False, **create_consumable_item)
        else:
            content += params.title + '\n'
        # 同时创建补充(印刷)
        supplement_item = {
            "consumable_id": consumable_obj.id,  # 耗材id， 可能是旧耗材，也可能是新的耗材
            "consumable_type": 3,
            "num": params.num,
            "supplement_status": 1,  # 0 已完成 1 已提交任务
        }
        await erp_office_consumable_supplement.create(db, commit=False, **supplement_item)

    await db.commit()
    if content:
        return await ApiSuccessResponse(content + '\n该校区的这些耗材已存在, 为您重定向到旧耗材进行补充')
    return await ApiSuccessResponse(True)


# 查询补充supplement分页
@router.get("/supplement")
async def get_supplement(
        page: int = 1,
        page_size: int = 10,
        consumable_type: str = None,
        center_id: int = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 查询未完成的耗材补充， 包含印刷和加印
    - consumable_type: 逗号分隔的int,eg."1,2" 补充类型 1 加印（缺） 2 加印（需） 3 普通印刷
    """
    if consumable_type:
        consumable_type = consumable_type.split(",")
    data = await get_consumable_supplement(db, page=page, page_size=page_size, consumable_type=consumable_type,
                                           center_id=center_id)
    count_data = await get_consumable_supplement(db, consumable_type=consumable_type, center_id=center_id)
    return await ApiSuccessResponse({
        "data": data,
        "total": len(count_data)
    })


# 任务确认
@router.post("/confirm_supplement")
async def confirm_supplement(
        params: ConfirmSupplement,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 印刷任务确认
    - confirm_type: 1 确认齐全 2 有遗漏-确认缺少,miss_num必传 3 有遗漏-其他备注（例如印刷不完整...），comments必传
    """
    if params.confirm_type == 1:  # 确认齐全, 补充表状态改为已完成，耗材表状态改为已入库，且数量+=补充数量
        supplement_obj = await erp_office_consumable_supplement.get_one(db, id=params.supplement_id)
        if not supplement_obj:
            return await ApiFailedResponse('该任务不存在')
        consumable_obj = await erp_office_consumable.get_one(db, id=supplement_obj.consumable_id)
        if not consumable_obj:
            return await ApiFailedResponse('该库存不存在')
        consumable_obj.num += supplement_obj.num
        consumable_obj.stock_status = 1  # 入库
        consumable_obj.update_time = datetime.now()
        consumable_obj.update_by = user.uid
        supplement_obj.supplement_status = 0  # 已完成
    elif params.confirm_type == 2:
        supplement_obj = await erp_office_consumable_supplement.get_one(db, id=params.supplement_id)
        if not supplement_obj:
            return await ApiFailedResponse('该任务不存在')
        consumable_obj = await erp_office_consumable.get_one(db, id=supplement_obj.consumable_id)
        if not consumable_obj:
            return await ApiFailedResponse('该库存不存在')
        # 入库
        consumable_obj.num += supplement_obj.num - params.miss_num
        consumable_obj.stock_status = 1
        consumable_obj.update_time = datetime.now()
        consumable_obj.update_by = user.uid
        supplement_obj.supplement_status = 0
        # 新增加印
        await erp_office_consumable_supplement.create(db, commit=False, **{
            "consumable_id": consumable_obj.id,
            "consumable_type": 1,  # 1 加印（缺） 2 加印（需） 3 普通印刷
            "num": params.miss_num,
            "supplement_status": 1,
        })

    elif params.confirm_type == 3:
        supplement_obj = await erp_office_consumable_supplement.get_one(db, id=params.supplement_id)
        if not supplement_obj:
            return await ApiFailedResponse('该任务不存在')
        consumable_obj = await erp_office_consumable.get_one(db, id=supplement_obj.consumable_id)
        if not consumable_obj:
            return await ApiFailedResponse('该库存不存在')
        consumable_obj.num += supplement_obj.num
        consumable_obj.stock_status = 1
        consumable_obj.update_time = datetime.now()
        consumable_obj.update_by = user.uid

        supplement_obj.supplement_status = 0
    else:
        return await ApiFailedResponse('确认类型错误')
    await db.commit()
    return await ApiSuccessResponse(True)


# 加印任务确认
@router.put("/update_supplement_confirm/{supplement_id}")
async def update_supplement_confirm(
        supplement_id: int,
        confirm_type: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 加印任务确认
    - confirm_type:  1 确认加印 2 忽略
    """
    if confirm_type == 1:
        supplement_obj = await erp_office_consumable_supplement.get_one(db, id=supplement_id)
        supplement_obj.consumable_type = 0  # 3 普通印刷
    elif confirm_type == 2:
        supplement_obj = await erp_office_consumable_supplement.get_one(db, id=supplement_id)
        supplement_obj.supplement_status = 0
    else:
        return await ApiFailedResponse('确认类型错误')
    await db.commit()
    return await ApiSuccessResponse(True)


# 分页查看耗材
@router.get("/consumable")
async def get_consumable(
        page: int = 1,
        page_size: int = 10,
        center_id: int = None,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 分页查看耗材（库存）
    - supplement_records： 补充印刷记录
    """
    conditions = {
        "center_id": center_id
    }
    data = await erp_office_consumable.get_many_with_pagination(db, page=page, page_size=page_size,
                                                                condition=conditions)
    count_data = await erp_office_consumable.get_many(db, condition=conditions)
    # 账户字典
    account_objs = await erp_account.get_many(db)
    account_dict = {i.id: i for i in account_objs}
    # 组合数据
    for i in data:
        i.supplement_records = await erp_office_consumable_supplement.get_many(db, {"consumable_id": i.id})
        i.first_create_by = {
            "id": i.create_by,
            "employee_name": account_dict.get(i.create_by).employee_name,
            "avatar": account_dict.get(i.create_by).avatar
        }
        i.update_by_name = account_dict.get(i.update_by).employee_name
    return await ApiSuccessResponse({
        "data": data,
        "total": len(count_data)
    })


# 加印耗材
@router.post("/update_supplement")
async def update_supplement(
        params: SupplementBase,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 库存加印
    - consumable_id: 耗材id
    - consumable_type: 2 加印（需）可不传
    - num: 加印数量（需）， 必传
    """
    consumable_obj = await erp_office_consumable.get_one(db, id=params.consumable_id)
    if not consumable_obj:
        return await ApiFailedResponse('该库存不存在')
    # 创建补充(加印)
    supplement_item = {
        "consumable_id": consumable_obj.id,
        "consumable_type": 2,  # 1 加印（缺） 2 加印（需） 3 普通印刷
        "num": params.num,
        "supplement_status": 1,  # 0 已完成 1 已提交任务
    }
    await erp_office_consumable_supplement.create(db, commit=True, **supplement_item)
    return await ApiSuccessResponse(True)


# 领用耗材
@router.put("/receive_consumable/{consumable_id}")
async def receive_consumable(
        consumable_id: int,
        num: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 领用耗材
    """
    consumable_obj = await erp_office_consumable.get_one(db, id=consumable_id)
    if not consumable_obj:
        return await ApiFailedResponse('该库存不存在')
    consumable_obj.num -= num
    await db.commit()
    return await ApiSuccessResponse(True)


# 不同校区间耗材调拨
@router.put("/transfer_consumable/{consumable_id}")
async def transfer_consumable(
        consumable_id: int,
        target_center_id: int,
        num: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 耗材调拨
    """
    consumable_obj = await erp_office_consumable.get_one(db, id=consumable_id)
    if not consumable_obj:
        return await ApiFailedResponse('该库存不存在')

    # 创建新的耗材
    create_consumable_item = {
        "title": consumable_obj.title,
        "center_id": target_center_id,
        "num": num,
        "receive_datetime": datetime.now(),
        "pages": consumable_obj.pages,
        "size": consumable_obj.size,
        "color": consumable_obj.color,
        "binding": consumable_obj.binding,
        "create_by": user.uid,
        "update_by": user.uid,
        "stock_status": 1,  # 入库状态 0 未入库 1 已入库

    }
    # 更新原耗材

    await erp_office_consumable.create(db, commit=False, **create_consumable_item)
    consumable_obj.num -= num
    await db.commit()
    return await ApiSuccessResponse(True)


# 修改耗材数量
@router.put("/update_consumable/{consumable_id}")
async def update_consumable(
        consumable_id: int,
        num: int,
        modify_type: int,
        db: AsyncSession = Depends(get_default_db),
        user: UserDict = Depends(role_required([])),
):
    """
    # 修改耗材数量（编辑处，只能修改耗材数量）
    - modify_type: 1 增加 2 减少
    """
    consumable_obj = await erp_office_consumable.get_one(db, id=consumable_id)
    if not consumable_obj:
        return await ApiFailedResponse('该库存不存在')
    if modify_type == 1:
        consumable_obj.num += num
    elif modify_type == 2:
        consumable_obj.num -= num
    else:
        return await ApiFailedResponse('修改类型错误')
    await db.commit()
    return await ApiSuccessResponse(True)
