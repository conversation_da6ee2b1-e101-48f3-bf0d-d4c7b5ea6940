from sqlalchemy import select, and_

from models.m_office import ErpOfficeCenter, ErpOfficeClassroom, ErpOfficeConsumableSupplement, ErpOfficeConsumable, ErpOfficeFixedAssets


async def get_consumable_supplement(db, page=None, page_size=None, consumable_type=None, center_id=None):
    selects = [
        ErpOfficeConsumableSupplement.id.label('supplement_id'),
        ErpOfficeConsumableSupplement.consumable_type,
        ErpOfficeConsumableSupplement.num.label('supplement_num'),
        ErpOfficeConsumableSupplement.supplement_status,
        ErpOfficeConsumableSupplement.create_time.label('supplement_create_time'),
        ErpOfficeCenter.id.label('center_id'),
        ErpOfficeCenter.center_name,
        ErpOfficeCenter.center_cover_img,
        ErpOfficeConsumable.id.label('consumable_id'),
        ErpOfficeConsumable.title,
        ErpOfficeConsumable.num.label('consumable_num'),
        ErpOfficeConsumable.receive_datetime,
        ErpOfficeConsumable.pages,
        ErpOfficeConsumable.size,
        ErpOfficeConsumable.color,
        ErpOfficeConsumable.binding,
        ErpOfficeConsumable.stock_status,  # 0 未入库 1 库存
    ]
    conditions = [
        ErpOfficeConsumableSupplement.supplement_status == 1,  # 0 已完成或忽略 1 已提交的任务
        ErpOfficeConsumableSupplement.disable == 0,
        ErpOfficeConsumable.disable == 0,
        ErpOfficeCenter.disable == 0

    ]
    if consumable_type:
        conditions.append(ErpOfficeConsumableSupplement.consumable_type.in_(consumable_type))
    if center_id:
        conditions.append(ErpOfficeCenter.id == center_id)
    stmt = (
        select(*selects)
        .select_from(ErpOfficeConsumableSupplement)
        .outerjoin(ErpOfficeConsumable, ErpOfficeConsumableSupplement.consumable_id == ErpOfficeConsumable.id)
        .outerjoin(ErpOfficeCenter, ErpOfficeCenter.id == ErpOfficeConsumable.center_id)
        .where(and_(*conditions)).order_by(ErpOfficeConsumableSupplement.create_time.desc())
    )
    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    # compile_stmt = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_stmt)
    result = await db.execute(stmt)
    return result.fetchall()

async def get_fixed_assets(db, page=None, page_size=None, center_id=None, type=None, classroom_id=None, status=None, keyword=None, count=False):
    selects = [
        ErpOfficeFixedAssets.id,
        ErpOfficeFixedAssets.name,
        ErpOfficeFixedAssets.serial_no,
        ErpOfficeFixedAssets.unit,
        ErpOfficeFixedAssets.create_time,
        ErpOfficeFixedAssets.update_time,
        ErpOfficeFixedAssets.type,
        ErpOfficeFixedAssets.amount,
        ErpOfficeFixedAssets.status,
        ErpOfficeClassroom.id.label('classroom_id'),
        ErpOfficeClassroom.room_name,
    ]
    conditions = [
        ErpOfficeFixedAssets.disable == 0,
        ErpOfficeClassroom.disable == 0,
    ]
    if center_id:
        conditions.append(ErpOfficeClassroom.center_id == center_id)
    if type:
        conditions.append(ErpOfficeFixedAssets.type == type)
    if classroom_id:
        conditions.append(ErpOfficeFixedAssets.room_id == classroom_id)
    if status:
        conditions.append(ErpOfficeFixedAssets.status == status)
    if keyword:
        conditions.append(ErpOfficeFixedAssets.name.like(f"%{keyword}%"))
    
    stmt = (
        select(*selects)
        .select_from(ErpOfficeFixedAssets)
        .outerjoin(ErpOfficeClassroom, ErpOfficeFixedAssets.room_id == ErpOfficeClassroom.id)
        .where(and_(*conditions))
        .order_by(ErpOfficeFixedAssets.create_time.desc())
    )
    # compile_stmt = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_stmt)

    if count:
        count_stmt = select(ErpOfficeFixedAssets.id).select_from(ErpOfficeFixedAssets).outerjoin(ErpOfficeClassroom, ErpOfficeFixedAssets.room_id == ErpOfficeClassroom.id).where(and_(*conditions))
        count_result = await db.execute(count_stmt)
        total_count = len(count_result.fetchall())
        return total_count

    if page and page_size:
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    return result.fetchall()
    
