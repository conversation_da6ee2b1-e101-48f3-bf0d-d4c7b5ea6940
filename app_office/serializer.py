from typing import Optional, List

from pydantic import BaseModel


class OfficeCenterBase(BaseModel):
    center_name: Optional[str] = None
    center_cover_img: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    school_spell: Optional[str] = None
    qrcode: Optional[str] = None
    sort: Optional[int] = None
    center_cover_img: Optional[str] = None
    center_type: Optional[int] = None


class OfficeClassroomBase(BaseModel):
    center_id: int
    room_name: Optional[str] = None
    room_cover_img: Optional[str] = None
    room_square: Optional[float] = None
    room_length: Optional[float] = None
    room_width: Optional[float] = None
    stu_cap_max: Optional[int] = None
    stu_cap_comfort: Optional[int] = None
    window_to: Optional[str] = None
    quiet_level: Optional[str] = None


class OfficeClassroomProblemBase(BaseModel):
    room_id: int
    content: str


class OfficeFixedAssetsBase(BaseModel):
    type: int
    name: str
    amount: int
    room_id: int
    serial_no: Optional[str] = None
    status: Optional[int] = None
    unit: Optional[str] = None


class OfficePurchasingItemBase(BaseModel):
    room_id: int
    type: int
    name: str
    num: int
    unit: Optional[str] = None


class OfficeConsumableBase(BaseModel):
    title: str
    center_id: int
    num: int
    receive_datetime: str
    pages: Optional[int] = None
    size: Optional[str] = None
    color: Optional[str] = None
    binding: Optional[str] = None


class StockUpdateParams(BaseModel):
    update_type: int  # 1 增加库存 2 减少库存
    update_num: int  # 修改库存数量


class StockRemoveBase(BaseModel):
    new_center_id: int
    num: int


class SupplementBase(BaseModel):
    consumable_id: int
    consumable_type: Optional[int] = None
    num: Optional[int] = None
    comments: Optional[str] = None


class ConfirmSupplement(BaseModel):
    supplement_id: int
    confirm_type: int
    miss_num: Optional[int] = None
    comments: Optional[str] = None


class OfficeWarehouseBase(BaseModel):
    type: int
    name: str
    amount: int
    center_id: int
    serial_no: Optional[str] = None
    status: Optional[int] = None
    unit: Optional[str] = None
