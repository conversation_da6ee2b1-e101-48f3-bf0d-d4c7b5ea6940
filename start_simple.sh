#!/bin/bash

# ERP 系统启动脚本
echo "🚀 启动 ERP 系统..."

# 检查是否在虚拟环境中
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ 检测到虚拟环境: $VIRTUAL_ENV"
else
    echo "⚠️  警告: 未检测到虚拟环境，建议先激活虚拟环境"
fi

# 启动后台服务
echo "📋 启动后台服务进程..."
nohup python service.py > logs/service.log 2>&1 &
SERVICE_PID=$!
echo "后台服务 PID: $SERVICE_PID"

# 等待2秒确保后台服务启动
sleep 2

# 启动 FastAPI 应用
echo "🌐 启动 FastAPI 应用..."
nohup python main.py > logs/api.log 2>&1 &
API_PID=$!
echo "FastAPI 应用 PID: $API_PID"

echo "=" * 50
echo "✅ 所有服务已启动"
echo "📊 服务信息:"
echo "  - 后台服务 PID: $SERVICE_PID (日志: logs/service.log)"
echo "  - FastAPI 应用 PID: $API_PID (日志: logs/api.log)"
echo ""
echo "💡 使用以下命令管理服务:"
echo "  查看日志: tail -f logs/service.log 或 tail -f logs/api.log"
echo "  停止服务: kill $SERVICE_PID $API_PID"
echo "  或使用: ./stop.sh"

# 保存 PID 到文件
echo $SERVICE_PID > service.pid
echo $API_PID > api.pid

echo "🎉 启动完成！" 