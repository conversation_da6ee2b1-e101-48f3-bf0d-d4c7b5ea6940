import hashlib
import json
from collections import defaultdict

from aioredis import Redis
from fastapi import APIRouter, Depends
from sqlalchemy import or_
from sqlalchemy.ext.asyncio import AsyncSession

from app_user.crud import get_roles_by_account
from app_user.modules import get_permissions_modules, get_all_nodes, collect_all_children_ids
from app_user.serializer import RoleUpdate, RoleCreate, RouteCreate, RouteUpdate, RoleRouteCreate, RoleRouteUpdate, \
    AccountRoleCreate, AccountRoleUpdate
from models.models import ErpAccount, ErpRole, ErpRoleRoute, ErpRoute, ErpAccountRole
from settings import logger, CF
from utils.db.account_handler import UserDict, get_current_active_user, role_required
from utils.db.db_handler import get_default_db, get_redis
from utils.db.model_handler import ModelDataHelper
from utils.enum.enum_account import Roles, EmployeeStatus, EmployeeType
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

# 获取CRUD实例
ERP_ROLE = CF.get_crud(ErpRole)
erp_account = CF.get_crud(ErpAccount)
ERP_ROLE_ROUTE = CF.get_crud(ErpRoleRoute)
ERP_ROUTE = CF.get_crud(ErpRoute)
ERP_ACCOUNT_ROLE = CF.get_crud(ErpAccountRole)

# 创建APIRouter实例
router = APIRouter(prefix="/role", tags=["角色"])

# 允许访问本路由的角色
allow_role = []


@router.get("/role")
async def query_role(page: int, page_size: int,
                     user: UserDict = Depends(role_required(allow_role)),
                     db: AsyncSession = Depends(get_default_db)):
    """
    查询角色列表
    - 参数:
      - page: 页码
      - page_size: 每页显示的数量
    - 返回: 分页后的角色列表
    """
    data = await ERP_ROLE.get_many_with_pagination(db, page, page_size, {})
    count = await ERP_ROLE.get_many(db, {})
    return await ApiSuccessResponse({
        "data": data,
        "count": len(count),
    })


@router.get("/role/{role_id}")
async def get_role(role_id: int,
                   user: UserDict = Depends(role_required(allow_role)),
                   db: AsyncSession = Depends(get_default_db)):
    """
    获取角色详情
    - 参数:
      - role_id: 角色ID
    - 返回: 角色详情
    """
    role = await ERP_ROLE.get_by_id(db, role_id)
    if not role:
        return await ApiFailedResponse('角色不存在')
    return await ApiSuccessResponse(role)


@router.post("/role")
async def create_role(role: RoleCreate,
                      user: UserDict = Depends(role_required(allow_role)),
                      db: AsyncSession = Depends(get_default_db)):
    """
    创建新角色
    - 参数:
      - role: 角色创建数据
    - 返回: 创建的角色
    """
    new_role = await ERP_ROLE.create(db, commit=True, **{
        "role_name": role.role_name,
        "role_desc": role.role_desc,
    })
    return await ApiSuccessResponse(new_role)


@router.put("/role/{role_id}")
async def update_role(role_id: int, role: RoleUpdate,
                      user: UserDict = Depends(role_required(allow_role)),
                      db: AsyncSession = Depends(get_default_db)):
    """
    更新角色信息
    - 参数:
      - role_id: 角色ID
      - role: 角色更新数据
    - 返回: 更新后的角色
    """
    existing_role = await ERP_ROLE.get_by_id(db, role_id)
    if not existing_role:
        return await ApiFailedResponse('角色不存在')
    updated_role = await ERP_ROLE.update_one(db, role_id, {
        "role_name": role.role_name,
        "role_desc": role.role_desc,
    })
    return await ApiSuccessResponse(updated_role)


@router.delete("/role/{role_id}")
async def delete_role(role_id: int,
                      user: UserDict = Depends(role_required(allow_role)),
                      db: AsyncSession = Depends(get_default_db)):
    """
    删除角色
    - 参数:
      - role_id: 角色ID
    - 返回: 删除成功的标志
    """
    existing_role = await ERP_ROLE.get_by_id(db, role_id)
    if not existing_role:
        return await ApiFailedResponse('角色不存在')
    await ERP_ROLE.delete_one(db, role_id)
    return await ApiSuccessResponse(True)


@router.get("/role_route/{role_id}")
async def get_role_route(role_id: int,
                         user: UserDict = Depends(role_required(allow_role)),
                         db: AsyncSession = Depends(get_default_db)):
    """
    获取角色路由详情
    - 参数:
      - role_id: 角色ID
    - 返回: 角色路由详情
    """
    routes = await ERP_ROLE_ROUTE.get_many(db, {"role_id": role_id})
    initial_route_ids = {route.route_id for route in routes}

    # 获取所有相关节点
    all_nodes = await get_all_nodes(db, ErpRoute)

    # 获取所有相关的节点ID
    all_route_ids = set(initial_route_ids)
    for route_id in initial_route_ids:
        child_route_ids = collect_all_children_ids(all_nodes, route_id)
        all_route_ids.update(child_route_ids)

    route_details = await ERP_ROUTE.get_many(db, raw=[ErpRoute.id.in_(list(all_route_ids))])
    return await ApiSuccessResponse(route_details)


@router.post("/role_route")
async def create_role_route(role_route: RoleRouteCreate,
                            user: UserDict = Depends(role_required(allow_role)),
                            db: AsyncSession = Depends(get_default_db)):
    """
    创建更新新角色路由
    - 参数:
      - role_route: 角色路由创建数据
    - 返回: 创建的角色路由
    """
    new_route_ids = role_route.route_ids
    exist_objs = await ERP_ROLE_ROUTE.get_many(db, {"role_id": role_route.role_id})
    old_route_ids = [i.route_id for i in exist_objs]
    remove_list = set(old_route_ids) - set(new_route_ids)
    add_list = set(new_route_ids) - set(old_route_ids)
    for i in add_list:
        await ERP_ROLE_ROUTE.create(db, commit=False, **{
            "role_id": role_route.role_id,
            "route_id": i,
        })
    for j in remove_list:
        await ERP_ROLE_ROUTE.delete_many(db, {
            "role_id": role_route.role_id,
            "route_id": j,
        }, commit=False)

    await db.commit()
    return await ApiSuccessResponse(True)


@router.delete("/role_route/{role_route_id}")
async def delete_role_route(role_route_id: int,
                            user: UserDict = Depends(role_required(allow_role)),
                            db: AsyncSession = Depends(get_default_db)):
    """
    删除角色路由
    - 参数:
      - role_route_id: 角色路由ID
    - 返回: 删除成功的标志
    """
    existing_role_route = await ERP_ROLE_ROUTE.get_by_id(db, role_route_id)
    if not existing_role_route:
        return await ApiFailedResponse("角色路由不存在")
    await ERP_ROLE_ROUTE.delete_one(db, role_route_id)
    return await ApiSuccessResponse(True)


# 路由增删改查

@router.get("/route")
async def query_route(
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db)):
    """
    查询路由列表
    - 参数:
    - 返回: 分页后的路由列表
    """
    data = await ERP_ROUTE.get_many(db, {})
    tree = ModelDataHelper.list_to_tree(root_pid=0, data=data)
    return await ApiSuccessResponse(tree)


@router.get("/route/{route_id}")
async def get_route(route_id: int,
                    user: UserDict = Depends(role_required(allow_role)),
                    db: AsyncSession = Depends(get_default_db)):
    """
    获取路由详情
    - 参数:
      - route_id: 路由ID
    - 返回: 路由详情
    """
    route = await ERP_ROUTE.get_by_id(db, route_id)
    if not route:
        return await ApiFailedResponse("路由不存在")
    return await ApiSuccessResponse(route)


@router.post("/route")
async def create_route(route: RouteCreate,
                       user: UserDict = Depends(role_required(allow_role)),
                       db: AsyncSession = Depends(get_default_db)):
    """
    创建新路由
    - 参数:
      - route: 路由创建数据
    - 返回: 创建的路由
    """
    new_route = await ERP_ROUTE.create(db, commit=True, **{
        "route_name": route.route_name,
        "route_path": route.route_path,
        "parent_id": route.parent_id,
        "meta": route.meta,
        "component": route.component,
        "identification": hashlib.md5(route.route_name.encode('utf-8')).hexdigest(),
    })
    return await ApiSuccessResponse(new_route)


@router.put("/route/{route_id}")
async def update_route(route_id: int, route: RouteUpdate,
                       user: UserDict = Depends(role_required(allow_role)),
                       db: AsyncSession = Depends(get_default_db)):
    """
    更新路由信息
    - 参数:
      - route_id: 路由ID
      - route: 路由更新数据
    - 返回: 更新后的路由
    """
    existing_route = await ERP_ROUTE.get_by_id(db, route_id)
    if not existing_route:
        return await ApiFailedResponse("路由不存在")
    update_item = {}
    if route.route_name:
        update_item.update({"route_name": route.route_name})
    if route.route_path:
        update_item.update({"route_path": route.route_path})
    if route.meta:
        update_item.update({"meta": route.meta})
    if route.component:
        update_item.update({"component": route.component})
    if route.parent_id > 0:
        update_item.update({"parent_id": route.parent_id})
    updated_route = await ERP_ROUTE.update_one(db, route_id, update_item)
    return await ApiSuccessResponse(updated_route)


@router.delete("/route/{route_id}")
async def delete_route(route_id: int,
                       user: UserDict = Depends(role_required(allow_role)),
                       db: AsyncSession = Depends(get_default_db)):
    """
    删除路由
    - 参数:
      - route_id: 路由ID
    - 返回: 删除成功的标志
    """
    existing_route = await ERP_ROUTE.get_by_id(db, route_id)
    if not existing_route:
        return await ApiFailedResponse("路由不存在")
    await ERP_ROUTE.delete_one(db, route_id)
    return await ApiSuccessResponse(True)


# 账户角色增删改查

@router.get("/account_role")
async def query_account_role(page: int = None,
                             page_size: int = None,
                             keywords: str = None,
                             user: UserDict = Depends(role_required(allow_role)),
                             db: AsyncSession = Depends(get_default_db)):
    """
    查询账户角色列表
    - 参数:
      - page: 页码
      - page_size: 每页显示的数量
    - 返回: 分页后的账户角色列表
    """
    conditions = [
        ErpAccount.employee_status.in_([
            EmployeeStatus.EMPLOYED.value,
            EmployeeStatus.IN_HANOVER.value,
            EmployeeStatus.PROBATION.value
        ]),

    ]
    if keywords:
        conditions.append(
            or_(
                ErpAccount.employee_name.ilike(f"%{keywords}%"),
                ErpAccount.employee_number.ilike(f"%{keywords}%"),
                ErpAccount.username.ilike(f"%{keywords}%"),
            )
        )
    # 分页查询账户
    accounts = await erp_account.get_many_with_pagination(db,
                                                          page=page,
                                                          page_size=page_size,
                                                          raw=conditions,
                                                          reverse=False)
    # 统计总数
    accounts_count_data = await erp_account.get_many(db, raw=conditions)
    # 构建账户角色缓存字典
    role_account = await get_roles_by_account(db)
    role_account_map = defaultdict(list)
    for i in role_account:
        role_account_map[i.account_id].append(i)
    # ^^^^^^^^^^

    for obj in accounts:
        obj.roles = role_account_map.get(obj.id)
    data = ModelDataHelper.filter_field_module(accounts, fields=[
        "employee_name", "employee_number", "username", "roles",
        "employee_gender", "employee_status", "employee_type", "id"
    ])
    return await ApiSuccessResponse({
        "data": data,
        "count": len(accounts_count_data),
    })


@router.get("/account_role/{account_id}")
async def get_roles(
        account_id: int,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db)):
    """
    获取账户角色详情
    - 参数:
      - account_id: 账户ID
    - 返回: 账户角色详情
    """
    account_role = await ERP_ACCOUNT_ROLE.get_many(db, {"account_id": account_id})
    for i in account_role:
        role = await ERP_ROLE.get_one(db, id=i.role_id)
        if not role:
            continue
        i.role_name = role.role_name
        i.role_desc = role.role_desc
        i.account_role_id = i.id
    if not account_role:
        return await ApiSuccessResponse([])
    return await ApiSuccessResponse(account_role)


@router.post("/account_role")
async def create_account_role(account_role: AccountRoleCreate,
                              user: UserDict = Depends(role_required(allow_role)),
                              db: AsyncSession = Depends(get_default_db)):
    """
    创建新账户角色
    - 参数:
      - account_role: 账户角色创建数据
    - 返回: 创建的账户角色
    """
    new_account_role = await ERP_ACCOUNT_ROLE.create(db, commit=True, **{
        "role_id": account_role.role_id,
        "account_id": account_role.account_id,
    })
    return await ApiSuccessResponse(new_account_role)


@router.delete("/account_role/{account_role_id}")
async def delete_account_role(account_role_id: int,
                              user: UserDict = Depends(role_required(allow_role)),
                              db: AsyncSession = Depends(get_default_db)):
    """
    删除账户角色
    - 参数:
      - account_role_id: 账户角色ID
    - 返回: 删除成功的标志
    """
    existing_account_role = await ERP_ACCOUNT_ROLE.get_by_id(db, account_role_id)
    if not existing_account_role:
        return await ApiFailedResponse("账户角色不存在")
    await ERP_ACCOUNT_ROLE.delete_one(db, account_role_id)
    return await ApiSuccessResponse(True)


@router.get("/permission")
async def get_user_permission(
        flatten: bool = False,
        account_id: int = None,
        # old: bool = False,
        user: UserDict = Depends(role_required(allow_role)),
        db: AsyncSession = Depends(get_default_db),
        redis: Redis = Depends(get_redis)
):
    """
    查询本账户角色
    - 说明：
      - 默认返回所有权限以及其子权限，flatten可展平并去重权限
    - 参数:
      - account_role_id: 账户角色ID
      - flatten: 是否展平并去重
      - old: 老接口的， 所有权限部分层级全部展示
    - 返回: 账户角色权限详情
    """
    if not account_id:
        account_id = user.uid
    # 实现逻辑
    data = await get_permissions_modules(db, account_id, flatten)
    return await ApiSuccessResponse(data)


