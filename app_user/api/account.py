import copy
import random
from datetime import datetime
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

import settings
from app_human_resources.crud import get_account_with_page
from app_user.crud import account_base, get_departments_by_account
from app_user.serializer import AccountEntity, AccountEntityUpdate, DeleteAccount
from models.m_teacher import ErpAccountTeacher
from models.models import ErpAccount, ErpSalaryBase, ErpJobLevel, ErpAccountDepartment, ErpDepartment
from modules.qy_wechat.base import QWechatBase
from settings import logger, CF, INIT_PASSWORD
from utils.db.account_handler import UserDict, AccountHandler, get_current_active_user
from utils.db.db_handler import get_default_db
from utils.db.model_handler import ModelDataHelper
from utils.enum.enum_account import EmployeeStatus, EmployeeType, SyncStatus
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

erp_account = CF.get_crud(ErpAccount)
ERP_SALARY_BASE = CF.get_crud(ErpSalaryBase)
ERP_ACCOUNT_TEACHER = CF.get_crud(ErpAccountTeacher)
ERP_ACCOUNT_DEPARTMENT = CF.get_crud(ErpAccountDepartment)
ERP_DEPARTMENT = CF.get_crud(ErpDepartment)

router = APIRouter(prefix="/account", tags=["账户"])


@router.post("/account")
async def create_erp_account(
        account: AccountEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db)):
    """
    # 创建ERP账户， 入职
    """
    exist_username = await erp_account.get_one(db, username=account.username,
                                               employee_status=EmployeeStatus.EMPLOYED.value)
    if exist_username:
        return await ApiFailedResponse('该手机号用户已存在')
    employee_number = await ModelDataHelper().create_increase_num(db, ErpAccount, "id", prefix="CD")
    account_item = {
        "username": account.username,
        "password": AccountHandler.get_password_hash(INIT_PASSWORD),
        "employee_number": employee_number,
        "employee_name": account.employee_name,
        "employee_idcard": account.employee_idcard,
        "employee_gender": account.employee_gender,
        "employee_education": account.employee_education,
        "employee_hire_date": account.employee_hire_date,
        "employee_birth": account.employee_birth,
        "employee_city": account.employee_city,
        "employee_address": account.employee_address,
        "employee_emergency": account.employee_emergency,
        "employee_status": account.employee_status,
        "employee_type": account.employee_type,  # 前端传递的职业类型
        "employee_major": account.employee_major,  # 专业
        "create_time": datetime.now(settings.TIME_ZONE),
        "update_time": datetime.now(settings.TIME_ZONE),
        "create_by": user.uid,
        "update_by": user.uid,
        "sync_status": SyncStatus.SUCCESS.value,
        "sync_msg": "success",
        "is_teacher": account.is_teacher or 0,
        "level_id": account.job_level or 2,
        "avatar": account.avatar or ""
    }
    qw_base = QWechatBase()
    address_access_token = await qw_base.get_address_access_token()
    access_token = await qw_base.get_access_token()
    erp_dept_ids = account.dept_ids
    dept_objs = await ERP_DEPARTMENT.get_many(db, raw=[
        ErpDepartment.id.in_(erp_dept_ids)
    ])
    qy_dept_ids = [i.qy_wechat_dept_id for i in dept_objs]

    userid_resp = await qw_base.get_userid_by_phone(address_access_token, phone=account.username)
    if not userid_resp:  # 如果企微不存在该用户
        userid = f"{account.username}_{random.randint(1000, 9999)}@jjswerp"
        obj = await qw_base.create_user(address_access_token, {
            "userid": userid,
            "name": account.employee_name,
            "mobile": account.username,
            "department": qy_dept_ids,
            "direct_leader": account.direct_leader,
            "position": account.position,
            "enable": 1,
        })
        if not obj:
            return await ApiFailedResponse(f'企微用户创建失败：{account.username}，{account.employee_name}')
    else:
        userid = userid_resp.get('userid')
    user_detail_info = await qw_base.get_user_detail_info(access_token, userid)
    if user_detail_info:
        account_item.update({
            "qy_wechat_nickname": user_detail_info.get('name'),
        })
    openid_resp = await qw_base.get_openid_by_userid(address_access_token, userid)
    qy_wechat_openid = openid_resp.get('openid') if openid_resp else ""
    account_item.update({
        "qy_wechat_openid": qy_wechat_openid,
        "qy_wechat_userid": userid,
        # "qy_wechat_department": qy_dept_ids,
        "qy_wechat_direct_leader": account.direct_leader,
        "qy_wechat_position": account.position,
    })
    new_employee = await erp_account.create(db, commit=False, **account_item)
    # 关联部门表
    for dept_id in erp_dept_ids:
        await ERP_ACCOUNT_DEPARTMENT.create(db, commit=False, **{
            "account_id": new_employee.id,
            "dept_id": dept_id,
            "is_main_dept": 1,
            "create_by": user.uid,
            "update_by": user.uid,
        })
    # 创建薪资表
    await ERP_SALARY_BASE.create(db, commit=False, **{
        "account_id": new_employee.id,
        "salary_base": account.salary_base,
        "salary_performance": account.salary_performance,
        "bank_city": account.bank_city,
        "bank_sub_name": account.bank_sub_name,
        "bank_card_number": account.bank_card_number,
    })
    # 创建教师表
    if account.is_teacher > 0:
        await ERP_ACCOUNT_TEACHER.create(db, commit=False, **{
            "account_id": new_employee.id,
            "teacher_grade": account.teacher_grade,
            "teacher_subject": account.teacher_subject,
            "teacher_certification": account.teacher_certification,
            "teacher_fee": account.teacher_fee,
        })

    await db.commit()
    return await ApiSuccessResponse(new_employee)


@router.put("/account/{account_id}")
async def update_erp_account(
        account_id: int,
        account: AccountEntityUpdate,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db)):
    """
    # 编辑账户
    """
    exist_username = await erp_account.get_one(db, id=account_id)
    if not exist_username:
        return await ApiFailedResponse('该用户不存在')
    user_id = copy.deepcopy(exist_username.qy_wechat_userid)
    update_item = {"update_by": user.uid,
                   "update_time": datetime.now()
                   }
    qy_update_item = {"userid": user_id}
    if account.password:
        update_item.update({"password": AccountHandler.get_password_hash(account.password)})
    if account.username and account.username != exist_username.username:
        update_item.update({"username": account.username})
    if account.employee_name and account.employee_name != exist_username.employee_name:
        update_item.update({"employee_name": account.employee_name})
        qy_update_item.update({"name": account.employee_name})
    if account.employee_idcard and account.employee_idcard != exist_username.employee_idcard:
        update_item.update({"employee_idcard": account.employee_idcard})
    if account.employee_gender and account.employee_gender != exist_username.employee_gender:
        update_item.update({"employee_gender": account.employee_gender})
        qy_update_item.update({"gender": account.employee_gender})
    if account.employee_education and account.employee_education != exist_username.employee_education:
        update_item.update({"employee_education": account.employee_education})
    if account.employee_hire_date and account.employee_hire_date != exist_username.employee_hire_date:
        update_item.update({"employee_hire_date": account.employee_hire_date})
    if account.employee_birth and account.employee_birth != exist_username.employee_birth:
        update_item.update({"employee_birth": account.employee_birth})
    if account.employee_city and account.employee_city != exist_username.employee_city:
        update_item.update({"employee_city": account.employee_city})
    if account.employee_address and account.employee_address != exist_username.employee_address:
        update_item.update({"employee_address": account.employee_address})
        qy_update_item.update({"address": account.employee_address})
    if account.employee_emergency and account.employee_emergency != exist_username.employee_emergency:
        update_item.update({"employee_emergency": account.employee_emergency})
    if account.employee_status is not None and account.employee_status != exist_username.employee_status:
        update_item.update({"employee_status": account.employee_status})
    if account.employee_type and account.employee_type != exist_username.employee_type:
        update_item.update({"employee_type": account.employee_type})
    if account.employee_major and account.employee_major != exist_username.employee_major:
        update_item.update({"employee_major": account.employee_major})
    
    if account.avatar and account.avatar != exist_username.avatar:
        update_item.update({"avatar": account.avatar})

    if account.is_teacher is not None and account.is_teacher != exist_username.is_teacher:
        update_item.update({"is_teacher": account.is_teacher})
    if account.level_id > 0 and account.level_id != exist_username.level_id:
        update_item.update({"level_id": account.level_id})

    # if account.qy_wechat_department_ids and account.qy_wechat_department_ids != exist_username.qy_wechat_department:
    #     update_item.update({"qy_wechat_department": account.qy_wechat_department_ids})
    #     qy_update_item.update({"department": account.qy_wechat_department_ids})
    # if account.department_ids:

    if account.qy_wechat_direct_leader and account.qy_wechat_direct_leader != exist_username.qy_wechat_direct_leader:
        update_item.update({"qy_wechat_direct_leader": account.qy_wechat_direct_leader})
        qy_update_item.update({"direct_leader": account.qy_wechat_direct_leader})
    if account.qy_wechat_position and account.qy_wechat_position != exist_username.qy_wechat_position:
        update_item.update({"qy_wechat_position": account.qy_wechat_position})
        qy_update_item.update({"position": account.qy_wechat_position})

    # 更新部门
    if account.dept_ids:
        # 先更新企微dict
        dept_objs = await ERP_DEPARTMENT.get_many(db, raw=[
            ErpDepartment.id.in_(account.dept_ids)
        ])
        qy_dept_ids = [i.qy_wechat_dept_id for i in dept_objs]
        qy_update_item.update({"department": qy_dept_ids})

        # 更新ERP dict
        old_dept = await ERP_ACCOUNT_DEPARTMENT.get_many(db, {"account_id": account_id})
        old_dept_ids = [i.dept_id for i in old_dept]
        new_dept_ids = account.dept_ids
        # print(f"new:{new_dept_ids}, old:{old_dept_ids}")
        remove_list = set(old_dept_ids) - set(new_dept_ids)
        add_list = set(new_dept_ids) - set(old_dept_ids)
        for i in add_list:
            await ERP_ACCOUNT_DEPARTMENT.create(db, commit=False, **{
                "account_id": account_id,
                "dept_id": i,
            })
        for j in remove_list:
            await ERP_ACCOUNT_DEPARTMENT.delete_many(db, {
                "account_id": account_id,
                "dept_id": j,
            }, commit=False)

    # 更新员工表
    await erp_account.update_one(db, obj_id=account_id, new_values=update_item, commit=False)

    # 创建教师表
    if account.is_teacher > 0:
        exist_teacher = await ERP_ACCOUNT_TEACHER.get_one(db, account_id=account_id)
        if exist_teacher:
            update_teacher_item = {}
            if account.teacher_grade and account.teacher_grade != exist_teacher.teacher_grade:
                update_teacher_item.update({"teacher_grade": account.teacher_grade})
            if account.teacher_subject and account.teacher_subject != exist_teacher.teacher_subject:
                update_teacher_item.update({"teacher_subject": account.teacher_subject})
            if account.teacher_certification and account.teacher_certification != exist_teacher.teacher_certification:
                update_teacher_item.update({"teacher_certification": account.teacher_certification})
            if account.teacher_fee and account.teacher_fee != exist_teacher.teacher_fee:
                update_teacher_item.update({"teacher_fee": account.teacher_fee})
            await ERP_ACCOUNT_TEACHER.update_one(db, obj_id=exist_teacher.id, new_values=update_teacher_item,
                                                 commit=False)
        else:
            await ERP_ACCOUNT_TEACHER.create(db, commit=False, **{
                "account_id": account_id,
                "teacher_grade": account.teacher_grade,
                "teacher_subject": account.teacher_subject,
                "teacher_certification": account.teacher_certification,
                "teacher_fee": account.teacher_fee,
            })
    await db.commit()

    if not user_id:
        return await ApiFailedResponse(f'数据库已更新，但企微未同步：userid不存在，读取userid为：{user_id}')
    qw_base = QWechatBase()
    address_access_token = await qw_base.get_address_access_token()
    qy_update_resp = await qw_base.update_user(address_access_token, params=qy_update_item)
    logger.info('qy_update_resp', qy_update_resp)
    return await ApiSuccessResponse(True)


@router.post("/manual_sync_account")
async def sync_account_erp2qw(
        username: str,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db)):
    """
    # 用于更新手机号后同步ERP和企微
    :param username:
    :return:
    """
    qw_base = QWechatBase()
    address_access_token = await qw_base.get_address_access_token()
    user = await erp_account.get_one(db, username=username)
    if not user:
        return await ApiFailedResponse('ERP中无此用户')
    qy_wechat_user = await qw_base.get_userid_by_phone(address_access_token, user.username)
    if not qy_wechat_user:
        # print(f'正在同步：{user.username}, {user.nickname}, user不存在')
        await erp_account.update_one(db, user.id, {
            "sync_msg": "userid sync failed",
            "sync_status": SyncStatus.FAILED.value,
        }, commit=False)
        return await ApiFailedResponse()
    qy_wechat_userid = qy_wechat_user['userid']
    openid_obj = await qw_base.get_openid_by_userid(address_access_token, qy_wechat_userid)
    open_id = openid_obj.get('openid') if openid_obj else ""
    print(f'正在同步：{user.username}, {user.employee_name}')
    await erp_account.update_one(db, user.id, {
        "password": AccountHandler.get_password_hash(INIT_PASSWORD),
        "openid": open_id,
        "employee_status": EmployeeStatus.INACTIVE.value,
        "qy_wechat_userid": qy_wechat_userid,
        "create_by": user.uid,
        "update_by": user.uid,
        "sync_msg": "success",
        "sync_status": SyncStatus.SUCCESS.value,
    })
    return await ApiSuccessResponse(True)


@router.get("/account")
async def query_erp_account(
        keywords: str = None,
        page: int = None,
        page_size: int = None,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 分页查询账户
    """
    # 数据
    data = await get_account_with_page(db, page, page_size, keywords=keywords)
    count = await get_account_with_page(db, page, page_size, count=True, keywords=keywords)

    # 收集所有需要查询的ID
    leader_ids = set()
    account_ids = set()
    level_ids = set()

    for employee in data:
        if employee.qy_wechat_direct_leader:
            leader_ids.update(employee.qy_wechat_direct_leader)
        account_ids.add(employee.id)
        if employee.level_id:
            level_ids.add(employee.level_id)

    # 批量查询并建立映射
    # 1. 职级映射
    ERP_JOB_LEVEL = CF.get_crud(ErpJobLevel)
    job_levels = await ERP_JOB_LEVEL.get_many(db)
    job_level_map = {i.id: i.level_name for i in job_levels}

    # 2. 领导信息映射
    leaders = await erp_account.get_many(db, raw=[
        ErpAccount.qy_wechat_userid.in_(list(leader_ids))
    ]) if leader_ids else []
    leader_map = {i.qy_wechat_userid: i.employee_name for i in leaders}

    # 3. 部门信息映射
    # departments = await get_departments_by_accounts(db, list(account_ids))  # 假设这是一个批量查询部门的函数
    departments = await get_departments_by_account(db, account_ids=list(account_ids))
    dept_map = {}
    for dept in departments:
        if dept.account_id not in dept_map:
            dept_map[dept.account_id] = []
        dept_map[dept.account_id].append(dept.dept_name)

    # 处理数据
    for employee in data:
        # 设置领导名称
        if employee.qy_wechat_direct_leader:
            employee.leader_name = ', '.join(
                leader_map.get(leader_id, '')
                for leader_id in employee.qy_wechat_direct_leader
            )
        else:
            employee.leader_name = ''

        # 设置职级名称
        employee.level_name = job_level_map.get(employee.level_id)

        # 设置部门名称
        employee.dept_names = dept_map.get(employee.id, [])

    # 筛选要展示的字段
    display_fields = [
        "username", "qy_wechat_userid", "employee_number", "employee_name", "employee_idcard",
        "employee_gender", "employee_education", "employee_hire_date", "employee_leave_date",
        "employee_birth", "employee_city", "employee_address", "employee_emergency",
        "employee_status", "employee_type", "create_by", "qy_wechat_openid",
        "qy_wechat_nickname", "qy_wechat_department", "qy_wechat_direct_leader",
        "qy_wechat_position", "sync_msg", "is_teacher", "level_id", "id",
        "employee_major", "leader_name", "level_name", "dept_names","avatar"
    ]

    data = ModelDataHelper.filter_field_module(data, fields=display_fields)

    return await ApiSuccessResponse({
        "count": count,
        "data": data
    })

    # # 职级
    # ERP_JOB_LEVEL = CF.get_crud(ErpJobLevel)
    # job_level = await ERP_JOB_LEVEL.get_many(db, )
    # job_level_map = {i.id: i.level_name for i in job_level}
    # # 附加数据
    # for employee_obj in data:
    #     # 增加直属领导
    #     if employee_obj.qy_wechat_direct_leader:
    #         leaders = await erp_account.get_many(db, raw=[
    #             ErpAccount.qy_wechat_userid.in_(employee_obj.qy_wechat_direct_leader)
    #         ])
    #         employee_obj.leader_name = ', '.join([i.employee_name for i in leaders])
    #     else:
    #         employee_obj.leader_name = ''
    #     employee_obj.level_name = job_level_map.get(employee_obj.level_id)
    #     # 增加部门
    #     dept = await get_departments_by_account(db, employee_obj.id)
    #     employee_obj.dept_names = [i.dept_name for i in dept]
    #     # print(employee_obj.dept_names)
    #
    # # 筛选要展示的字段
    # data = ModelDataHelper.filter_field_module(data, fields=[
    #     "username", "qy_wechat_userid", "employee_number", "employee_name", "employee_idcard", "employee_gender",
    #     "employee_education", "employee_hire_date", "employee_leave_date", "employee_birth", "employee_city",
    #     "employee_address", "employee_emergency", "employee_status", "employee_type", "create_by",
    #     "qy_wechat_openid", "qy_wechat_nickname", "qy_wechat_department", "qy_wechat_direct_leader",
    #     "qy_wechat_position", "sync_msg", "is_teacher", "level_id", "id", "employee_major", "leader_name",
    #     "level_name", "dept_names"
    # ])
    # return await ApiSuccessResponse({
    #     "count": count,
    #     "data": data
    # })


@router.post("/account_exit")
async def account_exit(
        delete_account: DeleteAccount,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 离职
    """
    account = await erp_account.get_one(db, id=delete_account.account_id)
    if not account:
        return await ApiFailedResponse('用户不存在或不在职')
    await erp_account.update_one(db, obj_id=delete_account.account_id, new_values={
        "employee_status": EmployeeStatus.IN_HANOVER.value,
        "leave_reason": delete_account.leave_reason,
        "leave_reason_detail": delete_account.leave_reason_detail,
        "employee_leave_date": delete_account.employee_leave_date,
        "successor_account_id": delete_account.successor_account_id,
        "attachment": delete_account.attachment,
    })

    return await ApiSuccessResponse(True)


@router.get("/account/{account_id}")
async def query_account(
        account_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db),
):
    """
    # 查询账户详情
    """
    employee_obj = await account_base(db, account_id)
    employee_dict = dict(employee_obj)
    account_leaders = employee_dict.get('qy_wechat_direct_leader')
    if not account_leaders:
        leaders = []
    else:
        leaders = await erp_account.get_many(db, raw=[
            ErpAccount.qy_wechat_userid.in_(account_leaders)
        ])
    depts = await get_departments_by_account(db, employee_obj.id)
    employee_dict['leaders'] = ', '.join([i.employee_name for i in leaders]) or ''
    employee_dict['dept_names'] = [i.dept_name for i in depts]
    employee_dict['dept_ids'] = [i.dept_id for i in depts]
    return await ApiSuccessResponse(employee_dict)
