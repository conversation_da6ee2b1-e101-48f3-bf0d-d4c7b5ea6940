from fastapi import APIRouter, Depends
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from models.models import ErpAccount
from modules.qy_wechat.base import QWechatBase
from settings import logger, CF
from utils.db.account_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.db.db_handler import get_default_db
from utils.db.model_handler import ModelDataHelper
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

erp_account = CF.get_crud(ErpAccount)

router = APIRouter(prefix="/auth", tags=["认证"])


@router.get("/scan_code_login")
async def _(code: str, db: AsyncSession = Depends(get_default_db)):
    """
    # 扫码或工作台登录
    """
    qw_base = QWechatBase()
    access_token = await qw_base.get_access_token()  # 获取access_token
    # address_access_token = await qw_base.get_address_access_token()  # 获取access_token
    userinfo = await qw_base.get_user_info(code, access_token)  # 获取用户信息
    if not userinfo:
        return await ApiFailedResponse('企微通信错误')
    logger.info(userinfo)
    userid = userinfo.get('userid')
    account_info = await erp_account.get_one(db, qy_wechat_userid=userid)
    if not account_info:
        return await ApiSuccessResponse({
            "qy_wechat_userid": userid
        }, 'ERP账号不存在')
    # print('db_account:', ModelDataHelper.model_to_dict(account_info))
    userinfo = {
        "uid": account_info.id,
        "username": account_info.username,
        "outside": False,
    }
    token = AccountHandler.create_access_token(userinfo)
    return await ApiSuccessResponse(
        {"access_token": token,
         "token_type": "bearer",
         "header": "Authorization",
         "meta": {
             "employee_name": account_info.employee_name,
             "employee_number": account_info.employee_number
         },
         })


@router.post("/token")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(),
                                 db: AsyncSession = Depends(get_default_db)):
    user = await AccountHandler().authenticate_user(db, form_data.username, form_data.password)
    if not user:
        return await ApiFailedResponse('不正确的用户名或密码')
    userinfo = {
        "uid": user.uid,
        "username": user.username,
        "outside": False,
    }
    token = AccountHandler.create_access_token(userinfo)
    return {"access_token": token,
            "token_type": "bearer",
            "header": "Authorization",
            "meta": {
                "employee_name": user.employee_name,
                "employee_number": user.employee_number,
            },
            }
