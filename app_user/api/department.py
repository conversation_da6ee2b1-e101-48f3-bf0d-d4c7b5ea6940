from datetime import datetime
from typing import List

from fastapi import APIRouter, Depends
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

import settings
from app_human_resources.modules import get_account_ids_by_dept_id
from app_user.serializer import DepartmentEntity
from models.models import ErpAccount, ErpDepartment, ErpAccountDepartment
from modules.qy_wechat.base import Q<PERSON><PERSON>atBase
from settings import logger, CF
from utils.db.account_handler import UserDict, get_current_active_user
from utils.db.db_handler import get_default_db
from utils.db.model_handler import ModelDataHelper
from utils.response.response_handler import ApiSuccessResponse, ApiFailedResponse

erp_account = CF.get_crud(ErpAccount)
ERP_DEPARTMENT = CF.get_crud(ErpDepartment)
ERP_ACCOUNT_DEPARTMENT = CF.get_crud(ErpAccountDepartment)

router = APIRouter(prefix="/department", tags=["部门"])


@router.get("/department")
async def query_department(
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db)):
    """
    # 查询部门
    """
    data = await ERP_DEPARTMENT.get_many(db, reverse=False)
    accounts = await erp_account.get_many(db, )
    account_dict = {i.id: i.employee_name for i in accounts}
    for i in data:
        if not i.manager_id:
            i.manager_name = ''
        else:
            i.manager_name = account_dict.get(i.manager_id)

    data = ModelDataHelper.filter_field_module(data, fields=['id', 'dept_name', 'manager_id', 'parent_id',
                                                             'dept_level', 'qy_wechat_dept_id', 'manager_name'])
    tree_data = ModelDataHelper.list_to_tree(1, data, )
    return await ApiSuccessResponse(tree_data)


@router.get("/account_by_department_id")
async def account_by_department_id(
        dept_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db)):
    """
    # 根据部门id查询账户信息
    """
    depts, account_ids = await get_account_ids_by_dept_id(db, dept_id)
    accounts = await erp_account.get_many(db, raw=[
        ErpAccount.id.in_(account_ids)
    ])
    data = ModelDataHelper.filter_field_module(accounts, fields=["id", "username", "employee_number", "employee_name",
                                                                 "qy_wechat_userid"])

    return await ApiSuccessResponse(data)


@router.post("/department")
async def create_department(
        department: DepartmentEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db)):
    """
    # 新增部门
    """
    qw_base = QWechatBase()
    address_access_token = await qw_base.get_address_access_token()
    p_dept = await ERP_DEPARTMENT.get_by_id(db, department.parent_id)
    resp = await qw_base.create_department(address_access_token, {
        "name": department.dept_name,
        "parentid": p_dept.qy_wechat_dept_id,
        "order": 1,
    })
    if not resp:
        return await ApiFailedResponse('企微通信失败')
    qy_wechat_dept_id = resp.get('id')
    obj = await ERP_DEPARTMENT.create(db, commit=True, **{
        "dept_name": department.dept_name,
        "manager_id": department.manager_id,
        "parent_id": department.parent_id,
        "create_by": user.uid,
        "update_by": user.uid,
        "dept_sort": 0,
        "qy_wechat_dept_id": qy_wechat_dept_id,

    })
    return await ApiSuccessResponse(obj)


@router.put("/department/{dept_id}")
async def update_department(
        dept_id: int,
        department: DepartmentEntity,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db)):
    """
    # 修改部门
    """
    qw_base = QWechatBase()
    address_access_token = await qw_base.get_address_access_token()
    current_dept = await ERP_DEPARTMENT.get_by_id(db, dept_id)
    qy_update_item = {
        "id": current_dept.qy_wechat_dept_id,
        "order": 1,
    }
    erp_update_item = {
        "create_by": user.uid,
        "update_by": user.uid,
        "dept_sort": 0,
    }
    if department.dept_name:
        qy_update_item.update({"name": department.dept_name})
        erp_update_item.update({"dept_name": department.dept_name})
    if department.manager_id:
        erp_update_item.update({"manager_id": department.manager_id})
    if department.parent_id:
        p_dept = await ERP_DEPARTMENT.get_by_id(db, department.parent_id)
        qy_update_item.update({"parentid": p_dept.qy_wechat_dept_id})
        erp_update_item.update({"parent_id": department.parent_id})
    resp = await qw_base.update_department(address_access_token, params=qy_update_item)
    if not resp:
        return await ApiFailedResponse('[企微]修改部门失败')
    obj = await ERP_DEPARTMENT.update_one(db,  dept_id, erp_update_item, commit=True)
    return await ApiSuccessResponse(obj)


@router.delete("/department")
async def delete_department(
        dept_id: int,
        user: UserDict = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_default_db)):
    """
    # 删除部门
    """
    qw_base = QWechatBase()
    address_access_token = await qw_base.get_address_access_token()
    department = await ERP_DEPARTMENT.get_by_id(db, dept_id)
    resp = await qw_base.delete_department(address_access_token, department.qy_wechat_dept_id)
    if not resp:
        return await ApiFailedResponse('企微通信失败')
    await ERP_DEPARTMENT.delete_one(db, dept_id)
    return await ApiSuccessResponse(True)
