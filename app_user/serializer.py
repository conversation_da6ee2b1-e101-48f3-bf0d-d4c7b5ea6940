from datetime import datetime, date
from typing import Union, List, Optional
from pydantic import BaseModel, Json


class AccountEntity(BaseModel):
    username: str
    employee_name: Optional[str]
    employee_idcard: Optional[str]
    employee_gender: Optional[int]
    employee_education: Optional[int]
    employee_hire_date: Optional[date]
    employee_birth: Optional[date]
    employee_city: Optional[str]
    employee_address: Optional[str]
    employee_emergency: Optional[str]
    direct_leader: List[str]
    dept_ids: List[int]
    position: Optional[str]
    employee_status: Optional[int]
    employee_type: Optional[int]
    avatar: Optional[str]

    is_teacher: Optional[int]
    # # ******** 补充 ##
    # 专业
    employee_major: str
    # 职级
    job_level: Optional[int]  # erp_job_level
    # 教学学段
    teacher_grade: Optional[int]  # setting_key
    # 学科
    teacher_subject: Optional[int]  # setting_key
    # 教资编号
    teacher_certification: Optional[str]
    # 课时费
    teacher_fee: Optional[float]
    # 薪资相关
    salary_base: Optional[float]
    salary_performance: Optional[float]
    # 开户地
    bank_city: Optional[str]
    # 开户行
    bank_sub_name: Optional[str]
    # 卡号
    bank_card_number: Optional[str]


class AccountEntityUpdate(BaseModel):
    username: Optional[str]
    employee_name: Optional[str]
    employee_idcard: Optional[str]
    employee_gender: Optional[int]
    employee_education: Optional[int]
    employee_hire_date: Optional[date]
    employee_birth: Optional[date]
    employee_city: Optional[str]
    employee_address: Optional[str]
    employee_emergency: Optional[str]
    password: Optional[str]
    dept_ids: List[int]
    employee_status: Optional[int]
    employee_type: Optional[int]
    employee_major: Optional[str]
    salary_base: Optional[float]
    salary_performance: Optional[float]
    is_teacher: Optional[bool]
    level_id: Optional[int]
    avatar: Optional[str]
    # qy_wechat_department_ids: Optional[List[int]]
    qy_wechat_direct_leader: Optional[List[str]]
    qy_wechat_position: Optional[str]
    # 教师表
    teacher_grade: Optional[int]
    teacher_subject: Optional[int]
    teacher_certification: Optional[str]
    teacher_fee: Optional[float]


class RoleCreate(BaseModel):
    role_name: str
    role_desc: Optional[str]


class RoleUpdate(BaseModel):
    role_name: Optional[str]
    role_desc: Optional[str]


class AccountRoleCreate(BaseModel):
    role_id: int
    account_id: int


class AccountRoleUpdate(BaseModel):
    role_id: Optional[int]
    account_id: Optional[int]


class RoleRouteCreate(BaseModel):
    role_id: int
    route_ids: List[int]


class RoleRouteUpdate(BaseModel):
    role_id: Optional[int]
    route_id: Optional[int]


class RouteCreate(BaseModel):
    route_name: str
    route_path: str
    parent_id: Optional[int]
    meta: Optional[Json]
    component: Optional[str]


class RouteUpdate(BaseModel):
    route_name: Optional[str]
    route_path: Optional[str]
    parent_id: Optional[int]
    meta: Optional[Json]
    component: Optional[str]


class DeleteAccount(BaseModel):
    account_id: int
    leave_reason: int
    leave_reason_detail: Optional[str]
    employee_leave_date: datetime
    successor_account_id: int
    attachment: Optional[Json]


class DepartmentEntity(BaseModel):
    dept_name: str
    manager_id: int
    parent_id: int
