import time
from enum import Enum
from typing import Union
from fastapi import Depends, HTTPException
from jose import JWTError, jwt
from pydantic import BaseModel
from fastapi.security import OAuth2PasswordBearer

from utils.db.account_handler import oauth2_scheme


# oauth2_scheme = OAuth2PasswordBearer(tokenUrl="https://server.mall.jjsw.vip/jjcourse/user/login")


class UserDictJJYW(BaseModel):
    uid: int
    HkAccountType: int
    ErpAccountType: int
    UserId: int
    ClassinUid: Union[int, None] = None


class AccountTokenKeyEnum(Enum):
    Erp = 'edu_userInfo'
    OldTree = 'Sys_Jwt_User'


async def get_user_key_info():
    return AccountTokenKeyEnum.OldTree.value


async def analysis_token(token: str, ):
    user_key_info = await get_user_key_info()
    try:
        decoded_token = jwt.decode(token, '', options={"verify_signature": False})
    except JWTError:
        # logger.error('jwt认证错误')
        return False
    if user_key_info not in decoded_token:
        return {"msg": "analysis_token: 令牌无效"}

    allow_time = int(decoded_token['exp'] - time.time())
    # logger.info(f'\033[91m** 令牌剩余时间：{allow_time} **\033[0m')
    if allow_time <= 0:
        return {"msg": "analysis_token: 令牌超期"}
    user_info = decoded_token[user_key_info]
    return user_info


async def get_current_user(token: str = Depends(oauth2_scheme)):
    access_token = token
    if not access_token:
        raise HTTPException(status_code=400, detail="请携带令牌")
    decode_token = await analysis_token(access_token)
    if not decode_token:
        raise HTTPException(status_code=400, detail="令牌错误")
    return UserDictJJYW(**decode_token)
