from sqlalchemy import select, and_, or_

from models.m_common import ErpClassinConfig
from models.m_teacher import ErpAccountTeacher
from models.models import ErpRoleRoute, ErpRoute, ErpAccountRole, ErpAccount, ErpRole, ErpAccountDepartment, \
    ErpJobLevel, ErpDepartment


#
# # 递归获取层级结构的函数
# async def fetch_routes(db, parent_id=None, account_id=None):
#     # 构建查询语句
#     stmt = select(
#         ErpRoute.id.label('route_id'),
#         ErpRoute.route_name.label('route_name'),
#         ErpRoute.route_path.label('route_path'),
#         ErpRoute.parent_id.label('parent_id'),
#         ErpRoute.component.label('component'),
#         ErpRoute.meta.label('meta'),
#     )
#
#     if parent_id is not None:
#         stmt = stmt.where(ErpRoute.parent_id == parent_id)
#     elif account_id is not None:
#         stmt = stmt.select_from(ErpAccountRole).join(
#             ErpRoleRoute, ErpAccountRole.role_id == ErpRoleRoute.role_id
#         ).join(
#             ErpRoute, ErpRoleRoute.route_id == ErpRoute.id
#         ).where(
#             ErpAccountRole.account_id == account_id,
#             ErpAccountRole.disable == 0,
#             ErpRoleRoute.disable == 0
#         )
#
#     result = await db.execute(stmt)
#     return result.fetchall()
#
#
# async def build_hierarchy(db, routes):
#     hierarchy = []
#     for route in routes:
#         children = await fetch_routes(db, parent_id=route.route_id)
#         hierarchy.append({
#             "route_id": route.route_id,
#             "route_name": route.route_name,
#             "route_path": route.route_path,
#             "parent_id": route.parent_id,
#             "meta": route.meta,
#             "component": route.component,
#             "children": await build_hierarchy(db, children)
#         })
#     return hierarchy
#
#
# async def get_permissions(db, account_id=None):
#     initial_routes = await fetch_routes(db, account_id=account_id)
#     return await build_hierarchy(db, initial_routes)


async def fetch_all_routes(db):
    """获取所有路由数据,只查询一次数据库"""
    stmt = select(
        ErpRoute.id.label('route_id'),
        ErpRoute.route_name.label('route_name'),
        ErpRoute.route_path.label('route_path'),
        ErpRoute.parent_id.label('parent_id'),
        ErpRoute.component.label('component'),
        ErpRoute.meta.label('meta'),
    )
    result = await db.execute(stmt)
    return {row.route_id: dict(row) for row in result.fetchall()}


async def fetch_user_route_ids(db, account_id):
    """获取用户有权限的路由ID列表"""
    stmt = select(ErpRoleRoute.route_id).select_from(ErpAccountRole).join(
        ErpRoleRoute, ErpAccountRole.role_id == ErpRoleRoute.role_id
    ).where(
        ErpAccountRole.account_id == account_id,
        ErpAccountRole.disable == 0,
        ErpRoleRoute.disable == 0
    )
    # compile_stmt = stmt.compile(compile_kwargs={"literal_binds": True})
    # print(compile_stmt)
    result = await db.execute(stmt)
    return {row.route_id for row in result.fetchall()}


def collect_parent_route_ids(routes_dict, route_ids):
    """收集指定路由ID的所有父节点ID"""
    all_route_ids = set(route_ids)
    
    def get_parent_ids(route_id):
        """递归获取指定路由的所有父节点ID"""
        if route_id not in routes_dict:
            return set()
        
        parent_ids = set()
        route = routes_dict[route_id]
        parent_id = route['parent_id']
        
        # 如果有父节点且父节点存在于路由字典中
        if parent_id is not None and parent_id != 0 and parent_id in routes_dict:
            parent_ids.add(parent_id)
            # 递归获取父节点的父节点
            parent_ids.update(get_parent_ids(parent_id))
        
        return parent_ids
    
    # 为每个用户权限路由收集其所有父节点
    for route_id in route_ids:
        parent_ids = get_parent_ids(route_id)
        all_route_ids.update(parent_ids)
    
    return all_route_ids


def build_tree(routes_dict, user_route_ids, parent_id=None):
    """构建树形结构"""
    tree = []
    for route_id, route in routes_dict.items():
        # 匹配逻辑：支持 parent_id 为 None、0 或指定值
        route_parent_id = route['parent_id']
        is_match = False
        
        if parent_id is None:
            # 查找根节点：parent_id 为 None 或 0 都视为根节点
            is_match = route_parent_id is None or route_parent_id == 0
        else:
            # 查找子节点：严格匹配 parent_id
            is_match = route_parent_id == parent_id
            
        if is_match and route_id in user_route_ids:
            node = route.copy()
            children = build_tree(routes_dict, user_route_ids, route_id)
            if children:
                node['children'] = children
            else:
                node['children'] = []
            tree.append(node)
    return tree


async def get_permissions(db, account_id=None):
    """获取权限树,自动包含父节点以确保完整的访问路径"""
    routes_dict = await fetch_all_routes(db)
    if account_id:
        # 获取用户直接拥有的权限
        user_route_ids = await fetch_user_route_ids(db, account_id)
        # 自动收集所有必要的父节点，确保用户能访问完整路径
        user_route_ids = collect_parent_route_ids(routes_dict, user_route_ids)
    else:
        user_route_ids = set(routes_dict.keys())
    return build_tree(routes_dict, user_route_ids, parent_id=None)


async def get_roles_by_account(db, account_id=None):
    selects = [
        ErpAccountRole.account_id,
        ErpRole.id,
        ErpRole.role_name,
        ErpRole.role_desc,
        ErpAccountRole.id.label('account_role_id')
    ]
    conditions = [
        ErpAccountRole.disable == 0,
        ErpRole.disable == 0
    ]
    if account_id:
        conditions.append(ErpAccountRole.account_id == account_id, )
    stmt = (
        select(*selects)
        .select_from(ErpAccountRole)
        .outerjoin(ErpRole, ErpRole.id == ErpAccountRole.role_id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()


async def account_base(db, account_id):
    selects = [
        ErpAccount.id,
        ErpAccount.employee_number,
        ErpAccount.username,
        ErpAccount.employee_name,
        ErpAccount.employee_gender,
        ErpAccount.employee_birth,
        ErpAccount.employee_education,
        ErpAccount.employee_major,
        ErpAccount.employee_hire_date,
        ErpAccount.employee_type,
        ErpAccount.employee_status,
        ErpAccount.is_teacher,
        ErpAccount.qy_wechat_position,
        ErpAccount.qy_wechat_direct_leader,
        # ErpAccount.qy_wechat_department,
        ErpAccount.level_id,
        ErpAccount.avatar,

        # ErpDepartment.dept_name,
        # ErpDepartment.id.label('department_id'),

        ErpAccountTeacher.teacher_subject,
        ErpAccountTeacher.teacher_grade,
        ErpAccountTeacher.teacher_certification,

        ErpAccountTeacher.teacher_avatar,
        ErpAccountTeacher.teacher_image,
        ErpAccountTeacher.teacher_desc,
        ErpAccountTeacher.teacher_qr_img,
        ErpAccountTeacher.teacher_tag,
        ErpAccountTeacher.teacher_fee,
        ErpJobLevel.level_name
    ]
    conditions = [
        ErpAccount.id == account_id
    ]
    stmt = (
        select(*selects)
        .select_from()
        .outerjoin(ErpAccountTeacher, ErpAccount.id == ErpAccountTeacher.account_id)
        .outerjoin(ErpJobLevel, ErpAccount.level_id == ErpJobLevel.id)
        # .outerjoin(ErpAccountDepartment, ErpAccountDepartment.account_id == ErpAccount.id)
        # .outerjoin(ErpDepartment, ErpDepartment.id == ErpAccountDepartment.dept_id)
        # .outerjoin(ErpAccountDepartment, ErpAccount.id == ErpAccountDepartment.account_id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchone()


async def get_departments_by_account(db, account_id=None, account_ids=None):
    selects = [
        ErpAccount.id.label('account_id'),
        ErpDepartment.id.label('dept_id'),
        ErpDepartment.dept_name
    ]
    conditions = [
        ErpAccountDepartment.disable == 0
    ]
    if account_id:
        conditions.append(ErpAccount.id == account_id)

    if account_ids:
        conditions.append(ErpAccount.id.in_(account_ids))
    stmt = (
        select(*selects)
        .select_from(ErpAccount)
        .outerjoin(ErpAccountDepartment, ErpAccountDepartment.account_id == ErpAccount.id)
        .outerjoin(ErpDepartment, ErpDepartment.id == ErpAccountDepartment.dept_id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()


async def get_classin_config(db):
    stmt = (
        select(ErpClassinConfig)
        .where(ErpClassinConfig.Group_Id == 100001)
    )
    result = await db.execute(stmt)
    return result.scalar()
