from typing import List, Set

from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from app_user.crud import get_permissions
from models.models import ErpAccount, ErpAccountDepartment, ErpDepartment
from utils.db.data_handler import query_child_obj


# # 展平树状层级并去除重复项
# def flatten_hierarchy(hierarchy):
#     """
#     展平树状层级
#     :param hierarchy:
#     :return:
#     """
#     flattened = []
#     seen = set()
#
#     def _flatten(node):
#         if node["route_id"] not in seen:
#             seen.add(node["route_id"])
#             flattened.append({
#                 "route_id": node["route_id"],
#                 "route_name": node["route_name"],
#                 "route_path": node["route_path"],
#                 "parent_id": node["parent_id"],
#                 "meta": node["meta"],
#                 "component": node["component"]
#             })
#             for child in node.get("children", []):
#                 _flatten(child)
#
#     for item in hierarchy:
#         _flatten(item)
#
#     return flattened
#
#
# def rebuild_hierarchy(permissions, all_permissions):
#     # 创建一个字典，用route_id作为键来快速查找权限
#     permission_map = {p['route_id']: p for p in all_permissions}
#
#     def build_tree(permission):
#         # 如果没有父节点，或者父节点不在所有权限中，则返回当前权限
#         if permission['parent_id'] == 0 or permission['parent_id'] not in permission_map:
#             return permission
#
#         parent = permission_map[permission['parent_id']]
#         # 递归构建父节点的树
#         parent_tree = build_tree(parent)
#
#         # 在父节点的children中查找当前权限
#         for child in parent_tree.get('children', []):
#             if child['route_id'] == permission['route_id']:
#                 return parent_tree
#
#         # 如果父节点中没有找到当前权限，则添加它
#         if 'children' not in parent_tree:
#             parent_tree['children'] = []
#         parent_tree['children'].append(permission)
#         return parent_tree
#
#     # 对每个用户权限构建树
#     trees = [build_tree(p) for p in permissions]
#
#     # 移除重复的树
#     unique_trees = []
#     for tree in trees:
#         if tree not in unique_trees:
#             unique_trees.append(tree)
#
#     return unique_trees
#
#
# def build_minimal_hierarchy(permissions, all_permissions):
#     # 创建一个字典，用route_id作为键来快速查找权限
#     permission_map = {p['route_id']: p for p in all_permissions}
#
#     def build_path(permission):
#         path = [permission]
#         current = permission
#         while current['parent_id'] != 0 and current['parent_id'] in permission_map:
#             parent = permission_map[current['parent_id']]
#             path.insert(0, parent)
#             current = parent
#         return path
#
#     def create_tree(path):
#         if not path:
#             return None
#         node = path[0].copy()
#         if len(path) > 1:
#             node['children'] = [create_tree(path[1:])]
#         else:
#             node['children'] = []
#         return node
#
#     # 为每个权限构建最小路径树
#     trees = [create_tree(build_path(p)) for p in permissions]
#
#     # 合并重复的树
#     def merge_trees(trees):
#         if not trees:
#             return []
#         result = []
#         for tree in trees:
#             existing = next((t for t in result if t['route_id'] == tree['route_id']), None)
#             if existing:
#                 existing['children'] = merge_trees(existing['children'] + tree['children'])
#             else:
#                 result.append(tree)
#         return result
#
#     return merge_trees(trees)
#
#
# async def get_permissions_modules(db, account_id, flatten):
#     from settings import CF
#     # 获取CRUD实例
#     erp_account = CF.get_crud(ErpAccount)
#     account = await erp_account.get_by_id(db, account_id)
#     if not account:
#         return []
#     all_permission = await get_permissions(db)
#     user_permission = await get_permissions(db, account.id)
#     if flatten:
#         user_permission = flatten_hierarchy(user_permission)
#         user_permission.sort(key=lambda x: x['route_id'], reverse=False)
#     else:
#         user_permission = build_minimal_hierarchy(user_permission, all_permission)
#     return user_permission


def flatten_hierarchy(hierarchy):
    """展平树状结构"""
    flattened = []
    seen = set()

    def _flatten(node):
        if node["route_id"] not in seen:
            seen.add(node["route_id"])
            flat_node = {k: v for k, v in node.items() if k != 'children'}
            flattened.append(flat_node)
            for child in node.get("children", []):
                _flatten(child)

    for item in hierarchy:
        _flatten(item)
    return flattened


async def get_permissions_modules(db, account_id, flatten):
    from settings import CF
    # 获取账户信息
    erp_account = CF.get_crud(ErpAccount)
    account = await erp_account.get_by_id(db, account_id)
    if not account:
        return []

    # 获取权限树
    permission_tree = await get_permissions(db, account.id)
    if flatten:
        # 展平并排序
        permissions = flatten_hierarchy(permission_tree)
        permissions.sort(key=lambda x: x['route_id'])
        return permissions
    return permission_tree


async def get_all_nodes(db: AsyncSession, model) -> List:
    """
    一次性获取所有节点
    :param db: 数据库会话
    :param model: 模型
    :return: 所有节点列表
    """
    stmt = select(model).where(model.disable == 0)
    result = await db.execute(stmt)
    return result.scalars().all()


def collect_all_children_ids(nodes: List, parent_id: int) -> Set[int]:
    """
    收集所有子节点ID
    :param nodes: 所有节点
    :param parent_id: 父节点ID
    :return: 子节点ID集合
    """
    all_nodes = set()

    def recursive_collect(node_id: int):
        children = [node for node in nodes if node.parent_id == node_id]
        for child in children:
            all_nodes.add(child.id)
            recursive_collect(child.id)

    recursive_collect(parent_id)
    return all_nodes


async def search_account_by_employee_name(db, keywords):
    selects = [
        ErpAccount.id,
        ErpAccount.employee_name,
        ErpAccount.qy_wechat_department,
        ErpAccount.qy_wechat_userid,
        ErpAccountDepartment.id.label('erp_account_department_id'),
        ErpDepartment.dept_name,
    ]
    conditions = [
        or_(
            ErpAccount.employee_name.ilike(f"%{keywords}%"),
            ErpAccount.employee_number.ilike(f"%{keywords}%"),
            ErpAccount.username.ilike(f"%{keywords}%"),
        ),
        ErpAccount.disable == 0,
        # ErpAccount.employee_status == EmployeeStatus.EMPLOYED.value
    ]
    stmt = (
        select(*selects)
        .select_from(ErpAccount)
        .outerjoin(ErpAccountDepartment, ErpAccount.id == ErpAccountDepartment.account_id)
        .outerjoin(ErpDepartment, ErpDepartment.id == ErpAccountDepartment.dept_id)
        .where(and_(*conditions))
    )
    result = await db.execute(stmt)
    return result.fetchall()
